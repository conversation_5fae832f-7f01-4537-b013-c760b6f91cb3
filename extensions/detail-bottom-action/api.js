import { requestV2 } from '@youzan/tee-biz-request';
import { errorToast } from '@youzan/tee-biz-util';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import { reverseGeocoder } from '@youzan/shop-tee-shared';
import { ZNB } from '@youzan/tee-biz-navigate';
import Config from './config';
import { getDistance } from './distance';

export default {
  /**
   * 确认收货
   * @param orderNo
   */
  confirmReceive({ orderNo, kdtId }) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/order/confirmReceiveV2.json',
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        withCredentials: true,
      })
        .then((res = {}) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  /**
   * 删除订单
   * @param orderNo
   */
  deleteOrder({ orderNo }) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/order/deleteOrder.json',
        method: 'POST',
        data: {
          order_no: orderNo,
        },
      })
        .then((res = {}) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  // 再来一单
  buyAgain(options) {
    const { url, data } = options;
    const { orderNo, kdtId } = data;
    return new Promise((resolve, reject) => {
      requestV2({
        path: url,
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        options: {
          rawResponse: true,
        },
      })
        .then((res = {}) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  // 延长收货
  extend(options) {
    const _option = {
      checkUrl: options.checkUrl || Config.EXTEND_RECEIVE.CHECK_EXTEND_URL,
      extendUrl: options.url || Config.EXTEND_RECEIVE.DO_EXTEND_URL,
      data: options.data || {},
      method: options.method,
      container: options.container,
    };
    return new Promise((resolve, reject) => {
      requestV2({
        path: options.checkUrl,
        data: options.data,
        method: options.method,
        options: {
          rawResponse: true,
        },
      }).then(
        (res) => {
          if (res && res.code === 0) {
            this._startExtend(Object.assign(_option, { resolve, reject }));
          }
        },
        (response) => {
          const err = response?.data || {};
          switch (err.code) {
            case Config.STATUS_MAP.TIME_NOT_ENOUGH:
            case Config.STATUS_MAP.ALREADY_EXTENDED:
              this._showInfoTips(err.msg, null, options.container);
              break;
            default:
              errorToast(response, { message: '延长收货失败，请稍后重试' });
              break;
          }
          reject(err);
        }
      );
    });
  },

  _startExtend(options) {
    Dialog.confirm({
      title: '延长收货时间',
      message: '每笔订单只能延长一次收货时间，如需多次延长请联系商家。',
      cancelButtonText: '确定延长',
      confirmButtonText: '取消',
      ref: options.container,
    }).catch(() => {
      requestV2({
        path: options.extendUrl,
        data: options.data,
        method: options.method,
        options: {
          rawResponse: true,
        },
      })
        .then((res) => {
          if (res && res.code === 0) {
            this._showInfoTips(res.msg, options.resolve, options.container);
          }
        })
        .catch((err) => {
          errorToast(err, { message: '延长收货失败，请稍后重试' });
          options.reject && options.reject();
        });
    });
  },

  _showInfoTips(msg = '', callback, container) {
    Dialog.alert({
      title: '延长收货时间',
      message: msg,
      confirmButtonText: '我知道了',
      ref: container,
    }).then(() => {
      callback && callback();
    });
  },
  // 再来一单，直接进入下单页
  directBuyAgain(options) {
    const { url, data } = options;
    const { orderNo, kdtId } = data;
    return new Promise((resolve, reject) => {
      requestV2({
        path: url,
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        options: {
          rawResponse: true,
        },
      })
        .then((res = {}) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  /* #ifdef web */
  async getAddress() {
    const list = await requestV2({
      path: '/wsctrade/uic/address/getAddressList.json',
      method: 'POST',
    });

    /**
     * 优先使用默认地址
     * 如果没有默认地址 取收货列表里距离当前定位 200m 以内的地址
     * 如果还没有则使用当前位置
     */
    let address = list.find((item) => item.isDefault);
    let location;

    if (!address) {
      location = await ZNB.getLocation().catch(() => ({}));
      const { latitude, longitude } = location || {};
      let minDistanceAddress;

      for (const item of list) {
        const distance = getDistance(latitude, longitude, +item.lat, +item.lon);
        item.distance = distance;

        if (distance < 200 && (!minDistanceAddress || distance < minDistanceAddress.distance)) {
          minDistanceAddress = item;
        }
      }

      address = minDistanceAddress;
    }

    if (!address) {
      if (!location) {
        location = await ZNB.getLocation().catch(() => ({}));
      }

      const poiInfo = await reverseGeocoder({ location }).catch(() => ({}));
      const { status, result } = poiInfo || {};
      if (status === 0) {
        address = {
          id: 0,
          province: result.ad_info.province,
          city: result.ad_info.city,
          county: result.ad_info.district,
          areaCode: result.ad_info.adcode,
          addressDetail: result.address,
          location: result.formatted_addresses.recommend,
          lat: location.latitude,
          lon: location.longitude,
        };
      }
    }

    // 尝试使用收货地址列表第 1 个
    if (!address && list.length) {
      address = list[0];
    }

    return {
      list,
      id: address?.id,
      current: address,
    };
  },

  localSale(data) {
    return requestV2({
      path: '/wsctrade/order/localSale.json',
      method: 'POST',
      data,
    }).then((result) => {
      const { kdtId, itemArriveTimeList = [] } = result;

      if (itemArriveTimeList[0]) {
        itemArriveTimeList[0].kdtId = kdtId;
      }

      return (kdtId && itemArriveTimeList[0]) || {};
    });
  },
  /* #endif */
};
