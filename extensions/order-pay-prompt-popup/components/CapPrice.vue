<template>
  <text class="cap-price">
    <text class="cap-price__unit">¥</text>
    <text class="cap-price__integer">{{ formatPrice[0] }}</text>
    <text class="cap-price__decimal">.{{ formatPrice[1] }}</text>
  </text>
</template>

<script>
export default {
  props: {
    price: {
      type: Number,
      default: 0,
    },
  },

  computed: {
    priceToNum() {
      return +this.price;
    },

    formatPrice() {
      const newPrice = (this.priceToNum / 100).toString();
      const priceArr = newPrice.split('.');
      if (priceArr.length === 1) {
        return [...priceArr, '00'];
      }
      if (priceArr.length > 1 && priceArr[1] < 10 && priceArr[1].length < 2) {
        return [priceArr[0], priceArr[1] + '0'];
      }

      return priceArr;
    },
  },
};
</script>

<style lang="scss" scoped>
.cap-price {
  &__unit {
    font-size: 12px;
    font-weight: bold;
  }

  &__integer {
    font-weight: bold;
    font-size: 18px;
  }

  &__decimal {
    font-weight: bold;
    font-size: 12px;
  }
}
</style>
