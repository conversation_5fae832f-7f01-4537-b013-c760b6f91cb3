import { url } from '@youzan/tee-util';
import Tee from '@youzan/tee';

export default {
  methods: {
    triggerLogger(et, ei, en) {
      const { info, componentName = '', bizName = '' } = this;
      const { linkOpt: { source = '' } = {} } = info;

      const { kdtId } = this;

      this.ctx.logger.log({
        et,
        ei,
        en,
        params: {
          biz_name: bizName,
          from_source: `${source}_${kdtId}`,
          component: componentName,
        },
      });
    },

    handelClick() {
      const { linkOpt: { path = '', href = '', source = '' } = {} } = this.info;
      const { kdtId } = this;

      if (!path && !href) return;

      this.triggerClickLogger && this.triggerClickLogger();

      let targetLink = path;
      /* #ifdef web */
      targetLink = href;
      /* #endif */

      const curPath = url.args.add(targetLink, {
        from_source: `${source}_${kdtId}`,
        kdt_id: kdtId,
      });

      // 小程序打开新小程序，web端打开链接
      /* #ifdef web */
      Tee.navigate({ url: curPath });
      /* #endif */

      /* #ifdef weapp */
      Tee.$native.navigateToMiniProgram({
        appId: 'wxf1fdc416d4ced1b3',
        path: curPath,
      });
      /* #endif */
    },
  },
};
