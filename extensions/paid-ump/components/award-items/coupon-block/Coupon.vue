<template>
  <award-container :custom-style="themeBackground5Style">
    <view class="coupon">
      <view class="goods">
        <view v-if="!curGoods.length" style="display: flex" @click="clickGoods(curGoods)">
          <image :src="curGoods.imageUrl" alt="goods-img" class="single-goods-img" />
          <view class="content">
            <view class="t-multi-ellipsis--l2 single-goods-name">{{ curGoods.title }}</view>
            <view v-if="isNotUndefined(curGoods.curPrice)">
              <view class="t-ellipsis single-goods-price">价格￥{{ curGoods.price }}</view>
              <view class="single-goods-cur-price t-ellipsis" :style="themeColorStyle">
                <goods-price :price="curGoods.curPrice + ''" prefix="券后价" />
              </view>
            </view>
            <view v-else class="single-goods-cur-price t-ellipsis" :style="themeColorStyle">
              <goods-price :price="curGoods.price + ''" />
            </view>
          </view>
        </view>
        <view v-else>
          <text>可用于以下商品</text>
          <view class="goods-container">
            <view
              v-for="(item, index) in curGoods"
              :key="index"
              class="goods-item"
              @click="clickGoods(item)"
            >
              <view v-if="item.imageUrl">
                <image :src="item.imageUrl" class="img" alt="goods-img" />
                <view class="price t-ellipsis">¥{{ item.price }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="preferential" :style="themeBorder30Style">
        <view class="hole hole-top"></view>
        <view class="hole hole-bottom"></view>

        <text class="money">
          <font-calculate
            :max-width="90"
            :cur-font-size="30"
            :min-font-size="12"
            :item="curCouponText"
            :color="themeColor"
          />
        </text>
        <view class="limit t-ellipsis">{{ curCoupon.thresholdCopywriting }}</view>
        <van-button
          custom-class="button"
          :color="themeColor"
          :loading="loading"
          round
          size="small"
          @click="clickBtn"
        >
          {{ !isReceived ? '立即领取' : '立即使用' }}
        </van-button>
        <van-count-down v-if="showDownTime" :time="downTime" use-slot @change="onChange">
          <view class="count-down" :style="themeColorStyle">
            {{ downTimeStr }}
          </view>
        </van-count-down>
        <view v-if="isReceived" class="received" :style="themeBorder50Style">
          <view :style="[themeBorder50Style, themeColor50Style, themeBackground10Style]">已领</view>
        </view>
      </view>
    </view>
  </award-container>
</template>

<script>
import args from '@youzan/utils/url/args';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Button from '@youzan/vant-tee/dist/button';
import CountDown from '@youzan/vant-tee/dist/count-down';
import Tee from '@youzan/tee';

import Price from '../../widgets/Price';
import AwardContainer from '../Container';
import FontCalculate from '../../widgets/FontCalculate';
import { COUPON_STATE, COUPON_STATE_FAIL_TEXT, RECEIVE_MAP } from '../../../constants';
import { takePromotionCoupon, getCouponRedirectPath } from '../../../api';
import formatMoney from '@youzan/utils/money/format';
import parseDate from '@youzan/utils/date/parseDate';
import { RGBA2RGB } from '../../../utils';

export default {
  name: 'coupon-card',
  components: {
    'van-button': Button,
    'van-count-down': CountDown,
    'goods-price': Price,
    'award-container': AwardContainer,
    'font-calculate': FontCalculate,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
    orderNo: String,
  },
  data() {
    return {
      isReceived: false,
      downTime: 0,
      /** 随机券专用 */
      curCouponValue: '',
      timeData: {},
      couponTakenId: 0,
      loading: false,
      memorizedGoodsAlias: [],
    };
  },
  computed: {
    themeBackground5Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.05)};`;
    },
    themeBackground10Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.1)};`;
    },
    themeBackgroundStyle() {
      return `background: ${this.themeColor};`;
    },
    themeBackground30Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.3)};`;
    },
    themeBorderStyle() {
      return `border-color: ${this.themeColor};`;
    },
    themeColorStyle() {
      return `color: ${RGBA2RGB(this.themeColor)};`;
    },
    themeBorder30Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.3)};`;
    },
    themeBorder50Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.5)};`;
    },
    themeColor50Style() {
      return `color: ${RGBA2RGB(this.themeColor, 0.5)};`;
    },
    curCouponText() {
      if (this.curCouponValue === '') return this.curCoupon;
      // 处理领取随机券之后的展示逻辑
      return {
        ...this.curCoupon,
        preDesc: '',
        valuesDesc: this.curCouponValue + '',
        unitDesc: '元',
      };
    },
    curGoods() {
      const { goods = [] } = this.info;

      const currentGoods = goods.slice(0, 3).map((item) => {
        const curItem = { ...item, price: +formatMoney(item.price, true, false) };
        if (this.isNotUndefined(curItem.preferentialPrice)) {
          curItem.curPrice = +formatMoney(item.preferentialPrice, true, false);
        }
        return curItem;
      });

      currentGoods.forEach((item) => {
        if (item?.alias && !this.memorizedGoodsAlias.includes(item.alias)) {
          this.memorizedGoodsAlias.push(item.alias);
          this.$emit('pure-log', {
            et: 'view', // 事件类型
            ei: 'view_goods_recommend', // 事件标识
            en: '支付有礼送券/码推荐商品曝光', // 事件名称
            params: {
              goods_alias: item.alias,
            },
          });
        }
      });

      if (currentGoods.length === 1) {
        return currentGoods[0];
      }
      if (currentGoods.length < 3) {
        const appendBlocks = new Array(3 - currentGoods.length).fill({});
        return [...currentGoods, ...appendBlocks];
      }
      return currentGoods;
    },
    // 单张优惠券
    curCoupon() {
      return this.info.coupon ?? {};
    },
    showDownTime() {
      return this.isReceived && this.downTime < 86400000 * 2 && this.downTime !== 0;
    },
    downTimeStr() {
      const { days, hours, minutes, seconds } = this.timeData;
      // 如果大于 2 天，直接不展示
      if (days > 1) return '';
      const times = [
        `${days}天`,
        `${this.timeFormat(hours)}:${this.timeFormat(minutes)}:${this.timeFormat(seconds)}`,
      ];
      if (days === 0) times.shift();
      return '仅剩' + times.join('');
    },
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        /** 更新券的领取状态 */
        this.isReceived = val.coupon.receiveStatus === RECEIVE_MAP.RECEIVED;
        this.couponTakenId = val.coupon.couponId;
        // 更新倒计时
        this.downTime = (val.coupon.expireAt ?? Date.now()) - Date.now();
      },
    },
  },

  methods: {
    isNotUndefined(obj) {
      return obj !== undefined;
    },
    clickGoods(goods) {
      if (goods.alias) {
        this.$emit('pure-log', {
          et: 'click', // 事件类型
          ei: 'click_goods_recommend', // 事件标识
          en: '支付有礼送券/码推荐商品点击', // 事件名称
          params: {
            goods_alias: goods.alias,
          },
        });
        Tee.navigate({
          url: args.add('/pages/goods/detail/index', {
            alias: goods.alias,
          }),
          type: 'navigateTo',
        });
      }
    },
    clickBtn() {
      this.$emit('log-attach', this.isReceived);

      if (!this.isReceived) {
        this.loading = true;
        takePromotionCoupon(this.orderNo)
          .then((data) => {
            this.loading = false;
            const errorText = COUPON_STATE_FAIL_TEXT[data.status];
            if (errorText) throw new Error(errorText);

            if (data.status === COUPON_STATE.HAS_FETCHED) {
              Toast('你已经领取过了');
            }
            this.couponTakenId = data.voucherIdentity.couponId;
            this.isReceived = true;
            if (this.curCoupon?.preDesc?.length > 0)
              this.curCouponValue = +formatMoney(data.value, true, false);
            this.downTime = parseDate(data.validEndTime).getTime() - Date.now();
          })
          .catch((e) => {
            this.loading = false;
            Toast(e.msg || e.message || '领取失败');
          });
      } else {
        getCouponRedirectPath({
          couponId: this.couponTakenId,
          groupType: this.info.coupon.activityTypeGroup === 2 ? 'code' : 'card',
        })
          .then(({ weappUrl, isSwitchTab }) => {
            Tee.navigate({ url: weappUrl, type: isSwitchTab ? 'reLaunch' : 'navigateTo' });
          })
          .catch(() => Toast('跳转异常，请稍后重试'));
      }
    },
    timeFormat(num) {
      return num < 10 ? '0' + num : num;
    },
    onChange(e) {
      this.timeData = e;
    },
  },
};
</script>

<style lang="scss" scoped>
.single-goods-img {
  border-radius: 8px;
  width: 96px;
  height: 96px;
  object-fit: cover;
  margin-right: 8px;
}

.content {
  // 72px = 最外层的 margin + padding, 107px = 右侧优惠信息展示，96px = 图片宽度，8px = 图片margin
  // 283px = 72px + 107px + 96px + 8px
  width: calc(100vw - 283px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.single-goods-price {
  font-size: 12px;
  color: #969799;
  text-decoration: line-through;
  margin-top: 16px;
}

.single-goods-cur-price {
  line-height: 20px;
  font-size: 12px;
  margin-top: 4px;
}

.single-goods-name {
  font-size: 14px;
  line-height: 20px;
}

.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.t-multi-ellipsis--l2 {
  -webkit-line-clamp: 2;
}
.t-multi-ellipsis--l2,
.t-multi-ellipsis--l3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.coupon {
  display: flex;
  overflow: hidden;
  height: 120px;
  padding: 0;
  border-radius: 8px;
}

.title {
  color: #969799;
  font-size: 14px;
}

.goods {
  flex: 1;
  padding: 12px;
  font-size: 12px;

  .goods-container {
    display: flex;
    margin-top: 8px;
    justify-content: space-between;

    .goods-item {
      width: 60px;
      position: relative;

      .img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
      }

      .price {
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        line-height: 16px;
        bottom: 0;
        left: 0;
        right: 0;
        color: #fff;
        text-align: center;
        border-radius: 0 0 4px 4px;
      }
    }

    .goods-item + .goods-item {
      margin-left: 8px;
    }
  }
}

.preferential {
  width: 106px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-left: dashed 1px;
  position: relative;

  .hole {
    width: 10px;
    height: 10px;
    position: absolute;
    background: #fff;
    border-radius: 50%;
  }

  .hole-top {
    left: -5px;
    top: -5px;
  }

  .hole-bottom {
    left: -5px;
    bottom: -5px;
  }

  .limit {
    color: #646566;
    font-size: 12px;
    line-height: 16px;
    margin-top: 4px;
    max-width: 86px;
  }

  .money {
    line-height: 30px;
  }

  .button {
    color: #fff;
    font-size: 14px;
    height: 24px;
    margin-top: 8px;
    width: 82px;
    padding: 0 12px;
    white-space: nowrap;
  }

  .count-down {
    font-size: 11px;
    line-height: 12px;
    margin-top: 4px;
    width: 94px;
    text-align: center;

    .block {
      display: inline-block;
      width: 15px;
    }
  }

  .received {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: 1px solid;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    position: absolute;
    right: -12px;
    top: -12px;
    transform: rotate(-45deg);

    view {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border: 1px dashed;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
