export const cloudData = {
  getService: ({ order, service, invoice }) => {
    const { expressType, deliveryTime, buyerMemo } = service || {};
    return {
      expressType: order.expressType,
      expressTypeDesc: expressType,
      deliveryTime,
      invoice: {
        show: invoice.show,
        value: invoice.value,
        tip: invoice.tip,
      },
      buyerMsg: buyerMemo,
    };
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
