<template>
  <view v-if="showReceiverInfo" class="receiver-info receiver-hairline--top">
    <van-icon v-if="hasDeliveryAddress" class="receiver-info__icon" name="location-o" size="16px" />
    <van-icon v-else class="receiver-info__icon" name="contact" size="14px" />
    <view class="receiver-info__content">
      <view class="receiver-info__title">
        <text>{{ receiverInfo.receiverLabel }}：{{ receiverInfo.receiverName }}</text>
        <text>{{ receiverInfo.receiverTel }}</text>
      </view>

      <view v-if="hasDeliveryAddress" class="receiver-info__address t-c-gray-darker">
        收货地址：{{ receiverInfo.deliveryAddress }}
      </view>
    </view>
    <view class="receiver-info--stripe-border"></view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      receiverInfo: {},
      showReceiverInfo: false,
    };
  },

  computed: {
    hasDeliveryAddress() {
      const { receiverInfo } = this;
      return receiverInfo && receiverInfo.deliveryAddress;
    },
  },

  created() {
    mapData(this, ['receiverInfo', 'showReceiverInfo']);
  },
};
</script>

<style lang="scss">
$text-color: #323233;
$gray-darker: #646566;

.receiver-info {
  position: relative;
  display: flex;
  padding: 16px 16px 16px 40px;
  background: #fff;
}

.receiver-info__icon {
  position: absolute !important;
  left: 16px;
  top: 17px;
  font-size: 0;
}

.receiver-info__content {
  flex: 1;
}

.receiver-info__title {
  display: flex;
  justify-content: space-between;
  line-height: 18px;
  font-size: 14px;
  font-weight: bold;
  color: $text-color;
}

.receiver-info__address {
  margin-top: 4px;
  font-size: 12px;
  color: $gray-darker;
}

.receiver-info--stripe-border::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: url('https://b.yzcdn.cn/v2/image/wap/address/<EMAIL>');
  background-size: 36px 2px;
}

.receiver-hairline--top {
  &::before {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-bottom: 1px solid #ebedf0;
  }
}
</style>
