<template>
  <award-container custom-style="padding: 0" custom-class="container" @click="clickHandle">
    <view class="reward-container">
      <view class="item">
        <view class="img-container">
          <image :src="reward[1].imgUrl" class="image" />
        </view>
        <view class="t-ellipsis reward-title">{{ reward[1].name }}</view>
      </view>
      <view class="item first-reward">
        <view class="img-container">
          <image :src="reward[0].imgUrl" class="image" />
        </view>
        <view class="t-ellipsis reward-title">{{ reward[0].name }}</view>
      </view>
      <view class="item">
        <view class="img-container">
          <image :src="reward[2].imgUrl" class="image" />
        </view>
        <view class="t-ellipsis reward-title">{{ reward[2].name }}</view>
      </view>
    </view>
    <image class="btn" src="//img01.yzcdn.cn/public_files/092ecc49fa630cf68c79c6b8e5e0c73e.png" />
  </award-container>
</template>

<script>
import AwardContainer from '../Container';
import { cdnImage } from '@youzan/tee-biz-util';

const defaultImgList = [
  {},
  {
    imgUrl: cdnImage('public_files/dea8b8ae6ae011c23f9fa24a0636ad78.png'),
    name: '',
  },
  {
    imgUrl: cdnImage('public_files/97743697e42efdc3f96468502af27d60.png'),
    name: '',
  },
];

export default {
  name: 'award-lucky-game',
  components: {
    'award-container': AwardContainer,
  },
  props: {
    prizes: {
      type: Array,
      default: () => [],
    },
    detailUrl: String,
  },
  computed: {
    reward() {
      const curConfig = [...this.prizes].sort((a, b) => a.probability - b.probability).slice(0, 3);

      return defaultImgList.map((item, ind) => {
        if (curConfig[ind]) return curConfig[ind];
        return item;
      });
    },
  },
  methods: {
    clickHandle() {
      // this.navigateTo(this.detailUrl);
      this.$emit('click-handle');
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  justify-content: space-between;
  background-image: url('//img01.yzcdn.cn/public_files/f6deba6ac44635dbec6cef95babd1760.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 120px;
  padding: 0 12px;
}

.reward-container {
  display: flex;
  position: absolute;
  bottom: 10px;
  height: 62px;
  left: 28px;
  align-items: center;
}

.item {
  background: #fff;
  box-shadow: inset #ffd6ab 0 0 20px 0;
  border: 2px solid #fff;
  border-radius: 5px;
  width: 54px;
  height: 54px;
  color: #ea2727;
  box-sizing: border-box;
  font-size: 9px;
  padding: 4px 4px 0 4px;

  .reward-title {
    text-align: center;
    line-height: 12px;
    margin-top: 2px;
  }
}

.first-reward {
  width: 62px;
  height: 62px;
}

.item + .item {
  margin-left: 6px;
}

.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.btn {
  width: 64px;
  height: 64px;
  position: absolute;
  right: 24px;
  top: calc(50% - 32px);
  animation: breath 800ms infinite linear;
}

.img-container {
  background: #fff;
  width: 100%;
  height: calc(100% - 12px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  .image {
    width: 24px;
    height: 24px;
    object-fit: cover;
    object-position: center;
    border-radius: 4px;
  }
}

.first-reward .img-container .image {
  width: 32px;
  height: 32px;
}

@keyframes breath {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.15);
  }

  100% {
    transform: scale(1);
  }
}
</style>
