<template>
  <view :style="themeCSS">
    <!-- 顶部 -->
    <van-search
      v-if="inSearch"
      :value="keyword"
      placeholder="请输入自提点名称"
      show-action
      @search="onSearch"
      @change="onChange"
      @cancel="onCancel"
    ></van-search>
    <view v-else class="banner">
      <view
        @click="selectCity"
        :class="[
          'city-text',
          'van-c-gray-darker',
          'van-font-14',
          showLoading ? 'disabledColor' : '',
        ]"
        >{{ selfFetch.cityName || '全部' }}</view
      >
      <view
        v-if="showInStockBtn && !isHideInStock"
        @click="switchShowInStock"
        :class="[
          'stock-btn',
          showInStock ? 'van-c-blue stock-color' : 'van-c-gray-darker',
          showLoading ? 'disabledColor' : '',
        ]"
        >只看有货网点</view
      >
      <van-icon @click="startSearch" name="search" size="16px" class="t-c-gray" />
    </view>
    <!-- 加载状态 -->
    <view v-if="showLoading" class="loadingIcon">
      <van-loading vertical>加载中...</van-loading>
    </view>
    <!-- 列表 -->
    <van-list v-if="showList" @load="loadList" :finished="finished">
      <view v-for="item in selfFetchList" :key="item.id">
        <van-cell
          v-if="
            !showInStock ||
            (item.orderSoldStatus !== shopStock.none && item.orderSoldStatus !== shopStock.part)
          "
          @click="handleSelect(item)"
          center
          value-class="distance-value-class"
        >
          <van-icon
            slot="icon"
            :class="
              item.id != currentShopId ? 'van-c-gray-dark' : 'self-fetch-address__theme-color'
            "
            :name="item.id != currentShopId ? 'circle' : 'checked'"
            size="20px"
            :style="{
              marginRight: '16px',
            }"
          />
          <view slot="title">
            <view class="t-font-14 t-c-333">
              {{ item.name }}
              <van-tag v-if="item.shopTag" plain type="danger">{{ item.shopTag }}</van-tag>
            </view>
            <view class="t-font-12 t-c-gray-dark" style="margin-top: 2px">{{ item.detail }}</view>
          </view>
          <view class="t-c-gray-dark t-font-12">{{ item.distanceStr }}</view>
        </van-cell>
      </view>
    </van-list>
    <view
      v-else-if="selfFetch.finished"
      class="empty-text t-font-14 t-c-gray-dark t-hairline--top"
      >{{
        inSearch || showInStock ? '没有符合条件的提货点' : '当前城市暂无提货点，请尝试更换城市'
      }}</view
    >
    <van-dialog ref="selfFetchAddressDialog" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Search from '@youzan/vant-tee/dist/search/index';
import List from '@youzan/vant-tee/dist/list/index';
import Icon from '@youzan/vant-tee/dist/icon/index';
import Cell from '@youzan/vant-tee/dist/cell/index';
import Tag from '@youzan/vant-tee/dist/tag/index';
import VanDialog from '@youzan/vant-tee/dist/dialog/index';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import { mapData } from '@youzan/ranta-helper-tee';
import { Loading } from '@vant/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import args from '@youzan/utils/url/args';
import omit from '@youzan/utils/object/omit';
import { errorToast } from '@youzan/tee-biz-util';
import { getCurrentKdtId, getShopMetaInfo } from '@youzan/tee-biz-shop';
import Storage, { crossStoreSelfFetchCacheKey } from './storage';
import debounce from '@youzan/weapp-utils/lib/debounce';
/* #ifdef web */
import { gotoH5Subpage } from '@youzan/wsc-tee-trade-common/lib/order-utils/goto-h5-subpage';
import { pushRoute } from '@youzan/tee-hash-router/hash-router/route';
/* #endif */
import shopStock from '@youzan/wsc-tee-trade-common/lib/order-utils/config/shopStock';
import eventKey from '@youzan/wsc-tee-trade-common/lib/order-utils/config/eventKey';
import {
  getAllSelfFetchList,
  exchangeBookKeyByTargetKdtId,
  crossStoreAutoEnterShop,
} from '@youzan/wsc-tee-trade-common/lib/order-utils/self-fetch';

// const TOTAL_LIMIT = 300;
const DEFAULT_PAGE_SIZE = 20;
const CROSS_STORE_QUERY_PAGE_SIZE = 20;
// const CONCURRENCE_FETCH_COUNT_LIMIT = 4;

async function retryRequest(requestFunction, maxAttempts) {
  let attempts = 0;
  while (attempts < maxAttempts) {
    try {
      // eslint-disable-next-line no-await-in-loop
      const response = await requestFunction();
      return response; // If the request is successful, return the response
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        throw error; // If max attempts reached, throw the error
      }
    }
  }
}

export default {
  components: {
    'van-search': Search,
    'van-list': List,
    'van-icon': Icon,
    'van-cell': Cell,
    'van-tag': Tag,
    'van-dialog': VanDialog,
    'van-loading': Loading,
  },
  props: {
    initData: Object,
    isHideInStock: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showInStock: true, // 只看有货网点
      showInStockBtn: false, // 是否展示只看有货网点按钮
      inSearch: false, // 是否搜索
      keyword: '', // 搜索关键词
      loading: true,
      showList: false, // 是否显示列表
      showLoading: true, // 进入加载,按钮置灰
      selfFetchList: [], // 自提点列表
      // 设备定位
      location: {
        cityName: '',
        cityCode: '',
        lat: '',
        lng: '',
      },
      // 自提
      selfFetch: {
        cityName: '',
        cityCode: '',
        // 自提点Id
        shop: {},
      },
      // 全局店铺信息
      shop: {
        kdtId: '',
      },
      goods: {
        list: [],
        unavailable: [],
      },
      shopStock: {}, // 一些常量
      themeColors: {},
      themeCSS: '',
      isRetailOrderScene: false,
      isRetailShelfPage: false,
      params: {},
      finished: false,
      isSkuOrderScene: '',
      /* #ifdef web */
      isDirectPushRoute: false,
      /* #endif */
    };
  },
  computed: {
    currentShopId() {
      const { shop } = this.selfFetch || {};
      const { id } = shop || {};
      return id;
    },
  },
  watch: {
    initData: {
      handler(initData) {
        this.setInfo(initData);
      },
      immediate: true,
    },
  },
  mounted() {
    // 获取参数
    // TODO 干掉db
    const { dbid = '', isRetailOrderScene = 0 } = this.$getPageQuery();
    this.isRetailOrderScene = Boolean(+isRetailOrderScene);
    if (dbid || this.initData) {
      const initData = this.ctx.lambdas.getDb(dbid) || this.initData || {};
      this.init(initData);
    }
  },
  created() {
    mapData(this, [
      'themeColors',
      'themeCSS',
      'isDirectPushRoute',
      'isRetailShelfPage',
      'isSkuOrderScene',
    ]);
  },
  methods: {
    init(initData) {
      this.setInfo(initData);
      // 没有店铺 则获取定位
      const shop = this.selfFetch?.shop || {};
      const [promise] = this.ctx.process.invoke('getLocation');
      promise
        .then((location) => {
          this.location = location;
          // 初次没有城市信息时, 以定位为准
          if (!shop.id) {
            this.selfFetch = {
              ...this.selfFetch,
              cityCode: location.cityCode,
              cityName: location.cityName,
            };
          }
          this.getShopList();
        })
        .catch((err) => {
          console.log('获取定位失败', err);
          this.getShopList();
        });
      this.shopStock = shopStock;

      const kdtId = getCurrentKdtId();
      getShopMetaInfo(kdtId)
        .then((shopMetaInfo) => {
          const { shopType, shopRole } = shopMetaInfo;
          // 零售单店不展示 只看有货网点 按钮
          const showInStockBtn = shopType !== 7 ? false : !(shopType === 7 && shopRole === 0);
          this.showInStockBtn = showInStockBtn;
        })
        .catch(() => {
          this.showInStockBtn = true;
        })
        .finally(() => {
          this._getShopInfo = true;
          this.setShowInStock();
        });
    },

    setShowInStock() {
      if (this._getShopInfo) {
        if (this.isSkuOrderScene && !this.isHideInStock && this.showInStockBtn) {
          this.showInStock = true;
          return;
        }
      }
      if (this.crossStoreFetch) {
        this.showInStock = false;
      }
    },

    setInfo(initData) {
      // 自提参数
      this.selfFetch = initData.selfFetch || {};
      // 店铺信息
      this.shop = initData.shop;
      // 获取商品
      this.goods = initData.goods;
      // 是否查询跨店自提点
      this.crossStoreFetch = !!initData.crossStoreFetch;
      this.setShowInStock();

      this.bookKey = initData.bookKey;

      this.buyerMsg = initData.buyerMsg;
      this.contact = initData.contact;
      this.prevPageRoute = initData.route;
      this.prevPageQuery = initData.query;
    },
    // =================== 搜索组件事件响应 start ===================
    // 开始请求列表 - 添加防抖避免重复触发
    onSearch: debounce(function () {
      this.getShopList(true);
    }, 300),
    // 搜索框输入
    onChange(event) {
      this.keyword = event.value || '';
    },
    // 取消搜索 - 添加防抖避免重复触发
    onCancel: debounce(function () {
      this.inSearch = false;
      this.keyword = '';
      this.getShopList(true);
    }, 300),

    // =================== 搜索组件事件响应 end ===================

    // =================== banner事件响应 start ===================
    // 前往选择城市
    selectCity() {
      if (this.showLoading) return;
      const { location } = this;
      /* #ifdef weapp */
      Tee.navigate({
        url: `/packages/trade-buy-subpage/order/self-fetch-address-city/index?cityName=${location.cityName}&cityCode=${location.cityCode}`,
        type: 'navigateTo',
      });
      /* #endif */
      /* #ifdef web */
      if (this.isDirectPushRoute) {
        const hash = `#/self-fetch-address-city?cityName=${location.cityName}&cityCode=${location.cityCode}`;
        pushRoute(hash);
      } else {
        gotoH5Subpage(
          { ctx: this.ctx },
          `/pay/wsctrade_buy/self-fetch-address-city?cityName=${location.cityName}&cityCode=${location.cityCode}`
        );
      }
      /* #endif */
      // 接受事件
      this.ctx.lambdas.onEventOnce(eventKey.selfFetchCity, this.handleCitySelect);
    },
    // 处理城市选择 - 添加防抖避免重复触发
    handleCitySelect: debounce(function (city) {
      this.selfFetch = {
        cityName: city.cityName,
        cityCode: city.cityCode,
        shop: {}, // 自提点Id
      };
      this.getShopList(true);
    }, 300),
    // 切换只显示有货网点 - 添加防抖避免重复触发
    switchShowInStock: debounce(function () {
      if (this.showLoading) return; // 加载的时候不让用户触发事件
      this.showInStock = !this.showInStock;
      // 跨店自提点始终前端切换过滤
      if (this.crossStoreFetch) return;
      const { finished } = this.selfFetch;
      if (!finished) {
        this.getShopList(true);
      }
    }, 300),

    // 开始搜索
    startSearch() {
      this.inSearch = true;
    },
    // =================== banner事件响应 end ===================

    // =================== 列表事件响应 start ===================
    async handleSelect(shop) {
      const { shopStock } = this;
      const status = shop.orderSoldStatus;
      let message = '';
      if (status === shopStock.part) {
        message = '订单部分商品在当前提货点缺货，此部分商品将不会购买下单';
      } else if (status === shopStock.none) {
        message = '订单所有商品在当前提货点缺货，请重新选择商品下单';
      }

      const finishFn = ({ otherParams = {}, skipBack = false, sessionVal } = {}) => {
        this.ctx.lambdas.triggerEvent('select-self-fetch-address', shop, otherParams);

        /* #ifdef weapp */
        if (this.isSkuOrderScene) {
          return;
        }
        /* #endif */

        /* #ifdef web */
        if (this.isSkuOrderScene) {
          // 连锁跨店场景, 直接跳转到下单页
          if (otherParams.bookKey) {
            Toast.loading({
              message: '跨店跳转中···',
              duration: 5000,
              mask: true,
            });
            const val = sessionVal[otherParams.bookKey];
            try {
              const url = args.add(otherParams.redirectUrl, {
                skuOrderSession: JSON.stringify({
                  [otherParams.bookKey]: {
                    ...val,
                    selfFetch: {
                      ...val.selfFetch,
                      list: [],
                    },
                    contact: {},
                  },
                }),
              });
              Tee.navigate({ url });
            } catch (error) {
              Tee.navigate({ url: otherParams.redirectUrl });
            }
          }
          return;
        }
        /* #endif */

        if (!skipBack) {
          Tee.navigateBack();
        }
      };

      const targetKdtId = shop.saleShopKdtId;
      if (this.crossStoreFetch) {
        if (this.shop.kdtId !== targetKdtId) {
          if (!this.isSkuOrderScene) {
            Toast.loading('跳转中···');
          } else {
            this.ctx.lambdas.triggerEvent('before-arrdress-change-book');
          }
          try {
            const getBookkeyParams = {
              bookKey: this.bookKey,
              targetKdtId,
              buyerMsg: this.buyerMsg,
              // 过滤掉赠品和换购商品
              changeItems: this.goods.list.filter((item) => !item.fromTmpAdded && !item.present),
            };
            // 点单宝跨店自提新增参数：https://qima.feishu.cn/docx/DBtidzDOtoBr3ExLd0ScPnJKnzq
            if (this.isRetailOrderScene || this.isRetailShelfPage) {
              getBookkeyParams.delivery = {
                selfFetch: {
                  ...shop,
                  lat: `${shop.lat}`,
                  lng: `${shop.lng}`,
                },
              };
            }
            const { bookKey } = await exchangeBookKeyByTargetKdtId(getBookkeyParams);
            const redirectUrl = this.getRedirectUrl(targetKdtId, bookKey);
            await this.autoEnterShop(targetKdtId, redirectUrl);

            const sessionVal = {
              [bookKey]: {
                selfFetch: {
                  ...this.selfFetch,
                  shop,
                  cityCode: shop.countyId,
                  cityName: shop.city,
                  time: '',
                  selfFetchStartTime: '',
                  selfFetchEndTime: '',
                },
                contact: this.contact,
                // 自提
                activeTab: 1,
              },
            };
            Storage.setItem(crossStoreSelfFetchCacheKey, JSON.stringify(sessionVal));
            const otherParams = {
              redirectUrl,
              bookKey,
              type: 'changeKdtId',
            };
            /* #ifdef weapp */
            finishFn({ otherParams, skipBack: true });
            /* #endif */

            /* #ifdef web */
            finishFn({ otherParams, sessionVal });
            /* #endif */
            if (!this.isSkuOrderScene) {
              Toast.clear();
            }
          } catch (error) {
            Toast.clear();
            console.warn('error', error);
            errorToast(error);
          }
        } else {
          finishFn();
        }
      } else if (!message) {
        // 触发事件
        finishFn();
      } else {
        Dialog.confirm({
          message,
          confirmButtonText: '知道了',
          showCancelButton: false,
          ref: this.$refs.selfFetchAddressDialog,
        })
          .then(() => {
            // 触发事件
            finishFn();
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },

    getRedirectUrl(kdtId, bookKey) {
      const url = args.add(this.prevPageRoute, {
        ...omit(this.prevPageQuery, 'prefetchKey'),
        kdt_id: kdtId,
        bookKey,
        book_key: bookKey,
      });

      let redirectUrl = '';
      /* #ifdef weapp */
      redirectUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(url)}`;
      /* #endif */

      /* #ifdef web */
      // sku下单链接处理
      if (this.isSkuOrderScene) {
        redirectUrl = url;
      } else {
        redirectUrl = `${location.origin}${url}`;
      }
      /* #endif */

      return redirectUrl;
    },

    async autoEnterShop(kdtId, redirectUrl) {
      /* #ifdef weapp */
      const [setApplicationScenePromise] = this.ctx.process.invoke('setApplicationScene', {
        sceneId: 9008,
        sceneSource: 'CUSTOM',
      });

      const [autoEnterShopPromise] = this.ctx.process.invoke(
        'autoEnterShop',
        { kdtId },
        { redirectUrl, logTag: 'self-fetch' }
      );

      await setApplicationScenePromise;

      return autoEnterShopPromise;
      /* #endif */
      /* #ifdef web */
      /* eslint-disable-next-line no-unreachable */
      return crossStoreAutoEnterShop({
        targetKdtId: kdtId,
        redirect_url: redirectUrl,
      });
      /* #endif */
    },

    // =================== 列表事件响应 end ===================

    async getState() {
      return new Promise((resolve) => {
        this.ctx.lambdas.triggerEvent('get-state', (state) => {
          resolve(state);
        });
      });
    },

    /** ************************************************************************
     * FIXME: 这个页面的实现有缺陷，会导致相同自提点数据渲染出两遍，需要重新实现这个页面。
     * 历史JIRA: https://jira.qima-inc.com/browse/ONLINE-783864
     * 复现方法: 将debounce的time调小到100，快速点击搜索icon。
     * 临时做法: 目前使用的是debounce进行处理，保证用户快速操作时不出现重复数据。但这会导致
     * 用户等待实现较长。由于是次要页面对性能要求不高,因此暂时这样处理可以接受。
     * 彻底解决：重新实现这个页面，移除debounce的使用，需要通过逻辑判断是否要进行fetch。
     ************************************************************************ */
    // 获取自提列表
    getShopList: debounce(async function (isSearch = false) {
      // 处理请求失败的情况，暂不做重新请求
      const catchFn = (err) => {
        this.loading = false;
        this.showLoading = false; // 失败也要不显示了
        console.log(err || '出错了，请稍后重试');
        this.ctx.hummer?.capture(
          `自提点列表getShopList加载错误:[kdtId: ${this.shop.kdtId}]:` + err
        );
      };

      try {
        this.loading = true;
        this.showList = true;
        // 拼参数
        const { goods } = this;
        this.items = this.items || [].concat(goods.list, goods.unavailable);
        this.subComboList =
          this.subComboList ||
          this.items?.reduce((result, item) => {
            return result.concat(
              item.combo?.subComboList.map((subCombo) => {
                const { goodsId, groupId, skuId, num } = subCombo;
                return { goodsId, groupId, skuId, num };
              })
            );
          }, []);

        const params = {
          kdtId: this.shop.kdtId,
          items: this.items.map((item) => ({
            goodsId: item.goodsId,
            skuId: item.skuId,
            num: item.num,
            combo: item.combo && {
              comboType: item.combo.comboType,
              subComboList: this.subComboList,
            },
          })),
          keyword: this.keyword,
          pageSize: DEFAULT_PAGE_SIZE,
        };
        let { cityCode } = this.selfFetch;
        if (cityCode) {
          // 接口返回的cityCode是区域编码，而且区域编码在别的地方有用到，所以在这边转换成城市编码
          cityCode = cityCode.slice(0, -2) + '00';
          params.cityCode = cityCode;
        }
        const { lat, lng } = this.location;
        if (lat && lng) {
          params.lat = lat;
          params.lng = lng;
        }

        // 跨店请求初次返回所有结果
        if (this.crossStoreFetch) {
          params.crossQuery = true;
          params.pageSize = CROSS_STORE_QUERY_PAGE_SIZE;
          params.fillInSoldStatusCnt = CROSS_STORE_QUERY_PAGE_SIZE;
        }
        params.page = 1;
        this.selfFetchList = [];
        // 比较每次的 getShopListFetchIndex 是否相等，进保留相等的，防止多次请求数据串掉
        this.getShopListFetchIndex = (this.getShopListFetchIndex || 0) + 1;
        this.params = params;
        if (isSearch) {
          this.loadList(params);
        }
      } catch (err) {
        catchFn(err);
      }
    }, 1000),
    loadList: debounce(async function (params = this.params) {
      const { selfFetchList, page } = await retryRequest(getAllSelfFetchList.bind(this, params), 5);
      const state = await this.getState();
      const curSelfFetchList =
        (await this.ctx.process.invokePipe('hook:mutateListAfterFetch', selfFetchList, state)) ||
        selfFetchList;
      // 防止多次请求数据串掉
      this.selfFetchList = (this.selfFetchList || []).concat(curSelfFetchList);
      // 不满一页则加载完成，不再请求
      this.finished = curSelfFetchList.length < params.pageSize;

      this.loading = false;
      // page为下一次的页码
      this.params.page = page;

      this.showLoading = false; // 加载完成，不显示了
    }, 100),
  },
};
</script>

<style lang="scss">
.long-cell {
  padding: var(--theme-page-cell-padding-top, 10px) var(--theme-page-cell-padding-right, 12px)
    var(--theme-page-cell-padding-bottom, 10px) var(--theme-page-cell-padding-left, 12px) !important;
}

.self-fetch-address {
  &__theme-color {
    color: var(--main-bg, #323233);
  }
}

.page {
  min-height: 100vh;
  background: #f9f9f9;
}
/* 网店列表 */
.shop-list {
  height: 100vh;
  box-sizing: border-box;
}
/* 顶部搜索、城市条 */
.banner {
  position: relative;
  display: flex;
  height: 45px;
  padding: 0 15px;
  background: #f8f8f8;
  box-sizing: border-box;
  flex: 0 0 auto;
  align-items: center;
  justify-content: space-between;
}
.loadingIcon {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.city-text {
  position: relative;
  padding-right: 12px;
}

.city-text::after {
  position: absolute;
  top: 5px;
  right: 0;
  width: 0;
  height: 0;
  border: 3px solid;
  border-color: #666 #666 transparent transparent;
  content: ' ';
  transform: rotate(135deg) scale(0.8);
}
/* 只看缺货网点切换按钮 */
.stock-btn {
  position: absolute;
  top: 50%;
  right: 50px;
  float: right;
  padding: 0 10px;
  margin-top: -12px;
  font-size: 12px;
  line-height: 24px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
}
.disabledColor {
  color: #c8c9cc !important;
  border-color: #dcdee0 !important;
}
.stock-btn.blue {
  border-color: var(--main-bg, #323233);
}

.empty-text {
  padding-top: 180px;
  text-align: center;
}

.distance-value-class {
  flex: none !important;
  padding-left: 10px;
}
.stock-color {
  color: var(--main-bg, #323233);
}

.t-c-gray-dark {
  color: #999;
}

.t-c-gray-darker {
  color: #666;
}
.fetch-address-list {
  /** 减去头部快递方式高度44px以及省市区选择高度45px */
  height: calc(75vh - 89px);
}
</style>
