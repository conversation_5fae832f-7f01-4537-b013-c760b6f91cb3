// Widgets
import Service from './Service.vue';

// Utils
import createStore from './store';
import { cloudData, isSameObject } from './utils';

// Dependencies
import { cloud } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';
import type { ServiceData } from '@youzan-cloud/cloud-biz-types';

export default class ServiceExtension {
  static widgets = {
    Service,
  };

  ctx: any;

  store: any;

  /**
   * service
   * @desc 订单服务
   * @type {Service}
   */
  @cloud('service', 'data')
  service: ServiceData;

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);

    mapCtxData(this, ['kdtId', 'orderNo', 'invoiceStatus']);
    mapStoreToCtx(this, {
      invoice: 'invoiceForService',
    });
    mapData(this, ['service', 'invoiceForService', 'order'], {
      callback: () => {
        const { order, service, invoiceForService: invoice = {} } = this.ctx.data;
        const newOpenData = {
          service: cloudData.getService({ order, service, invoice }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
