import { buildUrl } from '@youzan/tee-biz-util';
import { getGiftId } from '../api';
import { BUTTON_GROUP } from '../constants';
import type { WechatReceiptPageKeyEnum } from '@youzan-cloud/cloud-biz-types';
import { mergeMulPluginList } from '@youzan/cloud-biz-utils';
import { camelToSnake, snakeToCamel } from '../utils';

export default (ctx) => {
  return {
    initButtonList() {
      // 由于需要向开放标准对齐，标品按钮类型为大写，开放标准为驼峰。所以在这里hook交互时转换一下，到标品端再转换回来
      ctx.cloud
        // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
        .invoke('beforeActionRender', {
          actions: this.buttonItemList.map((item) => {
            return {
              ...item,
              type: snakeToCamel(item.type),
            };
          }),
        })
        .then((resList) => {
          const { newList } = mergeMulPluginList(
            this.buttonItemList,
            resList.map((item) => item.actions),
            {
              key: 'text',
            }
          );
          const newButtonItemList = newList.map((item) => {
            return {
              ...item,
              type: camelToSnake(item.type),
            };
          });
          this.groupButton = this.getGroupButton(newButtonItemList);
        });
    },

    getGroupButton(buttonItemList) {
      const result = {
        primary: [],
        default: [],
      };
      buttonItemList.forEach((item) => {
        const { isPrimary, text } = item;
        if (isPrimary) {
          result.primary.push({
            ...item,
            defaultText: text,
            className: 'action-block__btn-primary',
          });
        } else {
          result.default.push({
            ...item,
            defaultText: text,
            className: 'action-block__btn-default',
          });
        }
      });
      return result;
    },
    getButtonType(type) {
      const isFirstBtn = this.buttonList.indexOf(type) === 0;
      const onlyOneBtn = this.buttonList.length === 1;
      const buttonArr = [
        'CARD_VOUCHER',
        'GIVE_AWAY',
        'SUBSCRIPTION',
        'WISH_ORDER',
        'PICK_UP_CODE',
        'PRESENT_GIFT',
      ];
      const hasMainBtn = buttonArr.some((btn) => this.buttonList.indexOf(btn) > -1);
      const isPrimary =
        buttonArr.indexOf(type) !== -1 ||
        (type === 'DETAIL' && !hasMainBtn) ||
        isFirstBtn ||
        onlyOneBtn;
      return isPrimary;
    },

    async handleAction(action) {
      const { type, pageKey } = action;
      // 如果开放hook：beforeActionHandle返回reject，则中断操作
      // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
      await ctx.cloud.invoke('beforeActionHandle', action);
      // 自定义按钮并且没有pageKey，则不需要继续执行
      if (type === 'CUSTOM' && !pageKey) {
        return;
      }

      // 处理按钮点击埋点
      BUTTON_GROUP.forEach((item) => {
        if (item.key === type) {
          ctx.process.invoke('logger', 'click', `click_${item.key}`, `点击${item.value}`);
        }
      });
      switch (type) {
        case 'SHARE':
          this.openShare();
          break;
        case 'SUBSCRIPTION':
          this.jumpOut();
          break;
        case 'PICK_UP_CODE':
          this.jumpOut();
          break;
        case 'CARD_VOUCHER':
          this.jumpOut();
          break;
        case 'WISH_ORDER': // 心愿单
          this.jumpOut();
          break;
        case 'DETAIL':
          this.openDetail();
          break;
        case 'PRESENT_GIFT':
          this.goGift();
          break;
        case 'KEEP_SHOPPING':
          this.goShopping(); // 进店逛逛
          break;
        case 'ACTIVATE_CARD': // 激活权益卡
          this.goActivate();
          break;
        case 'SHARE_GIFT':
          this.shareGift();
          break;
        case 'CUSTOM':
          this.openPage(pageKey);
          break;
        default:
          this.jumpOut();
          break;
      }
    },
    // 跳转内置定义的页面
    openPage(pageKey: WechatReceiptPageKeyEnum) {
      switch (pageKey) {
        case 'order-detail':
          this.openDetail();
          break;
        case 'order-paid':
          this.jumpOut();
          break;
        default:
          this.jumpOut();
      }
    },
    // 跳转到支付结果页
    jumpOut() {
      ctx.process.invoke('openResult');
    },
    // 直接跳转分享落地页
    openShare() {
      ctx.process.invoke('openShare');
    },
    //  直接打开订单详情
    openDetail() {
      ctx.process.invoke('openDetail');
    },
    //  激活权益卡
    goActivate() {
      ctx.process.invoke('navigateTo', this.buttonGroup.ACTIVATE_CARD?.url);
    },
    // 进店逛逛
    goShopping() {
      ctx.process.invoke('openHome');
    },
    // 我要送礼
    goGift() {
      ctx.process.invoke(
        'navigateTo',
        buildUrl(`/trade/gift/invite?order_no=${this.orderNo}`, 'trade_youzan', this.kdtId)
      );
    },
    shareGift() {
      getGiftId(this.orderNo)
        .then((giftId) => {
          ctx.process.invoke(
            'navigateTo',
            buildUrl(
              `/wsctrade/gift/share?presenter_view=1&gift_id=${giftId}&kdt_id=${this.kdtId}`,
              'h5',
              this.kdtId
            )
          );
        })
        .catch(() => {
          // 获取失败跳转礼物记录
          ctx.process.invoke(
            'navigateTo',
            buildUrl(`/wsctrade/gift/gift-list?kdt_id=${this.kdtId}`, 'h5', this.kdtId)
          );
        });
    },
  };
};
