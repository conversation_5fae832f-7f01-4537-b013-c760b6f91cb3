<template>
  <view>
    <van-popup v-if="isShow" :show="isShow" round>
      <view class="retail-ori">
        <view class="error-info">当前收货地址超出配送范围</view>
        <view class="info" v-if="showSwitchBtn" :style="generalStyle" @click="onSelectShop"
          >切换可配送店铺</view
        >
        <view
          class="info"
          v-else-if="supportSwitchExpressWay"
          :style="generalStyle"
          @click="switchExpressWay"
        >
          切换配送方式
        </view>
        <view class="info" @click="onAddressTap">修改收货地址</view>
        <view class="info" @click="onClose">取消</view>
      </view>
    </van-popup>

    <van-popup v-if="!isTradeBuyPage && showNotEnoughPricePop" :show="showNotEnoughPricePop" round>
      <view class="retail-ori">
        <view class="error-info">
          当前店铺<text :style="generalStyle">{{
            retailOrderErrorInfo.localDeliveryStartFeeStr
          }}</text
          >起送，还差<text :style="generalStyle">{{
            retailOrderErrorInfo.localDeliveryLackFeeStr
          }}</text>
        </view>
        <view class="info" :style="generalStyle" @click="navigateBack">返回凑单</view>
        <view class="info" v-if="supportSwitchExpressWay" @click="switchExpressWay"
          >切换配送方式</view
        >
        <view class="info" @click="onClose">取消</view>
      </view>
    </van-popup>

    <van-popup v-if="showSwitchShopConfirmPop" :show="showSwitchShopConfirmPop" round>
      <view class="retail-ori">
        <view class="confirm-info">
          <view class="confirm-info--title">店铺切换成功</view>
          <view class="confirm-info--desc">请重新确认订单信息</view>
        </view>
        <view class="btn-group">
          <view class="btn" :style="generalStyle" @click="closeConfirmPop">知道了</view>
        </view>
      </view>
    </van-popup>

    <!-- 只有自提和同城配送才有必选品 -->
    <van-popup :show="showNeedRequiredPop && currentExpressType !== 0" round>
      <view class="retail-ori">
        <view class="error-info"
          >{{ currentExpressType === 1 ? '自提' : '商家配送' }}时需要选择必选品</view
        >
        <view class="footer">
          <view class="footer__btn info border-right" @click="onClose">取消</view>
          <view class="footer__btn is-confirm info" @click="handleBackAddRequired">返回加购</view>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script>
import Event from '@youzan/weapp-utils/lib/event';
import { mapData, mapEvent, mapProcess } from '@youzan/ranta-helper-tee';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import { isSupportLocalSwitchShop } from './api';
import Tee from '@youzan/tee';
import navigate from '@youzan/tee-biz-navigate';

export default {
  components: {
    'van-popup': Popup,
  },

  props: {
    source: String,
  },

  data() {
    return {
      showOutOfDeliveryPop: false,
      showNotEnoughPricePop: false,
      supportSwitchExpressWay: false,
      showSwitchShopConfirmPop: false,
      showExpressTabTrigger: false,
      showNeedRequiredPop: false,
      switchDeliveryTabTrigger: false,
      showSwitchBtn: false,
      retailOrderErrorInfo: {},
      themeColors: {},
      prepareLoading: false,
      currentExpressType: null,
      isSkuOrderScene: '',
      showPoiPrompt: false,
    };
  },

  computed: {
    generalStyle() {
      const { general = '' } = this.themeColors;
      return `color: ${general}`;
    },
    // 是否为下单页，部分弹窗下单页不再展示
    isTradeBuyPage() {
      return this.source === 'trade-buy';
    },
    isShow() {
      return !this.isTradeBuyPage && this.showOutOfDeliveryPop && !this.showPoiPrompt;
    },
  },

  created() {
    mapData(this, ['themeColors', 'bookKey', 'prepareLoading', 'isSkuOrderScene', 'showPoiPrompt']);
    mapData(this, {
      retailOrderErrorInfo: (val) => {
        if (this.isSkuOrderScene && this.prepareLoading) return;
        const {
          showOutOfDeliveryPop,
          showNotEnoughPricePop,
          canSelfFetch,
          showExpressTab,
          isSupportExpress,
          showNeedRequiredPop,
          currentExpressType,
        } = val;
        this.showOutOfDeliveryPop = showOutOfDeliveryPop;
        this.showNotEnoughPricePop = showNotEnoughPricePop;
        this.supportSwitchExpressWay = canSelfFetch || (showExpressTab && isSupportExpress);
        this.showNeedRequiredPop = showNeedRequiredPop;
        this.currentExpressType = currentExpressType;

        if (showOutOfDeliveryPop) {
          this.checkSwitchShop();
        }
      },
    });

    mapEvent(this, {
      toggleRequiredPopup: (val) => {
        this.showNeedRequiredPop = val;
      },
    });

    mapProcess(this, {
      setRetailDeliveryInitDate: (params) => {
        this.route = params.route;
        this.query = params.query;
        this._isFastOrderMode = !!params.isFastOrderMode;
      },
    });
  },

  mounted() {
    this.query = this.$getPageQuery();
    this.route = this.$getPageRoute();
    this.checkSwitchShopConfirmPop();

    // 店铺列表页切换完店铺之后，重新进入下单页
    Event.on('select-out-of-delivery-shop', (params) => {
      this.onClose();
      setTimeout(() => {
        // eslint-disable-next-line @youzan/dmc/tee-check
        Tee.navigate({
          url: params.redirectUrl,
          type: 'reLaunch',
        });
      }, 0);
    });
  },

  destroyed() {
    this.retailOrderErrorInfoUnWatch && this.retailOrderErrorInfoUnWatch();
    Event.off('select-out-of-delivery-shop');
  },

  methods: {
    // 判断是否展示切换店铺入口
    checkSwitchShop() {
      const {
        currentAddress: { lat: buyerLat, lon: buyerLng },
        orderKdtId,
      } = this.retailOrderErrorInfo;

      isSupportLocalSwitchShop({
        buyerLat,
        buyerLng,
        kdtId: orderKdtId,
      }).then((rs) => {
        // 展示切店逻辑 = 有店铺可切换
        this.showSwitchBtn = rs;
      });
    },

    checkSwitchShopConfirmPop() {
      const { from_source: from } = this.query;
      // 来源是 retailOrderShopSelect 展示确认信息弹窗
      this.showSwitchShopConfirmPop = from === 'retailOrderShopSelect';
    },

    closeConfirmPop() {
      this.showSwitchShopConfirmPop = false;
    },

    onClose() {
      this.ctx.data.retailOrderErrorInfo = {
        ...this.retailOrderErrorInfo,
        showNotEnoughPricePop: false,
        showOutOfDeliveryPop: false,
        showNeedRequiredPop: false,
      };
    },

    switchExpressWay() {
      this.onClose();
      const { showExpressTab, canSelfFetch, currentExpressType, isSupportExpress } =
        this.retailOrderErrorInfo;
      // 支持配送方式 且 选中不为快递方式

      if (showExpressTab && isSupportExpress && +currentExpressType !== 0) {
        if (this.isSkuOrderScene) return this.handleSwitchWay(0, 'extpress');
        // 原组件内事件 search onSelectExpressWay 0：配送；1：切换成快递
        Event.trigger('onSelectExpressWay', 1);
        return;
      }

      if (canSelfFetch) {
        if (this.isSkuOrderScene) return this.handleSwitchWay(1);
        // search address-tab:onSwitchTab 2：商家配送；1：自提
        this.ctx.process.invoke('switchDeliveryTab', 1);
      }
    },

    handleSwitchWay(id, type) {
      if (type === 'extpress') {
        return this.ctx.process.invoke('selectExpressType', id);
      }
      this.ctx.process.invoke('switchAddressTab', { id });
    },

    onAddressTap() {
      this.onClose();
      this.ctx.event.emit('onDeliveryAddressCardClick', true);
    },

    onSelectShop() {
      const { goods, currentAddress, orderKdtId } = this.retailOrderErrorInfo;
      const bookKey = this.query.bookKey || this.query.book_key || this.bookKey;
      let dbid = '';
      const { list = [], unavailable = [] } = goods;
      const infoData = {
        goodsInfo: {
          ...goods,
          list: list.concat(unavailable),
        },
        bookKey,
        isFastOrderMode: this._isFastOrderMode,
        address: currentAddress,
        orderKdtId,
        query: this.query,
        route: this.route,
      };
      /* #ifdef weapp */
      dbid = getApp().db.set(infoData);
      /* #endif */
      /* #ifdef web */
      window.sessionStorage.setItem('SWITCH_SHOP_DATA_OF_TRADE', JSON.stringify(infoData));
      /* #endif */

      this.ctx.logger.log({
        et: 'click',
        ei: 'outOfDeliveryBtnClick',
        en: '超出配送范围切换店铺按钮点击',
        pt: 'trade', // 页面类型
        params: {},
      });
      navigate({
        url: `/packages/shop-select/chain-store/retail-order-shop-select/index?dbid=${dbid}`,
        web: {
          type: 'jumpLink',
          jumpLink: {
            url: `/wsctrade/order/chainstore-switch-shop?bookKey=${bookKey}&addressId=${currentAddress.id}&orderKdtId=${orderKdtId}`,
          },
        },
      });
    },
    navigateBack() {
      this.onClose();
      if (this.isSkuOrderScene) return;
      this.ctx.logger.log({
        et: 'click',
        ei: 'notEnoughPriceBtnClick',
        en: '未满启动价返回凑单按钮点击',
        pt: 'trade', // 页面类型
        params: {},
      });
      /* #ifdef weapp */
      // 返回上一页失败，直接跳转首页 Tee.navigateBack 内部无跳转失败回调
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateBack({
        fail: () => {
          // eslint-disable-next-line @youzan/dmc/wx-check, @youzan/dmc/tee-check
          Tee.navigate({
            url: '/pages/home/<USER>/index',
            type: 'reLaunch',
          });
        },
      });
      /* #endif */

      /* #ifdef web */
      // eslint-disable-next-line @youzan/dmc/tee-check
      Tee.navigateBack();
      /* #endif */
    },
    handleBackAddRequired() {
      this.onClose();
      if (this.isSkuOrderScene) {
        return this.ctx.event.emit('skuOrder:hide');
      }
      const { currentAddress, currentExpressType } = this.retailOrderErrorInfo;
      const { lat, lon } = currentAddress;
      if (lat && lon && currentExpressType === 2) {
        Tee.setGlobal('requiredGoods:deliveryAddress', { lat, lon });
        Tee.navigateBack();
      } else if (currentExpressType === 1) {
        Tee.setGlobal('requiredGoods:selfFetch', true);
        Tee.navigateBack();
      }
    },
  },
};
</script>

<style lang="scss">
.retail-ori {
  width: 311tpx;

  .info {
    border-top: 1tpx solid #ebedf0;
    line-height: 48tpx;
    text-align: center;
  }

  .error-info {
    line-height: 106tpx;
    text-align: center;
  }

  .confirm-info {
    width: 311tpx;
    padding: 26tpx 0;
  }

  .confirm-info--title {
    font-weight: 500;
    font-size: 16tpx;
    line-height: 22tpx;
    text-align: center;
    color: #323233;
  }

  .confirm-info--desc {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: #646566;
  }

  .btn-group {
    display: flex;
    border-top: 1px solid #ebedf0;
    justify-content: space-around;
  }

  .btn {
    height: 48px;
    line-height: 48px;
    text-align: center;
    width: 50%;
  }

  .border-right {
    border-right: 1px solid #ebedf0;
  }
  .footer {
    display: flex;
    &__btn {
      flex: 1;
      color: #323233;
      &.is-confirm {
        color: #ee0a24;
      }
    }
  }
}
</style>
