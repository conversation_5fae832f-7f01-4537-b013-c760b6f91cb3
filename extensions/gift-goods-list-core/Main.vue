<template>
  <view>
    <block v-if="goodsList.length">
      <view class="gift-goods__banner-wrapper">
        <image :src="bannerImg" class="gift-goods__banner" mode="aspectFill" />
      </view>
      <view class="gift-goods__list">
        <view
          :class="[
            'goods-item',
            !item.stock ? 'goods-item--soldout' : '',
            !item.available ? 'goods-item--end' : '',
          ]"
          v-for="(item, key) in goodsList"
          :key="key"
        >
          <view @click="handleGoodsClick(item)" class="goods-item__wrapper">
            <view class="goods-item__image-wrap">
              <image :src="item.imgUrl" class="goods-item__image" mode="aspectFit" />
              <view class="goods-item__image-mask"></view>
            </view>
            <view class="goods-item__title">{{ item.title }}</view>
            <view class="goods-item__price">
              <text class="goods-item__price-unit">￥</text>{{ item.price }}
            </view>
            <view v-if="item.available" class="goods-item__buy-icon">
              <van-icon name="add-o" color="#323233" size="20px" @click="handleGoodsClick(item)" />
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="gift-goods--empty" v-else>
      <view class="gift-goods--empty__icon"> </view>
      <view class="gift-goods--empty__desc">暂无商品</view>
    </view>
    <view class="gift-goods__list-bottom"></view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { requestV2 } from '@youzan/tee-biz-request';
import money from '@youzan/weapp-utils/lib/money';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
/* #ifdef web */
import { createIntersectionObserver } from '@youzan/tee-api';
/* #endif */
export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      bannerImg: '',
      alias: '',
      goodsList: [],
      isFetching: true,
      pageNo: 1,
      pageSize: 8,
      nomore: false,
    };
  },

  beforeMount() {
    const res = this.ctx.data.shopConfigs || {};
    if (+res.gift_plug_status === 0) {
      Dialog.confirm({
        message: '送礼活动已结束',
        confirmButtonText: '返回',
      }).then(() => {
        Tee.navigateBack();
      });
      return;
    }

    this.fetchActivityAlias().then((alias) => {
      this.alias = alias;

      Toast.loading({ message: '加载中' });

      Promise.all([this.fetchGoodsList(), this.fetchPageBanner()])
        .then((res) => {
          const [goodsList, bannerImg] = res;
          Object.assign(this, {
            alias,
            goodsList,
            bannerImg,
            isFetching: false,
          });
          Toast.clear();
          // 第一次请求后，延迟一定时间等待渲染完成，注册观察器。
          this.timer = setTimeout(() => {
            this.onRegisterObserver();
          }, 500);
        })
        .catch((err) => {
          Toast(err.msg || '获取商品信息失败，请刷新重试');
        });
    });
  },

  destroyed() {
    this.observer && this.observer.disconnect();
    this.timer && clearTimeout(this.timer);
  },

  methods: {
    onRegisterObserver() {
      /* #ifdef web */
      this.observer = createIntersectionObserver(this, {
        thresholds: [0.5],
        observeAll: true,
        cache: true,
      }).relativeToViewport();
      /* #else */
      this.observer = this.createIntersectionObserver({
        thresholds: [0.5],
        observeAll: true,
        cache: true,
      }).relativeToViewport({ bottom: 30 });
      /* #endif */
      this.observer.observe('.gift-goods__list-bottom', (a) => {
        if (a.intersectionRatio > 0) {
          this.onReachBottom();
        }
      });
    },
    fetchActivityAlias() {
      return new Promise((resolve) => {
        this.ctx.env.getQueryAsync().then((query = {}) => {
          const { alias } = query;
          if (!alias) {
            requestV2({
              path: '/wscump/gift/giftcart.json',
            }).then((res = {}) => {
              const { alias } = res.gift_activity_info || {};
              alias && resolve(alias);
            });
          } else {
            resolve(alias);
          }
        });
      });
    },
    fetchGoodsList() {
      const { pageNo, pageSize, alias } = this;
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/giftgoodslist.json',
          data: {
            alias,
            pageNo,
            pageSize,
          },
        })
          .then((res) => {
            Object.assign(this, {
              pageNo: this.pageNo + 1,
            });
            resolve(this.mapGoodsListData(res));
          })
          .catch((err) => {
            reject(err.data);
          });
      });
    },

    fetchPageBanner() {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/giftgoodsbanner.json',
          data: {
            alias: this.alias,
          },
        })
          .then((res) => {
            const bannerImg = cdnImage(res, '!750x310.jpg');
            resolve(bannerImg);
          })
          .catch((err) => {
            reject(err.data);
          });
      });
    },

    handleGoodsClick(item) {
      if (!item.available) {
        Toast('该送礼商品已失效');
        return;
      }
      let url = `/pages/goods/detail/index?alias=${item.alias}&type=gift`;
      /* #ifdef web */
      url = `/wscgoods/detail/${item.alias}?type=gift`;
      /* #endif */
      // TODO: 跳转商品详情页 并且需要确认下h5的商详页是否支持 type = gift
      Tee.navigate({
        url,
      });
    },

    mapGoodsListData(goodsList) {
      return goodsList.map((item) => {
        const { title, image_url: imgUrl, price, alias, stock, available } = item;
        return {
          imgUrl: cdnImage(imgUrl, '!250x250.jpg'),
          price: money(price).toYuan(),
          title,
          alias,
          stock,
          available,
        };
      });
    },

    onReachBottom() {
      const { nomore, goodsList } = this;
      // 没有更多
      if (nomore) return;

      this.fetchGoodsList().then((res) => {
        if (!res.length) {
          this.nomore = true;
          return;
        }

        const newGoodsList = goodsList.concat(res);
        this.goodsList = newGoodsList;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.gift-goods {
  &__banner {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__banner-wrapper {
    width: 100%;
    height: 185px;
  }

  &__list {
    width: 100%;
    padding: 0 12px 12px 12px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
  }

  &--empty {
    text-align: center;
    padding-top: 176px;
  }

  &--empty__icon {
    display: block;
    width: 109px;
    height: 92px;
    background-image: url('https://img01.yzcdn.cn/public_files/2018/07/04/f5f975967d98e136fb8c59dcfebc6af3.png');
    background-size: 109px 92px;
    margin: 0 auto;
  }

  &--empty__desc {
    margin-top: 24px;
  }
}

.goods-item {
  width: 50%;
  box-sizing: border-box;
  position: relative;
  float: left;
  flex-basis: calc(50%);
  margin-top: 12px;

  &:nth-child(odd) {
    margin-right: 6px;
    flex-basis: calc(50% - 6px);
  }

  &--soldout &__image-mask::after,
  &--end &__image-mask::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background: rgba(0, 0, 0, 0.6);
    background-image: url(https://img01.yzcdn.cn/public_files/2018/09/10/f132f394c4ba881758dd0cd806c911f9.png);
    background-size: contain;
  }

  &--end &__image-mask::after {
    background-image: url(https://img01.yzcdn.cn/public_files/2018/08/02/fe97dab4a6bd8ff01e46a9ea97315e73.png);
  }

  &__wrapper {
    margin: 0 2.5px;
    background: #fff;
    border-radius: 8px;
  }

  &__image-wrap {
    min-height: 0;
    padding-top: 100%;
    height: 0;
    text-align: center;
    overflow: hidden;
    position: relative;
    z-index: 0;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  &__image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
    height: auto;
  }

  &__title {
    height: 34px;
    line-height: 17px;
    margin: 8px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &__price {
    padding: 12px 8px;
    font-size: 18px;

    &-unit {
      font-size: 12px;
    }
  }

  &__buy-icon {
    position: absolute;
    bottom: 12px;
    right: 14px;
  }
}
.gift-goods__list-bottom {
  height: 1px;
  width: 100%;
}
</style>
