import { IStoreObject, mergeStore } from './helper';

import selfFetch from './modules/self-fetch/index';
import voucherCard from './modules/voucher-card/index';

// 展示渲染视图层
import { createStore } from '@ranta/store';

const defaultStore = {
  state: {
    themeCSS: '',
    themeColors: '',
    orderNo: '',
    payResult: {},
    showTakeGoods: false,
    showSelfFetch: false,
    showVoucherCard: false,
  },
  getters: {},
  getActions(ctx) {
    return {
      initCodeInfoShowStatus(payResult) {
        this.showSelfFetch = !!payResult.buttonGroup?.SAVE_PICK_UP_CODE;
        this.showVoucherCard = !!payResult.buttonGroup?.SAVE_CARD_VOUCHER;
        this.showTakeGoods = !!payResult.buttonGroup?.TAKE_GOODS_CODE;
        if (this.showVoucherCard) {
          ctx.data.paidCodeInfoIsShow = true;
          this.initVoucherDetail();
        }
      },
    };
  },
};

const rootStore: IStoreObject = [defaultStore, ...[selfFetch, voucherCard]].reduce(
  (a, b) => mergeStore(a, b),
  {}
);

export default function createExtStore(ctx) {
  return createStore({
    state: () => ({
      ...rootStore.state,
    }),
    getters: {
      ...rootStore.getters,
    },
    actions: {
      ...rootStore.actions,
      ...defaultStore.getActions(ctx),
      ...selfFetch.getActions(ctx),
      ...voucherCard.getActions(ctx),
    },
  });
}
