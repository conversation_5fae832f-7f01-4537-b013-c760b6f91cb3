<template>
  <view v-if="showBar" class="best-block">
    <view>
      <text>{{ promotionTag[0] }}</text>
      <text class="price">￥{{ promotionTag[1] }}</text>
    </view>
    <view class="btn" @click="select">立即使用</view>
  </view>
</template>
<script>
export default {
  name: 'best-price-bar',

  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      showBar: false,
    };
  },

  computed: {
    promotionTag() {
      const { info } = this;
      const { promotionTag = '' } = info;
      return promotionTag.split('¥');
    },
  },

  watch: {
    info() {
      this.updateShowBar();
    },
  },

  methods: {
    select() {
      this.$emit('select');
    },
    updateShowBar() {
      const { info } = this;
      // 活动返回都为0 后端约定
      const showBar = Boolean(
        info.activityId === 0 && info.activityType === 0 && this.promotionTag[0]
      );
      this.ctx.cloud
        // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
        .invoke('beforeBottomBarNoticeRender', {
          notices: showBar ? [{ type: 'optimalPriceRecommendBar' }] : [],
        })
        .then(() => {
          this.showBar = showBar;
        })
        .catch(() => {
          this.showBar = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.best-block {
  position: relative;
  display: flex;
  height: 36px;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  box-sizing: border-box;
  background-color: var(--theme-tag-ump-bg-color, #f2f2ff);
  color: #332333;
  font-weight: 400;
  font-size: 12px;

  .price {
    color: var(--ump-price, #fff);
    font-weight: 500;
  }

  .btn {
    color: var(--ump-icon, #fff);
  }
}
</style>
