<template>
  <view>
    <view v-if="popupActivitiesList.length" class="activity-item" @click="toggleActivitiesPopup">
      <text class="activity-title">{{ discountName }}</text>
      <view class="activity-value-wrapper">
        <text class="activity-value">
          {{ formatedDecreaseAmount }}
        </text>
        <van-icon
          class="activity-question-icon"
          color="#1989fa"
          name="question-o"
          size="14px"
          @click.stop="toggleActivitiesPopup"
        />
      </view>
    </view>

    <!-- 活动 popup -->
    <activities-popup
      :show="showPopup"
      :list="popupActivitiesList"
      :decrease-amount="decreaseAmount"
      :theme-colors="themeColors"
      @close="toggleActivitiesPopup"
    />
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import ActivitiesPopup from './components/ActivitiesPopup';

import { formatPrice, replacePointsName } from './utils';

export default {
  components: {
    'van-icon': Icon,
    'activities-popup': ActivitiesPopup,
  },

  data() {
    return {
      activitiesList: [],
      decreaseAmount: 0,
      pointsName: '',
      themeColors: {},
      showPopup: false,
    };
  },

  computed: {
    discountName() {
      // 获取优惠的名字
      const activities = this.popupActivitiesList;
      if (activities.length === 1) {
        const defaultActivity = activities[0] || {};
        return defaultActivity.promotionTypeName || '店铺活动';
      }
      return '店铺活动';
    },

    formatedDecreaseAmount() {
      return formatPrice(this.decreaseAmount);
    },

    popupActivitiesList() {
      return (this.activitiesList || [])
        .filter((item) => item.isShopActivity === 1)
        .map((item) => {
          if (item.type === 'pointDeduction') {
            item.promotionTypeName = replacePointsName(item.promotionTypeName, this.pointsName);
            item.activityName = replacePointsName(item.activityName, this.pointsName);
          }
          return item;
        });
    },
  },

  created() {
    mapData(this, ['activitiesList', 'decreaseAmount', 'pointsName', 'themeColors']);
  },

  methods: {
    toggleActivitiesPopup() {
      this.showPopup = !this.showPopup;
    },
  },
};
</script>

<style lang="scss">
.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}

.activity-value-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.activity-title {
  font-size: 14px;
  color: #969799;
}

.activity-value {
  font-size: 14px;
  color: #323233;
}

.activity-question-icon {
  display: flex;
  margin-left: 6px;
}
</style>
