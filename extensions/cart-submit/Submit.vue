<template>
  <view v-if="!showEmptyCart && dataLoaded" :class="rootClass.root" :style="themeStyle + themeCSS">
    <!--  如果以后有多个 top-bar 了，我们需要做一个 top-bar-manager 进行统一管理 -->
    <best-price-bar :info="recommendPromotionInfoList" @select="selectBestPrice" />
    <view class="cart-bottom-wrap">
      <view class="t-hairline--top" />
      <view class="checkbox-wrap">
        <view class="checkbox">
          <van-icon
            size="20px"
            @click="handleCheckAllGoods"
            :custom-class="iconCustomClass"
            :name="iconName"
          />
        </view>
        <view class="desc" @click="handleCheckAllGoods">
          <view>全选</view>
        </view>
      </view>

      <view class="right-container t-clearfix">
        <view v-if="!isEditMode" class="total-price">
          <view class="price-desc">
            <!-- 有优惠明细时与合计在同一行显示 -->
            <text class="desc" v-if="isShowDiscountBar">
              {{ cartBottomData.desc }}
            </text>
            <text class="total-price__inner">
              <text class="price-label">合计：</text>
              <price
                class="price-inner"
                :class="{
                  active: cartBottomData.totalPrice > 0,
                }"
                :price="cartBottomData.totalPrice"
                :points-price="cartBottomData.totalPointsPrice"
                :points-name="shopCart.pointsName"
              />
            </text>
          </view>
          <view class="discount-desc">
            <view class="desc" v-if="isShowDiscountBar">
              <text
                >共优惠：<text class="desc-count">￥{{ discountValue }}</text></text
              >
              <van-tag
                class="discount-desc__tag"
                color="#f2f3f5"
                text-color="#323233"
                @click.native="clickDiscountDetail(!discountDetailVisible)"
              >
                <text>优惠明细</text>
                <van-icon :name="arrowClassName" />
              </van-tag>
            </view>
            <view class="desc" v-else>
              {{ cartBottomData.desc }}
            </view>
          </view>
        </view>

        <view class="submit-button">
          <van-button
            :loading="loading"
            :disabled="!totalNum || numChangeInvalid"
            @click="handleBtnTap"
            block
            :custom-class="submitBtnClass"
            i18n-ellipsis="90"
          >
            {{ buttonText }}
          </van-button>
        </view>
      </view>
    </view>

    <separate-buy-popup
      v-if="separateBuy.show"
      :separate-buy="separateBuy"
      :theme-css="themeCSS"
      :points-name="shopCart.pointsName"
      @buy="buyWithGoods"
      @close-separate-buy-popup="handleCloseSeparateBuyPopup"
    />
    <discount-detail-popup
      ref="discountDetailPopup"
      :goods-total-price="cartBottomData.goodsTotalPrice"
      :total-price="cartBottomData.totalPrice"
      :total-discount="cartBottomData.totalDiscount"
      :promotion-details="cartBottomData.promotionDetails"
      :visible="discountDetailVisible"
      :promotion-info="recommendPromotionInfoList"
      :points-price="cartBottomData.totalPointsPrice"
      :points-name="shopCart.pointsName"
      @click-discount-detail="clickDiscountDetail"
      @select="showPromotionPopup"
    />
  </view>
</template>

<script>
import VanLoading from '@youzan/vant-tee/dist/loading/index.vue';
import VanIcon from '@youzan/vant-tee/dist/icon/index.vue';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import VanTag from '@youzan/vant-tee/dist/tag/index.vue';
import SeparateBuyPopup from './components/separate-buy-popup/index';
import DiscountDetailPopup from './components/discount-detail-popup/index';
import Price from './Price';
import { mapState, mapActions } from '@ranta/store';

const SUBMIT_DELETE = '删除';
const SUBMIT_RESERVE = '立即预约';
const SUBMIT_SETTLE = '去结算';
const SUBMIT_SETTLE_COUPON = '领券结算';
export default {
  components: {
    'van-loading': VanLoading,
    'van-icon': VanIcon,
    'van-button': VanButton,
    'van-tag': VanTag,
    'separate-buy-popup': SeparateBuyPopup,
    'discount-detail-popup': DiscountDetailPopup,
    price: Price,
  },

  props: {
    cloudConfig: {
      type: Object,
      default: () => ({
        submitBtnText: '',
      }),
    },
  },

  data() {
    return {
      isShowDiscountBar: true, // TODO cart-unify
      /* 优惠明细面板是否可见 */
      discountDetailVisible: false,
      // 是否已领取最优优惠券
      isReceiveBestCoupon: true,
      ...mapState(this, [
        'isEditMode',
        'loading',
        'showEmptyCart',
        'separateBuy',
        'themeCSS',
        'themeStyle',
        'isDrugShop',
        'iconName',
        'rootClass',
        'iconCustomClass',
        'submitBtnClass',
        'totalNum',
        'btnName',
        'cartBottomData',
        'dataLoaded',
        'recommendPromotionInfoList',
        'customButtonText',
        'shopCart',
        'numChangeInvalid',
      ]),
    };
  },

  computed: {
    arrowClassName() {
      return this.discountDetailVisible ? 'arrow-down' : 'arrow-up';
    },
    discountValue() {
      return ((this.cartBottomData?.totalDiscount || 0) / 100).toFixed(2);
    },
    buttonText() {
      if (!!this.cloudConfig.submitBtnText) {
        // 如果三方有明确传入自定义结算按钮文案，则直接使用三方文案
        return this.cloudConfig.submitBtnText;
      }
      return this.isEditMode
        ? SUBMIT_DELETE
        : this.isDrugShop === true
        ? SUBMIT_RESERVE
        : this.isReceiveBestCoupon
        ? this.customButtonText || SUBMIT_SETTLE
        : SUBMIT_SETTLE_COUPON;
    },
  },

  externalClasses: ['custom-class'],

  watch: {
    // 领券结算按钮曝光次数 埋点
    isReceiveBestCoupon(val) {
      val &&
        this.ctx.logger &&
        this.ctx.logger.log({
          et: 'view', // 事件类型
          ei: 'get_coupons_button_view', // 事件标识
          en: '领券结算按钮曝光次数', // 事件名称
          params: {}, // 事件参数
        });
    },
  },
  created() {
    this.initActions();
    mapState(this, ['isReceiveBestCoupon']);
    // this.updatePopupBottom();
  },

  methods: {
    clickDiscountDetail(visible) {
      this.discountDetailVisible = visible;
      visible &&
        this.ctx.logger &&
        this.ctx.logger.log({
          et: 'click', // 事件类型
          ei: 'promotion_detail_button_click', // 事件标识
          en: '优惠明细按钮点击次数', // 事件名称
          params: {}, // 事件参数
        });
    },

    initActions() {
      mapActions(this, [
        'handleCheckAllGoods',
        'handleBtnTap',
        'buyWithGoods',
        'handleCloseSeparateBuyPopup',
        'updatePopupBottom',
        'selectBestPrice',
        'showPromotionPopup',
      ]);
    },
  },
};
</script>

<style lang="scss" scoped>
.cart-bottom {
  background: #fff;
  width: 100%;
  box-sizing: border-box;

  &::after {
    position: absolute;
    box-sizing: border-box;
    transform-origin: center;
    content: ' ';
    pointer-events: none;
    top: 0;
    right: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    border-top-width: 1px;
    transform: scale(0.5);
  }
}

.tips {
  font-weight: normal;
  color: #666;
  display: inline-block;
}

.total-price {
  box-sizing: border-box;
  font-size: 12px;
  line-height: 16px;
  color: #999;
  text-align: right;

  .desc {
    font-size: 12px;
    color: #969799;
    margin: 0;
  }

  .price-desc {
    .desc {
      margin-right: 3px;
    }
  }

  &__inner {
    display: inline-flex;
    justify-content: flex-end;
    flex-wrap: wrap;
  }

  .price-label {
    font-size: 14px;
    color: #323233;
  }
}

.loading-class {
  width: 100%;
  height: 100%;
}

.checkbox {
  display: inline-block;
  font-size: 18px;
  height: 18px;
  line-height: 1;
}

.desc {
  display: inline-block;
  font-size: 14px;
  margin-left: 5px;
  color: #333;
  vertical-align: middle;
  margin-top: -8px;

  &-count {
    font-family: Avenir;
  }
}

.discount-desc {
  &__tag {
    border-radius: var(--theme-radius-tag, 10px);
    width: 72px;
    height: 20px;
    padding: 0;
    justify-content: center;
  }
}

.icon--checked {
  color: var(--general, #f44);
}

.icon--unchecked {
  color: #bbb;
}

.price {
  margin-right: 10px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.5;
  color: #111;
  text-align: right;

  &--label {
    display: inline-block;
  }
}

.cart-bottom--safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.right-container {
  float: right;
  min-height: 56px;
  line-height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.pay-btn,
.pay-btn--disabled {
  display: inline-block;
  min-width: 96px !important;
  width: auto;
  white-space: nowrap;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: var(--theme-radius-button, 20px) !important;
  color: var(--main-text, #fff) !important;
  text-align: center !important;
  border: none !important;
  font-weight: 500;
  background: var(--main-bg, #f44) !important;
  font-size: var(--eo-font-size-16, 16px);
  /* #ifdef web */
  margin-left: 12px;
  /* #endif */
}

.submit-button {
  /* #ifdef weapp */
  margin-left: 12px;
  /* #endif */
}

.pay-btn--disabled {
  background: #e5e5e5 !important;
  color: #999 !important;
}

.cart-bottom-wrap {
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  background: #fff;
  z-index: 100;
}

.checkbox-wrap {
  flex-shrink: 0;
  width: 58px;
}

.t-hairline--top {
  position: absolute;
  box-sizing: border-box;
  transform-origin: center;
  content: ' ';
  pointer-events: none;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid #eee;
  transform: scale(0.5);
  border-top-width: 1px;
}
</style>
