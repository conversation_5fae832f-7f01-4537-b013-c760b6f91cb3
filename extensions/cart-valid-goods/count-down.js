function noop() {
  // do nothing
}
/**
 * 倒计时
 */
export default class CountDown {
  constructor({ seconds = 3600, onTimeChange }) {
    this.seconds = seconds;
    this.onTimeChange = typeof onTimeChange === 'function' ? onTimeChange : noop;
    this.tid = null;
  }

  run() {
    const endTime = new Date(Date.now() + this.seconds * 1000);
    this.tid = setInterval(() => {
      const remainTime = Math.max(0, endTime - Date.now());
      let day = 0;
      let hour = 0;
      let min = 0;
      let sec = 0;
      let remain = Math.floor(remainTime / 1000);

      day = Math.floor(remain / 86400);
      remain %= 86400;
      hour = Math.floor(remain / 3600);
      remain %= 3600;
      min = Math.floor(remain / 60);
      sec = remain % 60;

      this.onTimeChange({
        day,
        hour,
        min,
        sec,
      });

      if (remainTime <= 0) {
        clearInterval(this.tid);
        this.tid = null;
      }
    }, 200);
  }

  cancelCounter() {
    if (this.tid) {
      clearInterval(this.tid);
      this.tid = null;
    }
  }
}
