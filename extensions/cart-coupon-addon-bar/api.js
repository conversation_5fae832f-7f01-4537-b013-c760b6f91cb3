import request, { requestV2 } from '@youzan/tee-biz-request';
import { getPrefetchData } from '@youzan/tee-biz-prefetch';
import { getStorageSync, removeStorageSync } from '@youzan/tee-api';

const SEND_COUPON_URL = '/wscump/coupon/cart/send-coupon.json';

function getCouponAddOnInfoRequest({ kdtId: _kdtId, storeId, selectedPromotions, pdlive = '' }) {
  return request({
    path: '/wsctrade/cart/getCouponAddOnInfo.json',
    method: 'GET',
    data: {
      store_id: storeId,
      selectedPromotions,
      pdlive,
    },
    withCredentials: true,
  }).catch((e) => {
    console.error(e);
    return null; // 如果报错则什么都不返回
  });
}

export function getCouponAddOnInfo(options) {
  // 使用一次后key删除
  const prefetchKey = getStorageSync('trade-cart-addon-prefetchkey');
  if (prefetchKey) {
    removeStorageSync('trade-cart-addon-prefetchkey');
    return getPrefetchData({
      prefetchKey,
      normalFetchCb: () => {
        return getCouponAddOnInfoRequest(options);
      },
    });
  }
  return getCouponAddOnInfoRequest(options);
}

export function sendCoupon(data) {
  return requestV2({
    path: SEND_COUPON_URL,
    method: 'GET',
    data,
  });
}

export function getGuideInfo(data) {
  return requestV2({
    path: '/wscump/guide-member/get-guide-info.json',
    method: 'GET',
    data,
  });
}
