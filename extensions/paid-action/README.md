# 操作互动区 ext 设计说明

1. 交易相关弹框功能都可以收在 paid-action
2. 目前的按钮使用了很多 widget 设计，设计过重，后面需要考虑改掉

# 操作互动区业务说明

操作互动区的业务需要明白两个问题，一个是按钮展示规则，一个是那么多自动弹框的顺序规则

## 按钮展示规则

1. 通用场景按钮配置由后端返回，对应 `payResultVo.buttonGroup`字段
2. 社区团购订单使用自定义按钮配置展示，包含[查看详情（高亮），继续团购，分享一下，查看提货码]
3. 特别场景特殊处理小程序端：

- 视频号场景不展示分享按钮（按照 1177：视频号直播间和 1175：视频号 profile 页两个场景值判断） H5 端：
- 不展示订阅物流的：有赞精选 APP，三方 APP，百度小程序
- 不展示分享按钮的：喜马拉雅 APP
- 不展示提货码按钮的：医药处方订单
- 有赞客、有赞热卖小程序、头条小程序、新有赞精选 APP 和新有赞精选小程序不展示订阅物流、再来一单、分享一下

4. 目前小程序端还不支持再来一单

## 弹框逻辑规则

支付成功页支持的自动弹框包含以下表格列举业务场景

### 自动弹框顺序有两种控制规则：

1. 高优固定顺序弹框 a. [1, 'selfFetch', 'PICK_UP_CODE', '查看提货码'] b. [2, 'voucherCard', 'SAVE_CARD_VOUCHER', '查看券码'] c. [3, 'solitaire', '', '分享一下', '社群接龙'] d. [4, 'groupbuyShare', '', '分享一下', '社区团购'] e. [5, 'priorUse', '', '先用后付']
2. 装修接口管控顺序 a. [6, 'logistics_subscribe', 'SUBSCRIPTION', '订阅物流'] b. [7, 'pop_ad', '', '弹框广告'] c. [8, 'fission_coupons', '', '裂变券'] d. [9, 'auto_issue_coupon', '', '自动推券']

### 物流弹框规则说明

** 订阅物流自动弹框逻辑：**

1. 包含订阅物流按钮才需要弹，这是大前提，包含订阅物流两个视角体现：

- 第一个按钮是订阅物流，现有代码使用的逻辑
- 按钮里包含订阅物流（其实如果包含订阅物流按钮，那么一般情况都是排在第一个位置，还没想到其他特殊情况）

2. 如果近期已弹出过，则不需要再次自动弹框
3. 近期没有弹过 case H5 端:

- 店铺有绑定公众号或者小程序的，才需要弹框
- 支付宝和 QQ 小程序不服从上述逻辑，直接弹 case 微信小程序:
- 下单页没有弹出过微信订阅物流组件的，需要弹框

** 订阅物流弹框内容千奇百怪，每个端的要求都不一样，可以阅读代码`utils/subscription`了解 **
