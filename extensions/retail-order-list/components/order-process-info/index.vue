<template>
  <view class="order-process-info">
    <view v-for="(item, index) in itemDetail.orderProcesses" :key="index">
      <process-item :isPrimary="item.isPrimary" :opt="item" :location="location"></process-item>
    </view>
  </view>
</template>

<script>
import ProcessItem from './ProcessItem.vue';

export default {
  name: 'order-process-info',
  components: {
    ProcessItem,
  },
  props: {
    itemDetail: Object, // 当前状态详情
    location: Object,
  },
};
</script>

<style lang="scss" scoped>
.order-process-info {
  padding: 0 16px;
}
</style>
