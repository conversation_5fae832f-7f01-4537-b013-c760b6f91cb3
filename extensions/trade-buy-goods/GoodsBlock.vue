<template>
  <view v-if="showGoodsBlock && visible">
    <view class="index__block">
      <goods-list v-if="goods.list">
        <!-- #ifdef web -->
        <hotel
          slot="hotel-view"
          v-if="tradeTag.hasHotelGoods"
          :item-list="formattedGoods"
          :price="pay.itemPay"
        />
        <!-- #endif -->
      </goods-list>
      <store-gifts-list v-if="showStoreGoodsList" />
      <unavailable-goods />

      <extra-fees v-if="isWscShop" />

      <giveaway-card :reward-model="rewardModel" />
    </view>
  </view>
</template>
<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { checkRetailShop, checkWscSingleStore } from '@youzan/utils-shop';
import { mapState } from '@ranta/store';
import GiveawayCard from './components/GiveawayCard';

export default {
  components: {
    'giveaway-card': GiveawayCard,
  },
  props: {
    defaultGoodsBlockVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: this.defaultGoodsBlockVisible ?? true,
      storeGiftsInfo: {},
      rewardModel: [],
      isRetailShop: false,
      isWscShop: true,
      reserves: {},
      ...mapState(this, [
        'goods',
        'tradeTag',
        'display',
        'pay',
        'selfFetch',
        'formattedGoods',
        'showGoodsBlock',
      ]),
    };
  },

  computed: {
    itemList() {
      const { list } = this.goods || {};
      const itemList = (list || []).filter((item) => !item.fromTmpAdded && !item.present);
      return itemList;
    },
    isEmpty() {
      return this.itemList && this.itemList.length === 0;
    },
    hasSelfFetchBenefit() {
      return (
        this.storeGiftsInfo &&
        Object.keys(this.storeGiftsInfo).length &&
        (this.storeGiftsInfo.coupons.length ||
          this.storeGiftsInfo.couponsCode.length ||
          this.storeGiftsInfo.presents.length)
      );
    },
    showStoreGoodsList() {
      const { showAddressTab, showExpressTab } = this.display || {};
      if (showAddressTab && showExpressTab && this.selfFetch?.isAllow) {
        return this.hasSelfFetchBenefit && this.state?.address?.activeTab === 1;
      }
      return this.hasSelfFetchBenefit && this.selfFetch?.isAllow;
    },
  },

  created() {
    mapData(this, ['storeGiftsInfo', 'dataLoaded', 'rewardModel']);
    mapData(this, {
      tradeGoodsVisible: (visible) => {
        this.visible = visible;
      },
      appShop: (val) => {
        this.isRetailShop = checkRetailShop(val ?? {});
        this.isWscShop = checkWscSingleStore(val ?? {});
      },
    });
  },
};
</script>
<style lang="scss">
.long-cell {
  padding: var(--theme-page-cell-padding-top, 10px) var(--theme-page-cell-padding-right, 12px)
    var(--theme-page-cell-padding-bottom, 10px) var(--theme-page-cell-padding-left, 12px) !important;
}

.index {
  &__block {
    margin: var(--theme-page-card-margin-top, 0) var(--theme-page-card-margin-right, 12px)
      var(--theme-page-card-margin-bottom, 10px) var(--theme-page-card-margin-left, 12px);
    border-radius: var(--theme-radius-card, 8px);
    overflow: hidden;
    position: relative;

    &:empty {
      display: none;
    }
  }
}
</style>
