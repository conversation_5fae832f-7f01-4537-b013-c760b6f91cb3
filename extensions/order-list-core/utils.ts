import {
  OrderListItemGoods,
  OrderListTab,
  OrderListTabOpt,
  OrderListTabTypeEnum,
} from './extension';
import type {
  OrderListTabs,
  OrderListInfo,
  OrderListOrderInfo,
} from '@youzan-cloud/cloud-biz-types';

export const activeTabConfig = {
  activeTabMap: {
    // 所有订单
    all: 'all',
    // 待付款的订单
    waitPay: 'topay',
    // 待发货的订单
    waitShip: 'tosend',
    // 已发货的订单
    shipped: 'send',
    // 待评价的订单
    waitEvaluate: 'toevaluate',
    // 当前订单(零售)
    current: 'current',
    // 历史订单(零售)
    history: 'history',
  },
  getKeyByValue(value: string) {
    return Object.keys(this.activeTabMap).find((key) => this.activeTabMap[key] === value);
  },
  getValueByKey(key: OrderListTabTypeEnum) {
    return this.activeTabMap[key];
  },
};

/** ****************************************************************************
 * TODO: 这4个类型是为了转换三方的操作枚举值，因为标品和三方传入的枚举值不一致
 * 后续调整方案：需要将标品的这4个操作类型调整成和三方一样的值，我举个例子：
 * 比如说 延长收货 三方是 confirm 标品是 confirmReceive。后续标品也应该调整成confirm
 * ************************************************************************** */
export const actionTypeConfig = {
  _map: {
    confirm: 'confirmReceive',
    confirmHotel: 'confirmReceiveHotel',
    extend: 'laterReceive',
    share: 'shared',
  },
  getKeyByValue(value: string) {
    return Object.keys(this._map).find((key) => this._map[key] === value);
  },
  getValueByKey(key: string) {
    return this._map[key];
  },
};

export const yunBtnTypeMap = {
  _map: {
    /** *************************************************************************
     * 以下这些是开放1.0的订单列表存在的具体操作，超出部分都是跳转URL
     * 具体代码见:
     * https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade/blob/960e027128f5f3cd3018c12b713c17b2285418ee/client/pages/order/list/store/actions.js#L207-382
     ************************************************************************ */
    /* #ifdef web */
    cancel: 'cancel',
    'cancel-pay-on-delivery': 'cancelPayOnDelivery',
    'extend-receive': 'laterReceive',
    'confirm-receive': 'confirmReceive',
    'confirm-receive-hotel': 'confirmReceiveHotel',
    'buy-again': 'buyAgain',
    'direct-buy-again': 'directBuyAgain',
    'express-reminder': 'expressReminder',
    review: 'review', // <-- 从这个类型开始一下都是跳转URL
    reviewed: 'reviewed',
    peerpay: 'peerpay',
    gift: 'gift',
    topay: 'topay',
    'lottery-result': 'lotteryResult',
    'group-detail': 'groupDetail',
    'course-detail': 'courseDetail',
    transport: 'transport',
    evaluate: 'evaluate',
    'view-card-detail': 'viewCardDetail',
    'invite-help': 'inviteHelp',
    /* #endif */
    /* #ifdef weapp */
    cancelOrder: 'cancel',
    toPay: 'topay',
    /* #endif */
  },
  getKeyByValue(value: string) {
    return Object.keys(this._map).find((key) => this._map[key] === value);
  },
  getValueByKey(key: string) {
    return this._map[key] || key;
  },
};

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  cloudOrderListExtV1(yunDesignConfig): Record<string, any> {
    return yunDesignConfig.profile;
  },
  selectedTabV1({ activityTab }): string {
    return activityTab.type;
  },
  getOrderListTabs({
    orderListTabOpt,
    activityTab,
  }: {
    orderListTabOpt: OrderListTabOpt;
    activityTab: OrderListTab;
  }): OrderListTabs {
    return {
      tabs: orderListTabOpt.tabs.map((item) => {
        return {
          type: activeTabConfig.getKeyByValue(item.type),
          text: item.text,
          isSelected: item.type === activityTab.type,
        } as OrderListTab;
      }),
    };
  },
  getOrderListInfo({ activityTab, orderList }): OrderListInfo {
    const _orderList: OrderListOrderInfo[] = (orderList || []).map((item) => {
      const shopInfo = {
        shopName: item.shopName,
        kdtId: item.kdtId,
      };
      const orderInfo = {
        orderNo: item.orderNo,
        status: item.statusCode,
        statusText: item.orderStateStr,
        pickUpCode: item.pickUpCode || '',
        payInfo: {
          amountDesc: item.orderItems[0].payInfo.amountDesc,
          payAmount: item.orderItems[0].payInfo.payAmount,
          lastDesc: item.orderItems[0].payInfo.last,
        },
      };
      const goodsList: OrderListItemGoods[] = item.orderItems[0].items.map((item) => {
        return {
          num: item.num,
          skuText: item.sku.join('、'),
          payPrice: item.price,
          originPrice: Number(item.origin),
          price: item.price,
          id: item.goodsId,
          imgUrl: item.imgUrl || '',
          title: item.title,
          alias: item.alias,
        };
      });
      const orderActionBtns = item.btnList.map((item) => {
        return {
          name: item.text,
          type: actionTypeConfig.getKeyByValue(item.value) || item.value,
        };
      });
      return {
        shopInfo,
        orderInfo,
        goodsList,
        orderActionBtns,
      };
    });

    return {
      orderList: _orderList,
      hasMore: activityTab.hasNext,
      // FIXME: 目前三方不会用到，因此这里暂时写死，后续性能优化会重写loading状态
      // FIXME: 目前loading状态在order-list-list中
      isLoading: false,
    };
  },
};
