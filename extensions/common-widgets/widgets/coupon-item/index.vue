<template>
  <view
    :class="['t-coupon', disabled ? 't-coupon--disabled' : '', noClick ? 't-coupon--noClick' : '']"
    @click="onClick"
  >
    <!-- id < 0为虚拟优惠券 tag -->
    <view v-if="coupon.id < 0" class="t-coupon__virtual-tag" :style="vts">
      {{ coupon.extraInfo.CARRIER_TPL_NAME }}
    </view>
    <view class="t-coupon__content">
      <view class="t-coupon__head">
        <view
          class="t-coupon__amount"
          :class="{
            't-coupon-chinese_desc': !coupon.unitDesc,
          }"
        >
          <text v-if="isYuanUnit" class="yuan_unit"> ¥ </text>
          <text class="num">
            <view v-if="isYuanUnit && showDecimal"
              >{{ couponPriceDecimal[0]
              }}<text class="decimal">.{{ couponPriceDecimal[1] }}</text></view
            >
            <text v-else>{{ coupon.valueDesc }}</text>
          </text>
          <view class="t-coupon__amount-unit" v-if="!isYuanUnit">{{ coupon.unitDesc || '' }}</view>
        </view>
        <view class="t-coupon__condition">
          {{ coupon.condition }}
        </view>
      </view>
      <view class="t-coupon__body">
        <view class="t-coupon__name">{{ coupon.name }}</view>
        <view class="t-coupon__valid">
          <view>{{ validPeriod }}</view>
        </view>
        <van-checkbox
          v-if="!disabled && canCheck"
          :size="18"
          :value="chosen"
          class="t-coupon__corner"
          :checked-color="color"
        />
        <view class="t-coupon__corner--tag" v-if="cornerTag" :style="overlayingTagStyle">{{
          cornerTag
        }}</view>
      </view>
      <view class="t-coupon__right-num" v-if="needNum">x{{ coupon.num }}</view>
      <view v-if="couponLabel.hasLabel" class="t-coupon__label" :style="couponLabelStyle">{{
        couponLabel.label
      }}</view>
    </view>
    <view
      v-if="(disabled && coupon.reason) || (!disabled && (coupon.description || extraDescription))"
      class="t-coupon__dash"
    ></view>
    <view v-if="disabled && coupon.reason" class="t-coupon__description">{{ coupon.reason }}</view>
    <view v-if="!disabled && coupon.description" class="t-coupon__description">{{
      coupon.description
    }}</view>
    <view v-if="!disabled && extraDescription" class="t-coupon__description">{{
      extraDescription
    }}</view>
  </view>
</template>
<script>
import hexToRgba from '@youzan/utils/string/hexToRgba';
import Checkbox from '@youzan/vant-tee/dist/checkbox/index';
import { formatDate, COUPON_TYPE } from './utils';

export default {
  components: {
    'van-checkbox': Checkbox,
  },
  props: {
    coupon: Object,
    chosenCoupons: Array,
    disabled: Boolean,
    currency: {
      type: String,
      default: '¥',
    },
    color: String,
    priceColor: String,
    canCheck: {
      type: Boolean,
      default: true,
    },
    needNum: {
      type: Boolean,
      default: false,
    },
    noClick: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    chosen() {
      return !!this.chosenCoupons?.find((coupon) => coupon.id === this.coupon.id);
    },
    canChosenCouponsOverlying() {
      return this.chosenCoupons.some((coupon) => coupon.canOverlying);
    },
    getChosenStatus() {
      return !!this.chosenCoupons?.find((coupon) => coupon.id === this.coupon.id);
    },
    cornerTag() {
      const { length: chosenCount } = this.chosenCoupons;
      return this.canChosenCouponsOverlying &&
        chosenCount < 3 &&
        !!this.coupon.canOverlying &&
        !this.chosen
        ? '可叠加'
        : '';
    },
    extraDescription() {
      return this.canChosenCouponsOverlying && !this.coupon.canOverlying && !this.chosen
        ? '此券暂不可和已勾选的券叠加'
        : '';
    },
    validPeriod() {
      const { coupon } = this;
      if (!coupon) {
        return '';
      }
      const validPeriodCopywriting = coupon.extraInfo?.validPeriodCopywriting;

      return (
        validPeriodCopywriting || formatDate(coupon.startAt) + ' - ' + formatDate(coupon.endAt)
      );
    },
    couponLabel() {
      const { extraInfo = {} } = this.coupon || {};
      const { externalPlatformType = '' } = extraInfo;
      const label = COUPON_TYPE[externalPlatformType];

      return {
        hasLabel: externalPlatformType && label,
        label,
      };
    },
    overlayingTagStyle() {
      return `color: ${this.color}; border-color: ${this.color}`;
    },
    isYuanUnit() {
      return this.coupon.unitDesc === '元';
    },
    showDecimal() {
      return this.coupon.valueDesc.includes('.') && +this.coupon.valueDesc.split('.')[0];
    },
    couponPriceDecimal() {
      return this.coupon.valueDesc.split('.');
    },
    couponLabelStyle() {
      return `color: ${this.priceColor}; background-color: ${this.priceColor}38`;
    },
    vts() {
      return `color: ${this.color}; background-color: ${hexToRgba(this.color, 0.1)}`;
    },
  },
  methods: {
    onClick() {
      this.$emit('onChange', this.coupon.id);
    },
  },
};
</script>
<style lang="scss" scoped>
.t-coupon {
  margin: 0 12px 12px;
  overflow: hidden;
  background-color: #fff;
  border-radius: var(--theme-radius-card, 8px);
  position: relative;

  &:active {
    background-color: #f2f3f5;
  }

  &__content {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: 84px;
    padding: 14px 0;
    color: #323233;
  }

  &__head {
    box-sizing: border-box;
    padding: 0 8px;
    color: var(--ump-icon, #ee0a24);
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
    flex-basis: calc(
      100vw - 282px /* 除头部外，其他内容的宽度。 */
    ); // 尽量让右侧文本不换行，同时能给头部尽可能多的空间

    min-width: 106px; // 保证小屏幕下宽度不被过度压缩
    max-width: 136px; // 保证大屏幕下头部不过宽
  }

  &__label {
    position: absolute;
    left: 0;
    top: 0;
    padding: 1px 8px;
    border-radius: var(--theme-radius-tag, 4px) 0 var(--theme-radius-tag, 4px) 0;
    font-size: 10px;
    line-height: 14px;
  }

  &__amount,
  &__name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__valid {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
    font-size: 12px;
    color: #969799;
    padding-right: 40px;
    line-height: 16px;
  }

  &__amount {
    font-weight: 500;
    font-size: 30px;
    display: flex;
    align-items: baseline;
    font-family: Avenir;
    height: 36px;
    line-height: 32px;

    &-unit {
      display: inline-block;
      font-size: 40%;
      font-weight: 600;

      &:not(:empty) {
        margin-left: 2px;
      }
    }

    .yuan_unit {
      font-size: 16px;
      font-weight: 600;
      margin-right: 2px;
      font-family: Avenir;
    }

    .decimal {
      font-size: 16px;
      font-weight: 500;
    }
  }

  &-chinese_desc {
    font-size: 18px;
  }

  &__body {
    position: relative;
    flex: 1;
    border-radius: 0 8px 8px 0;
    border-radius: 0 var(--theme-radius-card, 8px) var(--theme-radius-card, 8px) 0;
  }

  &__condition {
    font-size: 12px;
    line-height: 1.333333;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    text-align: left;
    -webkit-line-clamp: 2;
  }

  &__name {
    width: calc(100% - 40px);
    margin: 8px 0 8px 0;
    font-size: 14px;
    line-height: 20px;
  }

  &__corner {
    position: absolute;
    top: 0;
    right: 16px;
    bottom: 0;
    display: flex;
    align-items: center;
  }

  &__dash {
    border-top: 1px dashed #ebedf0;
    position: absolute;
    left: 12px;
    right: 12px;
    height: 0;
  }

  &__description {
    padding: 8px 12px;
    font-size: 12px;
    color: #323233;
  }

  &__extra--des {
    padding-right: 40px;
    font-size: 12px;
    line-height: 20px;
    color: #323233;
  }

  &__corner--tag {
    position: absolute;
    top: -8px;
    right: -16px;
    width: 64px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    transform: rotate(45deg);
    text-align: center;
    font-size: 12px;
    opacity: 0.5;
  }

  &__right-num {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 5px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    background-color: var(--ump-tag-bg, rgba(238, 10, 36, 0.1));
    border-top-right-radius: var(--theme-radius-tag, 4px);
    border-bottom-left-radius: var(--theme-radius-tag, 4px);
    color: var(--ump-icon, #ee0a24);
  }

  &--disabled {
    &:active {
      background-color: #fff;
    }

    .t-coupon-item__content {
      height: 84px - 10px;
    }

    .t-coupon__head,
    .t-coupon__name,
    .t-coupon__valid {
      color: #969799;
    }
  }

  &--noClick {
    &:active {
      background-color: #fff;
    }
  }

  &__virtual-tag {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    height: 16px;
    padding: 0 8px;
    font-size: 10px;
    line-height: 16px;
    border-radius: 8px 0;
  }
}
</style>
