import type { EcardData } from '@youzan-cloud/cloud-biz-types';

// 虚拟卡券状态
export const ECARD_STATE = {
  NOT_VERIFIED: 1,
  VERIFIED: 2,
  DISABLED: 3,
  EXPIRED: 4,
  REFUND: 5,
};

// 虚拟卡券配置
export const ECARD_CONF = {
  [ECARD_STATE.NOT_VERIFIED]: {
    value: '',
    title: '待使用',
    suffix: '',
    weight: 1,
  },
  [ECARD_STATE.VERIFIED]: {
    value: '已使用',
    title: '已使用',
    suffix: '--del',
    weight: 3,
  },
  [ECARD_STATE.DISABLED]: {
    value: '已退款',
    title: '已退款',
    suffix: '--del',
    weight: 2,
  },
  [ECARD_STATE.EXPIRED]: {
    value: '已过期',
    title: '已过期',
    suffix: '--del',
    weight: 4,
  },
  [ECARD_STATE.REFUND]: {
    value: '退款中',
    title: '退款中',
    suffix: '',
    weight: 0,
  },
};

export const formateVirtualTicket = (virtualTicket) => {
  const res = {
    title: '',
    time: '',
    list: [],
  };

  res.time = virtualTicket.effectiveTimeDesc || '';
  res.list = virtualTicket.ticketItemResponseDTOList || [];

  // 根据权重设置 title
  let state = null;
  let weight = null;
  let notVerifiedNum = 0;

  res.list.forEach((item) => {
    const itemConfig = ECARD_CONF[item.verifyState];

    if (item.verifyState === ECARD_STATE.NOT_VERIFIED) {
      notVerifiedNum++;
    }

    if (weight === null || itemConfig.weight < weight) {
      res.title = itemConfig.title;
      weight = itemConfig.weight;
      state = item.verifyState;
    }
    item.verifyStateDetail = ECARD_CONF[item.verifyState];
  });

  res.title =
    state === ECARD_STATE.NOT_VERIFIED ? `${res.title}（${notVerifiedNum}张）` : res.title;

  return res;
};

export const cloudData = {
  getEcard: ({ virtualTicket }): EcardData => {
    const { title, time, list } = formateVirtualTicket(virtualTicket);
    return {
      title,
      time,
      list: (list || []).map((_) => ({ ..._, ecardNo: _.ticketNo })),
    };
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
