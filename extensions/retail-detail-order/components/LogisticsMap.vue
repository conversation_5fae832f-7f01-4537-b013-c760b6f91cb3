<!-- 地图容器，主要是为了兼容微信小程序和web端的地图组件 -->
<template>
  <view class="city-delivery">
    <view class="map-wrapper">
      <div class="map-container" ref="amap" />
      <!-- #ifdef weapp -->
      <map
        id="wx-map"
        :longitude="position[0]"
        :latitude="position[1]"
        :markers="markers"
        :scale="zoom"
        class="map-container"
      >
        <cover-view slot="callout">
          <cover-view :marker-id="MarkerIdEnum.Rider">
            <cover-view class="rider-marker">
              <cover-view class="rider-content">
                {{ riderContent.statusText }}
                <cover-view class="rider-distance">
                  {{ riderContent.distanceMode }}
                  <cover-view class="distance-text">{{ riderContent.distanceText }}</cover-view>
                </cover-view>
                <cover-view> {{ riderContent.estimateArriveTimeTip }} </cover-view>
              </cover-view>
              <cover-view class="rider-marker__angle"></cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
      <!-- #endif -->
      <view class="cap-map-board">
        <view class="toolbar-refresh" @click="onRefresh" />
        <van-cell
          class="cap-map-board__rider"
          title-style="font-weight: bold;padding-left: 5px;"
          :border="false"
          :title="'骑手：' + (localDelivery.distName || '')"
        >
          <a @click="onCallRider">
            <span>联系骑手</span>
          </a>
        </van-cell>
      </view>
    </view>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { makePhoneCall } from '@youzan/tee-api';
/* #ifdef weapp */
import mixinWxMap from '../mixins/logistics-map/wx-map';
/* #endif */
/* #ifdef web */
import mixinAmap from '../mixins/logistics-map/a-map';
/* #endif */

export default {
  name: 'logistics-map',
  components: {
    'van-cell': Cell,
  },
  /* #ifdef weapp */
  mixins: [mixinWxMap],
  /* #endif */
  /* #ifdef web */
  mixins: [mixinAmap],
  /* #endif */
  props: {
    distId: String,
    cityDetail: {
      type: Object,
      default: () => ({}),
    },
    localDelivery: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      markers: [],
    };
  },
  watch: {
    cityDetail: {
      handler(val) {
        if (!val) return;
        this.initMap(true).then(() => {
          this.initMapExtra();
        });
      },
      deep: true
    },
  },
  mounted() {
    this.initMap().then(() => {
      this.initMapExtra();
    });
  },
  computed: {
    position() {
      const lon = 120.126153;
      const lat = 30.275405;
      const { transporterLat, transporterLng, buyerLat, buyerLng, shopLat, shopLng } =
        this.cityDetail || {};
      const position = transporterLng
        ? [Number(transporterLng), Number(transporterLat)]
        : buyerLng
        ? [Number(buyerLng), Number(buyerLat)]
        : shopLng
        ? [Number(shopLng), Number(shopLat)]
        : [Number(lon), Number(lat)];

      return position;
    },
  },

  methods: {
    onRefresh() {
      this.$emit('refetchData');
    },

    onCallRider() {
      makePhoneCall({
        phoneNumber: `${this.localDelivery.distMobile}`,
      });
    },
  },
};
</script>

<style lang="scss">
$RIDER_BG_COLOR: #3e4043;

.map-wrapper {
  position: relative;
  height: 240px;
  background-color: #fbf9f4;
  .amap-logo {
    display: none !important;
  }
  .amap-copyright {
    visibility: hidden !important;
  }
}
.cap-map-board {
  position: absolute;
  left: 15px;
  right: 15px;
  bottom: 10px;
  font-size: 8px;
  z-index: 5;
}

.toolbar-refresh {
  display: inline-block;
  background: #fff;
  color: #222;
  width: 24px;
  height: 24px;
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: 2px 2px;
  margin-bottom: 15px;
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(51, 51, 51, 0.2);
  background-image: url('https://img01.yzcdn.cn/public_files/2019/01/08/9ea0af310abafe33c235152a70282f1e.png');
}

.cap-map-board__rider {
  padding: 14px 15px;
  line-height: 40px;
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(51, 51, 51, 0.2);
  position: relative;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  color: #323233;
  font-size: 14px;
  background-color: #fff;
  &::before {
    display: inline-block;
    content: '';
    width: 30px;
    height: 40px;
    background-image: url('//img01.yzcdn.cn/public_files/2019/01/08/7b6ab782158a37ef06f1a764ba6a67da.png');
    background-size: 30px;
    background-position: 0 5px;
    background-repeat: no-repeat;
  }

  a {
    color: #3283fa;

    span {
      margin-left: 5px;
    }

    &::before {
      display: inline-block;
      content: '';
      width: 15px;
      height: 15px;
      background-size: 15px 15px;
      vertical-align: -2px;
      background-image: url(//b.yzcdn.cn/v2/image/wap/trade/result/<EMAIL>);
    }
  }
}

.map-container,
.wx-map {
  height: 100%;
  width: 100%;

  .rider-marker {
    background: $RIDER_BG_COLOR;
    font-size: 14px;
    line-height: 18px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    position: relative;
    border-radius: 23px;

    .rider-circle {
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: #bd10e0;
    }

    .rider-content {
      padding: 5px;
      color: #fff;
      white-space: nowrap;

      .rider-distance {
        white-space: nowrap;
        font-size: 12px;
        color: #999;

        span,
        .distance-text {
          display: inline;
          color: #3283fa;
        }
      }
    }
    /* #ifdef web */
    &__angle {
      position: absolute;
      bottom: -12px;
      left: 40px;
      border-top: 12px solid $RIDER_BG_COLOR;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
    }

    &__rider {
      position: absolute;
      top: 60px;
      width: 60px;
      height: 50px;
      background-size: 60px 50px;
      background-repeat: no-repeat;

      &--east {
        background-image: url('//img01.yzcdn.cn/public_files/2019/01/09/d5aa8cfffe64fe1b215087a4cc6e58d7.png');
      }

      &--west {
        background-image: url('//img01.yzcdn.cn/public_files/2019/01/08/60134a95f52aab26657e8c65933343b0.png');
      }
    }
    /* #endif */
  }
  /* #ifdef web */

  .buyer-marker {
    width: 46px;
    height: 50px;
    background-image: url('//img01.yzcdn.cn/public_files/2019/01/08/da9f40dc7e88eade6f7a38ab34c77daf.png');
    background-size: 46px 50px;
    background-repeat: no-repeat;

    .buyer-thumb {
      width: 32px;
      height: 32px;
      position: absolute;
      top: 5px;
      left: 7px;
      border-radius: 16px;

      img {
        max-height: 100% !important;
        max-width: 100% !important;
      }
    }
  }
  /* #endif */
}
</style>
