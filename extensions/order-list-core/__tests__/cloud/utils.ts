import type { ZodArray, ZodObject, ZodUnion } from 'zod';

export const sleep = (ms = 50) => {
  return new Promise<void>((resolve) => {
    const timer = setTimeout(() => {
      if (timer) clearTimeout(timer);
      resolve();
    }, ms);
  });
};

export const validateDataType = (
  dataName: string,
  data: any,
  typeSchema: ZodObject<any> | ZodUnion<any> | ZodArray<any>
) => {
  // @ts-ignore
  expect(data).toBeValidated(typeSchema);
};

export const validateOpenDataType = async <T>(
  dataName: string,
  typeSchema: ZodObject<any> | ZodUnion<any>
) => {
  const data: T = await yz.data.get(dataName);
  validateDataType(dataName, data, typeSchema);
};

export const noop = () => {};

export const rejectPromiseNoop = () => Promise.reject();
export const resolvePromiseNoop = () => Promise.resolve();

export const addCallTimes = async (name: string) => {
  const times = (await storage.get(name)) ?? 0;
  await storage.set(name, times + 1);
};
export const getCallTimes = (name: string) => storage.get(name);
