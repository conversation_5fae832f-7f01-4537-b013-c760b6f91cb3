<template>
  <view>
    <van-icon
      v-if="navArrowTop"
      name="arrow-left"
      color="#111111"
      size="20px"
      :style="{ top: navArrowTop + 'px' }"
      class="arrow"
      @click="goBack"
    />
    <view class="gift-share" v-if="!isFetching">
      <view class="gift-share__body">
        <view class="gift-share__body-title">
          <image class="gift-share__body-gift-icon" src="https://b.yzcdn.cn/icon/gift-icon1.png" />
          <text v-if="presenterView">你送出一份礼物</text>
          <text v-else>{{ presenterInfo.nickName }}送你一份礼物</text>
        </view>
        <message-input :message="message" :presenter-view="presenterView" :gift-id="giftId" />
        <view class="gift-share__body-box-container">
          <view class="gift-share__body-box">
            <image class="gift-share__body-box-main" src="https://b.yzcdn.cn/gift/box-bg1.png" />
            <image
              class="gift-share__body-box-bg-extra"
              src="https://b.yzcdn.cn/gift/gift-box-extra-bg1.png"
            />
            <view class="gift-share__body-box-goods">
              <image class="gift-share__body-box-goods-img" :src="goodsInfo.goodsImg" />
            </view>
          </view>
        </view>
      </view>
      <gift-goods
        :goods-list="goodsList"
        :is-show-price="presenterView"
        @jump="handlePickGiftClick"
      />

      <address-bar v-if="!presenterView" :current-address="currentAddress" :logistics="logistics" />

      <user-list
        :game-type="gameType"
        :receiver-list="receiverList"
        :lottery-info="lotteryInfo"
        :given-num="givenNum"
        :gift-num="giftNum"
      />

      <bottom-links :kdt-id="kdtId" :is-fix="true" />

      <view v-if="isShowTips" class="gift-share__tips">
        <block v-if="validEndTimeStr">请于 {{ validEndTimeStr }} 前填写地址</block>
        <block v-else-if="gameType === 2">
          <block v-if="!lotteryInfo.isEnd">
            距离开奖剩余 {{ lotteryInfo.countdown.hour }}:{{ lotteryInfo.countdown.minute }}:{{
              lotteryInfo.countdown.second
            }}
          </block>
          <view
            v-else-if="lotteryInfo.isEnd && lotteryNotWin"
            class="gift-share__tips gift-share__tips-disabled"
          >
            抽奖已经结束
          </view>
        </block>
        <block v-else-if="gameType === 1">
          <view
            v-if="isOpenGift && isActivityEnd"
            class="gift-share__tips gift-share__tips-disabled"
          >
            礼物已领完
          </view>
        </block>
      </view>
      <action-btn
        :presenter-view="presenterView"
        :show-fill-address-tips="showFillAddressTips"
        :is-open-gift="isOpenGift"
        :is-activity-end="isActivityEnd"
        :can-get-gift="canGetGift"
        :finally-taken="finallyTaken"
        :is-presenter="isPresenter"
        :use-up="useUp"
        :out-dated="outDated"
        :game-type="gameType"
        :engage="engage"
        :lottery-info="lotteryInfo"
        :gift-order-no="giftOrderNo"
        :valid-end-time-tr="validEndTimeStr"
        @address="handleEditAddressClick"
        @jump="handlePickGiftClick"
        @share="handleShareGift"
        @represent="handleRepresent"
        @createPoster="handleCreatePoster"
        @takeGiftClick="handleTakeGiftClick"
      />
    </view>

    <!-- #ifdef web -->
    <share-mask :show="showShareMask" @close="showShareMask = false" />
    <!-- #endif -->

    <van-popup
      :show="showPosterPopup"
      closeable
      safe-area-inset-bottom
      custom-class="poster-popup"
      @close="showPosterPopup = false"
    >
      <image class="poster-img" v-if="posterUrl" :src="posterUrl" alt="分享海报" />
      <view class="poster-tips">长按图片保存至相册</view>
    </van-popup>
    <user-authorize-popup />
  </view>
</template>

<script>
import { setShareData, cdnImage } from '@youzan/tee-biz-util';
import { requestV2 } from '@youzan/tee-biz-request';
import queryGeo from '@youzan/wsc-tee-trade-common/lib/order-utils/requests/queryGeo';
import Tee from '@youzan/tee';
/* #ifdef weapp */
// eslint-disable-next-line @youzan-open/tee/no-define-in-branch
import navigate from '@/helpers/navigate';
// eslint-disable-next-line @youzan-open/tee/no-define-in-branch
import authorize from '@/helpers/authorize';
/* #endif */
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import VanActionSheet from '@youzan/vant-tee/dist/action-sheet/index.vue';
import VanPopup from '@youzan/vant-tee/dist/popup/index.vue';
import Countdown from '@youzan/weapp-utils/lib/countdown';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import time from '@youzan/weapp-utils/lib/time';
import buildUrl from '@youzan/utils/url/buildUrl';
/* #ifdef web */
import ShareMask from './components/share-mask/index.vue';
/* #endif */
import { getCurrentKdtId } from '@youzan/shop-core-tee';
import { GIFT_NEW_GET, GIFT_LOTTERY, GLOBAL_ADDRESS_SELECT_ACTION } from './constants';

/* #ifdef weapp */
const app = getApp();
/* #endif */
// eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
export default {
  components: {
    'van-icon': Icon,
    'van-action-sheet': VanActionSheet,
    'van-popup': VanPopup,
    /* #ifdef web */
    'share-mask': ShareMask,
    /* #endif */
  },

  data() {
    return {
      giftId: '',
      navArrowTop: 0,
      showActionSheet: false,
      // 是否是拆礼物页面跳转过来的
      isFromOpenGiftPage: false,
      // 是否是拆礼物场景
      isOpenGift: false,
      // 拆礼物场景是否可以领取礼物
      canGetGift: false,
      // 拆礼物场景 活动是否结束
      isActivityEnd: false,
      // 送礼人自己查看
      presenterView: false,
      presenterInfo: {},
      address: null,
      // 礼物中商品信息
      goodsInfo: {},
      // 礼包留言
      message: '',
      // 填写地址的截止时间
      validEndTimeStr: '',
      // 收礼人列表
      receiverList: [],
      // 是否已经领完
      useUp: false,
      // 是否通过转赠或填写地址最终领取
      finallyTaken: false,
      isFetching: true,
      giftNum: 0,
      givenNum: 0,
      giftOrderNo: '',
      currentAddress: {},
      logistics: {},
      // 礼包玩法类型
      gameType: 2,
      // 抽奖
      lotteryInfo: {},
      timer: null,
      showPosterPopup: false,
      engage: false,
      posterUrl: '',
      showShareMask: false,
      lotteryStatus: ['ENGAGE', 'GET', 'FAIL', 'OPENING'],
      userInfo: {},
      goodsList: [],
    };
  },

  computed: {
    lotteryNotWin() {
      const { gameType, lotteryInfo } = this;
      return gameType === 2 && lotteryInfo.userStatus !== 'GET';
    },
    showFillAddressTips() {
      const { engage, outDated, currentAddress, presenterView } = this;
      const engageNotOutDate = engage && !outDated;
      const notFillAddress = !currentAddress.tel;

      // 非送礼者模式 && 参与活动且没有过期 && 抽奖玩法下中奖
      const showFillAddressTips =
        !presenterView && engageNotOutDate && notFillAddress && !this.lotteryNotWin;

      return showFillAddressTips;
    },
    isShowTips() {
      const { gameType, lotteryInfo, validEndTimeStr } = this;
      // 展示填写地址截止时间
      // 抽奖的情况下，未开奖倒计时
      // 开奖了，但是没中奖
      return (
        (this.showFillAddressTips && validEndTimeStr) ||
        (gameType === 2 && (!lotteryInfo.isEnd || (lotteryInfo.isEnd && this.lotteryNotWin))) ||
        (this.isOpenGift && this.isActivityEnd && gameType === 1)
      );
    },
  },

  created() {
    this.ctx.env.getQueryAsync().then((query) => {
      const {
        giftId = '',
        presenterView = 0,
        kdtId = 0,
        isFromOpenGiftPage = 0,
      } = mapKeysCase.toCamelCase(query);
      this.presenterView = !!+presenterView;
      this.isFromOpenGiftPage = !!+isFromOpenGiftPage;
      this.kdtId = kdtId || getCurrentKdtId();
      this.initGiftPackageDetail(giftId, this.presenterView);
      /* #ifdef weapp */
      this.presenterView ? wx.showShareMenu({ withShareTicket: true }) : wx.hideShareMenu();
      // 设置沉浸式导航栏样式
      this.ctx.data.barConfig = {
        navigationbar_type: 'immersion',
        style_color_custom_background_color: '#ffffff',
        style_color_type: 'custom',
        type: 'navigationbar_config',
        title_switch: '1',
        style_color_custom_type: 'purecolor',
        navigationbar_config_type: 'custom',
        style_color_custom_font_color: 'black',
        title_position: 'center',
        title_content_type: 'text',
        back_icon_style: 'background: none;',
      };
      this.ctx.data.pageTitle = this.isFromOpenGiftPage ? '收礼物' : '送礼物';
      const { top } = wx.getMenuButtonBoundingClientRect();
      this.navArrowTop = top + 5;
      /* #endif */
      this.setAddressSelectListener();
      this.setUserInfo();
    });
  },

  destroyed() {
    /* #ifdef weapp */
    app.off(null, null, this);
    /* #endif */
    this.timer && this.timer.stop();
  },

  methods: {
    goBack() {
      if (getCurrentPages().length > 1) {
        Tee.navigateBack();
      } else {
        // 返回首页
        wx.reLaunch({
          url: '/pages/home/<USER>/index',
        });
      }
    },
    fetchWeappPoster() {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/get-gift-poster.json',
          data: {
            goodsImg: this.goodsInfo.goodsImg,
          },
          method: 'POST',
        })
          .then((res) => {
            resolve(res.img);
          })
          .catch((resp) => {
            reject(resp.data || {});
          });
      });
    },
    async setPageShareData() {
      const imageUrl = await this.fetchWeappPoster();
      const { gameType, userInfo, giftId, kdtId } = this;
      let path;
      /* #ifdef weapp */
      path = `/packages/gift/share/index?gift_id=${giftId}&kdt_id=${kdtId}&is_from_open_gift_page=1`;
      /* #else */
      path = buildUrl(
        `wsctrade/gift/share?gift_id=${giftId}&kdt_id=${kdtId}&is_from_open_gift_page=1`,
        'h5'
      );
      /* #endif */

      const shareConfig = {
        title: `${userInfo.nickname || ''}送你一份礼物~快打开看看吧`,
        path,
        /* #ifdef web */
        desc: `${gameType === GIFT_NEW_GET ? '拆开看看' : '快来抢吧'}`,
        /* #endif */
        imageUrl,
      };
      /* #ifdef weapp */
      this.ctx.data.shareConfig = shareConfig;
      /* #else */
      setShareData(shareConfig, this);
      /* #endif */
    },

    setAddress(address) {
      const {
        userName,
        tel,
        address: fullAddress,
        province,
        city,
        county,
        addressDetail,
      } = address;
      const currentAddress = {
        tel,
        name: userName,
        province,
        city,
        district: county,
        street: addressDetail,
      };
      Dialog.confirm({
        context: this,
        title: '确认收件信息',
        message: `${userName}，${tel}，${fullAddress}`,
        showCancelButton: true,
        confirmButtonText: '确认地址',
        cancelButtonText: '返回修改',
      })
        .then(() => {
          const receiverInfo = this.mapAddressToReceiverInfo(address);
          this.checkAddressValidation(receiverInfo).then((validate) => {
            if (validate) {
              this.setAddressState(receiverInfo, currentAddress);
            } else {
              this.handleInvalidAddressSelect();
            }
          });
        })
        .catch(() => {
          Dialog.close();
          this.goAddressSelectPage();
        });
    },

    setAddressSelectListener() {
      /* #ifdef weapp */
      // eslint-disable-next-line @youzan-open/ranta-tee/event-listener-pairs
      app.on(
        GLOBAL_ADDRESS_SELECT_ACTION,
        (address) => {
          this.setAddress(address);
        },
        this
      );
      /* #else */
      const url = new URL(location.href);
      url.searchParams.delete('address_id');
      history.replaceState(null, '', url.href);
      if (window._global.selectedAddress) {
        const address = window._global.selectedAddress;
        const { province, city, county, addressDetail } = address;
        address.address = `${province} ${city} ${county} ${addressDetail}`;
        this.setAddress(address);
      }
      /* #endif */
    },

    // 当选择的地址不合法时
    handleInvalidAddressSelect() {
      Dialog.confirm({
        title: '',
        message: '当前收货地址不支持配送，请修改收货地址',
        showCancelButton: true,
        confirmButtonText: '修改地址',
        cancelButtonText: '取消',
        asyncClose: true,
      })
        .then(() => {
          this.goAddressSelectPage();
        })
        .catch(() => {
          Dialog.close();
        });
    },

    mapAddressToReceiverInfo(address) {
      const { county, addressDetail, tel, userName } = address;
      return {
        receiverAddress: {
          ...address,
          district: county,
          detail: addressDetail,
        },
        receiverPhone: tel,
        receiverName: userName,
      };
    },

    initGiftPackageDetail(giftId, presenterView) {
      Toast.loading({
        message: '加载中',
      });
      const promises = [this.fetchGiftPackageInfo(giftId)];
      if (this.isFromOpenGiftPage) {
        promises.push(this.fetchGiftBoxInfo(giftId));
      }
      Promise.all(promises)
        .then(([resp, giftBox]) => {
          Toast.clear();
          if (giftBox) {
            this.initGiftBox(giftBox);
          }
          Object.assign(this, {
            ...resp,
            // 初始化时将message容器和原始信息赋值
            newMemo: resp.message,
            presenterView,
            giftId,
            isFetching: false,
          });

          if (resp.gameType === GIFT_LOTTERY) {
            this.initLotteryCountdown(resp?.lotteryInfo?.lotteryTime);
          }
          this.setPageShareData();
        })
        .catch((err) => {
          console.error(err);
          Toast(err.msg || '获取礼物详情失败');
        });
    },

    initLotteryCountdown(lotteryTime) {
      const remainTime = lotteryTime - Date.now();

      if (remainTime > 0) {
        this.timer = new Countdown(remainTime, {
          onChange: (_, timeDataStr) => {
            const { day, minute, second } = timeDataStr;
            let { hour } = timeDataStr;
            hour = +day * 24 + Number(hour);
            hour = hour > 10 ? hour : '0' + hour;
            this.lotteryInfo.countdown = { hour, minute, second };
          },
          onEnd: () => {
            this.initGiftPackageDetail(this.giftId, this.presenterView);
          },
        });
      }
    },

    async handleCreatePoster() {
      Toast.loading({
        message: '海报生成中',
      });
      const code = await this.fetchWeappCode();
      const { img } = await this.getDownloadPoster({
        nickname: this.userInfo.nickname || '',
        message: this.message,
        goodsImg: this.goodsInfo.goodsImg,
        /* #ifdef weapp */
        weappCode: code,
        /* #else */
        h5Code: code,
        /* #endif */
        avatar: this.userInfo.avatar || '',
        goodsName: this.goodsInfo.goodsTitle,
        endTime: this.gameType === 2 ? this.parseLotteryTime(this.lotteryInfo.lotteryTime) : '',
      });
      /* #ifdef weapp */
      this.saveShareCard(img);
      /* #else */
      this.posterUrl = img;
      this.showPosterPopup = true;
      /* #endif */
    },

    handleShareGift() {
      this.showShareMask = true;
    },

    async setUserInfo() {
      const userInfo = (await this.ctx.lambdas?.getUserInfo?.()) || null;
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },

    // 送礼失效日期字符串
    _getValidEndStr(timeStamp) {
      return timeStamp === -1 ? '' : time.moment(timeStamp, 'YYYY-MM-DD HH:mm');
    },

    handlePickGiftClick() {
      this.ctx.dmc.redirectTo('GiftGoodsList', {
        alias: this.activityAlias,
        kdt_id: this.kdtId,
      });
    },

    // 填写地址, 使用有赞地址
    handleEditAddressClick() {
      const { validEndTime } = this;
      if (validEndTime !== -1 && Date.now() > validEndTime) {
        Dialog.confirm({
          title: '',
          message: '已超出兑换日期，请联系商家',
        });
      } else {
        this.goAddressSelectPage();
      }
    },

    goAddressSelectPage() {
      /* #ifdef weapp */
      const dbid = app.db.set({ switchable: true });
      navigate.navigate({
        url: `/packages/order-native/address-list/index?dbid=${dbid}`,
      });
      /* #else */
      // 处理重定向URL，去除address_id参数
      const url = new URL(location.href);
      url.searchParams.delete('address_id');
      const redirectUrl = encodeURIComponent(url.href);
      Tee.navigate({
        url: `/wsctrade/order/address/list?switchable=true&redirect_url=${redirectUrl}`,
      });
      /* #endif */
    },

    // 通过地址获取经纬度
    _fetchGeoByAddress(address) {
      return new Promise((resolve) => {
        queryGeo(address)
          .then((res) => {
            resolve(res);
          })
          .catch(() => {
            resolve({});
          });
      });
    },

    // 校验地址是否是运费模板中
    checkAddressValidation(receiverInfo) {
      return new Promise((resolve, reject) => {
        this._fetchGeoByAddress(receiverInfo.receiverAddress).then((res) => {
          const receiverData = { ...receiverInfo };
          // 地址中合并经纬度信息
          receiverData.receiverAddress = Object.assign(receiverData.receiverAddress, res);
          requestV2({
            path: '/wscump/gift/checkdelivery.json',
            data: {
              receiver: JSON.stringify(receiverData),
              giftOrderNo: this.giftOrderNo,
            },
          })
            .then((res) => {
              resolve(res);
            })
            .catch((err) => {
              reject(err.data || {});
            });
        });
      });
    },

    // 填写地址，更新state
    setAddressState(receiverInfo, currentAddress) {
      this.ensureAddress(JSON.stringify(receiverInfo))
        .then(() => {
          Toast('收礼地址设置成功');
          // 确认领取
          Object.assign(this, {
            finallyTaken: true,
            currentAddress,
          });
          // TODO: 这里实际上在小程序是跳到了礼物的列表页面的时候让订单列表页面刷新
          // TODO: 注意处理这个事件触发器
          // this.$emit('ensure:gift:address');
        })
        .catch((err) => {
          Toast(err.msg || '确认地址失败，请重试');
        });
    },

    fetchWeappCode() {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/generate-gift-poster.json',
          data: {
            /* #ifdef weapp */
            page: 'packages/gift/share/index',
            query: {
              gift_id: this.giftId,
              is_from_open_gift_page: 1,
            },
            /* #else */
            // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
            url: `https://h5.youzan.com/wsctrade/gift/share?gift_id=${this.giftId}&kdt_id=${this.kdtId}&is_from_open_gift_page=1`,
            /* #endif */
          },
          method: 'POST',
        })
          .then((res) => {
            resolve(
              /* #ifdef weapp */
              res.img,
              /* #else */
              res
              /* #endif */
            );
          })
          .catch((resp) => {
            reject(resp.data || {});
          });
      });
    },
    getDownloadPoster(data) {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/get-download-poster.json',
          data,
          method: 'POST',
        })
          .then((res) => {
            resolve(res);
          })
          .catch((resp) => {
            reject(resp.data || {});
          });
      });
    },

    saveShareCard(imageUrl) {
      authorize('scope.writePhotosAlbum')
        .then(() => {
          wx.downloadFile({
            url: imageUrl,
            success: (res) => {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  Toast.clear();
                  Toast('已成功保存至相册');
                },
                fail: () => {
                  Toast.clear();
                  Toast('保存至相册失败，请重试');
                },
              });
            },
            fail: () => {
              Toast.clear();
              Toast('保存至相册失败，请重试');
            },
          });
        })
        .catch(() => {
          Toast.clear();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.saveShareCard();
                  }
                },
              });
            },
          });
        });
    },

    fetchGiftPackageInfo(giftId) {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/giftdetail.json',
          data: { giftId },
        })
          .then((resp) => {
            resp = mapKeysCase.toCamelCase(resp);
            resp.validEndTimeStr = this._getValidEndStr(resp.validEndTime);
            resp.goodsInfo.originImg = resp.goodsInfo.goodsImg;
            resp.goodsList = (resp.goodsList || []).map((item = {}) => ({
              ...item,
              goodsImg: cdnImage(item.goodsImg, '!300x300.jpg'),
            }));
            if (resp.goodsList?.length) {
              resp.goodsInfo.goodsImg = resp.goodsList[0].goodsImg;
            }
            if (resp.gameType === 2) {
              resp.lotteryInfo.userStatus = this.lotteryStatus[resp.lotteryInfo.userStatus];
              resp.lotteryInfo.countdown = {};
            }
            resolve(resp);
          })
          .catch((err) => {
            reject(err.data || {});
          });
      });
    },

    handleRepresent() {
      const { validEndTime } = this;
      if (validEndTime !== -1 && Date.now() > validEndTime) {
        Dialog.alert({
          title: '',
          message: '已超出兑换日期，不可转赠',
        });
      } else {
        this.rePresentGift(this.giftOrderNo)
          .then((resp) => {
            if (resp.alias) {
              this.ctx.dmc.redirectTo('GiftShare', {
                giftId: resp.alias,
                presenter_view: 1,
                kdt_id: this.kdtId,
              });
              this.finallyTaken = true;
            } else {
              Toast('转赠失败，请重试');
            }
          })
          .catch((err) => {
            Toast(err.msg || '转赠失败，请重试');
          });
      }
    },

    // 转赠好友
    rePresentGift(giftOrderNo) {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/represent.json',
          data: {
            memo: '',
            giftOrderNo,
          },
        })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err.data || {});
          });
      });
    },

    ensureAddress(receiverInfo) {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/chooseaddress.json',
          data: {
            receiver: receiverInfo,
            giftOrderNo: this.giftOrderNo,
          },
        })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    parseLotteryTime(timestamp) {
      const leftPad = (i) => {
        if (i < 10) {
          return '0' + i;
        }
        return i;
      };

      const instance = new Date(timestamp);
      const month = leftPad(instance.getMonth() + 1);
      const day = leftPad(instance.getDate());
      const hour = leftPad(instance.getHours());
      const minute = leftPad(instance.getMinutes());

      return `${month}月${day}日 ${hour}:${minute}开奖`;
    },

    initGiftBox(res) {
      const { effectiveTime, gameType, gameEndTime, alreadyTaken, useUp } = res;
      const outDated = Date.now() > effectiveTime && effectiveTime > -1;
      const gameEnd = gameType === 2 && Date.now() >= gameEndTime;
      // 已经参与过、活动过期
      if (alreadyTaken || outDated) {
        return;
        // 礼物领取完、活动结束
      }
      if (useUp || gameEnd) {
        this.isActivityEnd = true;
      } else {
        this.canGetGift = true;
      }
      this.isOpenGift = true;
    },

    // 点击领取或抽奖，因为领完失败的，直接重定向
    handleTakeGiftClick() {
      const { giftId } = this;
      const engagePromise =
        this.gameType === 2 ? this.engageLottery(giftId) : this.takeGift(giftId);

      engagePromise.then(this.commonGiftSuccessHandler).catch(this.commonGiftFailHandler);
    },

    commonGiftSuccessHandler(res) {
      // 如果存在礼单号, 代表领取或者参加抽奖成功
      if (res.orderNo) {
        Toast.success(this.gameType === 2 ? '已参与抽奖' : '已收下礼物');
        // 等用户看到提示后再跳转
        setTimeout(() => {
          this.ctx.dmc.redirectTo('GiftShare', {
            gift_id: this.giftId,
            kdt_id: this.kdtId,
          });
        }, 2000);
      }
    },

    commonGiftFailHandler(err) {
      if (err.code === 101504001 || err.code === 101504010 || err.code === 101504024) {
        this.onTakeGiftError(err.code);
      } else {
        Toast(err.msg || '领取礼物失败，请重试');
      }
    },

    onTakeGiftError(errCode) {
      const msgMap = {
        101504001: '来晚啦，礼物已被抢光',
        101504010: '来晚啦，礼物已经过期了',
        101504024: '来晚啦，礼盒已经开奖了',
      };

      Toast(msgMap[errCode]);
      this.ctx.dmc.redirectTo('GiftShare', {
        gift_id: this.giftId,
        kdt_id: this.kdtId,
      });
    },

    // 获取礼盒信息
    fetchGiftBoxInfo(giftId) {
      return this.fetch('wscump/gift/giftbox.json', { giftId });
    },

    // 点击领取
    takeGift(giftId) {
      return this.fetch('wscump/gift/takegift.json', { giftId });
    },

    // 点击抽奖
    engageLottery(giftId) {
      return this.fetch('wscump/gift/engagelottery.json', { giftId });
    },

    fetch(path, data) {
      return new Promise((resolve, reject) => {
        requestV2({
          path,
          data,
        })
          .then((res) => {
            res = mapKeysCase.toCamelCase(res);
            resolve(res);
          })
          .catch((err) => {
            reject(err.data || {});
          });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
$COMMON_COLOR: #e0493f;

.arrow {
  position: absolute;
  left: 18px;
}
.gift-share {
  background: url('https://b.yzcdn.cn/gift/gift-bg-2.png');
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 96px;

  &__body {
    display: flex;
    flex-direction: column;
    justify-content: center;

    &-title {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      color: #5f3f36;
      font-weight: 500;
    }

    &-gift-icon {
      width: 21px;
      height: 19px;
      margin-right: 6px;
      margin-top: 4px;
    }

    &-message {
      text-align: center;
      font-size: 16px;
      color: #b19286;
      margin-top: 8px;
    }

    &-box-container {
      display: flex;
      justify-content: center;
    }

    &-box {
      position: relative;
    }

    &-box-main {
      position: relative;
      width: 240px;
      height: 250px;
    }

    &-box-bg-extra {
      width: 240px;
      height: 250px;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 4;
    }

    &-box-goods {
      width: 130px;
      height: 130px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-55%, -55%) rotate(-11deg);
      padding: 5px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 8px;
      z-index: 2;

      &-img {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 8px !important;
      }
    }
  }

  &__header-margin {
    padding-top: 66px;
    background: $COMMON_COLOR;
  }

  &__tips {
    position: fixed;
    bottom: calc(66px + env(safe-area-inset-bottom));
    text-align: center;
    width: 100%;
    padding: 12px 0;
    background: #fff3e4;
    font-size: 14px;
    color: #5f3f36;
    font-weight: 500;

    &-disabled {
      background: #e3e3e3;
      color: #666;
    }
  }

  &__use-up {
    position: absolute;
    height: 75px;
    width: 75px;
    background-image: url('https://img01.yzcdn.cn/public_files/2018/07/08/09013da2815cf063eee470eb2fd14d96.png');
    background-size: 75px 75px;
    right: 19.5px;
    top: 308px;
  }

  &__receive-btn-group,
  &__receive-btn-group--padding {
    padding: 0 15px;
    box-sizing: border-box;
    margin-top: 15px;
  }

  &__receive-btn-group--padding {
    padding: 0 30px;
  }

  &__btn {
    margin-top: 52px;
    width: 100%;
    background: $COMMON_COLOR;
    color: #fff;
    height: 50px;
    line-height: 50px;
    border-radius: 2px;

    &-lottery {
      margin-top: 10px;
    }
  }
}

.lottery-countdown {
  color: #333;
  margin-top: 45px;
}

.countdown-time-unit {
  display: inline-block;
  background: #000;
  min-width: 18px;
  border-radius: 2px;
  color: #fff;
  font-size: 12px;
  margin: 0 1px;
}

.user-list--margin {
  margin-top: 17px !important;
}
</style>
/* #ifdef web */
<style lang="scss">
.poster-popup {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: none !important;
  width: 100%;
  height: 100%;
}

.poster-img {
  width: 70%;
}
.poster-tips {
  font-size: 14px;
  color: white;
  padding-top: 20px;
  text-align: center;
}
</style>
/* #endif */
