export default {
  // 商品类型 tag
  tag: {
    presale: { text: '预售', type: 'danger' },
    selfFetch: { text: '自提', type: 'danger' },
    periodBuy: { text: '周期购', type: 'danger' },
    fCode: { text: 'F码专享', type: 'danger' },
    bargain: { text: '砍价', type: 'danger' },
    inSourcing: { text: '内购价', type: 'danger' },
    seckill: { text: '秒杀', type: 'danger' },
    present: { text: '赠品', type: 'danger' },
    timelimitedDiscount: { text: '限时折扣', type: 'danger' },
    auction: { text: '降价拍', type: 'danger' },
    customerDiscount: { text: '会员折扣', type: 'danger' },
    plusBuy: { text: '加价购', type: 'danger' },
    crossBorder: { text: '海淘', type: 'danger' },
    exchangeCoupon: { text: '兑换券', type: 'danger' },
  },
};
