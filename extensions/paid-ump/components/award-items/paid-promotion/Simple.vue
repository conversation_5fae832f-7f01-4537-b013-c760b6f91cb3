<template>
  <award-container v-if="curInfo">
    <view class="container">
      <image :src="curInfo.img" class="icon" />
      <view class="content">
        <view :class="['card-text', curInfo.desc ? 't-ellipsis' : 't-multi-ellipsis--l2']">
          {{ curInfo.text || '' }}
        </view>
        <view class="card-desc" v-if="curInfo.desc">{{ curInfo.desc || '' }}</view>
      </view>
      <van-button custom-class="button" :color="themeColor" round size="small" @click="clickBtn">{{
        curInfo.btnText
      }}</van-button>
    </view>
  </award-container>
</template>

<script>
import { PROMOTION_TYPES } from '../../../constants';
import AwardContainer from '../Container';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import { cdnImage } from '@youzan/tee-biz-util';

const infoMapper = {
  [PROMOTION_TYPES.Survey]: {
    img: cdnImage('public_files/80fa7fcede0ea92f7363f0b1735c25c6.png'),
    text: '填写问卷，让我们可以提供更好的服务',
    btnText: '立即查看',
  },
  [PROMOTION_TYPES.Feature]: {
    img: cdnImage('public_files/0708826399af0b984f500735b4c01a0e.png'),
    text: '查看更多店铺优惠',
    desc: '赶紧去看看吧~~~',
    btnText: '立即查看',
  },
  [PROMOTION_TYPES.Seller]: {
    img: cdnImage('public_files/585699ec541cc9484bd329961686437b.png'),
    text: '恭喜你可成为销售员，分享赚佣金',
    btnText: '立即申请',
  },
};

export default {
  name: 'award-simple-show',
  components: {
    'award-container': AwardContainer,
    'van-button': VanButton,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
  },
  computed: {
    curInfo() {
      const type =
        this.info.promotionType === PROMOTION_TYPES.Seniorseller
          ? PROMOTION_TYPES.Seller
          : this.info.promotionType;
      return infoMapper[type];
    },
  },
  methods: {
    clickBtn() {
      this.$emit('click-btn');
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  padding: 12px;
  align-items: center;
  justify-content: space-between;
}

.icon {
  height: 40px;
  width: 40px;
}
.content {
  margin-left: 10px;
  text-align: left;
  flex: 1;

  .card-text {
    font-size: 14px;
    color: #323233;
    line-height: 20px;
  }
  .card-desc {
    margin-top: 4px;
    font-size: 12px;
    color: #999;
    line-height: 16px;
    padding-top: 4px;
  }
}

.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.t-multi-ellipsis--l2 {
  -webkit-line-clamp: 2;
}
.t-multi-ellipsis--l2,
.t-multi-ellipsis--l3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.button {
  white-space: nowrap;
  color: #fff;
  font-size: 14px !important;
  height: 24px !important;
  width: 82px !important;
  padding: 0 12px;
  margin-left: 12px;
}
</style>
