import hexToRgba from '@youzan/utils/string/hexToRgba';

// 匹配双字节 一个中文顶2个英文字符
// eslint-disable-next-line no-control-regex
const getRealLen = (str) => str.replace(/[^\x00-\xff]/g, '__').length;

export default {
  showCouponBar() {
    return this.hasValidGoods && this.couponList.length > 0;
  },
  topCouponList() {
    const MAX_COUPON_COUNT = 3;

    let displayCouponList = this.couponList.slice(0, MAX_COUPON_COUNT);
    // 3个的长度如果超过32的话，就隐藏第3个优惠券; 一个中文顶2个英文
    if (this.couponList.length >= MAX_COUPON_COUNT) {
      const cnt = displayCouponList.reduce((pre, current) => {
        return (
          pre + `${current.isVip ? '会员占位' : ''}` + current.use_threshold_and_value_copywriting
        );
      }, '');

      const MAX_DISPLAY_LENGTH = 32;
      if (getRealLen(cnt) > MAX_DISPLAY_LENGTH) {
        displayCouponList = displayCouponList.slice(0, MAX_COUPON_COUNT - 1);
      }
    }

    return displayCouponList;
  },

  themeGeneralAlpha10Color() {
    const { general } = this.themeColors;
    return hexToRgba(general, 0.1);
  },

  hasCheckedGoods() {
    return this.checkedGoodsList.length;
  },
};
