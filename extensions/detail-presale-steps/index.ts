import PresaleSteps from './PresaleSteps.vue';
import { cloud } from '@youzan/ranta-helper';
import type { OrderDetailPresale } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class PresaleStepsExtension {
  ctx: any;

  /**
   * presaleSteps
   * @desc 预售阶段信息
   * @type {PresaleSteps}
   */
  @cloud('presale', 'data')
  presale: OrderDetailPresale;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['presaleSteps'], {
      callback: () => {
        const { presaleSteps } = this.ctx.data;
        const newOpenData = {
          presale: cloudData.getPresale({ presaleSteps }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    PresaleSteps,
  };
}
