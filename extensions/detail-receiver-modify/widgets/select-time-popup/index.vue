<template>
  <view class="select-time-popup">
    <time-picker
      :show="show && loadedTime"
      type="time"
      :config="timeBucketConfig"
      :titles="['选择送达时间', '选择送达时间']"
      :value="timeConfig.text"
      @confirm="onConfirm"
      @close="onClose"
    />
  </view>
</template>

<script>
import { mapActions, mapState } from '@ranta/store';
import Toast from '@youzan/vant-tee/dist/toast/toast';

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loadedTime: false,
      ...mapState(this, ['timeBucketConfig', 'timeConfig']),
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.initTimeData();
      }
    },
  },

  created() {
    mapActions(this, ['getDeliveryTimeBucket', 'selectTimeConfig']);
  },
  methods: {
    initTimeData() {
      this.ctx.data.timePickerAddress = {
        activeTab: 0,
      };
      this.ctx.data.timePickerExtra = {};
      if (this.timeBucketConfig.timeBucket) {
        this.loadedTime = true;
      } else {
        this.getDeliveryTimeBucket().then((res) => {
          if (!res) {
            Toast('暂无配送时间');
            return;
          }
          this.loadedTime = true;
        });
      }
    },
    onConfirm(value) {
      this.selectTimeConfig(value);
    },
    onClose() {
      this.ctx.data.showTimeModifyPopup = false;
    },
  },
};
</script>

<style lang="scss">
.select-time-popup {
  position: fixed;
  z-index: 10000;
}
</style>
