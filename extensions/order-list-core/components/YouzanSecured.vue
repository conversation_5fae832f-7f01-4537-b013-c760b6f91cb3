<template>
  <van-cell
    v-if="yzGuarantee.isShow"
    :value="yzGuarantee.statusDesc"
    value-class="yz-guarantee__value"
    :border="false"
    custom-style="padding: 4px 12px"
  >
    <view slot="title" class="yz-guarantee__title">
      <image
        class="yz-guarantee__icon"
        lazy-load
        :src="yzGuarantee.icon"
        :alt="yzGuarantee.iconAlt"
      />
      <view class="yz-guarantee__text">{{ yzGuarantee.textDesc }}</view>
    </view>
  </van-cell>
</template>
<script>
import Cell from '@youzan/vant-tee/dist/cell/index';

export default {
  name: 'youzan-secured',

  components: {
    'van-cell': Cell,
  },

  props: {
    yzGuarantee: {
      type: Object,
      default: () => ({ isShow: false }),
    },
  },
};
</script>
<style lang="scss" scoped>
.yz-guarantee {
  &__title {
    display: flex;
    align-items: center;
    height: 100%;
    flex: 1 0;
  }

  &__icon {
    width: 50px;
    height: 14px;
    margin-right: 8px;
    display: inline-block;
  }

  &__text {
    display: inline;
    font-size: 14px;
    color: #969799;
    line-height: 20px;
    flex: 1;
  }

  &__value {
    flex: 0 0 auto !important;
    font-size: 12px;
  }
}
</style>
