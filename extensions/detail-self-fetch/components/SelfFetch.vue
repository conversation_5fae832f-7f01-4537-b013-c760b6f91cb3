<template>
  <view class="self-fetch">
    <view
      v-if="
        eShowSelfFetch ||
        !hasPrescriptionDrugGood ||
        (hasPrescriptionDrugGood && rxStatus.rxStatus >= 30 && !rxStatus.closeType)
      "
    >
      <view class="self-fetch__cell">
        <view class="self-fetch__title">提货凭证 {{ selfFetchNameStr }}</view>
        <view class="self-fetch__addr-cell">
          <view class="self-fetch__addr-cell__addr">
            {{ selfFetchInfo.address }}
          </view>

          <!-- #ifdef weapp -->
          <view
            v-if="selfFetchInfo.lng && selfFetchInfo.lat"
            class="self-fetch__addr-cell__icon t-hairline--left"
            @click="onLocationWeappClick"
          />
          <!-- #endif -->

          <!-- #ifdef web -->
          <view
            v-if="isWeixin && selfFetchInfo.lng && selfFetchInfo.lat"
            class="self-fetch__addr-cell__icon t-hairline--left"
            @click="onLocationWebClick"
          />
          <!-- #endif -->
        </view>

        <view
          :class="[
            selfFetchInfo.qrcode ? '' : 'self-fetch__content--without-qrcode',
            'self-fetch__content',
          ]"
          @click="onContactContentClick"
        >
          <view
            v-if="selfFetchInfo.qrcode"
            class="self-fetch__content__qrcode"
            :style="{ backgroundImage: 'url(' + selfFetchInfo.qrcode + ')' }"
          />
          <!-- <van-loading class="self-fetch__content__loading" v-else /> -->
          <view class="self-fetch__content__info">
            <view v-if="selfFetchInfo.qrcode" class="self-fetch__content__info__code">
              提货码：<view class="text-inline">{{
                selfFetchInfo.fetchNo || '正在生成中...'
              }}</view>
            </view>
            <view v-if="selfFetchInfo.fetcher" class="self-fetch__content__info__fetcher">
              提货人：<view class="text-inline">{{ selfFetchInfo.fetcher }}</view>
            </view>
            <view v-if="selfFetchInfo.time" class="self-fetch__content__info__time">
              预约时间：<view class="text-inline">{{ selfFetchInfo.time }}</view>
            </view>
          </view>
        </view>
        <!-- 增加提示 -->
        <view class="self-fetch__tips">
          <text>为保障您的权益，未到店提货前请不要将二维码/数字券号提供给商家</text>
        </view>
        <view v-if="selfFetchInfo.tel" class="self-fetch__action">
          <view class="self-fetch__action__call" @click="onContactBtnClick">
            <view class="self-fetch__action__call__icon" />
            <text>联系提货点</text>
          </view>
        </view>
      </view>
    </view>

    <view class="self-fetch-block__cell drug-self-fetch" v-else>
      <view class="self-fetch-block__content__info__item">
        <text class="self-fetch-block__content__info__item__label">提货人：</text>
        <text class="self-fetch-block__content__info__item__value">{{
          selfFetchInfo.fetcher
        }}</text>
      </view>
      <view class="self-fetch-block__content__info__item">
        <text class="self-fetch-block__content__info__item__label">提货地址：</text>
        <text class="self-fetch-block__content__info__item__value">{{
          selfFetchInfo.address
        }}</text>
      </view>
      <view class="self-fetch-block__content__info__item">
        <text class="self-fetch-block__content__info__item__label">提货时间：</text>
        <text class="self-fetch-block__content__info__item__value">{{ selfFetchInfo.time }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Loading from '@youzan/vant-tee/dist/loading/index.vue';
import { makePhoneCall } from '@youzan/tee-api';
import navigate from '@youzan/tee-biz-navigate';

/* #ifdef web */
// eslint-disable-next-line @youzan-open/tee/no-define-in-branch
import ZNB from '@youzan/znb';
/* #endif */

export default {
  components: {
    'van-loading': Loading,
  },

  props: {
    selfFetchInfo: Object,
    isPeriodBuy: Boolean,
    orderNo: String,
    kdtId: Number,
    isWeixin: Boolean,
    itemInfo: {
      type: Array,
      default: () => [],
    },
    rxStatus: Object,
    eShowSelfFetch: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {
    hasPrescriptionDrugGood() {
      return this.itemInfo.some((item) => item.extra.IS_PRESCRIPTION_DRUG_GOODS);
    },
    selfFetchNameStr() {
      return this.selfFetchInfo.name ? `(${this.selfFetchInfo.name})` : '';
    },
  },

  created() {},

  methods: {
    onLocationWeappClick() {
      const { lng, lat, shopName, address } = this.selfFetchInfo;

      if (lng && lat) {
        Tee.$native.openLocation({
          address,
          latitude: lat,
          longitude: lng,
          name: shopName,
        });
      }
    },

    onLocationWebClick() {
      const { lng, lat, shopName, addressDetail } = this.selfFetchInfo;
      const longitude = Number(lng) - 0.0065;
      const latitude = Number(lat) - 0.006;
      if (this.isWeixin) {
        ZNB.openLocation({
          latitude, // 纬度，浮点数，范围为90 ~ -90
          longitude, // 经度，浮点数，范围为180 ~ -180。
          name: shopName, // 位置名
          address: addressDetail, // 地址详情说明
          scale: 14, // 地图缩放级别,整形值,范围从1~28。默认为最大
        });
      }
    },

    onContactContentClick() {
      const { isPeriodBuy, orderNo, kdtId } = this;
      let selfFetchUrl = '';
      // 周期购自提
      if (isPeriodBuy) {
        selfFetchUrl = `/v2/trade/order/periodselffetchcode?order_no=${orderNo}`;
      } else {
        selfFetchUrl = `/wsctrade/order/selffetch/detail?orderNo=${orderNo}&kdtId=${kdtId}`;
      }
      // openWebView
      navigate({ url: selfFetchUrl, query: { title: '自提订单提货凭证' } });
    },

    onContactBtnClick() {
      makePhoneCall({ phoneNumber: this.selfFetchInfo.tel });
    },
  },
};
</script>

<style lang="scss">
.text-inline {
  display: inline;
}

.self-fetch {
  margin-top: 10px;
  background: #fff;
  &__tips {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 12px;
    color: #636566;
    margin-top: 12px;
  }
}

.self-fetch__cell {
  padding: 15px;
}

.self-fetch__title {
  color: #323233;
  font-size: 14px;
  font-weight: 500;
}

.self-fetch__addr-cell {
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.self-fetch__addr-cell__addr {
  font-size: 12px;
  line-height: 16px;
  color: #999;
}

.self-fetch__addr-cell__icon {
  height: 24px;
  width: 28px;
  background: url('https://img.yzcdn.cn/public_files/2018/12/17/b3caca272aa807ce5b3056ea043df7be.png')
    no-repeat right center;
  background-size: 14px 18px;
}

.self-fetch__content {
  display: flex;
  align-items: stretch;
  margin-top: 15px;
  padding: 15px;
  min-height: 40px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.self-fetch__content--without-qrcode {
  padding: 0;
  box-shadow: none;
  border-radius: 0;
}

.self-fetch__content__qrcode {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 100%;
  background-position: center;
}

.self-fetch__content__loading {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 120%;
  background-position: center;
  text-align: center;
  line-height: 80px;
}

.self-fetch__content__info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: #646566;
}

.self-fetch__content__info .text-inline {
  color: #323233;
}

.self-fetch__content__info__code .text-inline {
  font-size: 14px;
  font-weight: bold;
  color: #323233;
}

.self-fetch__action {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.self-fetch__action__call {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 8px;
  min-width: 60px;
  font-size: 14px;
  line-height: 28px;
  color: #323233;
  background-color: #fff;
  border: 1px solid #eee;
  box-sizing: border-box;
  border-radius: 15px;
}

.self-fetch__action__call__icon {
  margin-right: 6px;
  display: inline-block;
  height: 12px;
  width: 12px;
  background: url('https://img.yzcdn.cn/public_files/2018/12/17/c7e95ec374fd995d4dd7694d7345cebb.png')
    no-repeat center center;
  background-size: 12px;
}
.t-hairline--left {
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    transform: scale(0.5);
    border-left-width: 1px;
  }
}
.drug-self-fetch {
  padding: 10px 12px;
  background-color: #fff;
  // height: 138px;
  /* display: flex;
    flex-direction: column;
    justify-content: space-between */

  .self-fetch-block__content__info__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    &__label {
      color: #333;
      font-size: 14px;
    }
    &__value {
      color: #666;
      font-size: 14px;
      text-align: right;
    }
  }
  .self-fetch-block__content__info__item:last-child {
    margin: 0;
  }
}
</style>
