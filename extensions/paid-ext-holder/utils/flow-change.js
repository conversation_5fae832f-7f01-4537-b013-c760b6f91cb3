import { requestV2 } from '@youzan/tee-biz-request';
import format from '@youzan/utils/money/format';

const goodsCouponMessage = '商品兑换券';

export const adType = {
  coupon: 'coupon',
  seckill: 'seckill',
  lottery: 'lottery',
  choose: 'choose',
};

export const couponType = {
  reward: 'reward',
  discount: 'discount',
  random: 'random',
  goods: 'goods',
};

export const lotteryResultType = {
  default: 'lottery_result',
  coupon: 'lottery_result_coupon',
  seckill: 'lottery_result_seckill',
};

const getDefaultData = (data) => (data && data.length > 0 ? data[0] : {});

export function getIsShowUnit(type) {
  // 商品兑换券、折扣券不显示单位
  return type !== couponType.discount && type !== couponType.goods;
}

export function getIsShowMax(type) {
  // 随机金额券显示最高
  return type === couponType.random;
}

export function getIsShowDiscount(type) {
  // 随机金额券显示最高
  return type === couponType.discount;
}

// 随机金额券根据金额长度获取字体大小
export function getCouponValueFontSize(len) {
  switch (len) {
    case 1:
    case 2:
      return 30;
    case 3:
      return 24;
    case 4:
      return 19;
    default:
      return 12;
  }
}

export function getCouponUseInfo(info = '') {
  return info.split('，')[0] + '使用';
}

export function formatPrice(money = 0, cent = true, showComma = false) {
  // 如果是字符串就不需要格式化 直接返回
  if (money && typeof money === 'string') {
    return money;
  }
  return format(money, cent, showComma);
}

export function getPriceWithCent(price = '0.00', index) {
  if (typeof price === 'number') {
    price = formatPrice(price);
  }
  if (!price.includes('.')) {
    price = `${price}.00`;
  }
  return price.split('.')[index];
}

export function getSeckillGoodsImg(data = '') {
  const picture = JSON.parse(data);
  return picture && picture.length > 0 ? picture[0]?.url : '';
}

export function getSecKillAdContentList(data) {
  return data.map((item) => ({
    ...item,
    bigPrice: getPriceWithCent(item.secKillPrice, 0),
    centPrice: getPriceWithCent(item.secKillPrice, 1),
    picture: getSeckillGoodsImg(item.picture),
  }));
}

export function fetchPayResultShopADApi() {
  return requestV2({
    path: '/wsctrade/order/payresult/shopad.json',
  }).catch(() => {
    // do nothing
  });
}

// 优惠券弹窗有特殊逻辑，重新定一套获取逻辑
export function getCouponValueFromDialogFontSize(len) {
  switch (len) {
    case 1:
    case 2:
      return 25;
    case 3:
      return 20;
    case 4:
      return 15;
    default:
      return 12;
  }
}

export function getCouponValue(value) {
  value = formatPrice(value);
  const [bigMoney, cent = '0'] = value.split('.');
  const centInt = Number(cent);
  if (centInt > 0) {
    // 如果是10的倍数，那就省略分
    if (centInt % 10 === 0) {
      return `${bigMoney}.${parseInt(cent / 10, 10)}`;
    }
    return `${bigMoney}.${cent}`;
  }
  return bigMoney;
}

export function getCouponQuota({ value = 0, maxValue = 0, couponType: type }) {
  if (type === couponType.discount) {
    return getCouponValue(value * 10);
  }
  if (type === couponType.goods) {
    return goodsCouponMessage;
  }
  if (type === couponType.random) {
    return getCouponValue(maxValue);
  }
  return getCouponValue(value);
}

export function getCouponFontSizeStyle(data) {
  const { couponType: type } = data;
  const priceValue = getCouponQuota(data);
  let { length } = priceValue;
  if (priceValue.includes('.')) {
    length -= 1;
  }
  let couponValueFontSize = getCouponValueFontSize(length);
  if (type === couponType.goods) {
    couponValueFontSize = 14;
  }
  return `font-size: ${couponValueFontSize}px`;
}

export function getCouponAdContentList(data) {
  return data.map((item) => {
    if (item.availableGoods) {
      item.availableGoods = item.availableGoods.map((goods) => ({
        ...goods,
        bigPrice: getPriceWithCent(goods.price, 0),
        centPrice: getPriceWithCent(goods.price, 1),
        price: formatPrice(goods.price),
      }));
    }

    return {
      ...item,
      isShowMax: getIsShowMax(item.couponType),
      isShowUnit: getIsShowUnit(item.couponType),
      isShowDiscount: getIsShowDiscount(item.couponType),
      couponQuota: getCouponQuota(item),
      couponValueFontSize: getCouponFontSizeStyle(item),
      preferentialCopyWriting: getCouponUseInfo(item.preferentialCopyWriting),
    };
  });
}

// 根据后端返回值
export function getShopAdData(data) {
  // adType 广告类型 1-直接展示 20-抽奖 30-多选
  const { adType: type, resourceResponse } = data || {};
  let { couponAdContentList = [], secKillAdContentList = [] } = data || {};

  // 处理一下优惠券的数据
  couponAdContentList = getCouponAdContentList(couponAdContentList);
  // 处理一下秒杀的数据
  secKillAdContentList = getSecKillAdContentList(secKillAdContentList);

  // 优惠券
  if (type === 1 && couponAdContentList.length > 0) {
    return {
      type: adType.coupon,
      data: getDefaultData(couponAdContentList),
    };
  }
  // 秒杀
  if (type === 1 && secKillAdContentList.length > 0) {
    return {
      type: adType.seckill,
      data: getDefaultData(secKillAdContentList),
    };
  }
  // 抽奖
  if (type === 20 && resourceResponse) {
    return {
      type: adType.lottery,
      data: {
        couponAdContentList,
        secKillAdContentList,
        resourceResponse,
      },
    };
  }
  // 多选
  if (type === 30 && (couponAdContentList.length > 0 || secKillAdContentList.length > 0)) {
    const couponList = couponAdContentList.map((item) => ({ ...item, adType: adType.coupon }));
    const seckillList = secKillAdContentList.map((item) => ({ ...item, adType: adType.seckill }));
    return {
      type: adType.choose,
      data: couponList.concat(seckillList),
    };
  }
  return '';
}
