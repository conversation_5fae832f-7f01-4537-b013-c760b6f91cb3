<template>
  <view v-if="showAwardBlock" class="new-award">
    <text class="title">{{ awardTitle }}</text>
    <view class="award-block" :style="awardStyle">
      <!-- 优惠券 -->
      <award-coupon
        v-if="curCoupon"
        @log-attach="logAttach"
        :order-no="orderNo"
        :info="curCoupon"
        :theme-color="themeGeneralColor"
      />
      <!-- 储值金 -->
      <award-store-value
        v-if="award.cashBack"
        @log-attach="logAttach"
        :info="award.cashBack"
        :theme-color="themeGeneralColor"
      />
      <!-- 赠品(支付有礼的位置，N选一) -->
      <award-paid-promotion
        :promotion-info="award.paidPromotionInfo"
        :order-no="orderNo"
        @log-attach="logAttach"
        :theme-color="themeGeneralColor"
      />
      <!-- 引导办会员 -->
      <member-guide v-if="showMemberGuide" :pay-result="payResult" />
      <!-- 会员权益卡 -->
      <award-member v-if="showMember" :info="curMember" @log-attach="logAttach" />
      <!-- 先用后付 -->
      <award-prior-use v-if="curPriorUse" :info="award.prior" />
      <!-- 积分 -->
      <award-point
        v-if="award.credit"
        @log-attach="logAttach"
        :info="award.credit"
        :theme-color="themeGeneralColor"
      />
    </view>
    <mask-block v-if="showMask" @jump="expandAll" :custom-style="maskStyle" />
  </view>
</template>

<script>
import omit from '@youzan/utils/object/omit';
import AwardCoupon from './award-items/coupon-block/index';
import AwardPaidPromotion from './award-items/paid-promotion';
import PriorUse from './award-items/PriorUse';
import StoreValue from './award-items/StoreValue';
import Point from './award-items/Point';
import Member from './award-items/Member';
import MemberGuide from './award-items/MemberGuide';
import { isDefaultPromotion, PROMOTION_TYPES, ActivityQrCodeTypes } from '../constants';
import { formatAwardInfo } from '../helper';
import deepClone from '@youzan/weapp-utils/lib/clone-deep';
import MaskBlock from './widgets/MaskBlock';
import { checkAfterPayGuide } from '../utils';

const app = getApp();

export default {
  components: {
    'award-coupon': AwardCoupon,
    'award-paid-promotion': AwardPaidPromotion,
    'award-store-value': StoreValue,
    'award-point': Point,
    'member-guide': MemberGuide,
    'award-member': Member,
    'mask-block': MaskBlock,
    'award-prior-use': PriorUse,
  },
  props: {
    themeGeneralColor: String,
    newAwardInfo: Object,
    payResult: Object,
    orderNo: String,
    hideBenefitcard: Boolean,
  },
  data() {
    return {
      showMask: false,
      blockHeight: 0,
      isExpand: false,
    };
  },

  computed: {
    maskStyle() {
      return `left: 0;right:0;top: ${this.blockHeight - 36}px`;
    },
    awardStyle() {
      return `width:100%;height: ${this.blockHeight === 0 ? 'initial' : this.blockHeight + 'px'}`;
    },
    award() {
      const awardInfo = deepClone(this.newAwardInfo);

      return formatAwardInfo(awardInfo, this.orderNo);
    },
    showMemberGuide() {
      // 是否有支付后引导会员数据
      return checkAfterPayGuide(this.payResult);
    },
    showAwardBlock() {
      return (
        this.isNotNull(omit(this.award, ['recommendCoupon', 'fissionCoupon'])) ||
        this.showMemberGuide
      );
    },
    curCoupon() {
      // 在支付成功页的优惠券展示目前仅限支付有礼，在商家小票页会把自动推券的也归在这个样式里。
      // 而且后续可能会支持到进店有礼之类的，所以这边会做一个处理，单独与支付有礼拆开来，方便后续扩展
      const { imageType, promotionType, coupon, couponGiftBag, detailUrl } =
        this.award.paidPromotionInfo ?? {};
      if (!isDefaultPromotion(imageType)) return null;

      switch (promotionType) {
        case PROMOTION_TYPES.Couponpackage: {
          return {
            type: PROMOTION_TYPES.Couponpackage,
            coupons: couponGiftBag.coupons,
            total: couponGiftBag.couponCount,
            alias: couponGiftBag.alias,
            detailUrl,
          };
        }
        case PROMOTION_TYPES.Promocode:
        case PROMOTION_TYPES.Tradeincard: {
          return {
            type: PROMOTION_TYPES.Tradeincard,
            goods: coupon?.goodsList ?? [],
            coupon,
          };
        }
      }
      return null;
    },
    curMember() {
      return this.award.formatedMemberCard ?? null;
    },
    curPriorUse() {
      return this.award?.creditPay?.show;
    },
    awardTitle() {
      // WARNING: 如果优惠信息仅微页面、活码时，顶部title展示特殊文案。
      if (this.newAwardInfo.paidPromotionInfo) {
        if (
          !this.isNotNull(this.award.cashBack) &&
          !this.isNotNull(this.curCoupon) &&
          !this.isNotNull(this.award.memberCardList) &&
          !this.isNotNull(this.award.credit)
        ) {
          const { promotionType } = this.award?.paidPromotionInfo ?? {};
          if (promotionType === PROMOTION_TYPES.ActivitiesQrCode) {
            const { activityQrCodeType } = this.award?.paidPromotionInfo ?? {};
            return `加微信${
              activityQrCodeType === ActivityQrCodeTypes.Group ? '群' : ''
            }，获取更多优惠信息`;
          }
          if (promotionType === PROMOTION_TYPES.Feature) {
            return '浏览更多内容';
          }
        }
      }
      return '恭喜你获得';
    },
    showMember() {
      return this.curMember && !this.hideBenefitcard;
    },
  },
  mounted() {
    // api请求刷新过数据之后来一遍更新height
    this.$nextTick(() => {
      this.getPaidActionHeight();
    });
  },
  methods: {
    getPaidActionHeight() {
      setTimeout(async () => {
        if (this.isExpand) return;
        const awardBlockStyle = await this.getBoundingClientRectHeight('.award-block');

        const { height: awardBlockHeight } = awardBlockStyle;
        console.log('height?', awardBlockHeight, awardBlockStyle);
        // 如果元素小于边框 并且有内容 显示蒙层
        if (awardBlockHeight > 336) {
          this.showMask = true;
          this.blockHeight = 336;
        }
      }, 600);
    },
    isNotNull(obj) {
      return !!obj && JSON.stringify(obj) !== '{}';
    },
    expandAll() {
      this.blockHeight = 0;
      this.showMask = false;
      this.isExpand = true;
    },
    logAttach({ ei, et, en, params = {} }) {
      // console.log('action!!', ei, et, en);
      app.logger?.log({
        et,
        ei,
        en,
        si: app.getKdtId(),
        params,
      });
    },
    getBoundingClientRectHeight(selector) {
      return new Promise((resolve) => {
        const query = this.createSelectorQuery();
        query
          .select(selector)
          .boundingClientRect()
          .exec((res) => {
            const result = res[0] ? res[0] : {};
            resolve(result);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.new-award {
  padding: 16px 12px;
  background: #fff;
  margin: 0 12px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.title {
  color: #969799;
  font-size: 14px;
}
</style>
