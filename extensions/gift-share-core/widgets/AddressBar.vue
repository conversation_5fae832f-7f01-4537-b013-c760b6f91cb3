<template>
  <!-- 填写地址相关处理 -->
  <view class="order-address" v-if="currentAddress.tel">
    <view class="order-address__wrapper">
      <view class="order-address__wrapper-title">收礼地址</view>
      <view v-if="logistics.title" class="order-address__wrapper-logistics">
        <view>{{ logistics.title }}</view>
        <view>{{ logistics.context }}</view>
        <view>{{ logistics.time }}</view>
      </view>
      <view class="order-address__wrapper-address">
        {{ currentAddress.province }}{{ currentAddress.city }}{{ currentAddress.district }}
        {{ currentAddress.street }}
      </view>
      <view class="order-address__wrapper-user-info"
        >{{ currentAddress.name }} {{ currentAddress.tel }}</view
      >
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';

export default {
  components: {
    'van-icon': Icon,
    'van-cell': Cell,
  },

  props: {
    currentAddress: {
      type: Object,
      default: () => ({}),
    },
    logistics: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" scoped>
.order-address__wrapper {
  background: #fff;
  padding: 16px;
  margin: 12px;
  border-radius: 8px;

  &-title {
    color: #c8996b;
    font-size: 14px;
    font-weight: 500;
  }

  &-logistics {
    padding: 12px;
    background: #f7f8fa;
    color: #666;
    margin-top: 8px;
    font-size: 14px;
    line-height: 25px;
    border-radius: 4px;
  }

  &-address {
    color: #333;
    font-size: 16px;
    font-weight: 500;
    margin-top: 8px;
    margin-bottom: 2px;
  }

  &-user-info {
    font-size: 14px;
    color: #666;
  }
}
</style>
