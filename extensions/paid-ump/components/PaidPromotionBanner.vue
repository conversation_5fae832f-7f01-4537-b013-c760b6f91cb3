<template>
  <view v-if="show" @click="onClick" class="paid-promotion-block">
    <activity-qrcode v-if="isActivityQrCode" :paid-promotion="paidPromotion" />
    <activity-ump v-else :order-no="orderNo" :paid-promotion="paidPromotion" />
  </view>
</template>

<script>
import ActivityUmp from './ActivityUmp';
import ActivityQrCode from './ActivityQrCode';
import { SupportedActivity, PROMO_TYPE_ACTION } from '../constants';

const app = getApp();

const PROMO_TYPE_ACTION_KEYS = Object.keys(PROMO_TYPE_ACTION);

export default {
  components: {
    'activity-ump': ActivityUmp,
    'activity-qrcode': ActivityQrCode,
  },
  props: {
    orderNo: String,
    paidPromotion: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    show() {
      const { promotionType } = this.paidPromotion;
      return PROMO_TYPE_ACTION_KEYS.indexOf(promotionType) !== -1 || this.isActivityQrCode;
    },
    isActivityQrCode() {
      const { promotionType } = this.paidPromotion;
      return promotionType === SupportedActivity.activitiesQrCode;
    },
  },
  methods: {
    onClick() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_zhifuyouli',
          en: '支付有礼点击',
          si: app.getKdtId(),
        });
    },
  },
};
</script>

<style lang="scss">
.paid-promotion-block {
  display: flex;
  height: 100px;
  box-sizing: content-box;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
  font-size: 0;
  margin-top: 12px;
  padding: 0 12px;
}
</style>
