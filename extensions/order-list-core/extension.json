{"extensionId": "@wsc-h5-trade/order-list-core", "name": "@wsc-h5-trade/order-list-core", "pathInBundle": "@wsc-h5-trade/order-list-core", "version": "1.6.3", "bundle": "<builtin>", "platform": ["web", "weapp"], "lifecycle": ["beforePageMount", "onPageShow", "onPullDownRefresh", "pageDestroyed"], "widget": {"default": "Main", "provide": ["SearchBar", "TopNotice", "CpsGoodsOrder", "OrderListTabs", "SelfFetchPopup", "FailGoodsDialog", "OrderSharePopup", "OrderCancelPopup", "TopNoticeBindPhone", "TopNoticeOtherOrder", "ListItemHeader", "ListEmptyTip", "ListItemFooter", "OrderListItem", "ListItemBody", "ListItemTag", "WxvideoGuidePopup", "ListItem", "OrderList", "OrderListWrapper", "WelikeEntryWrapper"], "consume": ["CpsGoodsOrder", "CommonDialog", "ShopNavWeapp", "ListItemHeader", "ListEmptyTip", "ListItemFooter", "ListItem", "ListItemBody", "OrderListItem", "ListItemTag", "OrderList", "OrderListWrapper", "AfterListItemBodyByYun", "AfterListItemFooterBy<PERSON>un", "OrderInfo", "OrderListItemInquiry", "ButtonDemo", "<PERSON><PERSON><PERSON><PERSON>", "WelikeEntry", "WelikeEntryWrapper", "UserAuthorize"]}, "data": {"provide": {"query": ["r", "w"], "bizName": ["r", "w"], "isIvr": ["r", "w"], "isKsApp": ["r", "w"], "isXhsApp": ["r", "w"], "isAlipayApp": ["r", "w"], "isQQApp": ["r", "w"], "isEduShop": ["r", "w"], "isDrugShop": ["r", "w"], "isRetailShop": ["r", "w"], "activityTab": ["r", "w"], "searchCondition": ["r", "w"], "levelupTipTitle": ["r", "w"], "cpsConfigKey": ["r", "w"], "orderMark": ["r", "w"], "pointsName": ["r", "w"], "pageType": ["r", "w"], "openAppConfig": ["r", "w"], "userInfo": ["r", "w"], "shopMetaInfo": ["r", "w"], "isControlRecommendShow": ["r"], "showRecommend": ["r", "w"]}, "consume": {"shop": ["r"], "kdtId": ["r", "w"], "yunDesignConfig": ["r"], "isShowRetailOrderList": ["r"], "topNavHeight": ["r"], "createLevelupTip": ["r"]}}, "process": {"define": ["<PERSON><PERSON><PERSON><PERSON>", "setActivityTab", "refreshOrderList", "fetchMoreOrder", "gotoOrderSearch", "gotoOrderDetail", "activityTabChange", "handleBuyAgainFailGoods", "doOrderFooterAction"], "invoke": ["setKdtId", "beforeUpdateKdtId", "handleBuyAgainFailGoods", "beforeSetup"]}, "event": {"emit": ["checkLevelupTip"], "listen": ["closeLevelupTip", "confirmLevelupTip", "onFetchLevelupTip"]}, "lambda": {"consume": ["getUserInfo"]}}