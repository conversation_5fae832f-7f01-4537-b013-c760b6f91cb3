<template>
  <van-popup
    :show="isShowSelfFetchPopup"
    closeable
    position="bottom"
    custom-style="height: 100%"
    @click="onClose"
  >
    <view class="self-fetch">
      <view class="layout-wrapper">
        <image
          v-if="selfFetchInfo.verifyCodeSrc"
          class="self-fetch__qrcode"
          :src="selfFetchInfo.verifyCodeSrc"
          alt="二维码"
        />
        <view class="self-fetch__code">{{ selfFetchInfo.verifyCode || '正在生成中...' }}</view>
        <view v-if="selfFetchInfo.verifyCodeSrc" class="self-fetch__desc"
          >请将核销码出示给收银员</view
        >
      </view>
    </view>
  </van-popup>
</template>

<script>
// Components
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

// Dependencies
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-popup': Popup,
  },

  data() {
    return {
      ...mapState(this, ['isShowSelfFetchPopup', 'selfFetchInfo']),
    };
  },

  methods: {
    onClose() {
      this.store.toggleSelfFetchPopup({ isShow: false });
    },
  },
};
</script>

<style lang="scss" scoped>
.self-fetch {
  position: relative;
  height: 100%;
}

.layout-wrapper {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0;
  text-align: center;
}

.self-fetch__qrcode {
  width: 238px;
  height: 238px;
  border: 1px solid #dcdee0;
}

.self-fetch__code {
  font-family: Avenir;
  font-size: 24px;
  font-weight: bold;
  color: #4a4a4a;
  text-align: center;
  padding: 12px 0;
}

.self-fetch__desc {
  color: #969799;
  font-size: 14px;
  padding: 10px 0 15px;
  text-align: center;
}
</style>
