<template>
  <view
    v-if="show"
    :class="[bgTransparent ? 'bg-transparent' : '', 'action-block', 'action-block--normal']"
    :style="themeCSS"
  >
    <view class="action-block__btn-primary-block" v-if="primaryBtnList.length">
      <view v-for="item in primaryBtnList" :key="item.type" class="action-block__btn-primary">
        <!-- 社区团购查看提货码按钮 -->
        <groupbuy-self-fetch-button
          v-if="item.type === BTNS_MAP.PICK_UP_CODE && item.btnBizType === 'GROUPBUY'"
          :border="item.block"
        />
        <van-button
          v-else
          :custom-class="'action-block__btn block'"
          :custom-style="'border-radius: var(--theme-radius-button, 999px)'"
          :color="'var(--icon, #323233)'"
          @click="processPaidAction(item)"
          plain
        >
          {{ item.defaultText }}
        </van-button>
      </view>
    </view>

    <view
      v-for="(item, index) in normalBtnList"
      :key="item.type"
      :class="['action-block__btn-default', index > 0 ? 'action-block__btn-default-next' : '']"
    >
      <!-- 社区团购自定义按钮 -->
      <groupbuy-continue-button v-if="item.type === BTNS_MAP.GROUPBUY" />
      <groupbuy-share-button
        v-else-if="item.type === BTNS_MAP.SHARE && item.btnBizType === 'GROUPBUY'"
      />
      <solitaire-share-button
        v-else-if="item.type === BTNS_MAP.SHARE && item.btnBizType === 'SOLITAIRE_BUY'"
      />

      <!-- 交易侧的按钮都在这里，简单的按钮不需要开新的widget，查看详情，再来一单等默认情况 -->
      <van-button
        v-else
        :custom-class="'action-block__btn ' + ((item.block && 'block') || '')"
        :custom-style="
          !item.block ? 'border: none;' : 'border-radius: var(--theme-radius-button, 999px)'
        "
        :color="item.block ? 'var(--icon, #323233)' : '#969799'"
        @click="processPaidAction(item)"
        plain
      >
        {{ item.defaultText }}
      </van-button>
    </view>
    <!-- 先用后付弹框 -->
    <prior-use-dialog />

    <!-- 订阅物流弹框 -->
    <subscription-dialog />

    <!-- #ifdef web -->
    <!-- 支付补贴 -->
    <paid-subsidy-dialog />

    <!-- 心愿墙 -->
    <wish-msg-popup v-if="showWishMsg" @close="handleNextPopup" />
    <!-- #endif -->

    <!-- 权益卡激活弹窗 -->
    <activate-benefitcard-dialog />

    <!-- #ifdef weapp -->
    <!-- 微信小程序订阅提醒动画 -->
    <paid-subscription-msg-tips />
    <!-- #endif -->
  </view>
</template>

<script>
import get from '@youzan/utils/object/get';
import { getStorageSync } from '@youzan/tee-api';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { ZNB } from '@youzan/tee-biz-navigate';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import { getPopConfig } from '../api';
import { mapState, mapActions } from '@ranta/store';
import toCamelCase from '@youzan/utils/string/toCamelCase';

/* #ifdef web */
import { checkRetailShop } from '@youzan/utils-shop';
/* #endif */
import { BTNS_MAP, SHOP_AD_MAP, NEEDS_CALCULATE_SHOP_AD_NUMBERS } from '../constans';
/* #ifdef web */
// 引入消息弹框
import '../utils/imc-uniform';
/* #endif */
import env from '@youzan/wsc-tee-trade-common/lib/env';

/* #ifdef web */
const mpData = get(window, '_global.mp_data', {});
/* #endif */

const WAIT_LIMIT = 3; // 等待任务队列长度，很多事情后面三个数据准备好后才能继续

export default {
  components: {
    'van-button': Button,
  },
  data() {
    this.payResultLoaded = false;
    this.newAwardInfoLoaded = false; // 权益相关数据是否加载完成
    this.waitLength = 0; // 等待任务队列长度，很多事情后面三个数据准备好后才能继续：payResult, canUseTradeUmpV1, newAwardInfo
    this.hasWxSubscribePopped = false; // 这里指下单页是否已弹出微信订阅消息弹框，如果已弹出，订阅物流不需要自动弹

    let isRetailApp = false;
    /* #ifdef web */
    isRetailApp = checkRetailShop(mpData);
    /* #endif */

    /* #ifdef weapp */
    isRetailApp = getApp().globalData.isRetailApp;
    /* #endif */
    return {
      themeColors: {},
      BTNS_MAP,
      show: false,
      popupOrder: [], // 弹窗展示顺序
      currentPopupIndex: -1, // 当前展示的弹窗
      canUseTradeUmpV1: true, // 支付后营销场景
      showAwardV2Block: true,
      paidCodeInfoIsShow: false,
      newAwardInfo: {},
      showWishMsg: false,
      mpData: {},
      themeCSS: '',
      isRetailApp,
      isSkyline: false,
      emptyPaidExtHolderBlock: false,
      ...mapState(this, [
        'kdtId',
        'hasPaid',
        'orderNo',
        'isHideYouzanFollow', // 是否隐藏有赞公众号订阅
        'hidePickUpCodeBtn', // 云定制字段 默认展示
        'mpData',
        'payResult',
        'btnList',
      ]),
    };
  },

  computed: {
    bgTransparent() {
      return (
        this.showAwardV2Block ||
        !this.hasPaid ||
        this.paidCodeInfoIsShow ||
        !this.emptyPaidExtHolderBlock
      );
    },
    primaryBtnList() {
      // TAKE_GOODS_CODE(取货号)场景不展示按钮，因为需要根据此能力显示取货码在页面，因此不能后端直接去掉，需要前端过滤隐藏掉
      return (this.btnList || []).filter(
        (item) => item.block && item.type !== BTNS_MAP.TAKE_GOODS_CODE
      );
    },
    normalBtnList() {
      return (this.btnList || []).filter((item) => !item.block);
    },
  },

  watch: {
    currentPopupIndex(val) {
      const { popupOrder, payResult = {} } = this;
      let newPopupOrder = [...popupOrder];

      /* #ifdef web */
      /**
       * 批发订单
       * 不需要展示 订阅弹窗,心愿单,心愿单,裂变券,自动推券,先用后付 弹窗, 做个过滤
       */
      if (payResult?.isWholesaleOrder) {
        newPopupOrder = popupOrder.filter((popupOrderId) => {
          return ![
            'subscription',
            'wishMsg',
            'subsidy',
            SHOP_AD_MAP.fission_coupons,
            SHOP_AD_MAP.auto_issue_coupon,
            SHOP_AD_MAP.prior_use,
          ].includes(popupOrderId);
        });
      }

      // 赞拼拼订单不展示任何弹窗
      if (env?.isFxZpp) {
        newPopupOrder = [];
      }

      /* #endif */

      this.currentPopType = newPopupOrder[val];
      const popupFnMap = this.handlePopupOrder();
      console.log('[PaidAction] try pop: ', this.currentPopType);

      if (popupFnMap[this.currentPopType]) {
        this.ctx.cloud
          .invoke('beforeAutoPopupDisplay', { popupType: toCamelCase(this.currentPopType) })
          .then(() => {
            popupFnMap[this.currentPopType]();
          })
          .catch((e) => {
            this.handleNextPopup(); // 虽然报错了，但是需要继续弹下一个弹窗
            console.log('hook:beforeAutoPopupDisplay三方报错', e);
          });
      } else {
        // this.handleNextPopup();
      }
    },
  },

  created() {
    this.currentShopAdIndex = 0;
    mapData(this, [
      'showAwardV2Block',
      'themeCSS',
      'themeColors',
      'isSkyline',
      'paidCodeInfoIsShow',
      'emptyPaidExtHolderBlock',
    ]);
    mapData(this, {
      payResult: (val) => {
        this.payResult = val;
        this.mpData = val.mpData ?? {};

        // 错误状态不展示paid-action
        this.show = !val.showError;

        if (this.orderNo && !this.payResultLoaded) {
          this.payResultLoaded = true;

          this.waitLength += 1;
          this.tryStartPop();
        }
      },
      newAwardInfo: (val) => {
        this.newAwardInfo = val;
        if (!this.newAwardInfoLoaded) {
          this.newAwardInfoLoaded = true;
          this.waitLength += 1;
          this.tryStartPop();
        }
      },
      canUseTradeUmpV1: (val) => {
        this.canUseTradeUmpV1 = val;
        // canUseTradeUmpV1==false不需要等待newAwardInfo，故+2结束等待
        if (this.canUseTradeUmpV1) this.waitLength += 1;
        else this.waitLength += 2;

        this.tryStartPop();
      },
      changeSubscribe: (val) => {
        this.hasWxSubscribePopped = !!val ?? false;
      },
    });

    mapEvent(this, {
      // 有赞云定制 处理“查看提货码按钮”； {参数：false（默认，显示提货码按钮），true（隐藏提货码按钮）}
      hidePickUpCodeBtn: (hide = false) => {
        this.hidePickUpCodeBtn = hide;
      },
      // 处理“分享一下”
      handleOpenShare: () => this.handleOpenShare(),
      // 监听自开启弹窗关闭事件
      closeAutoOpenPopup: ({ isRealClose = false } = {}) => {
        // isRealClose: 确认是弹窗弹出后自动关闭，则计入弹窗管控内
        if (
          isRealClose &&
          this.isPageShopAdNeedsMaxNumbers &&
          this.pageMaxNumbers &&
          NEEDS_CALCULATE_SHOP_AD_NUMBERS.indexOf(this.currentPopType) !== -1
        ) {
          this.currentShopAdIndex++;
        }

        // 若当前页面有受数量管控的弹窗，且已经弹到最大数值，则不再继续
        if (this.pageMaxNumbers && this.currentShopAdIndex === this.pageMaxNumbers) {
          return;
        }

        this.handleNextPopup();
      },
    });

    mapActions(this, ['processPaidAction']);

    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    });
    /* #endif */
    this.ctx.hummer.mark?.log?.({
      tag: 'pay-result',
      scene: ['route'],
      extra: { isSkyline: this.ctx.data.isSkyline },
    });
  },

  methods: {
    // 云定制 处理“分享一下”
    handleOpenShare() {
      // 社区接龙分享
      if (this.payResult.orderTags?.SOLITAIRE_BUY) {
        this.ctx.event.emit('openSolitaireDialog');
        return;
      }
      // 社区团购分享
      if (this.payResult.isGroupBuy) {
        this.ctx.event.emit('openGroupbuyShareDialog');
        return;
      }
      // 普通交易链路分享
      this.ctx.event.emit('showTradeShareDialog');
    },

    formateBtnStyle(item) {
      return `display: ${item.block ? 'block' : 'inline-block'}`;
    },

    tryStartPop() {
      if (!this.hasPaid) return;

      // 主要数据都准备好了
      if (this.waitLength >= WAIT_LIMIT) {
        console.log('[PaidAction startPop]');
        /* #ifdef weapp */
        this.getPagePopNumbers().then(this.getPopupOrder.bind(this));
        /* #endif */

        /* #ifdef web */
        Promise.resolve().then(() => {
          this.getPopupOrder();
        });
        /* #endif */

        // 上报按钮相关日志
        this.reportWuliuLogger();
      }
    },

    getPopupOrder() {
      // 弹窗展示顺序：自提/电子卡券码心愿单的弹窗 > 裂变优惠券的弹窗 > 订阅物流的弹窗
      // 下面的心愿单和补贴，只有H5需要

      // 高优展示的弹窗
      const outOfSortkeys = [
        /* #ifdef web */
        'wishMsg',
        'subsidy',
        /* #endif */
        'solitaire',
        'groupbuyShare',
        SHOP_AD_MAP.prior_use,
      ];
      // 受管控的弹窗
      const defaultSortKeys = [
        /* #ifdef weapp */
        SHOP_AD_MAP.pop_ad,
        /* #endif */
        SHOP_AD_MAP.fission_coupons,
        SHOP_AD_MAP.auto_issue_coupon,
        SHOP_AD_MAP.card_grant,
      ];

      /* #ifdef weapp */
      getPopConfig({
        keys: defaultSortKeys,
        popPosition: 5,
      })
        .then((resp = []) => {
          if (resp.length === 0) return Promise.reject();

          const order = [];
          resp
            .filter((item) => item.status === 1)
            .sort((a, b) => a.sort - b.sort)
            .forEach((item, index) => {
              order[index] = SHOP_AD_MAP[item.popType];
            });
          this.popupOrder = outOfSortkeys.concat(order);
          this.handleNextPopup();
        })
        .catch(() => {
          this.popupOrder = [].concat(outOfSortkeys, defaultSortKeys);
          this.handleNextPopup();
        });
      /* #endif */

      /* #ifdef web */
      this.popupOrder = [].concat(outOfSortkeys, defaultSortKeys);
      this.handleNextPopup();
      /* #endif */
    },
    handleNextPopup() {
      this.currentPopupIndex++;
    },
    handlePopupOrder() {
      /**
       * 2. 自动打开弹窗逻辑
       *   - 订阅弹窗如果点击了我知道则不再自动弹出
       *   - 其他的自提码、电子卡券弹窗每次都弹出
       */
      const clickRecord = getStorageSync(`pay_success_express_dialog_click_${this.kdtId}`);
      const firstButtonType = this.btnList[0]?.type;
      const hasMpOrWeappAccount = this.mpData.mpId || this.mpData.followWeChatMp; // 商家是否绑定了公众号或者小程序
      const setTimeoutEmitWrapper = (type, value) => {
        let fn = type;
        if (typeof type === 'string') {
          fn = () => {
            this.ctx.event.emit(`${type}`, value);
          };
        }
        setTimeout(fn, 200);
      };

      const disableAutoPopupEmitWrapper = (type, value) => {
        /* #ifdef web */
        if (!this.ctx.data.disableAutoPopup) {
          setTimeoutEmitWrapper(type, value);
        }
        /* #endif */
        /* #ifdef weapp */
        setTimeoutEmitWrapper(type, value);
        /* #endif */
      };

      return {
        // 社群接龙
        solitaire: () => {
          this.payResult.orderTags?.SOLITAIRE_BUY
            ? setTimeoutEmitWrapper('openSolitaireDialog')
            : this.handleNextPopup();
        },
        // 社区团购分享
        groupbuyShare: () => {
          this.payResult.isGroupBuy
            ? setTimeoutEmitWrapper('openGroupbuyShareDialog')
            : this.handleNextPopup();
        },
        // 订阅物流弹窗
        [SHOP_AD_MAP.logistics_subscribe]: () => {
          if (firstButtonType === 'SUBSCRIPTION') {
            // 近期没有点击过“我知道了”
            if (!clickRecord) {
              /* #ifdef web */
              // 在支付宝环境下
              // 点击积分去到个人中心页后跳转回来也不再自动弹出
              if (this.isAlipayApp || this.isQQApp) {
                disableAutoPopupEmitWrapper('showSubscriptionDialog', true);
                return;
              }
              if (hasMpOrWeappAccount) {
                /**
                 * 点击了我知道了不在提示不自动弹出订阅物流
                 * 点击积分去到个人中心页后跳转回来也不再自动弹出
                 * 如果没有绑定公众号或者小程序也不自动弹出
                 */
                disableAutoPopupEmitWrapper('showSubscriptionDialog', true);
                return;
              }
              /* #endif */

              /* #ifdef weapp */
              if (!this.hasWxSubscribePopped) {
                disableAutoPopupEmitWrapper('showSubscriptionDialog', true);
                return;
              }
              /* #endif */
            }
          }

          this.handleNextPopup();
        },
        // 裂变劵
        [SHOP_AD_MAP.fission_coupons]: () => {
          const haveFission = Object.keys(this.newAwardInfo?.fissionCoupon ?? {}).length > 0;
          this.canUseTradeUmpV1 && haveFission
            ? setTimeoutEmitWrapper('openFissionPopup')
            : this.handleNextPopup();
        },
        // 自动推劵
        [SHOP_AD_MAP.auto_issue_coupon]: () => {
          const haveRecommend = Object.keys(this.newAwardInfo?.recommendCoupon ?? {}).length > 0;
          // 社区团购的订单不进行自动推劵
          this.canUseTradeUmpV1 && haveRecommend && !this.payResult.isGroupBuy
            ? setTimeoutEmitWrapper('openAutoSendCouponPopup')
            : this.handleNextPopup();
        },
        // 弹窗广告
        [SHOP_AD_MAP.pop_ad]: () => {
          setTimeoutEmitWrapper('showShopAdDialog', 5);
        },
        // 补贴
        subsidy: () => {
          /* #ifdef web */
          setTimeoutEmitWrapper('openSubsidyPopup');
          /* #endif */
        },
        // 心愿单
        wishMsg: () => {
          /* #ifdef web */
          setTimeoutEmitWrapper(() => {
            this.showWishMsg = true;
          });
          /* #endif */
        },
        // 先用后付
        [SHOP_AD_MAP.prior_use]: () => {
          if (this.canUseTradeUmpV1 && this.newAwardInfo?.creditPay?.alert) {
            /* #ifdef web */
            if (env.isWeixin) {
              setTimeoutEmitWrapper('showPriorUseDialog');
            } else {
              this.handleNextPopup();
            }
            /* #endif */
            /* #ifdef weapp */
            setTimeoutEmitWrapper('showPriorUseDialog');
            /* #endif */
          } else {
            this.handleNextPopup();
          }
        },
        // 激活权益卡
        [SHOP_AD_MAP.card_grant]: () => {
          // 带了未激活权益卡
          if (Object.keys(this.newAwardInfo?.formatedMemberCard ?? {}).length) {
            setTimeoutEmitWrapper('showActivateBenefitcardDialog');
          } else {
            this.handleNextPopup();
          }
        },
      };
    },

    /**
     * 按钮曝光统计
     */
    reportWuliuLogger() {
      // 按钮曝光统计
      if (this.btnList.some((btn) => btn.type === BTNS_MAP.SUBSCRIPTION)) {
        this.ctx.logger.log({
          et: 'view',
          ei: 'show_button_wuliu',
          en: '“订阅物流”按钮曝光',
          si: this.kdtId,
        });
      }
    },

    getPagePopNumbers() {
      // 小程序下需要
      return new Promise((resolve) => {
        const app = getApp();
        // 获取页面弹窗个数
        app
          .getShopConfigData()
          .then((shopConfig = {}) => {
            const { shop_ad_numbers_for_pages: shopAdNumbersForPages = '{}' } = shopConfig;

            try {
              this.pageMaxNumbers = (JSON.parse(shopAdNumbersForPages) || {}).paidSuccess;
            } catch (e) {
              console.log('获取页面弹窗个数失败', e);
            }

            this.isPageShopAdNeedsMaxNumbers = true;
            resolve();
          })
          .catch(resolve);
      });
    },
  },
};
</script>

<style lang="scss">
.action-block {
  padding: 0 20px 24px;
  background: linear-gradient(to bottom, #fff 50%, #f7f8fa);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  &.bg-transparent {
    background: transparent;
  }

  &__btn-primary-block {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &--normal {
    text-align: center;
  }

  &__btn-primary,
  &__btn-default {
    white-space: nowrap;
    font-size: 14px;
    position: relative;
  }

  &__btn-primary {
    /* 由于skyline不支持inline模式，主按钮需要用宽度100%单独成为一行 */
    display: flex;
    justify-content: center;
    margin: 0 12px 16px;
    /* 子组件按钮宽度需要内部去算，由于action-block的左侧padding限制会导致超出容器 */
    overflow: hidden;
  }

  &__btn-default-next {
    &::before {
      position: absolute;
      box-sizing: border-box;
      content: '';
      pointer-events: none;
      width: 1px;
      height: var(--eo-font-size-18, 18px);
      z-index: 1;
      left: 0;
      top: 50%;
      border-left: 1px solid #dcdee0;
      transform: scaleX(0.5) translateY(-50%);
      transform-origin: left top;
    }
  }

  &__btn-split {
    display: inline-block;
    width: 1px;
    height: 16px;
    background: #dcdee0;
    vertical-align: middle;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -8px;
  }

  &__btn {
    background: transparent !important;
    height: 36px !important;
    line-height: 36px !important;

    --button-normal-font-size: var(--eo-font-size-14, 14px);

    &.block {
      width: auto;
    }
  }
}
</style>
