# @wsc-tee-trade/detail-price

计价

![UI呈现](https://img.yzcdn.cn/public_files/a6551821b15644e06e325c8972aa07d3.png)

## 调试方式

## 存疑

- 格式化金额是否可以通过 npm 包提供的形式抽离代码？比如 formatedPayUmpDiscountMoney
- `附加费用` 中遍历的 dialog 是否能抽离成公共的？
- InfoDialog 是否能用 `trade-common` 中的？
- `支付方式` 跳转 `link` 的写法是否可以优化？

## Widget.Provide

| 名称            | 说明 |
| --------------- | ---- |
| Price `default` | ???  |

## Widget.Consume

| 名称            | 说明     |
| --------------- | -------- |
| PopupActivities | ???      |
| PanelActivities | ???      |
| Microtransfer   | 小额打款 |
| InfoDialog      | ???      |

## Data.Consume

| 名称             | 类型               | 说明       |
| ---------------- | ------------------ | ---------- |
| themeColors      | -                  | 店铺风格   |
| kdtId            | _number_           | 店铺 ID    |
| order            | _Order_            | 订单信息   |
| orderBizExtra    | -                  | ???        |
| goods            | -                  | ???        |
| payment          | _Payment_          | 支付信息   |
| showPostage      | -                  | ???        |
| buyWayDetailUrl  | -                  | ???        |
| fissionTicketNum | _number_           | 内购券数量 |
| useBeforePayData | _UseBeforePayData_ | 先用后付   |
