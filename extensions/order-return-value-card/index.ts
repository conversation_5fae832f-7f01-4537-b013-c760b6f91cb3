import OrderReturnValueCard from './OrderReturnValueCard.vue';
import { cloud } from '@youzan/ranta-helper';
import type { ReturnValueCard } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class OrderReturnValueCardExtension {
  ctx: any;

  /**
   * returnValueCard
   * @desc 储值返现
   * @type {ReturnValueCard}
   */
  @cloud('returnValueCard', 'data')
  returnValueCard: ReturnValueCard;

  firstDataInit = false;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['returnValueCard'], {
      callback: () => {
        const { returnValueCard } = this.ctx.data;
        const newOpenData = {
          returnValueCard: cloudData.getReturnValueCard({ returnValueCard }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    OrderReturnValueCard,
  };
}
