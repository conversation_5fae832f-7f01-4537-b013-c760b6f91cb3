<template>
  <view class="page-wrapper" v-if="!loading">
    <view class="block">
      {{ info.title }}
    </view>

    <view class="block">
      <view class="van-font-bold">{{ info.blocks[0].title }}</view>
      <view class="content">
        {{ info.blocks[0].contents[0] }}
      </view>
    </view>

    <view class="block">
      <view class="van-font-bold">{{ info.blocks[1].title }}</view>
      <view class="content">
        {{ info.blocks[1].contents[0] }}
      </view>
      <view class="content">
        <!-- eslint-disable-next-line max-len -->
        {{ info.blocks[1].contents[1] }}
        <view class="content">
          <view> {{ info.blocks[1].contents[2] }} </view>
          <view> {{ info.blocks[1].contents[3] }} </view>
        </view>
      </view>
    </view>

    <view class="block">
      <view class="van-font-bold">{{ info.blocks[2].title }}</view>
      <view class="content">
        <view>
          {{ info.blocks[2].contents[0] }}
        </view>
        <view>
          {{ info.blocks[2].contents[1] }}
        </view>
        <view>{{ info.blocks[2].contents[2] }} </view>
      </view>
    </view>

    <view class="block">
      <view class="van-font-bold">{{ info.blocks[3].title }}</view>
      <view class="content">
        {{ info.blocks[3].contents[0] }}
      </view>
    </view>
    <view class="block">
      <view class="van-font-bold">{{ info.blocks[4].title }}</view>
      <view class="content">
        <view>
          {{ info.blocks[4].contents[0] }}
          <view class="content">
            <view> {{ info.blocks[4].contents[1] }}</view>
            <view> {{ info.blocks[4].contents[2] }} </view>
          </view>
        </view>
        <view> {{ info.blocks[4].contents[3] }}</view>
        <view>
          {{ info.blocks[4].contents[4] }}
        </view>
        <view> {{ info.blocks[4].contents[5] }} </view>
      </view>
    </view>
  </view>
</template>

<script>
import { requestV2 } from '@youzan/tee-biz-request';

export default {
  data() {
    return {
      loading: true,
      info: {},
    };
  },
  created() {
    requestV2({
      path: '/wscump/gift/get-rules-info.json',
    })
      .then((res) => {
        this.info = res;
        this.loading = false;
        console.log(this.info);
      })
      .catch(() => {
        this.loading = false;
      });
  },
};
</script>

<style lang="scss" scoped>
.block {
  margin-bottom: 10px;
}

.page-wrapper {
  margin-top: 10px;
  padding: 0 10px;
  font-size: 12px;
}

.content {
  padding-left: 10px;
}

.van-font-bold {
  font-weight: bold;
}
</style>
