{"name": "@wsc-tee-trade/gift-share-core", "version": "0.0.0", "platform": ["web", "weapp"], "displayName": "中文名", "description": "描述信息", "extensionId": "@wsc-tee-trade/gift-share-core", "pathInBundle": "@wsc-tee-trade/gift-share-core", "lifecycle": ["onPageScroll", "beforePageMount", "onShareAppMessage"], "widget": {"default": "Main", "provide": ["ActionBtn", "AddressBar", "GiftGoods", "MessageInput", "UserList"], "consume": ["ActionBtn", "AddressBar", "GiftGoods", "MessageInput", "UserList", "GoodsItem", "BottomLinks", "UserAuthorize"]}, "component": {"provide": [], "consume": ["UserAuthorizePopup"]}, "lambda": {"provide": [], "consume": ["getUserInfo"]}, "data": {"provide": {"pageTitle": ["r", "w"], "barConfig": ["r", "w"]}, "consume": {"kdtId": ["r"]}}, "event": {"emit": [], "listen": []}, "process": {"invoke": [], "define": []}}