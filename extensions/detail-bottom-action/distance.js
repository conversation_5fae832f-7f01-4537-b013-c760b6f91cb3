function getRad(d) {
  return (d * Math.PI) / 180.0;
}

export function getDistance(lat1, lng1, lat2, lng2) {
  let sf = Math.sin(getRad((lat1 + lat2) / 2));
  let sg = Math.sin(getRad((lat1 - lat2) / 2));
  let sl = Math.sin(getRad((lng1 - lng2) / 2));

  const fl = 1 / 298.257;

  sg *= sg;
  sl *= sl;
  sf *= sf;

  const s = sg * (1 - sl) + (1 - sf) * sl;
  const c = (1 - sg) * (1 - sl) + sf * sl;

  const w = Math.atan(Math.sqrt(s / c));
  const r = Math.sqrt(s * c) / w;
  const d = 2 * w * 6378137.0;
  const h1 = (3 * r - 1) / 2 / c;
  const h2 = (3 * r + 1) / 2 / s;

  return d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg));
}
