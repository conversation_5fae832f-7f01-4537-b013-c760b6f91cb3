import MainWidget from './Main.vue';
import SelfFetchCodeInfo from './widgets/SelfFetchCodeInfo.vue';
import VoucherCardCodeInfo from './widgets/VoucherCardCodeInfo.vue';
import TakeGoodsCodeInfo from './widgets/TakeGoodsCodeInfo.vue';

import createExtStore from './store';
import { mapCtxData } from '@ranta/store';
import { cloud, mapData, mapEvent } from '@youzan/ranta-helper';
import { OrderPaidEcardInfoVM, OrderPaidEcardStatusEnum } from '@youzan-cloud/cloud-biz-types';

export default class Extension {
  ctx: any;

  store: any;

  unwatchVoucherCard: any;

  unwatchVoucherCardIsError: any;

  /**
   * ecard
   * @desc 电子卡券信息
   * @type {OrderPaidEcardInfoVM}
   */
  @cloud('ecard', 'data')
  ecard: OrderPaidEcardInfoVM;

  // Tips: 如果未使用 options.ctx，可以移除 constructor 函数
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.data.paidCodeInfoIsShow = false;

    this.store = createExtStore(this.ctx);

    mapCtxData(this, ['themeCSS', 'themeColors', 'orderNo', 'payResult']);

    mapData(this, {
      payResult(val) {
        if (val.buttonGroup) {
          this.store.initCodeInfoShowStatus(val);
        }
      },
    });

    mapEvent(this, {
      saveVoucherCardPoster: this.store.saveVoucherCardPoster,
      saveSelfFetchPoster: this.store.saveSelfFetchPoster,
    });

    this.initCloudData();
  }

  initCloudData() {
    // 页面数据加载完成后，如果没有查看券码则非电子卡券结果页，则电子卡券数据设置为null
    mapData(this, {
      payResult(val) {
        if (val.buttonGroup) {
          const showVoucherCard = !!val.buttonGroup?.SAVE_CARD_VOUCHER;
          if (!showVoucherCard) {
            this.ecard = null;
          }
        }
      },
    });

    this.unwatchVoucherCard = this.store.watch('voucherCard', () => {
      const { barcode, qrcode, code } = this.store.voucherCard;
      const status = 'success' as OrderPaidEcardStatusEnum;
      this.ecard = {
        barcode,
        qrcode,
        virtualCode: code,
        status,
      };
    });
    this.unwatchVoucherCardIsError = this.store.watch('voucherCardIsError', () => {
      if (this.store.voucherCardIsError) {
        const status = 'fail' as OrderPaidEcardStatusEnum;
        this.ecard = {
          barcode: null,
          qrcode: null,
          virtualCode: null,
          status,
        };
      }
    });
  }

  pageDestroyed() {
    this.unwatchVoucherCard && this.unwatchVoucherCard();
    this.unwatchVoucherCardIsError && this.unwatchVoucherCardIsError();
  }

  static widgets = {
    Main: MainWidget,
    SelfFetchCodeInfo,
    VoucherCardCodeInfo,
    TakeGoodsCodeInfo,
  };
}
