import { createStore } from '@ranta/store';
import state from './state';
import getters from './getters';
import getActions from './actions';

function registerWatch(store) {
  store.watch('kdtId', (kdtId) => {
    console.log('store.watch.kdtId', kdtId);
    if (!kdtId) {
      return;
    }
    store.getCouponAddon(kdtId);
  });
}

function listenEvent(ctx, store) {
  ctx.event.listen('updatingCart', () => {
    store.getCouponAddon();
  });
}

export default function createSkuStore(ctx) {
  const store = createStore({
    state: () => ({
      ...state,
    }),
    getters: {
      ...getters,
    },
    actions: {
      ...getActions(ctx),
    },
  });
  registerWatch(store);
  listenEvent(ctx, store);
  return store;
}
