<template>
  <view class="prior-use-base-container">
    <view class="base-card">
      <view class="left">
        <view class="icon">
          <image class="icon-image" v-if="formatInfo.icon" :src="formatInfo.icon" />
        </view>
        <view class="content t-ellipsis">
          <view class="text">{{ formatInfo.text }}</view>
          <view class="desc">{{ formatInfo.desc }}</view>
        </view>
      </view>
      <view class="right">
        <view class="button" @click="onClick">{{ formatInfo.buttonText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { cdnImage } from '@youzan/tee-biz-util';

const app = getApp();

export default {
  name: 'prior-use-new-ump',
  components: {},
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
  },
  computed: {
    formatInfo() {
      return {
        icon: cdnImage('public_files/882f03f55476af5ab5b3f2cc75394ec5.png'),
        text: '0元下单专属资格',
        desc: '先用后付，确认收货后付款',
        buttonText: '立即领取',
      };
    },
  },
  mounted() {
    app.logger &&
      app.logger.log({
        et: 'view',
        ei: 'show_ump_xianyonghoufu_wxapp',
        en: '“先用后付”营销曝光',
        si: app.getKdtId(),
      });
  },
  created() {},
  methods: {
    onClick() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'open_xianyonghoufu_ump_wxapp',
          en: '“先用后付”店家开通',
          si: app.getKdtId(),
        });
      let envVersion = 'release';
      try {
        // @ts-ignore
        envVersion = __wxConfig.envVersion;
      } catch {}
      try {
        wx.navigateToMiniProgram({
          appId: 'wx7138c43cf7b11573',
          envVersion,
          path: '/pages/prior-use/index?sources=pay_success_ump',
          success: () => {
            /** 成功跳转到有赞支付，开始监听回切 */
            const cb = () => {
              wx.offAppShow(cb);
            };
            wx.onAppShow(cb);
          },
          fail: (error) => {
            /** 跳转失败 */
            console.error(error);
          },
        });
      } catch (error) {
        Toast('网络错误, 请稍后重试');
      }
    },
  },
};
</script>

<style lang="scss">
.prior-use-base-container {
  border-radius: 8px;
  width: calc(100vw - 48px);
  box-sizing: border-box;
  margin-top: 12px;
  .base-card {
    height: 56px;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    border: 0 solid #ebedf0;
    background: linear-gradient(115deg, #f3dfc7 0%, #fedaae 100%) !important;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .icon {
        .icon-image {
          height: 40px;
          width: 40px;
        }
      }
      .content {
        margin-left: 10px;
        text-align: left;
        .text {
          font-size: 15px;
          color: #654009 !important;
          font-weight: bold;
          line-height: 20px;
        }
        .desc {
          margin-top: 3px;
          font-size: 12px;
          line-height: 16px;
          padding-top: 6px;
          color: #654009 !important;
        }
      }
    }
    .right {
      .button {
        color: #fff;
        font-size: 14px;
        height: 24px;
        white-space: nowrap;
        margin-top: 8px;
        padding: 0 12px;
        width: 54px;
        background-image: linear-gradient(90deg, #feae79 0%, #ff3d3d 100%);
        border-radius: 16px;
        text-align: center;
        line-height: 24px;
      }
    }
  }
}
</style>
