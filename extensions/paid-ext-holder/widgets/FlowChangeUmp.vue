<template>
  <view v-if="show" class="fc-ump">
    <!-- 优惠券 -->
    <flow-change-ump-coupon v-if="isShowCoupon" :origin-data="originData" @onLogger="logger" />
    <!-- 秒杀 -->
    <flow-change-ump-seckill v-if="isShowSeckill" :origin-data="originData" @onLogger="logger" />
    <!-- 三选一 -->
    <flow-change-ump-choose v-if="isShowChoose" :origin-data="originData" @onLogger="logger" />
    <!-- 抽奖 -->
    <flow-change-ump-lottery v-if="isShowLottery" :origin-data="originData" @onLogger="logger" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
/* #ifdef web */
import env from '@youzan/wsc-tee-trade-common/lib/env';
/* #endif */
import { fetchPayResultShopADApi, getShopAdData, adType } from '../utils/flow-change';

import Coupon from './components/FlowChangeUmpCoupon.vue';
import Seckill from './components/FlowChangeUmpSeckill.vue';
import Choose from './components/FlowChangeUmpChoose.vue';
import Lottery from './components/FlowChangeUmpLottery.vue';

export default {
  name: 'flow-change-ump',

  components: {
    'flow-change-ump-coupon': Coupon,
    'flow-change-ump-seckill': Seckill,
    'flow-change-ump-choose': Choose,
    'flow-change-ump-lottery': Lottery,
  },

  data: () => ({
    show: false,
    shopAd: {},
    originData: {},
    isShowCoupon: false,
    isShowSeckill: false,
    isShowChoose: false,
    isShowLottery: false,
  }),

  created() {
    this.init();
  },

  mounted() {},

  methods: {
    init() {
      /* #ifdef web */
      // 排除掉支付宝小程序和QQ小程序
      if (env.isAlipayApp || env.isQQApp) {
        this.$emit('is-show', false);
        return;
      }
      /* #endif */

      fetchPayResultShopADApi().then((data = {}) => {
        // 老数据直接不展示
        if (data.imageUrl) {
          this.$emit('is-show', false);
          return;
        }

        const shopAdData = getShopAdData(data);

        this.shopAd = shopAdData || {};
        this.originData = shopAdData?.data || {};
        this.show = !!shopAdData;

        if (shopAdData && shopAdData.type) {
          this.isShowCoupon = shopAdData.type === adType.coupon;
          this.isShowSeckill = shopAdData.type === adType.seckill;
          this.isShowChoose = shopAdData.type === adType.choose && shopAdData.data?.length === 3;
          this.isShowLottery = shopAdData.type === adType.lottery;
          this.$emit(
            'is-show',
            this.isShowCoupon || this.isShowSeckill || this.isShowChoose || this.isShowLottery
          );
        } else {
          this.$emit('is-show', false);
        }
      });
    },

    handleToNavigator(data) {
      const { adImageConfig = '{}', psCode } = data;
      const { url, weappUrl, appId } = JSON.parse(adImageConfig);

      /** #ifdef weapp */
      Tee.$native.navigateToMiniProgram({
        appId,
        path: `${weappUrl}&dc_ps=${psCode}`,
        envVersion: 'trial',
      });
      /** #endif */

      /** #ifdef web */
      Tee.navigate({
        url: `${url}&dc_ps=${psCode}`,
        type: 'navigateTo',
      });
      /** #endif */
    },

    logger({ data, type, eventType, index, isNavigator = true }) {
      let value = {
        et: 'view', // 事件类型
        ei: 'ad_exchange_view', // 事件标识
        en: '图片曝光', // 事件名称
      };
      if (eventType === 'click') {
        value = {
          et: 'click', // 事件类型
          ei: 'ad_exchange_click', // 事件标识
          en: '广告点击', // 事件名称
        };
        isNavigator && this.handleToNavigator(data);
      }
      let channel = 'web';
      /** #ifdef weapp */
      channel = 'weapp';
      /** #endif */
      const { psCode, cooperateKdtId } = data;
      if (psCode && cooperateKdtId) {
        this.ctx.logger?.log({
          ...value,
          params: {
            ps_code: psCode,
            type,
            index,
            channel,
            activity_kdt_id: cooperateKdtId,
            component: 'ad_exchange_banner',
          }, // 事件参数
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.fc-ump {
  margin-top: 12px;
  padding: 0 12px;
}
</style>
