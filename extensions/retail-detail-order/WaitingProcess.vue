<template>
  <view class="waiting-process" v-if="processData.needDisplay" :style="themeCSS">
    <block v-if="processData.selfFetchPending">
      前面还有<view class="theme-color">{{ processData.orderNum }}单/{{ processData.goodsNum }}杯</view
      >制作中
    </block>
    <block v-if="processData.selfFetchMaking">
      商品制作中
    </block>
    <block v-if="processData.selfFetchDone">你的商品已准备好，请尽快前往领取</block>

    <block v-if="processData.expressPending">
      前面还有<view class="theme-color">{{ processData.orderNum }}单/{{ processData.goodsNum }}杯</view
      >制作中
    </block>
    <block v-if="processData.expressMaking">
      商品制作中
    </block>
    <block v-if="processData.expressShipping">你的商品已配送，请留意骑手电话</block>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      themeCSS: '',
      processData: {},
    };
  },

  created() {
    mapData(this, ['processData', 'themeCSS']);
  },

  // computed: {
  //   waitingTime() {
  //     const { productTime = 0 } = this.processData;
  //     if (productTime < 60) {
  //       return `${productTime}秒`;
  //     }
  //     if (productTime < 60 * 60) {
  //       return `${(productTime / 60).toFixed()}分钟`;
  //     }
  //     const hour = Math.floor(productTime / 3600);
  //     const minute = ((productTime % 3600) / 60).toFixed() || '';
  //     return `${hour}小时${minute}${minute ? '分钟' : ''}`;
  //   },
  // },
};
</script>

<style lang="scss">
.waiting-process {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f8fa;
  border-radius: 4tpx;
  height: 40tpx;
  font-size: 12tpx;
  color: #969799;
}

.theme-color {
  display: inline;
  color: var(--general);
}
</style>
