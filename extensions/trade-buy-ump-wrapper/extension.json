{"name": "@wsc-tee-trade/trade-buy-ump-wrapper", "version": "0.0.0", "platform": ["weapp"], "displayName": "中文名", "description": "描述信息", "extensionId": "@wsc-tee-trade/trade-buy-ump-wrapper", "pathInBundle": "@wsc-tee-trade/trade-buy-ump-wrapper", "widget": {"default": "Main", "provide": ["Main"], "consume": ["TradeBuyUmpBlock"]}, "data": {"consume": {"state": ["r"], "isExistDiscountExcludePrePayCard": ["r"], "couponModel": ["r"], "displayCard": ["r"], "displayFreeCard": ["r"], "customerCards": ["r"], "unavailableCustomerCards": ["r"], "pointDeduction": ["r"]}}, "event": {"emit": ["sku:show", "sku:hide", "showCouponList", "postagetool:show", "reward:show", "toggleMembershipDialog"]}, "process": {"invoke": ["closeValueCard", "setPrepayCardCheckStatus", "fetchShow", "confirmOrder", "setOrderForbidCoupon", "setOrderForbidPreference", "setDisableStoredDiscount", "setPointDeductionUsed", "setCustomCard", "setDisplayCard", "setPeriodBuyChosenIndex", "switchPlusBuyGoodsProcess", "switchGoods", "addAllGoods", "fetchSkuData", "addCouponProcess", "setCouponChosenIdProcess", "exchangeCouponPromise", "getAddsOnePageDetail", "calcPostage", "modifyCacheOrder", "resetPostageTool", "savePostageTool", "setUmpActivity", "setPointDeductionCost", "mutateState", "selectCustomerCard", "openValueCard", "toggleCouponListPopup", "setIgnoreVirtualCoupon", "showPrePayCardRecharge", "setSelfFetch"]}}