# @wsc-tee-trade/detail-bottom-action

底部操作按钮

![UI呈现](https://img.yzcdn.cn/public_files/c7fe55120af7bcbbf6b28bc55c844df8.png)

## 调试方式

## 存疑

- 解释 `shareCouponStyleVersion`
- 新再来一单的 `result.destination` 是什么意思 2 表示什么？
- 再来一单的 weapp 和 web 为什么逻辑判断是不一致的？
- `领优惠券` 按钮有两种样式，它们的逻辑是什么？

## Widget.Provide

| 名称                   | 说明 |
| ---------------------- | ---- |
| BottomAction `default` | ???  |

## Widget.Consume

| 名称           | 说明 |
| -------------- | ---- |
| MessageContact | ???  |

## Data.Provide

| 名称            | 说明 |
| --------------- | ---- |
| levelupTiptitle | ???  |
| bottomBtns      | ???  |

## Data.Consume

| 名称               | 说明 |
| ------------------ | ---- |
| order              | ???  |
| itemList           | ???  |
| gift               | ???  |
| orderBizExtra      | ???  |
| orderBizUrl        | ???  |
| goods              | ???  |
| invoice            | ???  |
| grouponLink        | ???  |
| guaranteeOrderInfo | ???  |
| miniprogram        | ???  |
| env                | ???  |
| isIVR              | ???  |
| isIvrOwner         | ???  |
| ivrRedirectUrl     | ???  |
| orderBizExtra      | ???  |
| orderBizUrl        | ???  |

## Event.Emit

| 名称                 | 说明 |
| -------------------- | ---- |
| showTradeShareDialog | ???  |
| checkLevelupTip      | ???  |
| refreshOrderInfo     | ???  |

## Event.Listen

| 名称              | 说明 |
| ----------------- | ---- |
| onFetchLevelupTip | ???  |

## Process.Define

| 名称            | 说明 |
| --------------- | ---- |
| confirmReceive  | ???  |
| viewEvaluate    | ???  |
| evaluate        | ???  |
| shareOrder      | ???  |
| showBatchRefund | ???  |
| extendOrder     | ???  |
| cancelOrder     | ???  |
| buyAgain        | ???  |
| directBuyAgain  | ???  |
