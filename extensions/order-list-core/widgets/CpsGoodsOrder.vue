<template>
  <van-cell
    v-if="visible"
    title="查看周边好物订单"
    icon="goods-collect-o"
    class="around"
    custom-style="border-radius: 8px;"
    is-link
    @click="showAroundGoodsOrder"
  />
</template>

<script>
// Components
import Cell from '@youzan/vant-tee/dist/cell/index.vue';

// Utils
import api from '../utils/api';

// Dependencies
import { mapState } from '@ranta/store';
import get from '@youzan/utils/object/get';
import navigate from '@youzan/tee-biz-navigate';

const checkCpsGoodsRecommend = async () => {
  return new Promise((resolve) => {
    /* #ifdef web */
    const cpsHasOpened = get(window, '_global.shopConfigs.cps_goods_recommend_opened_flag', '0');
    resolve(cpsHasOpened === '1');
    /* #endif */

    /* #ifdef weapp */
    api.API_QUERY_CPS_CONFIG().then((configs) => {
      try {
        const configJson = get(configs, 'cps_goods_recommend_order_list', '{}');
        const enable = JSON.parse(configJson).enable || false;
        resolve(enable);
      } catch (e) {
        console.error(e);
        resolve(false);
      }
    });
    /* #endif */
  });
};

export default {
  components: {
    'van-cell': Cell,
  },

  data() {
    return {
      isShow: false,
      ...mapState(this, ['activityTab']),
    };
  },

  computed: {
    visible() {
      return this.isShow && this.activityTab.type === 'all';
    },
  },

  created() {
    checkCpsGoodsRecommend().then((isShow) => {
      this.isShow = isShow;
    });
  },

  methods: {
    showAroundGoodsOrder() {
      /* #ifdef web */
      navigate({
        web: {
          type: 'znb',
          znb: {
            // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
            url: 'https://maijia.youzan.com/v3/trade/record#/list/yzrm?type=all&entry=tang',
          },
        },
      });
      /* #endif */

      /* #ifdef weapp */
      wx.navigateToMiniProgram({
        appId: 'wx63d3fc92d8fdeba2',
        path: 'packages/h5/all/index?entry=tang',
      });
      /* #endif */
    },
  },
};
</script>

<style scoped lang="scss">
.around {
  display: flex;
  margin: 12px 12px 0;
  width: auto;
}
</style>
