import Main from './Main.vue';
import { bridge } from '@youzan/ranta-helper';
import { ShopInfo } from './types';
import { mapData } from '@youzan/ranta-helper-tee';

export default class ShopExtension {
  ctx: any;

  /**
   * shopInfo
   * @deprecated 从 2.0 开始
   * @desc 店铺信息
   * @type {ShopInfo}
   */
  @bridge('shopInfo', 'data')
  shopInfo: ShopInfo;

  constructor(options) {
    this.ctx = options.ctx;
    mapData(this, ['shopInfo'], {
      callback: () => {
        const { shopInfo } = this.ctx.data;
        const { kdtId, shopName } = shopInfo;
        this.shopInfo = {
          kdtId,
          shopName,
        };
      },
    });
  }

  static widgets = {
    Main,
  };
}
