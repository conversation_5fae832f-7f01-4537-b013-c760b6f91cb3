<!-- eslint-disable max-len -->
<template>
  <c-info-dialog title="打包费说明" :show="curShow" @close="onClose" :theme-colors="themeColors">
    <view class="v-packing-fee-info">
      <view
        >根据国家有关政策要求，不得免费提供塑料袋。为保障你的商品安全、卫生送达，基于外卖/外带业务特性将对你的商品，使用购物袋进行打包，会适当对包装材料进行收费。<!-- 按订单计费 --><text
          v-if="curInfo.isOrder"
          >标准{{ curInfo.price }}元/单。</text
        ><!-- 按阶梯计费 --><text v-else-if="curInfo.isLadder">{{ curInfo.desc }}</text>
      </view>
      <!-- 按商品计费 -->
      <view v-if="!curInfo.isOrder && !curInfo.isLadder" class="v-packing-fee-by-goods">
        <view>收费标准：</view>
        <view v-for="(item, index) in curInfo.goodsList" :key="index">
          <view class="v-packing-fee-goods-item">
            <view class="v-packing-fee-goods-item__name">{{ item.name }}</view>
            <view class="v-packing-fee-goods-item__num">x{{ item.nums }}</view>
            <view class="v-packing-fee-goods-item__price">￥{{ item.price }}</view>
          </view>
        </view>
      </view>
    </view>
  </c-info-dialog>
</template>

<script>
import { mapData } from '@youzan/ranta-helper';
import { mapEvent } from '@youzan/ranta-helper-tee';
import CommonInfoDialog from '@youzan/wsc-tee-trade-common/components/InfoDialog.vue';

export default {
  components: {
    'c-info-dialog': CommonInfoDialog,
  },
  props: {
    show: Boolean,
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      curShow: false,
      curInfo: {},
      themeColors: {},
    };
  },
  watch: {
    show: {
      handler(val) {
        this.curShow = val;
      },
      immediate: true,
    },
    info: {
      handler(val) {
        this.praseInfo(val);
      },
      immediate: true,
    },
  },
  created() {
    this.initListen();
    mapData(this, ['themeColors']);
  },
  methods: {
    initListen() {
      mapEvent(this, {
        togglePackingFeeDialog: ({ show, info = {} }) => {
          this.curShow = show;
          this.praseInfo(info);
        },
      });
    },
    praseInfo(info) {
      let isOrder = false;
      let isLadder = false;
      const { desc, realPay } = info;
      let goodsList = [];
      const price = realPay / 100;
      try {
        // packagingFeeType 为 0，2, 4时会返回json字符串
        const { itemsFeeDetails = [], packagingFeeType } = JSON.parse(desc) || {};
        // 按订单
        if (packagingFeeType === 0) {
          isOrder = true;
        }
        goodsList = itemsFeeDetails.map((it) => {
          const item = {
            ...it,
            name: it.n || it.name,
            price: it.p || it.price,
            nums: it.c || it.nums,
          };
          return {
            ...item,
            price: ((item.price * item.nums) / 100).toFixed(2),
          };
        });
      } catch (error) {
        isLadder = true;
      }
      this.curInfo = {
        desc,
        isOrder,
        isLadder,
        price,
        goodsList,
      };
    },
    onClose() {
      this.curShow = false;
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss">
.v-packing-fee-info {
  margin-top: -20px;
  max-height: 60vh;
  overflow-y: auto;
}
.v-packing-fee-by-goods {
  margin-top: 20px;
}
.v-packing-fee-goods-item {
  display: flex;
  align-items: center;
  &__name {
    width: 150px;
    word-break: break-all;
  }
  &__num {
    margin-left: 10px;
    width: 20px;
  }
  &__price {
    margin-left: 30px;
  }
}
</style>
