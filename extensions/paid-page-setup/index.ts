import Toast from '@youzan/vant-tee/dist/toast/toast';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import Tee from '@youzan/tee';
import { hideShareMenu } from '@youzan/tee-api';
import pick from '@youzan/utils/object/pick';
import { args } from '@youzan/tee-util/lib/common/url';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import get from '@youzan/utils/object/get';
import MainWidget from './Main.vue';
import { checkPayApi, fetchPayResultApi, getWaitBuyList, getGiftId } from './api';
import { requestQuery, callbackGenerator, routeHelper, isBlindBoxOrder } from './utils/index';
import { cloudData, isSameObject } from './utils/cloud';
import { isSimpleStyle, formatPayResult } from './utils/format';
import { PAY_RESULT_TEMPLATE, SHARE_CMPT_MAP } from './utils/constant';
import { normalShare } from './utils/share-data';
/* #ifdef web */
import buildUrl from '@youzan/utils/url/buildUrl';
import { successNotify } from './utils/notify';
/* #endif */
import env from '@youzan/wsc-tee-trade-common/lib/env';
import { cloud, bridge, useAsHook } from '@youzan/ranta-helper';
import type {
  GoodsTypeV1,
  PayResultV1,
  OrderDetailUrlV1,
  OrderTypeV1,
  OrderNoV1,
  VirtualTicketDetailUrlV1,
  PayWayStrV1,
} from './types';
import type { OrderPaidOrderInfo, OrderPaidPayment } from '@youzan-cloud/cloud-biz-types';

let app = {} as Record<string, any>;
/* #ifdef weapp */
app = getApp();
/* #endif */

const hideLoading = () => {
  /* #ifdef weapp */
  Tee.$native.hideLoading();
  /* #endif */

  /* #ifdef web */
  Toast.clear();
  /* #endif */
};

export default class PageSetupExtension {
  ctx: any;

  query: Record<string, any>;

  changeSubscribe: unknown;

  groupbuyShareData: Record<string, any>;

  showSolitaire: boolean;

  solitaireShareData: Record<string, any>;

  /**
   * payment
   * @desc 结算信息
   */
  @cloud('payment', 'data')
  payment: OrderPaidPayment;

  /**
   * orderInfo
   * @desc 订单信息
   */
  @cloud('orderInfo', 'data')
  orderInfo: OrderPaidOrderInfo;

  /**
   * orderNo
   * @deprecated 从 2.0 开始
   */
  @bridge('orderNo', 'data')
  orderNoV1: OrderNoV1;

  /* #ifdef web */

  /**
   * goodsType
   * @deprecated 从 2.0 开始
   */
  @bridge('goodsType', 'data')
  goodsTypeV1: GoodsTypeV1;

  /**
   * payResult
   * @deprecated 从 2.0 开始
   */
  @bridge('payResult', 'data')
  payResultV1: PayResultV1;

  /**
   * orderDetailUrl
   * @deprecated 从 2.0 开始
   */
  @bridge('orderDetailUrl', 'data')
  orderDetailUrlV1: OrderDetailUrlV1;

  /**
   * orderType
   * @deprecated 从 2.0 开始
   */
  @bridge('orderType', 'data')
  orderTypeV1: OrderTypeV1;

  /**
   * virtualTicketDetailUrl
   * @deprecated 从 2.0 开始
   */
  @bridge('virtualTicketDetailUrl', 'data')
  virtualTicketDetailUrlV1: VirtualTicketDetailUrlV1;
  /* #endif */

  /* #ifdef weapp */
  /**
   * payWayStr
   * @deprecated 从 2.0 开始
   */
  @bridge('payWayStr', 'data')
  payWayStrV1: PayWayStrV1;
  /* #endif */

  /* #ifdef weapp */
  /**
   * beforeGoToUrl
   * @deprecated 从 2.0 开始
   */
  @bridge('beforeGoToUrl', 'asyncEvent')
  beforeGoToUrlV1 = useAsHook<() => Promise<void>>();
  /* #endif */

  constructor(options) {
    this.ctx = options.ctx;

    this.initProcesses();
    this.initEvents();
    this.initWatchs();
    this.initCloudData();

    this.ctx.env.getQueryAsync().then((query = {}) => {
      // 初始化基本参数
      this.initQuery(query);

      /* #ifdef weapp */
      this.initWeappData(query);
      /* #endif */

      /* #ifdef web */
      this.initWebData();
      // 清除骨架屏
      // @ts-ignore
      window.onPageLoadClearSkeleton?.();
      /* #endif */

      // 非必要数据，放在最后初始化，甚至可以用setTimeout延迟下
      this.initCommon();
    });
  }

  static widgets = {
    Main: MainWidget,
  };

  beforePageCreate() {
    this.initLogger();
  }

  initLogger() {
    let pageBizVersion = '';
    /* #ifdef web */
    pageBizVersion = 'ranta_web';
    /* #endif */
    /* #ifdef weapp */
    pageBizVersion = 'ranta_weapp';
    /* #endif */
    this.ctx.logger?.setPageInitConfig({
      // 设置埋点扩展参数，统计端的调用量
      eventParams: {
        page_biz_version: pageBizVersion,
      },
    });
  }

  initQuery(query = {} as Record<string, any>) {
    let bizOrderNo;
    // eslint-disable-next-line camelcase
    const { order_no, orderNo, request_no, dbid, phase } = query;
    this.query = {};
    // eslint-disable-next-line camelcase
    if (order_no || orderNo || request_no) {
      // eslint-disable-next-line camelcase
      bizOrderNo = order_no || orderNo || request_no;
    }

    if (phase) {
      this.query.phase = phase ? Number(phase) : null;
    }

    /* #ifdef web */
    this.query.kdtId = get(window, '_global.kdtId') || query.kdt_id;
    /* #endif */

    /* #ifdef weapp */
    this.query.kdtId = app.getKdtId();
    if (dbid) {
      const orderData = app.db.get(dbid, false) || {};
      bizOrderNo = orderData.order_no;
      this.query.queryDbid = dbid;
      if (orderData.phasePaymentStage) {
        this.query.phase = orderData.phasePaymentStage;
      }
      this.changeSubscribe = orderData.changeSubscribe;
    }
    /* #endif */

    this.query.orderNo = bizOrderNo;

    this.ctx.data.kdtId = this.query.kdtId;
    this.ctx.data.orderNo = this.query.orderNo;
    this.ctx.data.hasPaid = false;
    this.ctx.data.phasePaymentStage = this.query.phase;
  }

  initCommon() {
    // 以下主要为cps-recommend-goods, recommend-goods提供静态数据
    // 批发订单详情不展示CPS周边好物入口 & 商品推荐模块, 包括小程序&H5
    if (!this.ctx.data?.payResult?.isWholesaleOrder) {
      this.ctx.data.requestExtraParams = 'coupon';
      this.ctx.data.bizName = 'pay_success';
      this.ctx.data.cpsConfigKey = 'cps_goods_recommend_pay_success';
    }

    /* #ifdef weapp */
    // 小程序页面title使用店铺名
    app.getShopInfo().then((shopInfo) => {
      if (shopInfo?.shop_name) {
        this.ctx.data.navigationTitle = shopInfo.shop_name;
      }
    });
    /* #endif */

    /* #ifdef web */
    // 群团团 title使用支付成功
    if (get(window, '_global.env.isFxZpp', false)) {
      document.title = '支付成功';
    }
    /* #endif */

    /* #ifdef weapp */
    this.ctx.data.buyerId = app.getBuyerId();
    /* #endif */
    /* #ifdef web */
    this.ctx.data.buyerId = get(window, '_global.buyer.id') || get(window, '_global.buyer_id', 0);
    /* #endif */

    // 服务于底部footer组件
    this.ctx.data.miniprogram = env; // 其实env也主要是读的_global.miniprogram
    // 服务于底部footer组件
    let mpAccount = {};
    let platformInfo = {};
    /* #ifdef web */
    mpAccount = get(window, '_global.mp_account', {});
    platformInfo = get(window, '_global.platformInfo', {});
    /* #endif */
    this.ctx.data.mpAccount = mpAccount;
    this.ctx.data.platformInfo = platformInfo;
  }

  /* #ifdef web */
  initWebData() {
    const sourcePayResult = get(window, '_global.payResult');
    const firstShopPayResult = get(sourcePayResult, 'shopPayResultGroup[0]', {});
    const firstOrderPayResult = get(firstShopPayResult, 'orderPayResultGroup[0]', {});
    const { payResultVO = {} } = sourcePayResult;

    this.setPayResultV2({
      ...payResultVO,
      ...pick(firstShopPayResult, ['mpData', 'shopCoupons', 'kdtId']),
      award: {
        ...pick(firstShopPayResult, ['cashInfo', 'credit', 'hasMemberCard', 'memberCards']),
      },
      ...firstOrderPayResult,
    });
    this.ctx.inited();
  }
  /* #endif */

  setPayResultV2(payResult) {
    // 尽量精简payResult，避免大对象
    this.ctx.data.fetching = false;
    const showWait = payResult.pageTemplate === PAY_RESULT_TEMPLATE.WAIT;
    const showError = payResult.pageTemplate === PAY_RESULT_TEMPLATE.PAY_ERROR;
    const showOversale = payResult.pageTemplate === PAY_RESULT_TEMPLATE.OVERSALE_ERROR;
    const hasPaid = payResult.payState === 'ALL_PAID' && !showError && !showOversale;
    this.ctx.data.hasPaid = hasPaid;

    // 状态处理
    if (showWait) {
      setTimeout(() => this.ctx.event.emit('checkPay', true), 0);
    }

    payResult.showError = showError;
    payResult.showOversale = showOversale;
    payResult.showWait = showWait;
    formatPayResult(payResult);

    const orderItems = payResult.orderItems || [];
    delete payResult.orderItems;
    this.ctx.data.orderItems = orderItems;

    const firstGoods = orderItems[0] || {};
    const { goodsType } = firstGoods;
    this.ctx.data.goodsType = goodsType;

    /* #ifdef web */
    const { buttonGroup = {} } = payResult;
    const defaultOrderUrl = buildUrl(
      `/wsctrade/order/detail?order_no=${this.ctx.data.orderNo}&kdt_id=${this.ctx.data.kdtId}`,
      'h5',
      this.ctx.data.kdtId
    );
    this.ctx.data.orderDetailUrl = buttonGroup.DETAIL ? buttonGroup.DETAIL.url : defaultOrderUrl;
    this.ctx.data.virtualTicketDetailUrl = buttonGroup.SAVE_CARD_VOUCHER
      ? buttonGroup.SAVE_CARD_VOUCHER.url
      : '';
    /* #endif */

    this.ctx.data.orderType = payResult.orderType;

    this.ctx.data.payResult = payResult;
    this.ctx.data.payWayStr = payResult?.payWayStr; // 云定制专用字段 duplicate

    // 支付后置逻辑
    if (hasPaid) {
      // 改价 TODO
      if (payResult.dialog) {
        this.ctx.data.showChangePriceDialog = true; // 唤起改价弹窗
      }

      /* #ifdef web */
      // 支付成功，通知appsdk
      successNotify(payResult, this.ctx.logger);
      /* #endif */
    }

    /* #ifdef web */
    // 我要送礼订单，直接跳走
    if (payResult.activityType === 63) {
      const { kdtId, orderNo } = this.ctx.data;
      getGiftId(orderNo)
        .then((giftId) => {
          this.ctx.dmc.redirectTo('GiftShare', {
            gift_id: giftId,
            kdt_id: kdtId,
            presenter_view: 1,
          });
        })
        .catch(() => {
          // 获取失败跳转礼物记录
          this.ctx.dmc.redirectTo('GiftList', {
            kdt_id: kdtId,
          });
        });
    }
    /* #endif */
  }

  initWeappData(_query) {
    this.ctx.data.fetching = true;
    this.ctx.data.changeSubscribe = this.changeSubscribe;
    // 初始化
    this.groupbuyShareData = {};
    this.showSolitaire = false;
    this.solitaireShareData = {};
    Tee.$native.showLoading({
      title: '加载中',
    });
    this.fetchPayResult();
    hideShareMenu();

    getApp().on('ranta-paid-skyline', (val: boolean) => {
      this.ctx.data.isSkyline = val;
    });

    app.logger &&
      app.logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'paySuccess', // 页面类型
      });
  }

  /* #ifdef weapp */
  onShareAppMessage(event) {
    console.log(event);
    const { dataset } = event.target || {};
    const { detail = '' } = dataset || {};
    let shareData = null;
    let shouleGroup = true;

    if (detail === 'fission') {
      // 裂变优惠券
      shareData = this.ctx.process.invoke('getFissionShare')?.[0] || {};
    } else if (detail === 'inSourcingCoupon') {
      // 内购券分享
      shareData = this.ctx.process.invoke('getInSourcingCouponShare');
    } else if (this.showSolitaire) {
      // 社群接龙分享
      shouleGroup = false;
      shareData = {
        ...this.solitaireShareData,
        title: this.solitaireShareData.activityName,
        imageUrl: this.solitaireShareData.shareImg,
      };
    } else if (this.ctx.data.payResult.isGroupBuy) {
      // 社群团购分享
      shareData = this.groupbuyShareData;
    } else {
      shouleGroup = false;
      shareData = normalShare({
        shareMessage: this.ctx.data.payResult?.shareMessage,
        orderItems: this.ctx.data.orderItems,
        query: this.query,
      });
    }
    // 处理分享数据
    const loggerParams = shareData.loggerParams || {};
    this.ctx.logger.log({
      et: 'click',
      ei: 'share',
      en: '转发',
      params: {
        share_cmpt: 'native_custom',
        ...loggerParams,
      },
      si: app.getKdtId(),
    });

    // 获取分享来源
    const shareFrom = event.from;

    const shareCmpt = SHARE_CMPT_MAP[shareFrom];
    // 拼装额外分享参数
    let sharePath = shareData.path || '/packages/home/<USER>/index';
    const allQueryData = args.getAll(sharePath);
    const queryData = {
      ...allQueryData,
      is_share: 1,
      share_cmpt: shareCmpt,
      shopAutoEnter: 1,
    } as Record<string, any>;
    queryData.kdt_id = app.getKdtId() || '';
    // 增加 dc_ps 追踪
    // 增加埋点数据
    const logGlobalInfo = app.logger.getGlobal() || {};
    const contextInfo = logGlobalInfo.context || {};
    const userInfo = logGlobalInfo.user || {};

    if (contextInfo.dc_ps) {
      queryData.dc_ps = contextInfo.dc_ps || '';
    }

    if (userInfo.uuid) {
      queryData.from_uuid = userInfo.uuid || '';
    }
    // 门店id
    if (app.getShopInfoSync().isMultiStore) {
      queryData.offlineId = app.getOfflineId();
    }
    // 拼接分享query
    sharePath = args.add(sharePath, queryData);

    let newSharePath = sharePath;
    // 分享页面都通过 blank-page 来进行中转
    if (shouleGroup) {
      newSharePath = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        sharePath
      )}`;
    }
    const finalShareData = {
      ...shareData,
      path: newSharePath,
      success: callbackGenerator('success'),
      fail: callbackGenerator('fail'),
    };

    return finalShareData;
  }
  /* #endif */

  fetchPayResult(_disableCache = false) {
    Toast.loading({
      // 持续时间最长为10s
      duration: 10000,
    });
    const query = requestQuery(this.ctx.data);
    return fetchPayResultApi(query)
      .then((payResult) => {
        Toast.clear();
        this.payResultSuccessHandler(payResult);
        // 通知分阶段加载完成点
        this.ctx.inited();
      })
      .catch((response) => {
        const { msg } = response?.data || {};
        Toast.clear();
        this.ctx.inited();
        return Dialog.confirm({
          title: msg || '未知异常',
          showCancelButton: false,
          message: this.ctx.data.orderNo,
        })
          .then((_resp) => {
            Dialog.close();
          })
          .catch(() => {
            Dialog.close();
          });
      });
    // }
  }

  // 仅用于接口回调，因为_global.payResult字段有些许不一致
  payResultSuccessHandler(payResult = {} as Record<string, any>) {
    const { activityType, buttonGroup, needRedirect, redirectDTO, shareMessage, shopPayResult } =
      payResult as Record<string, any>;

    /* #ifdef weapp */
    // 盲盒订单支付成功自动跳转
    if (isBlindBoxOrder(activityType)) {
      const h5Url = buttonGroup?.DETAIL?.url || '';
      return Tee.$native.redirectTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(h5Url)}`,
      });
    }

    if (shopPayResult?.paymentMemberCard?.cardAlias) {
      // 处理购买付费权益卡，跳转逻辑
      if (needRedirect && redirectDTO.webPageRedirectUrl) {
        return Tee.navigate({
          url: `/pages/common/webview-page/index?src=${encodeURIComponent(
            redirectDTO.webPageRedirectUrl
          )}`,
          type: 'redirectTo',
        });
      }
    }

    const formatPath = routeHelper(this.ctx.env.route, payResult.pageTemplate, this.query);
    // 判断是否需要跳转别的状态页
    if (formatPath) {
      return Tee.$native.redirectTo({
        url: formatPath,
      });
    }

    // 分享
    if (shareMessage) {
      Tee.$native.showShareMenu();
    }
    /* #endif */

    /** #ifdef web */
    // 因支付完成后页面模版改变，降价拍订单跳转
    if (
      payResult.pageTemplate === PAY_RESULT_TEMPLATE.AUCTION ||
      payResult.pageTemplate === PAY_RESULT_TEMPLATE.AUCTION_WAIT
    ) {
      Tee.navigate({
        url: args.add('/wsctrade/order/payresult/auction', args.getAll(location.href)),
      });
    }
    /* #endif */

    // 先删除接口返回的老的shopCoupons（特别服务于裂变，新代码不再使用这边改变语义的字段）
    delete payResult.shopCoupons;
    // 重新复制，后面可以用于多个业务场景
    payResult.shopCoupons = get(payResult, 'shopPayResult.shopCoupons', []);

    const kdtId = get(payResult, 'shopPayResult.kdtId');
    if (kdtId) {
      payResult.kdtId = kdtId;
    }

    // 减轻负载
    delete payResult.shopPayResult;

    // 如果不是等待状态，则取消loading，如果是，则继续loading
    if (payResult.pageTemplate !== PAY_RESULT_TEMPLATE.WAIT) {
      hideLoading();
    }
    this.setPayResultV2(payResult);
  }

  fetchWaitBuyList() {
    const waitBuyGoodsIds = app.storage.get('waitBuyGoodsIds');
    if (!waitBuyGoodsIds) {
      return;
    }

    getWaitBuyList({
      waitBuyGoodsIds,
    })
      .then(({ goodsList, expressType }) => {
        if (goodsList && goodsList.length) {
          this.ctx.data.waitBuyDialog = {
            show: true,
            goodsList,
            expressType,
          };
        }
      })
      .catch(() => {
        // do nothing
      });
  }

  setPayResult(payResult = {} as Record<string, any>) {
    // 确定是否需要展示简版支付成功页，如果是营销后优化一期就不简化了
    payResult.simpleStyle = isSimpleStyle(payResult) && !this.ctx.data.canUseTradeUmpV1;
  }

  checkPay(firstLoad) {
    const query = requestQuery(this.ctx.data);
    this.ctx.data.isRefreshing = true;
    const reFetchPayResult = this.fetchPayResult.bind(this);

    const delayTimes = [...new Array(4).fill(1000), ...new Array(4).fill(2000)];
    let reqCount = 0;
    function doCheckPay() {
      if (firstLoad) {
        if (reqCount >= delayTimes.length) {
          hideLoading();
          this.ctx.data.isRefreshing = false;
          return;
        }
      }

      checkPayApi(query)
        .then((data) => {
          if (data?.allOrderPaid) {
            reFetchPayResult();
            this.ctx.data.isRefreshing = false;
          } else if (firstLoad) {
            setTimeout(doCheckPay.bind(this), delayTimes[reqCount++]);
          } else {
            hideLoading();
            this.ctx.data.isRefreshing = false;
          }
        })
        .catch(() => {
          if (firstLoad) {
            setTimeout(doCheckPay.bind(this), delayTimes[reqCount++]);
          } else {
            hideLoading();
            this.ctx.data.isRefreshing = false;
          }
        });
    }

    doCheckPay.bind(this)();
  }

  initWatchs() {
    mapData(this, ['solitaireShareData', 'showSolitaire', 'groupbuyShareData']);
  }

  initEvents() {
    mapEvent(this, {
      checkPay: (firstLoad) => {
        this.checkPay(firstLoad);
      },
    });
  }

  initProcesses() {
    this.ctx.process.define('beforeGoToUrl', (payload) => {
      return new Promise<void | Record<string, any>>((resolve, reject) => {
        /* #ifdef web */
        resolve({ hasBindingByCloud: false });
        /* #endif */
        /* #ifdef weapp */
        this.ctx.cloud
          .invoke('beforeGoToUrlV1', payload)
          .then((res) => {
            const hasBindingByCloud =
              !!res?.length; /* 根据结果长度判断是否有被三方使用，如果一个结果都没有则表示三方未使用该事件 */
            resolve({ hasBindingByCloud });
          })
          .catch((e) => {
            reject(e);
          });
        /* #endif */
      });
    });
  }

  pageDestroyed() {
    /* #ifdef weapp */
    if (this.query.queryDbid) {
      app.db.delete(this.query.queryDbid);
    }
    /* #endif */
  }

  initCloudData() {
    const mapBridgeData = (context, watchList) => {
      watchList.forEach(({ watchDataName, bridgeDataName, getNewCloudData }) => {
        mapData(context, [watchDataName], {
          isSetData: false,
          callback: () => {
            const newCloudData = getNewCloudData();
            if (!isSameObject(newCloudData, context[bridgeDataName])) {
              context[bridgeDataName] = newCloudData;
            }
          },
        });
      });
    };
    mapBridgeData(this, [
      {
        watchDataName: 'orderNo',
        bridgeDataName: 'orderNoV1',
        getNewCloudData: () => {
          const { orderNo } = this.ctx.data;
          return cloudData.getOrderNoV1({ orderNo });
        },
      },
      /* #ifdef web */
      {
        watchDataName: 'goodsType',
        bridgeDataName: 'goodsTypeV1',
        getNewCloudData: () => {
          const { goodsType } = this.ctx.data;
          return cloudData.getGoodsTypeV1({ goodsType });
        },
      },
      {
        watchDataName: 'payResult',
        bridgeDataName: 'payResultV1',
        getNewCloudData: () => {
          const { payResult } = this.ctx.data;
          return cloudData.getPayResultV1({ payResult });
        },
      },
      {
        watchDataName: 'orderDetailUrl',
        bridgeDataName: 'orderDetailUrlV1',
        getNewCloudData: () => {
          const { orderDetailUrl } = this.ctx.data;
          return cloudData.getOrderDetailUrlV1({ orderDetailUrl });
        },
      },
      {
        watchDataName: 'orderType',
        bridgeDataName: 'orderTypeV1',
        getNewCloudData: () => {
          const { orderType } = this.ctx.data;
          return cloudData.getOrderTypeV1({ orderType });
        },
      },
      {
        watchDataName: 'virtualTicketDetailUrl',
        bridgeDataName: 'virtualTicketDetailUrlV1',
        getNewCloudData: () => {
          const { virtualTicketDetailUrl } = this.ctx.data;
          return cloudData.getVirtualTicketDetailUrlV1({ virtualTicketDetailUrl });
        },
      },
      /* #endif */
      /* #ifdef weapp */
      {
        watchDataName: 'payWayStr',
        bridgeDataName: 'payWayStrV1',
        getNewCloudData: () => {
          const { payWayStr } = this.ctx.data;
          return cloudData.getPayWayStrV1({ payWayStr });
        },
      },
      /* #endif */
      {
        watchDataName: 'payResult',
        bridgeDataName: 'payment',
        getNewCloudData: () => {
          const { payResult } = this.ctx.data;
          return cloudData.getPayment({ payResult });
        },
      },
    ]);

    mapData(this, ['orderNo', 'orderType', 'payResult'], {
      isSetData: false,
      callback: () => {
        const { orderNo, orderType, payResult } = this.ctx.data;
        const newOpenData = {
          orderInfo: cloudData.getOrderInfo({ orderNo, orderType, payResult }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
