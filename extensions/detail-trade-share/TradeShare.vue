<template>
  <view v-if="isAllowShareOrder" class="trade-share">
    <!-- 分享遮罩层 -->
    <!-- #ifdef weapp -->
    <van-action-sheet
      :show="visible"
      :actions="shareActions"
      cancel-text="取消"
      :safe-area-inset-bottom="true"
      :close-on-click-overlay="true"
      @close="closeActionSheet"
      @cancel="closeActionSheet"
      @select="handleActionClick"
    ></van-action-sheet>
    <!-- #endif -->

    <!-- #ifdef web -->
    <van-share-sheet
      :show="visible"
      :options="shareActions"
      title="立即分享给好友"
      @close="closeActionSheet"
      @cancel="closeActionSheet"
      @select="handleActionClick"
    />
    <!-- #endif -->

    <!-- 海报 -->
    <van-popup custom-class="trade-share-popup" :show="showPoster">
      <view class="trade-share__poster__container">
        <view class="trade-share__poster_img_container">
          <!-- #ifdef weapp -->
          <image
            v-show="posterSrc"
            class="trade-share__poster_img"
            :src="posterSrc"
            mode="widthFix"
            @longpress="saveToLocal"
          />
          <image
            v-show="!posterSrc"
            class="trade-share__poster_img_placeholder"
            :src="previewSrc"
            mode="widthFix"
          />
          <!-- #endif -->

          <!-- #ifdef web -->
          <image v-if="posterSrc" :src="posterSrc" class="order-share__img" @click.stop />
          <image v-if="!posterSrc" class="order-share__img" :src="previewSrc" @click.stop />
          <!-- #endif -->
        </view>
        <!-- #ifdef weapp -->
        <view v-if="posterSrc" class="trade-share__poster_close_container" @click="closePoster">
          <view class="trade-share__poster_img_close"></view>
        </view>
        <!-- #endif -->

        <!-- #ifdef web -->
        <view v-if="posterSrc" class="order-share__close-container" @click="closePoster">
          <view class="order-share__img-close" />
        </view>
        <!-- #endif -->
      </view>
      <view v-if="posterSrc" class="trade-share__poster_word">长按图片保存至相册</view>
    </van-popup>
    <van-dialog ref="van-dialog-detail-share" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import ActionSheet from '@youzan/vant-tee/dist/action-sheet/index';
import Popup from '@youzan/vant-tee/dist/popup/index';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import VanDialog from '@youzan/vant-tee/dist/dialog/index';
import { ShareSheet } from '@vant/tee';
import { object, url } from '@youzan/tee-util';
import authorize, { addAnalyticsParams } from './util';
import { buildUrl, cdnImage, errorToast } from '@youzan/tee-biz-util';
import { requestV2 } from '@youzan/tee-biz-request';
import { getStorageSync, saveImageToPhotosAlbum, setClipboardData } from '@youzan/tee-api';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import fullfillImage from '@youzan/utils/url/fullfillImage';

let app = {};
const SHARE_CMPT_MAP = {
  menu: 'native_wechat', // 右上角
  button: 'native_custom', // 按钮
};
/* #ifdef weapp */
app = getApp();
/* #endif */

const callbackGenerator = (type, kdtId) => {
  return () => {
    this.ctx.logger.log({
      et: 'click',
      ei: 'share_result',
      en: '分享结果',
      params: {
        share_result: type,
      },
      si: kdtId,
    });
  };
};

const DOUYIN_ICON_URL = cdnImage('public_files/2019/04/11/1cff235c0af6904d68b56ad2685f8cd5.png');

const ALIPAY_URL = cdnImage('public_files/25f6dc47d89621f157a6acdffd4f7240.png');

const QQ_URL = cdnImage('public_files/3bf583f60492dab7cb0a14f34531d1aa.png');

export default {
  components: {
    'van-action-sheet': ActionSheet,
    'van-share-sheet': ShareSheet,
    'van-popup': Popup,
    'van-dialog': VanDialog,
  },
  data() {
    return {
      visible: false,
      showPoster: false, // 是否展示海报
      posterSrc: '', // 分享海报地址
      previewSrc: cdnImage('public_files/3e22c11fb1e0e5e4927a017fec1b565b.png'),
      isAllowShareOrder: false,
      isNewHotelGood: false,
      isMultiStore: false,
      orderNo: '',
      kdtId: 0,
      offlineId: 0,
      miniprogram: {},
      goodsList: [],
      orderBizUrl: {},
    };
  },
  computed: {
    shareActions() {
      let shareActions = [];
      /* #ifdef weapp */
      const { isAllowShareOrder, isNewHotelGood } = this;
      // 分享订单shareActions
      shareActions = [
        {
          key: 'sendFriend',
          name: '发送给朋友',
          openType: 'share',
        },
        {
          key: 'sharePoster',
          name: '生成海报',
        },
      ];
      if (isAllowShareOrder) {
        // 新酒店订单只保留sendFriend
        if (isNewHotelGood) {
          shareActions.pop();
        }
      }
      /* #endif */

      /* #ifdef web */
      const copyLink = {
        key: 'CopyLink',
        name: '复制链接',
        icon: 'link',
        className: 'js-copy',
      };
      const poster = { key: 'Haibao', name: '分享海报', icon: 'poster' };
      const weixin = { key: 'Wechat', name: '微信', icon: 'wechat' };
      const douyin = { key: 'Toutiao', name: '抖音', icon: DOUYIN_ICON_URL };
      const alipayApp = {
        key: 'Alipay',
        name: '分享到支付宝',
        icon: ALIPAY_URL,
      };
      const qqApp = { key: 'qq', name: '分享到QQ', icon: QQ_URL };
      const isTTApp = object.get(this.miniprogram, 'isTTApp', false);
      const isAlipayApp = object.get(this.miniprogram, 'isAlipayApp', false);
      const isQQApp = object.get(this.miniprogram, 'isQQApp', false);
      const isXhsApp = object.get(this.miniprogram, 'isXhsApp', false);
      const isKsApp = object.get(this.miniprogram, 'isKsApp', false);
      // 支付宝小程序
      if (isAlipayApp) {
        shareActions = [alipayApp, copyLink, poster];
      } else if (isQQApp) {
        // QQ 小程序
        shareActions = [qqApp, copyLink, poster];
      } else if (isXhsApp) {
        shareActions = [copyLink];
      } else if (this.isNewHotelGood) {
        // 新酒店商品过滤掉分享海报
        shareActions = [copyLink, weixin];
      } else if (isTTApp) {
        shareActions = [copyLink, douyin];
      } else if (isKsApp) {
        shareActions = [copyLink];
      } else {
        shareActions = [weixin, copyLink, poster];
      }
      /* #endif */
      return shareActions;
    },
    shareUrl() {
      let tmpUrl = addAnalyticsParams(
        this.ctx,
        url.args.add(buildUrl('/wsctrade/order/share', 'h5', this.kdtId), {
          order_no: this.orderNo,
          kdt_id: this.kdtId,
          show_share_guide: 1,
        })
      );
      if (this.isNewHotelGood) {
        tmpUrl = this.orderBizUrl.newHotelGoodsDetailUrl || '';
      }
      const defaultValue = buildUrl(
        `/wsctrade/order/share?kdt_id=${this.kdtId}1&order_no=${this.orderNo}&show_share_guide=1`,
        'h5'
      );
      return tmpUrl || defaultValue;
    },
    shareUrlNoGuide() {
      /* #ifdef web */
      // 去除show_share_guide参数
      if (!this.shareUrl) {
        return '';
      }
      const tmp = new URL(this.shareUrl);
      const param = url.args.getAll(tmp.href);
      delete param.show_share_guide;
      const baseUrl = this.shareUrl.replace(tmp.search, '');

      return buildUrl(url.args.add(baseUrl, param), '', this.kdtId);
      /* #endif */
    },
  },
  created() {
    mapData(this, [
      'isAllowShareOrder',
      'isNewHotelGood',
      'orderNo',
      'kdtId',
      'offlineId',
      'miniprogram',
      'isMultiStore',
      'goodsList',
      'orderBizUrl',
    ]);
    mapEvent(this, {
      showTradeShareDialog: () => {
        this.visible = true;
      },
    });

    this.ctx.process.define('getShareAppMessage', this.getShareAppMessage);
  },
  destroyed() {
    this.unwatchIsAllowShareOrder && this.unwatchIsAllowShareOrder();
    this.unwatchMiniprogram && this.unwatchMiniprogram();
    this.unwatchIsNewHotelGood && this.unwatchIsNewHotelGood();
    this.unwatchOrderNo && this.unwatchOrderNo();
    this.unwatchKdtId && this.unwatchKdtId();
    this.unwatchIsMultiStore && this.unwatchIsMultiStore();
    this.unwatchOfflineId && this.unwatchOfflineId();
    this.unwatchOrderBizUrl && this.unwatchOrderBizUrl();
    this.unwatchGoodsList && this.unwatchGoodsList();
    // this.ctx.event.remove('hideTradeShareDialog');
  },
  methods: {
    // 关闭上拉菜单
    closeActionSheet() {
      this.visible = false;
    },
    // 点击菜单
    handleActionClick(item) {
      const { key } = item;

      /* #ifdef weapp */
      switch (key) {
        case 'sendFriend':
          this.closeActionSheet();
          break;
        case 'sharePoster':
          this.closeActionSheet();
          this.doGetPoster(true);
          break;
        default:
          break;
      }
      /* #endif */

      /* #ifdef web */
      if (
        item.key === 'Wechat' ||
        item.key === 'Toutiao' ||
        item.key === 'Alipay' ||
        item.key === 'qq'
      ) {
        this.closeActionSheet();
        if (this.shareUrl) {
          this.ctx.logger.log({ ei: 'click_window_friend', en: '点击分享好友' });
          // 肯定到这里
          // 设置自动打开分享提示的开关然后跳转分享落地页面
          const tmp = new URL(this.shareUrl);
          const loggerData = this.ctx.logger.getLogParams();
          const context = (loggerData && object.get(loggerData, 'context')) || {};

          // const { kdtId: newKdtId = 0, kdt_id: oldKdtId = 0 } = window._global;
          // const kdtId = newKdtId || oldKdtId;
          const baseUrl = this.shareUrl.replace(tmp.search, '');
          const params = {
            ...url.args.getAll(tmp.href),
            dc_ps: context.dc_ps || '',
            from_source: context.from_source || '',
            from_params: context.from_params || '',
            is_share: 1, // 分享标识
            show_share_guide: 1,
          };
          const newUrl = buildUrl(url.args.add(baseUrl, params), '', this.kdtId);
          // 延时跳转防止埋点发不出去
          setTimeout(() => {
            Tee.navigate({
              url: newUrl,
              type: 'redirectTo',
            });
          }, 100);
        } else {
          // 在当前页面显示分享提示
          // 有默认值，肯定不会到这里的
          // guideShare();
        }
      } else if (item.key === 'Haibao') {
        this.ctx.logger.log({ ei: 'click_window_bill', en: '点击海报' });
        this.closeActionSheet();
        this.doGetPoster(true);
      } else if (item.key === 'CopyLink') {
        this.ctx.logger.log({ ei: 'click_window_copy', en: '点击复制链接' });
        setClipboardData(this.shareUrlNoGuide).then(() => {
          Toast({
            message: '复制成功',
            duration: 800,
          });
        });
      }
      /* #endif */
    },
    // 关闭海报
    closePoster() {
      this.showPoster = false;
    },
    // 获取海报
    doGetPoster(needRetry = false, retry = 0) {
      this.showPoster = true;
      if (this.posterSrc) {
        return;
      }

      Toast.loading({
        message: '正在生成',
        // 持续时间最长为10s
        duration: 10000,
      });

      /* #ifdef web */
      requestV2({
        url: '/wsctrade/poster/order-share.json',
        method: 'GET',
        data: {
          order_no: this.orderNo,
          url: this.shareUrlNoGuide,
        },
      })
        .then((res) => {
          Toast.clear();
          // this.showPoster = true;
          this.posterSrc = fullfillImage(res, 'large');
        })
        .catch((err) => {
          if (needRetry) {
            setTimeout(() => {
              this.doGetPoster(false, 1);
            }, 1000);
          } else {
            Toast.clear();
            errorToast(err, { message: '生成海报失败，请稍后再试' });
          }
        });

      /* #endif */

      /* #ifdef weapp */
      const { isRetailApp } = app.globalData;
      const guestKdtId = app.getKdtId();
      const _kdtId = isRetailApp ? app.getHQKdtId() : app.getKdtId();
      const kdtId = (app.getAppKdtId && app.getAppKdtId()) || _kdtId;
      const path = '/packages/order/share-page/index';
      const offlineId = app.getOfflineId();
      //  const offlineId = this.offlineId;

      const dcPs = getStorageSync('logv3:dc_ps') || '';

      const scene = {
        page: path,
        guestKdtId,
        kdtId,
        dcPs,
        offlineId,
        order_no: this.orderNo,
      };

      requestV2({
        path: '/wsctrade/poster/order-share.json',
        data: {
          retry,
          kdtId: guestKdtId,
          order_no: this.orderNo,
          pageType: 'goods',
          page: 'pages/common/blank-page/index',
          scene,
        },
      })
        .then((res) => {
          Toast.clear();
          this.posterSrc = res;
        })
        .catch((response) => {
          const { msg } = response?.data || {};
          Toast.clear();
          if (needRetry) {
            setTimeout(() => {
              this.doGetPoster(false, 1);
            }, 1000);
          } else {
            Dialog.alert({
              title: msg || '未知异常',
              message: this.orderNo,
              ref: this.$refs['van-dialog-detail-share'],
            });
          }
        });
      /* #endif */
    },
    // 图片保存到本地
    saveToLocal() {
      /* #ifdef web */
      return;
      /* #endif */

      /* #ifdef weapp */
      // eslint-disable-next-line no-unreachable
      const { posterSrc } = this;
      if (!posterSrc) return;

      Toast.loading({ message: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(posterSrc)
            .then(() => {
              Toast.clear();
              Toast({
                message: '保存成功',
                type: 'success',
                duration: 2000,
              });
              this.closePoster();
            })
            .catch((_err) => {
              Toast.clear();
              Toast({
                message: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          Toast.clear();
          Toast({
            message: '请允许访问相册后重试',
            icon: 'none',
            duration: 1000,
          });
          setTimeout(() => {
            Tee.$native.openSetting({
              success: ({ authSetting }) => {
                if (authSetting['scope.writePhotosAlbum']) {
                  this.saveToLocal();
                }
              },
            });
          }, 1000);
        });
      /* #endif */
    },
    // 图文卡片保存
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        app.downloadFile({
          url: tempFilePath,
          success(res) {
            if (res.statusCode === 200) {
              saveImageToPhotosAlbum(res.tempFilePath).then(resolve).catch(reject);
            } else {
              reject();
            }
          },
          fail: reject,
        });
      });
    },

    getShareAppMessage(shareFrom) {
      const { isNewHotelGood, orderNo, kdtId, orderBizUrl, goodsList } = this;
      let nameMaxlen = 0;
      let message = '';
      let subTitle = '';
      if (goodsList.length > 1) {
        nameMaxlen = 14;
        message = '等，必须推荐给你';
      } else {
        nameMaxlen = 15;
        message = '，必须推荐给你';
      }
      if (goodsList[0].title.length > nameMaxlen) {
        subTitle = goodsList[0].title.substr(0, nameMaxlen) + '...等' || '一些东西';
      } else {
        subTitle = goodsList[0].title || '一些东西';
      }

      let path = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        `/packages/order/share-page/index?order_no=${orderNo}&kdt_id=${kdtId}&is_share=1`
      )}`;

      if (isNewHotelGood) {
        path = `/pages/common/webview-page/index?src=${encodeURIComponent(
          orderBizUrl.newHotelGoodsDetailUrl
        )}`;
      }

      const shareData = {
        title: `我买了${subTitle}${message}`,
        imageUrl: goodsList[0] ? goodsList[0].thumb : '',
        path,
      };

      // 获取分享来源
      const shareCmpt = SHARE_CMPT_MAP[shareFrom];
      // 拼装额外分享参数
      let sharePath = shareData.path || '/packages/home/<USER>/index';
      const allQueryData = url.args.getAll(sharePath);
      const { ban_default_kdt_id: banDefaultKdtId } = allQueryData;
      const queryData = {
        ...allQueryData,
        is_share: 1,
        share_cmpt: shareCmpt,
        shopAutoEnter: 1,
      };

      // 增加 kdt_id
      if (banDefaultKdtId === 'true') {
        queryData.kdt_id = allQueryData.kdt_id || this.kdtId || '';
      } else {
        queryData.kdt_id = this.kdtId || '';
      }

      // if (banDefaultKdtId === 'true') {
      //   queryData.kdt_id = allQueryData.kdt_id || app.getKdtId() || '';
      // } else {
      //   queryData.kdt_id = app.getKdtId() || '';
      // }

      // 增加 dc_ps 追踪
      // 增加埋点数据
      const logGlobalInfo = app.logger.getGlobal() || {};
      const contextInfo = logGlobalInfo.context || {};
      const userInfo = logGlobalInfo.user || {};

      if (contextInfo.dc_ps) {
        queryData.dc_ps = contextInfo.dc_ps || '';
      }

      if (userInfo.uuid) {
        queryData.from_uuid = userInfo.uuid || '';
      }

      // 门店id
      if (this.isMultiStore) {
        queryData.offlineId = this.offlineId;
      }

      // 门店id
      // if (app.getShopInfoSync().isMultiStore) {
      //   queryData.offlineId = app.getOfflineId();
      // }

      // 拼接分享query
      sharePath = url.args.add(sharePath, queryData);

      // 分享页面都通过 blank-page 来进行中转
      const newSharePath = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        sharePath
      )}`;
      const finalShareData = {
        ...shareData,
        path: newSharePath,
        success: callbackGenerator('success', this.kdtId),
        fail: callbackGenerator('fail', this.kdtId),
      };

      return finalShareData;
    },
  },
};
</script>

<style lang="scss">
.trade-share .tee-image {
  border-radius: 10rpx;
}
.trade-share-popup {
  overflow: visible !important;
  background: none !important;
  border-radius: 10rpx !important;
}
.trade-share__poster {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: flex;
  flex-flow: column center;
  justify-content: center;
  align-items: center;
}

.trade-share__poster_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.trade-share__poster__container {
  position: relative;
  border-radius: 10rpx;
  width: 64vw;
}

.trade-share__poster_img_container {
  position: relative;
  max-height: 76vh;
  overflow-y: scroll;
  width: 100%;
  border-radius: 10rpx;
}

.trade-share__poster_img {
  border-radius: 10rpx;
  width: 64vw;
  overflow: hidden;

  &--web {
    width: 240px;
    border-radius: 5px;
  }
}

.trade-share__poster_img_placeholder {
  height: 50vh;
  border-radius: 10rpx;
  width: 64vw;
  overflow: hidden;
}

.trade-share__poster_close_container {
  position: absolute;
  z-index: 100;
  right: -18px;
  top: -50px;
  height: 60px;
  width: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.trade-share__poster_img_close {
  height: 22px;
  width: 22px;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.trade-share__poster_img_close::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.trade-share__poster_img_close::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.trade-share__poster_img_close::before,
.trade-share__poster_img_close::after {
  content: '';
  width: 12px;
  height: 1px;
  background: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
}

.trade-share__poster_word {
  font-size: 14px;
  z-index: 100;
  color: white;
  padding-top: 20px;
  text-align: center;
  position: relative;
}

.order-share {
  &__img {
    width: 100%;
    border-radius: 5px;
  }

  &__close-container {
    position: absolute;
    right: -18px;
    top: -50px;
    z-index: 100;
    height: 60px;
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__img-close {
    height: 22px;
    width: 22px;
    border: 1px solid #fff;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &__img-close::after,
  &__img-close::before {
    content: '';
    width: 12px;
    height: 1px;
    background: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
  }

  &__img-close::after {
    transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
  }

  &__img-close::before {
    transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
  }

  @media only screen and (min-width: 375px) {
    // &__img {
    //   width: 300px;
    // }

    &__img-word {
      font-size: 14px;
    }
  }
}
</style>
