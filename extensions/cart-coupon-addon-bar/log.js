export default function ({ buyerId: BUYER_ID, Logger }) {
  let logGoodsExpo = (goodsId) => {
    Logger.log({
      et: 'view', // 事件类型
      ei: 'recommended_goods_view', // 事件标识
      en: '推荐商品曝光', // 事件名称
      params: {
        buyer_id: BUYER_ID,
        goods_id: goodsId,
        component: 'cart_coupons_component',
      }, // 事件参数
    });
  };

  let goodsUniqueMap = {};
  return {
    destroy() {
      logGoodsExpo = null;
      goodsUniqueMap = null;
    },
    logGatherBarExpo() {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'gather_bar_view', // 事件标识
        en: '凑单条曝光', // 事件名称
        pt: 'cart', // 页面类型
        params: {
          buyer_id: BUYER_ID,
        }, // 事件参数
      });
    },
    logGatherBarClick(type) {
      Logger.log({
        et: 'click', // 事件类型
        ei: 'gather_click', // 事件标识
        en: '凑单按钮点击', // 事件名称
        pt: 'cart', // 页面类型
        params: {
          buyer_id: BUYER_ID,
          ooperation_name: type,
        },
      });
    },
    logCouponGetClick({ couponId, activityId }) {
      Logger.log({
        et: 'click', // 事件类型
        ei: 'coupon_redemption_click', // 事件标识
        en: '立即领取点击', // 事件名称
        params: {
          buyer_id: BUYER_ID,
          coupon_id: couponId,
          activity_id: activityId,
          source: 'cart',
          component: 'cart_coupons_component',
        }, // 事件参数
      });
    },
    logGatherPopupExpo() {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'cart_coupons_component_view', // 事件标识
        en: '购物车优惠券凑单面板曝光', // 事件名称
        params: {
          buyer_id: BUYER_ID,
          component: 'cart_coupons_component',
        },
      });
    },
    logCouponGetExpo(activityId) {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'coupon_redemption_view', // 事件标识
        en: '立即领取曝光', // 事件名称
        params: {
          buyer_id: BUYER_ID,
          component: 'cart_coupons_component',
          activity_id: activityId,
        }, // 事件参数
      });
    },
    logGoAddonBtnExpo(activityId) {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'gather_view', // 事件标识
        en: '去凑单按钮曝光', // 事件名称
        params: {
          activity_id: activityId,
          buyer_id: BUYER_ID,
          component: 'cart_coupons_component',
        },
      });
    },
    logGoAddonBtnClick(activityId) {
      Logger.log({
        et: 'click', // 事件类型
        ei: 'gather_click', // 事件标识
        en: '去凑单按钮点击', // 事件名称
        params: {
          activity_id: activityId,
          buyer_id: BUYER_ID,
          component: 'cart_coupons_component',
        },
      });
    },
    logGoodsExpo,
    batchLogGoodsExpo(addOnVoucherDetailDTOList = []) {
      addOnVoucherDetailDTOList.forEach?.(({ allAbleCartGoods = [] }) => {
        allAbleCartGoods.forEach?.(({ goodsId, skuId }) => {
          const id = `${goodsId}_${skuId}`;
          if (!goodsUniqueMap[id]) {
            goodsUniqueMap[id] = 1;
            logGoodsExpo(goodsId);
          }
        });
      });
      goodsUniqueMap = {};
    },
    logGoodsClick({ goodsId, activityId, type }) {
      Logger.log({
        et: 'click', // 事件类型
        ei: 'recommended_goods_click', // 事件标识
        en: '推荐商品点击', // 事件名称
        params: {
          activity_id: activityId,
          buyer_id: BUYER_ID,
          goods_id: goodsId,
          component: 'cart_coupons_component',
          type,
        }, // 事件参数
      });
    },
    logGoodsDecreasedToastExpo() {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'toast_view', // 事件标识
        en: '互斥toast提示曝光', // 事件名称
        params: {
          buyer_id: BUYER_ID,
          component: 'cart_coupons_component',
        },
      });
    },
  };
}
