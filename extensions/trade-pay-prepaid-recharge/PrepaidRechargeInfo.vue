<template>
  <view v-if="rechargeInfo" :style="style">
    <van-cell :border="false" center>
      <view class="info-container">
        <view class="title">随单充值</view>
        <view class="right" @click="jumpTo">
          <view class="value">{{ value }}</view>
          <van-icon v-if="rechargeInfo.jump" class="arrow" name="arrow" />
        </view>
      </view>
    </van-cell>
  </view>
</template>

<script>
import { Cell, Icon } from '@vant/tee';
import { mapData, getPlugins } from '@youzan/ranta-helper-tee';
import formatMoney from '@youzan/utils/money/format';

export default {
  components: {
    'van-cell': Cell,
    'van-icon': Icon,
  },
  props: {
    margin: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      rechargeInfo: null,
      themeCSS: {},
      kdtId: 0,
    };
  },
  computed: {
    value() {
      return `¥${this.rechargeInfo ? formatMoney(this.rechargeInfo.payAmount) : 0}`;
    },
    style() {
      return `margin:${this.margin};${this.themeCSS}`;
    },
  },
  created() {
    mapData(this, ['rechargeInfo', 'kdtId', 'themeCSS']);
  },
  methods: {
    jumpTo() {
      getPlugins().dmc.navigate('RechargeHome', {
        kdt_id: this.kdtId,
        redirectPage: 'record',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info-container {
  display: flex;
  justify-content: space-between;

  .right {
    display: flex;
    align-items: center;
  }
  .title {
    color: #969799;
  }
  .arrow {
    margin-left: 8px;
  }
  .value {
    color: var(--icon, #323233);
    font-weight: 700;
  }
}
</style>
