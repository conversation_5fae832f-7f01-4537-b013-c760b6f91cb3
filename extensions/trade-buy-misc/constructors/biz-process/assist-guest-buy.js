import { isEmpty as isEmptyObject } from '@youzan/tee-util/lib/helper';
import { url } from '@youzan/tee-util';

const { args } = url;
let hasSetBehalfOrderAddress = false;

function getGuestAddress() {
  try {
    const guideBehalfOrderDeliveryJson =
      _global.prepare?.orderCreation?.extensions?.DAOGOU_BEHALF_DELIVERY || '{}';
    // const guideBehalfOrderDeliveryJson = '{"addressDetail":"不存在的地址啦啦啦","areaCode":"330106","city":"杭州市","community":"","country":"中国","countryType":1,"county":"西湖区","houseNumber":"","isDefault":0,"label":"","lat":"30.26627924764097","lon":"120.13686419040977","poiId":"B023B00850","poiType":3,"postalCode":"","province":"浙江省","source":1,"tel":"15658829999","type":2,"userId":6366605,"userName":"张广荣","recipients":"张广荣"}';
    const guideBehalfOrderDelivery = JSON.parse(guideBehalfOrderDeliveryJson);

    const guideBehalfOrderAddress = guideBehalfOrderDelivery?.address || {};

    if (!isEmptyObject(guideBehalfOrderAddress)) {
      const {
        addressDetail,
        areaCode,
        city,
        county,
        isDefault = 0,
        recipients: name,
        province,
        source = 1,
        tel,
        type,
        recipients: userName,
      } = guideBehalfOrderAddress;

      if (!addressDetail) throw new Error('缺失详细地址');

      return {
        addressDetail,
        areaCode,
        city,
        county,
        isDefault,
        name,
        province,
        source,
        tel,
        type,
        userName,
      };
    }
    return {};
  } catch (error) {
    console.error('代客下单 - 客户确认订单地址获取失败', error);
    return {};
  }
}

// 设置代客下单默认地址
export function setGuestAddress(state, options = { isConfirm: false }) {
  // 代客下单特判
  const { isConfirm } = options;
  if (
    !isConfirm &&
    !hasSetBehalfOrderAddress &&
    args.get('type', window.location.href) === 'behalf-order'
  ) {
    // 选择其他地址 || 新增地址 || 修改地址并使用 等多种行为都会处理 setGuestAddress 方法
    // 为保证该逻辑只在页面进来初始化执行，因此使用 hasSetBehalfOrderAddress 来标记
    hasSetBehalfOrderAddress = true;
    const guestAddress = getGuestAddress();
    if (!isEmptyObject(guestAddress)) {
      state.address.inlineForm = guestAddress;
    }
  }
  return state;
}
