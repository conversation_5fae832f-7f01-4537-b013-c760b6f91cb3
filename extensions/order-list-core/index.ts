/* #ifdef web */
import OrderSharePopup from './widgets/OrderSharePopup.vue';
import { ZNB } from '@youzan/tee-biz-navigate';
/* #endif */

/* #ifdef weapp */
import skynet from '@youzan/wsc-tee-trade-common/lib/utils/skynet';
import Event from '@youzan/weapp-utils/lib/event';
/* #endif */

// Widgets
import Main from './Main.vue';
import SearchBar from './widgets/SearchBar.vue';
import TopNotice from './widgets/TopNotice.vue';
import CpsGoodsOrder from './widgets/CpsGoodsOrder.vue';
import SelfFetchPopup from './widgets/SelfFetchPopup.vue';
import FailGoodsDialog from './widgets/FailGoodsDialog.vue';
import OrderListTabsWidget from './widgets/OrderListTabs.vue';
import OrderCancelPopup from './widgets/OrderCancelPopup.vue';
import TopNoticeBindPhone from './widgets/TopNoticeBindPhone.vue';
import TopNoticeOtherOrder from './widgets/TopNoticeOtherOrder.vue';
import ListEmptyTip from './widgets/ListEmptyTip.vue';
import ListItemHeader from './widgets/ListItemHeader.vue';
import ListItemFooter from './widgets/ListItemFooter.vue';
import OrderListItem from './widgets/OrderListItem.vue';
import ListItemBody from './widgets/ListItemBody.vue';
import ListItemTag from './widgets/ListItemTag.vue';
import WxvideoGuidePopup from './widgets/WxvideoGuidePopup.vue';
import ListItem from './widgets/ListItem.vue';
import OrderList from './widgets/OrderList.vue';
import OrderListWrapper from './widgets/OrderListWrapper.vue';
import WelikeEntryWrapper from './widgets/WelikeEntryWrapper.vue';

// Utils
import { storeModules } from './store';
import {
  CPS_CONFIG_KEY,
  DEFAULT_TAB_LIST,
  getDirectBuyAgainBtnConfig,
  getPointsName,
} from './utils/constants';
import type {
  BeforeOrderBtnClickAsync,
  BtnClickHandleParams,
  BtnClickHandleParamsWeapp,
  CloudBtnListCondition,
  Query,
  SetGlobalBtnsDisplayParams,
} from './extension';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';

import type {
  OrderListTabs,
  OrderListInfo,
  SwitchOrderListTabParams,
  RefreshOrderInfoParams,
  RefreshOrderListParams,
  OrderListBeforeOrderActionHandle,
  OrderListBeforeCancelOrderPopupToggle,
  OrderListBeforeOrderBtnsRender,
  OrderListHandleOrderActionParams,
} from '@youzan-cloud/cloud-biz-types';

// Dependencies
import get from '@youzan/utils/object/get';
import { checkEduShop } from '@youzan/utils-shop';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';
import { bridge, cloud, cloudHook } from '@youzan/ranta-helper';
import {
  setNavigationBarTitle,
  getStorageSync,
  removeStorage,
  stopPullDownRefresh,
} from '@youzan/tee-api';
import { createStore } from '@youzan/wsc-tee-trade-common/lib/store';
import { actionTypeConfig, activeTabConfig, cloudData, isSameObject, yunBtnTypeMap } from './utils';

export default class Extension {
  static widgets = {
    Main,
    SearchBar,
    CpsGoodsOrder,
    OrderListTabs: OrderListTabsWidget,
    SelfFetchPopup,
    FailGoodsDialog,
    OrderCancelPopup,
    TopNotice,
    TopNoticeBindPhone,
    TopNoticeOtherOrder,
    ListEmptyTip,
    ListItemHeader,
    ListItemFooter,
    OrderListItem,
    ListItemBody,
    ListItemTag,
    WxvideoGuidePopup,
    ListItem,
    OrderList,
    OrderListWrapper,
    WelikeEntryWrapper,
    /* #ifdef web */
    OrderSharePopup,
    /* #endif */
  };

  ctx: any;

  store: any;

  isPageMounted = false;

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.scene = options.scene;
    this.store = createStore(this.ctx, storeModules);

    // Init Ctx Data
    getPointsName().then((pointsName) => {
      this.store.SET_POINTS_NAME(pointsName);
    });
    this.ctx.data.cpsConfigKey = CPS_CONFIG_KEY;
    this.ctx.data.bizName = 'order_list';

    // 控制推荐商品是否展示
    this.ctx.data.isControlRecommendShow = true;

    mapCtxData(this, {
      kdtId: 'kdtId',
      shopMetaInfo: 'shop',
      yunDesignConfig: 'yunDesignConfig',
      topNavHeight: 'topNavHeight',
      isShowRetailOrderList: 'isShowRetailOrderList',
    });
    mapStoreToCtx(this, [
      'shopMetaInfo',
      'isRetailShop',
      'isIvr',
      'isKsApp',
      'isXhsApp',
      'isAlipayApp',
      'isQQApp',
      'isEduShop',
      'isDrugShop',
      'kdtId',
      'bizName',
      'activityTab',
      'openAppConfig',
      'searchCondition',
      'showRecommend',
      'orderMark',
      'pointsName',
      'pageType',
      'query',
      'userInfo',
    ]);

    // Init Ctx Process
    mapProcess(this, {
      fetchMoreOrder: this.store.fetchMoreOrder,
      setQuery: this.store.SET_QUERY,
      setActivityTab: this.store.setActivityTab,
      refreshOrderList: this.store.refreshList,
      doOrderFooterAction: this.store.doOrderFooterAction,
      handleBuyAgainFailGoods: this.store.handleBuyAgainFailGoods,
      activityTabChange: this.store.activityTabChange,
      gotoOrderDetail: this.store.gotoOrderDetail,
      gotoOrderSearch: this.store.gotoOrderSearch,
    });

    this.ctx.lambdas?.getUserInfo?.().then((userInfo) => {
      this.store.SET_USER_INFO(userInfo);
    });

    getDirectBuyAgainBtnConfig().then((directBuyAgainBtnConfig) => {
      this.store.SET_DIRECT_BUY_AGAIN_BTN_CONFIG(directBuyAgainBtnConfig);
    });

    /* #ifdef web */
    window.addEventListener('pageshow', function (e) {
      if (e.persisted) {
        window.location.reload();
      }
    });
    /* #endif */

    this.initCloudData();

    /* #ifdef weapp */
    this.onShowListener = this.onShowListener.bind(this);
    this.initEvent();
    /* #endif */
  }

  beforePageMount({ query }: { query: Query }) {
    /**
     * NOTICE: 对于 isPageMounted 的说明
     * 订单列表页可能会作为tabbar页面，装修对于tabbar的实现是将页面当做一个组件，然后装修的tabbar会主动触发生命周期
     * 这样会导致beforePageMount执行两次
     * 第一次：装修主动触发
     * 第二次：ranta框架回放触发（因为在extension.json中设置了需要触发这个生命周期）
     * 导致的结果是：当订单列表作为一个tabbar页面时，首次加载会出现重复订单的情况
     * 因此通过 isPageMounted 判断是否需要拦截第二次重复的 beforePageMount
     */
    if (this.isPageMounted) return;
    this.isPageMounted = true;

    setNavigationBarTitle('订单列表');
    const { kdtId } = this.store;
    /* #ifdef web */
    // @ts-ignore
    ZNB.init({ kdtId });
    /* #endif */

    const oldKdtId = getStorageSync('order-list:updateKdtId:oldKdtId');
    if (oldKdtId) {
      removeStorage('order-list:updateKdtId:oldKdtId');
      this.store.updateKdtId(oldKdtId);
    }

    // 页面初始化，透出一些初始化数据，允许不同业务方修改
    this.ctx.process
      .invokePipe('beforeSetup', {
        orderListTabOpt: {
          isShow: true,
          tabs: DEFAULT_TAB_LIST,
        },
        searchBarOpt: {
          isShow: true,
        },
      })
      .then(({ orderListTabOpt, searchBarOpt }) => {
        // TODO: 这个splice以后应该拆到 edu-order-list 里面
        // TODO: 感觉好像有时序问题
        if (checkEduShop(this.ctx.data.shopMetaInfo)) {
          orderListTabOpt.tabs.splice(2, 0, {
            type: 'going',
            text: '已付款',
          });
        }
        this.store.SET_ORDER_LIST_TAB_OPT(orderListTabOpt);
        this.store.SET_SEARCH_BAR_OPT(searchBarOpt);

        this.store.resetTab();
        const defaultType = get(this, 'store.orderListTabOpt.tabs[0].type', '');
        this.store.SET_QUERY({ type: defaultType, ...query });
        this.store.refreshList();
      });
  }

  onPullDownRefresh() {
    this.store.setActivityTab({ ...this.store.activityTab, page: 0, hasNext: true });
    this.store.refreshList();
    stopPullDownRefresh();
  }

  /* ****************************************************************************************
   * 这里的作用是为了在weapp环境中，订单列表跳转到其他页面，完成操作后更新订单列表对应的订单信息
   * 例如：订单列表 -> 跳评价页面 -> 完成评价 -> 返回订单列表 -> 更新对应订单信息让[立即评价]按钮隐藏
   * 使用onpageshow并不是最好的方法
   * 他的好处是：逻辑全部在订单列表，其他页面不需要做任何处理
   * 他的缺点是：由于onpageshow触发时机比较滞后，会让用户感受到订单信息的变化，也就是按钮会从有到没有的过程
   * !如果从用户体验角度考虑，更好的方式是通过event在其他页面完成操作后通知订单列表更新对应订单信息，但是这种
   * !做法的缺点是以后如果其他页面做了重构，可能会丢失event的触发，导致订单列表信息不更新
   ***************************************************************************************** */
  onPageShow() {
    /* #ifdef weapp */
    // 当页面重新展示时判断下是从哪个订单跳走的
    const { orderNo } = getStorageSync('order-list:processOrder') || {};
    if (orderNo) {
      removeStorage('order-list:processOrder');
      this.store.refreshOrderInfo({ orderNo });
    }
    /* #endif */
  }

  /* #ifdef weapp */
  onShowListener(options = {}) {
    // @ts-ignore
    const { extraData } = options.referrerInfo || {};
    const { status, req_extradata: reqExtradata = {} } = extraData || {};
    if (extraData) {
      skynet.info('[确认收货组件-callback]', options);
    }
    // 微信确认收货组件回调参数
    // status可能值: success(确认收货成功)、fail(确认收货失败)、cancel(关闭组件)
    if (status && status !== 'cancel' && reqExtradata.transaction_id) {
      // 微信侧确认收货不管是否成功，都不影响有赞侧确认收货
      this.store.confirmReceiveAction({
        btn: { value: 'confirmReceive' },
        isShowConfirm: status === 'fail',
      });
    }
  }

  initEvent() {
    Event.on('onShow', this.onShowListener);
  }

  pageDestroyed() {
    Event.off('onShow', this.onShowListener);
  }
  /* #endif */

  initCloudData() {
    /* #ifdef weapp */
    mapData(
      this,
      {
        yunDesignConfig: (yunDesignConfig) => {
          this.cloudOrderListExt = cloudData.cloudOrderListExtV1(yunDesignConfig);
        },
      },
      {
        isSetData: false,
      }
    );
    /* #endif */
    this.store.watch(
      'activityTab',
      () => {
        const { activityTab } = this.store;
        const newOrderListTabs = cloudData.getOrderListTabs({
          orderListTabOpt: this.store.orderListTabOpt,
          activityTab,
        });
        const hasSelectedTab = newOrderListTabs.tabs.some(({ isSelected }) => isSelected);
        const newSelectedTab = cloudData.selectedTabV1({ activityTab });
        if (!isSameObject(this.selectedTab, newSelectedTab)) {
          this.selectedTab = newSelectedTab;
        }
        if (!isSameObject(this.orderListTabs, newOrderListTabs) && hasSelectedTab) {
          this.orderListTabs = newOrderListTabs;
        }
      },
      { immediate: true }
    );
    this.store.watch(
      'orderListCount',
      () => {
        const { activityTab, orderList } = this.store;
        const newOrderListInfo = cloudData.getOrderListInfo({ activityTab, orderList });
        if (!isSameObject(this.orderListInfo, newOrderListInfo)) {
          this.orderListInfo = newOrderListInfo;
        }
      },
      { immediate: true }
    );
  }

  @cloud('orderListTabs', 'data')
  orderListTabs: OrderListTabs;

  @cloud('orderListInfo', 'data')
  orderListInfo: OrderListInfo;

  @cloud('handleOrderAction', 'method')
  handleOrderAction(params: OrderListHandleOrderActionParams) {
    const { type, orderNo } = params;

    const { orderList } = this.store;
    const listIndex = (orderList || []).findIndex((item) => orderNo === item.orderNo);
    const listItem = (orderList || [])[listIndex];

    if (!listItem) {
      throw new Error('找不到对应的订单');
    }

    // 处理buyAgain的特殊逻辑，因为我们没有让三方感知到directBuyAgain
    const btn = ((listItem || {}).btnList || []).find((item) => {
      if (type === 'buyAgain') {
        return ['buyAgain', 'directBuyAgain'].includes(item.value);
      }
      if (actionTypeConfig.getValueByKey(type)) {
        return item.value === actionTypeConfig.getValueByKey(type);
      }
      return item.value === type;
    });

    if (!btn) {
      throw new Error('对应的操作不存在');
    }

    this.store.doOrderFooterAction({
      btn,
      listIndex,
      listItem,
    });
  }

  @cloud('switchOrderListTab', 'method')
  switchOrderListTab(params: SwitchOrderListTabParams) {
    const type = activeTabConfig.getValueByKey(params.type);
    const tab = this.store.orderListTabOpt.tabs.find((item) => item.type === type);
    if (!tab) {
      throw new Error('找不到对应的tab');
    }
    this.store.activityTabChange(tab);
  }

  @cloud('refreshOrderInfo', 'method')
  refreshOrderInfo(params: RefreshOrderInfoParams) {
    const { orderNo } = params;
    this.store.refreshOrderInfo({ orderNo });
  }

  @cloud('refreshOrderList', 'method')
  refreshOrderList(params: RefreshOrderListParams = {}) {
    const { keyword } = params;
    if (keyword) {
      this.store.SET_QUERY({ ...this.store.query, keyword });
    }
    this.store.refreshList();
  }

  @cloud('fetchOrderList', 'method')
  fetchOrderList() {
    this.store.fetchMoreOrder(this.store.activityTab.type);
  }

  @cloud('beforeOrderActionHandle', 'hook', { allowMultiple: true })
  beforeOrderActionHandle = cloudHook<OrderListBeforeOrderActionHandle>();

  @cloud('beforeCancelOrderPopupToggle', 'hook', { allowMultiple: false })
  beforeCancelOrderPopupToggle = cloudHook<OrderListBeforeCancelOrderPopupToggle>();

  @cloud('beforeOrderBtnsRender', 'hook', { allowMultiple: false })
  beforeOrderBtnsRender = cloudHook<OrderListBeforeOrderBtnsRender>();

  @bridge('btnClickHandle', 'process')
  btnClickHandle(params: BtnClickHandleParams | BtnClickHandleParamsWeapp) {
    const { listIndex } = params;
    const { orderList } = this.store;
    let _type = '';
    /* #ifdef web */
    _type = params.type;
    /* #endif */
    /* #ifdef weapp */
    _type = (params as BtnClickHandleParamsWeapp).value;
    /* #endif */
    const type = yunBtnTypeMap.getValueByKey(_type);

    const listItem = orderList[listIndex];

    if (!listItem) {
      throw new Error('找不到对应的订单');
    }
    const btn = ((listItem || {}).btnList || []).find((item) => item.value === type);
    if (!btn) {
      throw new Error('对应的操作不存在');
    }
    this.store.doOrderFooterAction({
      btn,
      listIndex,
      listItem,
    });
  }

  @bridge('updateConfirmReceiveText', 'process')
  updateConfirmReceiveText(message: string) {
    this.store.setReceivingContent(message);
  }

  @bridge('addButtonList', 'process')
  addButtonList(cloudBtnListCondition: CloudBtnListCondition) {
    this.store.setCloudBtnListCondition(cloudBtnListCondition);
    const {
      condition: { filterOrderStatus = [], activityTypes = [] },
      cloudBtnList = [],
    } = cloudBtnListCondition;

    const list = this.store.orderList.map((listItem) => {
      const isMatchedOrderStatus = !filterOrderStatus.includes(listItem.status);
      const isMatchedActivityType =
        !activityTypes.length || listItem.find((v) => activityTypes.includes(+v.activity_type));
      if (isMatchedOrderStatus && isMatchedActivityType) {
        listItem.btnList = listItem.btnList.concat(cloudBtnList);
      }
      return listItem;
    });
    this.store.setActivityTab({
      ...this.store.activityTab,
      list,
    });
  }

  @bridge('beforeOrderBtnClickAsync', 'asyncEvent')
  beforeOrderBtnClickAsync = cloudHook<BeforeOrderBtnClickAsync>();

  /* #ifdef weapp */
  @bridge('selectedTab', 'data')
  selectedTab: string;

  @bridge('cloudOrderListExt', 'data')
  cloudOrderListExt: Record<string, any>;

  @bridge('setGlobalBtnsDisplay', 'process')
  setGlobalBtnsDisplay(params: SetGlobalBtnsDisplayParams) {
    const hideButtonTypes = params.type && Array.isArray(params.type) ? params.type : [];
    if (params.confirmReceiveHotel === false) {
      hideButtonTypes.push('confirmReceiveHotel');
    }
    this.store.yunConfig.hideButtonTypes = hideButtonTypes.map((type) => {
      return yunBtnTypeMap.getValueByKey(type);
    });
    const list = this.store.orderList.map((listItem) => {
      listItem.btnList = listItem.btnList.filter(
        (btn) => !this.store.yunConfig.hideButtonTypes.includes(btn.value)
      );
      return listItem;
    });
    this.store.setActivityTab({
      ...this.store.activityTab,
      list,
    });
  }
  /* #endif */
}
