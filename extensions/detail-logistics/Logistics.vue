<template>
  <view>
    <van-cell-group v-if="showLogisticsInfo && logistics && isShowLogistics" :border="false">
      <view class="hairline--top"></view>
      <retail-logistics
        v-if="showLogisticsDetailInfo"
        :express-data="order.expressData"
        @gotoDetail="gotoExpressPage"
      ></retail-logistics>
      <van-cell v-else :is-link="!!logistics.detailUrl" :border="false" @click="onClick">
        <view class="order-logistics">
          <van-icon class="icon" name="logistics" size="16px" />

          <view class="desc">
            <view class="desc-detail">
              {{ logistics.detail }}
            </view>
            <view v-if="logistics.time" class="desc-time">
              {{ logistics.time }}
            </view>
          </view>
        </view>
      </van-cell>
    </van-cell-group>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import navigate from '@youzan/tee-biz-navigate';
import { url } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef web */
import { isRetailShop } from '@youzan/utils-shop';
/* #endif */
/* #ifdef weapp */
const { isRetailApp } = getApp().globalData;
/* #endif */

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-icon': Icon,
  },

  data() {
    return {
      logistics: {},
      showLogisticsInfo: false,
      isShowLogistics: true, // 有赞云使用
      order: {},
    };
  },

  computed: {
    /* #ifdef web */
    bizEnv() {
      return url.args.get('bizEnv', window?.location?.href, '');
    },
    /* #endif */
    isRetail() {
      /* #ifdef web */
      return isRetailShop;
      /* #endif */
      /* #ifdef weapp */
      // eslint-disable-next-line no-unreachable
      return isRetailApp;
      /* #endif */
    },
    showLogisticsDetailInfo() {
      const { expressData = [], expressType } = this.order;
      const { localDeliveryVO = {} } = expressData[0] || {};
      return this.isRetail && expressType === 2 && [2, 3].includes(localDeliveryVO.distStatusCode);
    },
  },

  created() {
    mapData(this, ['order', 'showLogisticsInfo', 'isShowLogistics']);
    mapData(this, {
      logistics: (logistics) => {
        if (logistics != null && logistics.detail != null) {
          // 物流信息 detail 换行符处理
          logistics.detail = logistics.detail.split('<br>').join('\n');
        }
      },
    });
  },

  methods: {
    gotoExpressPage() {
      this.ctx.process.invoke('gotoExpressPage');
    },

    onClick() {
      if (!this.logistics.detailUrl) {
        return;
      }

      const { isPeriodBuy, isSelfFetch, isMultiPeriodBuy } = this.ctx.data;
      if (isPeriodBuy && !isSelfFetch && !isMultiPeriodBuy) {
        let logisticsDetailUrl = this.logistics.detailUrl;
        /* #ifdef web */
        if (this.bizEnv) {
          logisticsDetailUrl = url.args.add(logisticsDetailUrl, { bizEnv: this.bizEnv });
        }
        /* #endif */
        navigate({
          url: logisticsDetailUrl,
          type: 'navigateTo',
        });
      } else {
        this.gotoExpressPage();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$text-color: #323233;
$gray-darker: #646566;

.order-logistics {
  display: flex;
  position: relative;
}

.icon {
  padding: 2px 0 0 0;
  float: left;
  height: 16px;
  font-size: 0;
  color: $text-color;
}

.desc {
  text-align: left;
}

.desc-detail {
  margin: 0 0 0 6px;
  font-size: 14px;
  // line-height: 20px;
  color: $text-color;
  font-weight: bold;
}

.desc-time {
  margin: 10px 0 0 6px;
  font-size: 12px;
  line-height: 16px;
  color: $gray-darker;
}

.hairline--top {
  position: relative;
  margin: 0 16px;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
</style>
