// 商品按钮
export const BTN = {
  addToCart: {
    text: '加入购物车',
    value: 'addToCart',
    type: 'default',
    plain: true,
    round: true,
  },
  refund: {
    text: '申请退款',
    value: 'refund',
    type: 'danger',
    plain: true,
    round: true,
  },
  message: {
    text: '查看留言',
    value: 'message',
    type: 'default',
    plain: true,
    round: true,
  },
  postpone: {
    text: '我要顺延',
    value: 'postpone',
    type: 'default',
    plain: true,
    round: true,
  },
  reorder: {
    text: '再来一单',
    value: 'reorder',
    type: 'default',
    plain: true,
    round: true,
  },
};

// 商品类型 tag
export const TAG = {
  presale: { text: '预售', type: 'danger' },
  selfFetch: { text: '自提', type: 'primary' },
  periodBuy: { text: '周期购', type: 'primary' },
  enjoyBuy: { text: '随心订', type: 'danger' },
  fCode: { text: 'F码专享', type: 'danger' },
  bargain: { text: '砍价', type: 'danger' },
  inSourcing: { text: '内购价', type: 'danger' },
  seckill: { text: '秒杀', type: 'danger' },
  present: { text: '赠品', type: 'danger' },
  timelimitedDiscount: { text: '限时折扣', type: 'danger' },
  auction: { text: '降价拍', type: 'danger' },
  customerDiscount: { text: '会员折扣', type: 'primary' },
  plusBuy: { text: '加价购', type: 'danger' },
  crossBorder: { text: '海淘', type: 'primary' },
  exchangeCoupon: { text: '兑换券', type: 'danger' },
  drug: { text: '处方药', type: 'primary' },
};

export const REFUND_ACTION = {
  TO_DETAIL: 1,
  TO_APPLY: 2,
  TOAST: 3,
  NOT_SUPPORT: 4,
};
