import { getCouponAddOnInfo } from '../api';
import { money } from '@youzan/tee-util';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { reorderCouponByOrigin } from './helper';
import { cdnImage } from '@youzan/tee-biz-util';
import args from '@youzan/utils/url/args';

export default function (ctx) {
  return {
    initLogger(logger) {
      this.loggers = logger;
    },
    async getCouponAddon(kdtId = 0) {
      if (!!kdtId) {
        kdtId = ctx.data.kdtId;
      }
      const params: { kdtId: number; storeId: any; selectedPromotions?: string; pdlive?: string } =
        {
          kdtId,
          storeId: ctx.data.offlineId,
        };
      if (ctx.data.selectedPromotions) {
        params.selectedPromotions = JSON.stringify(ctx.data.selectedPromotions);
      }
      /* #ifdef web */
      // 私域直播购物车
      const pdlive = args.get('pdlive', window.location.href);
      if (pdlive) {
        params.pdlive = pdlive;
      }
      /* #endif */
      const originData = await getCouponAddOnInfo(params);
      if (!originData) {
        return;
      }
      const { couponAddOnData = {}, canUseCouponAddOn = {} } = originData;
      this.canUseCouponAddOn = canUseCouponAddOn;
      ctx.data.canUseCouponAddOn = canUseCouponAddOn;
      this.couponAddOnData = couponAddOnData;
      this.originData = originData;
      // 这个逻辑太恶心了，后面不好维护，以后最好不要出现这种逻辑@虹静
      if (!originData?.couponAddOnData?.addOnCopywriting) {
        // this.abTestIsLogged = false;
        this.gatherBarExposed = false;
      }
      this.handleCouponAddOnDataChange(couponAddOnData);
    },
    handleCouponAddOnDataChange(couponAddOnData) {
      if (!this.gatherBarExposed && couponAddOnData?.addOnCopywriting) {
        this.loggers?.logGatherBarExpo?.();
        this.gatherBarExposed = true;
      }
      if (couponAddOnData?.addOnVoucherDetailDTOList?.length > 0) {
        let coupons = couponAddOnData?.addOnVoucherDetailDTOList || [];
        coupons = coupons.map(({ allAbleCartGoods, ...rest }) => ({
          allAbleCartGoods: allAbleCartGoods.map((good) => ({
            ...good,
            price: money.toYuan(good.price),
            attachmentUrl: cdnImage(good.attachmentUrl, '!200x200.jpg'),
          })),
          ...rest,
        }));
        if (this.couponOriginOrder && this.showPopup) {
          coupons = reorderCouponByOrigin(
            coupons,
            this.couponOriginOrder,
            this.loggers?.logGoodsDecreasedToastExpo
          );
        }

        this.couponOriginOrder = coupons;
        this.coupons = coupons;
      } else {
        // 无有效数据时
        /* 凑单面板开启时 */
        if (this.showPopup) {
          this.showPopup = false;
          Toast('暂无可用优惠券');
        }
        // 在面板关闭后清除数据
        setTimeout(() => {
          this.coupons = [];
        }, 1000);
      }
    },
    clearCouponOriginOrder() {
      this.couponOriginOrder = null;
    },
  };
}
