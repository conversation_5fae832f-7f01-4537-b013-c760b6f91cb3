<template>
  <van-popup
    :show="show"
    position="bottom"
    :custom-class="popupContainerClass"
    @close="handleClose"
  >
    <view class="message-popup__title t-hairline--bottom">商品留言</view>
    <van-cell-group custom-class="message-popup__cell-group" :border="false">
      <van-cell
        v-for="(item, index) in formatedMessage"
        :key="index"
        :title="item.key"
        title-width="80px"
        value-class="message-popup__cell__value"
        :border="index !== formatedMessage.length - 1"
      >
        <image
          v-if="item.type === 'image'"
          :src="item.value"
          mode="aspectFit"
          style="width: 70px; height: 70px"
          @click="previewImg(item.value)"
        />
        <text v-else>{{ item.value || '无' }}</text>
      </van-cell>
    </van-cell-group>
    <view :class="buttonWarpClass">
      <van-button
        round
        block
        size="large"
        :custom-style="themeStyle"
        custom-class="message-popup__bottom-btn"
        @click="handleClose"
      >
        关闭
      </van-button>
    </view>
  </van-popup>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Button from '@youzan/vant-tee/dist/button/index';
import { previewImage } from '@youzan/tee-api';
import { cdnImage as fullfillImage } from '@youzan/tee-biz-util';

export default {
  components: {
    'van-popup': Popup,
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-button': Button,
  },

  props: {
    message: Object,
    show: Boolean,
    themeColors: Object,
  },

  computed: {
    themeStyle() {
      return `background: ${this.themeColors.general}; border-color: ${this.themeColors.general}; color: ${this.themeColors['main-text']}`;
    },

    popupContainerClass() {
      let result = '';
      /* #ifdef web */
      result = 'goods-message-list';
      /* #endif */
      return result;
    },
    buttonWarpClass() {
      let result = 'message-popup__bottom-wrapper';
      /* #ifdef web */
      result += ' message-popup__bottom-wrapper--fixed';
      /* #endif */
      return result;
    },

    formatedMessage() {
      const { message = {} } = this;
      return Object.keys(message).map((key) => {
        const type = /^\s*http(s)*:\/\/.+/.test(message[key]) ? 'image' : 'text';
        return {
          key,
          type,
          value: type === 'image' ? fullfillImage(message[key], '!200x200.jpg') : message[key],
        };
      });
    },
  },

  methods: {
    handleClose() {
      this.$emit('close');
    },
    // 预览图片
    previewImg(src) {
      previewImage({ current: 0, urls: [src] });
    },
  },
};
</script>

<style lang="scss">
.message-popup {
  max-height: 386px;
  background-color: #fff;
}

.goods-message-list {
  width: 100%;
  min-height: 70%;
  max-height: 85%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.message-popup__title {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  text-align: center;
  color: #323233;
}

.message-popup__cell-group {
  max-height: 350px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.message-popup__cell__value {
  text-align: left !important;
  color: #646566;
}

.message-popup__bottom-wrapper {
  margin: 0 16px;
  border-radius: 999px;

  &--fixed {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 5px;
  }
}

.message-popup__bottom-btn::after {
  border: none !important;
}

.message-popup__bottom-btn::before {
  border: none !important;
}

.message-popup__bottom-btn {
  border: none !important;
}

.t-hairline--bottom {
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    transform: scale(0.5);
    border-bottom-width: 1px;
  }
}
</style>
