<template>
  <view v-if="!!bottomBtns.length && !isTradeComponent3">
    <view :class="['bottom-action', deviceType]">
      <view class="btns-wrapper t-hairline--top">
        <!-- #ifdef web -->
        <van-button
          v-if="showGoOrderButton"
          plain
          round
          size="small"
          custom-class="order__btn"
          @click="handleGoOrder"
        >
          返回点单
        </van-button>
        <!-- #endif -->
        <view :key="index" v-for="(item, index) in bottomBtns">
          <van-button
            v-if="item.value !== 'coupon' || (item.value === 'coupon' && item.version === 'v1')"
            plain
            round
            size="small"
            :type="item.type"
            :custom-class="item.value === 'more' ? 'action__btn--more' : 'action__btn'"
            @click="handleBtnClick(item)"
          >
            {{ item.name }}
          </van-button>

          <!-- #ifdef web -->
          <image
            v-if="item.value === 'coupon' && item.version === 'v2'"
            class="action__btn--coupon"
            src="https://img01.yzcdn.cn/public_files/c8031bdf30a4ae009fbb74627c8e107b.gif"
            alt="领优惠券"
            @click="handleBtnClick(item)"
          />
          <!-- #endif -->

          <!-- #ifdef weapp -->
          <image
            v-if="item.value === 'coupon' && item.version === 'v2'"
            class="action__btn--coupon"
            mode="aspectFit"
            src="https://img01.yzcdn.cn/public_files/c8031bdf30a4ae009fbb74627c8e107b.gif"
            alt="领优惠券"
            @click="handleBtnClick(item)"
          />
          <!-- #endif -->
        </view>
      </view>
      <!-- #ifdef web -->
      <qtt-comment-tip v-if="showQttCommentTip" :btns="bottomBtns" />
      <qtt-share v-if="showQttShare" @click="closeQttShare" />
      <!-- #endif -->
    </view>
    <!-- #ifdef weapp -->
    <view :class="['bottom-action-placeholder', deviceType]"></view>
    <!-- #endif -->
  </view>
</template>

<script>
import { mapState } from '@ranta/store';
import Button from '@youzan/vant-tee/dist/button/index';
import { isNewIphone } from '@youzan/decorate-tee/src/common/utils/iphone';
import { BOTTOM } from './constants';
/* #ifdef web */
import { ZNB } from '@youzan/tee-biz-navigate';
import QttCommentTip from './QttCommentTip.vue';
import QttShare from './QttShare.vue';
/* #endif */

const LIMIT_NUM = 3;

export default {
  components: {
    'van-button': Button,
    /* #ifdef web */
    QttCommentTip,
    QttShare,
    /* #endif */
  },

  data() {
    return {
      ...mapState(this, [
        'allBtns',
        'showActionSheet',
        'showFailGoodsDialog',
        'orderExtra',
        'bottomBtnsDisplay',
        /* #ifdef web */
        'showQttCommentTip',
        'showQttShare',
        /* #endif */
      ]),
      /* #ifdef web */
      showGoOrderButton: _global?.miniprogram?.isTTApp,
      /* #endif */
    };
  },

  computed: {
    bottomBtns() {
      // 控制底部按钮的数量，超过展示"更多"
      const btns = this.allBtns.slice(0, LIMIT_NUM);
      if (this.allBtns.length > LIMIT_NUM) {
        btns.push(BOTTOM.more);
      }
      return btns;
    },

    deviceType() {
      let isIphoneX = false;
      /* #ifdef weapp */
      isIphoneX = isNewIphone();
      /* #endif */

      /* #ifdef web */
      isIphoneX = /iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812;
      /* #endif */

      return isIphoneX ? 'iPhone-X' : '';
    },

    // 交易组件3.0屏蔽相关操作后面会下线!
    isTradeComponent3() {
      const { orderExtra } = this;

      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}');
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION === 'TRADE_COMPONENT_3_0' ||
          BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN'
        );
      } catch (error) {
        return false;
      }
    },
  },

  methods: {
    // NOTICE: handleBtnClick 必须保留，因为在template中使用，'btn' 入参直接来自 emit 提供的数据
    handleBtnClick(btn) {
      this.store.handleBtnClick(btn);
    },
    /* #ifdef web */
    handleGoOrder() {
      ZNB.navigate({
        ttUrl: '/packages/retail-shelf/shelf/index',
        // znb 好像没有判断抖音环境，先这样处理一下
        url: '/packages/retail-shelf/shelf/index',
        type: 'reLaunch',
      });
    },
    closeQttShare() {
      this.store.closeQttShare();
    },
    /* #endif */
  },
};
</script>

<style lang="scss">
$iphone-x-bottom: 34px;

@mixin iphone-x-after($iphone-x-bottom) {
  content: ' ';
  height: $iphone-x-bottom;
  background: #fff;
  width: 100%;
  display: block;
}

.bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  background: #fff;

  .btns-wrapper {
    padding: 10px 16px;
    overflow: hidden;

    .t-button {
      float: right;
      height: 32px;
    }
  }

  &.iPhone-X::after {
    @include iphone-x-after($iphone-x-bottom);
  }
}
/* #ifdef web */
.order__btn {
  float: left !important;
  font-size: 14px !important;
}
/* #endif */

.action__btn {
  float: right;
  margin-left: 12px;
  font-size: 14px !important;
}

.action__btn--more {
  float: right;
  margin-left: 10px;
  font-size: 14px !important;
  min-width: 0 !important;
  border: none !important;
}

.action__btn--coupon {
  position: fixed;
  bottom: 240px;
  right: 12px;
  width: 40px;
  height: 46px;
}

.order-detail-more-btn-sheet {
  .t-action-sheet__item {
    width: 100%;
  }
}

.t-hairline--top {
  position: relative;

  &::after {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-top: 1px solid #ebedf0;
  }
}
.bottom-action-placeholder {
  &::before {
    display: block;
    content: '';
    height: 50px;
  }
  &.iPhone-X::after {
    @include iphone-x-after($iphone-x-bottom);
  }
}
</style>
