<template>
  <view class="cart-empty-block" v-if="showEmptyCart" :style="themeStyle">
    <empty-tip />
    <view class="cart-empty-mask" v-if="!isAuthProtocol" @click="toAuth"></view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      showEmptyCart: false,
      isAuthProtocol: false,
      themeStyle: '',
    };
  },
  created() {
    mapData(this, ['isAuthProtocol', 'showEmptyCart', 'themeStyle']);
  },
  methods: {
    toAuth() {
      this.ctx.process.invoke('authProtocol');
    },
  },
};
</script>

<style lang="scss" scoped>
.cart-empty-block {
  position: relative;
}
.cart-empty-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
