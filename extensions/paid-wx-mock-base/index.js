import navigate from '@youzan/tee-biz-navigate';
import { args } from '@youzan/tee-util/lib/common/url';

export default class PaidWxMockBaseExt {
  constructor(options) {
    this.ctx = options.ctx;
    this.init();
  }

  init() {
    const kdtId = args.get('kdt_id', location.href) || 491391;
    const orderNo = args.get('order_no', location.href) || 'E20210625113333039702038';
    this.ctx.data.outBizNo = orderNo;
    const rect = document.documentElement.getBoundingClientRect();
    this.ctx.data.height = rect.height;
    this.ctx.data.width = rect.width;
    /** 最大的内容展示高度 */
    this.ctx.data.realHeight = 960 / (640 / rect.width);
    this.ctx.data.kdtId = kdtId;
    // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
    this.ctx.data.returnUrl = 'www.youzan.com';
    this.ctx.process.define('navigateTo', (url) => {
      navigate({
        web: {
          type: 'safeLink',
          safeLink: {
            url,
          },
        },
      });
    });
    this.ctx.process.define('logger', (et, ei, en, params = {}) => {
      this.ctx.logger?.log({
        et, // 事件类型
        ei, // 事件标识
        en, // 事件名称
        params, // 事件参数
      });
    });
  }
}
