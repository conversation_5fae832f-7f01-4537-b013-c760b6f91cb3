import { stringifyAddress } from '../trade-buy-self-fetch/stringify-address';
import { getVerifyAddress, getVerifyAddressLngLat } from './common/address';

interface OrderStateInfo {
  orderState: any;
  orderStateTitle: string;
  // autoReceiveOrderTime: order.autoReceiveOrderTime,
  showOrderStatusToGroup: boolean;
  showOrderStatusSended: boolean;
  // closeType: order.closeType,
  closeTypeStr: string;
  // progressBar: order.progressBar,
  // marketingOrderSource: order.marketingOrderSource,
}

interface ReceiverInfo {
  receiverName: string;
  receiverTel: string;
  receiverLabel: '联系人' | '收货人';
  deliveryAddress: string;
}

interface SelfFetchInfo {
  qrcode: string;
  fetchNo: string;
  fetcher: string;
  time: string;
  tel: string;
  address: string;
  lng: string;
  lat: string;
  shopName: string;
}

interface ReturnValueCard {
  assertBusinessDetail: any[];
  cardAssetIsActive: any;
}

interface Service {
  postage: string;
  expressType: string;
  localDeliveryDesc: string;
  localDeliveryImg: string;
  deliveryTime: string;
  buyerMemo: string;
  showHotelUnsubscribeRemind: boolean;
  serviceTel: string;
}

interface Time {
  createTime: string;
  payTitle: '下单时间' | '付款时间';
  payTime: string;
  expressTime: string;
  successTime: string;
}

interface VerifyAddressInfo {
  name: string;
  address: string;
  lng: string;
  lat: string;
}

interface TransferStatInfo {
  totalCount: number;
  totalAmount: number;
  leftCount: number;
  totalFinishedCount: number;
  totalFinishedAmount: number;
}

interface TransferOrderListInfo {
  amount: number;
  finishedTime: string;
  itemName: string;
  kdtId: number;
  outOrderNo: string;
  operatorPhoneNo: string;
  remark: string;
  src: string;
  status: string;
  transferOrderNo: string;
  targetType: string;
  transferTime: string;
  type: string;
  targetPhoneNo: string;
}

interface MicroTransferInfo {
  transferStat: TransferStatInfo;
  transferOrderList: TransferOrderListInfo;
}

/**
 * com.youzan.ph.trade.buyer.api.search.dto.ItemInfoFormatDTO
 * 商品详情格式化信息（详情页）
 */
interface IItemInfoFormatDTO {
  /** 现商品单价，减去了商品优惠。以分为单位 */
  unitPrice?: number;
  /** 商品应付金额 */
  payPrice?: number;
  /** 商品数量 */
  num?: string;
  /** 划线价 */
  origin?: string;
  /** 商品扩展字段 */
  extra?: string;
  /** 商品名 */
  goodsName?: string;
  /** 商品信息 */
  goodsInfo?: string;
}

// 有赞担保
interface GuaranteeOrderInfo {
  aliases: string[];
  feedback: any; // 是否售后中
  hasYzSecured: any; // 是否是有赞担保订单
  orderNo: string; // E单号
  orderSuccessTime: string; // 订单完成时间
  orderStatus: number; // 订单状态
  orderPayTime: string; // 订单支付时间
  payWay: number; // 支付方式
}

/**
 * com.youzan.ph.trade.buyer.api.search.dto.MainOrderInfoFormatDTO
 * 订单主体信息
 */
interface IMainOrderInfoFormatDTO {
  /** 订单号 */
  orderNo?: string;
  /** 订单ID */
  orderId?: number;
  /** 支付时间 */
  payTime?: string;
  /** 订单状态描述 */
  stateDesc?: string;
  /** 交易完成时间 */
  successTime?: string;
  /** 物流方式 */
  expressType?: number;
  /** 是否是拼团订单  orderType=10并且不是原价购买的订单 */
  isGroupOnOrder?: boolean;
  /** 订单状态 */
  state?: number;
  /** 是否允许联系商家按钮展示 */
  isAllowContactSeller?: boolean;
  /** 是否允许确认收货 */
  isAllowConfirmReceive?: boolean;
  /** 是否次卡核销 */
  isAllowTimesCardVerify?: boolean;
  /** 订单类型名称 */
  orderTypeName?: string;
}

interface IStoreAddressDo {
  /**
   * 网点id
   */
  id: number;

  /**
   * 网点名称
   */
  name: string;

  /**
   * 省
   */
  province: string;

  /**
   * 市
   */
  city: string;

  /**
   * 区
   */
  area: string;

  /**
   * 详细地址
   */
  address: string;

  /**
   * 精度
   */
  lng: number;

  /**
   * 维度
   */
  lat: number;
}

// 店铺信息
interface IShopInfoDto {
  shopName: string;
  kdtId: number;
  headKdtId: number;
  /**
   * 门店ID
   */
  storeId?: number;
  /**
   * 门店名称
   */
  offlineStoreName?: string;
  /**
   * 门店信息 - 与电子卡券跨店核销紧相关
   */
  storeAddress?: IStoreAddressDo;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.res.format.ChildInfoFormatDTO
 */
interface IChildInfoFormatDTO {
  /** 送礼标记 */
  giftSign?: string;
  childInfoList?: IChildInfoDetail[];
  /** 礼单详情。 */
  giftInvite?: IGiftInviteInfo;
  /** 送礼订单号 */
  giftNo?: string;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.res.format.InvoiceFormatDTO
 */
interface IInvoiceFormatDTO {
  /** 是否补开发票，true是，false否 */
  isAllowInvoice?: boolean;
  /** 销售方id(店铺id) */
  kdtId?: number;
  /** 开票记录id */
  invoiceId?: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 发票代码 */
  invoiceCode?: string;
  /** 商家发票资产id */
  invoiceAssetId?: string;
  /** 开票状态描述 */
  status?: number;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.req.OrderResultDTO.OrderBizExtra
 */
interface IOrderBizExtra {
  /** 是否可以加入购物车 */
  isShowAddCart?: boolean;
  /** 是否使用新的快照 */
  isUsingNewSnapshot?: boolean;
  /** 显示前端自提组件 */
  showSelfFetch?: boolean;
  /** 显示配送方式 */
  showDeliveryType?: boolean;
  /** 是否有发生过或还在流程中的维权/退款 */
  hasRefund?: boolean;
  /** 显示收货地址 */
  showAddress?: boolean;
  /** (自提/同城)结束时间 */
  parcelArriveEndTime?: string;
  /** 自提订单标记 */
  isSelfFetch?: boolean;
  /** 是否展示查看课程 */
  showCourseDetail?: boolean;
  /** 是否支付有礼 */
  paidPromotion?: boolean;
  /** 是否展示整单退款，如果展示，批量退款前端不展示 */
  showWholeRefund?: boolean;
  /** (自提/同城)开始时间 */
  parcelArriveStartTime?: string;
  /** 酒店内容相关字段 */
  hotelVO?: IHotelVO;
  /** 显示虚拟卡券 */
  showVirtualTicket?: boolean;
  /** 是否展示查看评价按钮 */
  isShowViewEvaluate?: boolean;
  /** 批量退款是否展示退货退款选项 */
  showReturnGoodsRefund?: boolean;
  /** 显示运费 */
  showPostage?: boolean;
  /** 好友拼单 */
  jointDTO?: IJointDTO;
  /** 是否展示批量退款按钮 */
  showBatchRefund?: boolean;
  /** 使用内购券数量 */
  fissionTicketNum?: number;
  /** 运费险类型 */
  freightInsuranceType?: number;
  /** 周期购订单标记 */
  isPeriodBuy?: boolean;
  /** 是否是付费优惠券订单 */
  hasPayCouponsOrder?: boolean;
  /** 是否展示评价按钮 */
  isShowEvaluate?: boolean;
  /** 酒店订单标记 */
  isHotel?: boolean;
  /** 预付卡账户是否被激活 */
  cardAssetIsActive?: boolean;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.childOrder.GiftInviteInfo
 */
interface IGiftInviteInfo {
  /** 总份数 */
  totalNum?: number;
  /** 订单别名 */
  orderAlias?: string;
  /** 别名 */
  alias?: string;
  /** 已送出份数 */
  givenNum?: number;
  /** url */
  url?: string;
}

interface FaceToFaceCourseInfo {
  goodsId: number;
  alias: string;
  skuId: number;
  goodsTitle: string;
  /**
   * 教育商品类型
   */
  owlType: number;
  courseType: number;
  courseSellType: number;

  price: number;

  /**
   * 价格信息
   */
  extPriceDTO: {
    originPrice: number;
    currentPrice: number;
    originTotalAmount: number;
    currentTotalAmount: number;
  };

  /**
   * 规格信息
   */
  extItemSkuDTO: object;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.res.format.OrderAddressInfoFormatDTO
 */
interface IOrderAddressInfoFormatDTO {
  /** 自提信息 */
  selfFetchInfo?: string;
  /** 收件人电话 */
  receiverTel?: string;
  /** 配送城市 */
  deliveryCity?: string;
  /** 自提点ID */
  selfFetchId?: number;
  /** 订单号 */
  orderNo?: string;
  /** 店铺ID */
  kdtId?: number;
  /** 同城配送结束时间 */
  deliveryEndTime?: string;
  /** 收件人姓名 */
  receiverName?: string;
  /** 配送省份 */
  deliveryProvince?: string;
  /** 同城送配送范围 */
  areaList?: IAreaModel[];
  /** 等同于老交易的address_detail */
  deliveryStreet?: string;
  /** 收货扩展信息 */
  addressExtra?: string;
  selfFetchInfoFormatDTO?: ISelfFetchInfoFormatDTO;
  /** 区/县 */
  deliveryDistrict?: string;
  /** 国家名称 */
  deliveryCountry?: string;
  /** 邮政编码 */
  deliveryPostalCode?: string;
  /** 自提码对象 */
  selfFetchResult?: ISelfFetchInfo;
  addressExtraFormatDTO?: IAddressExtraFormatDTO;
  /** 自提时间区间 */
  selfFetchTime?: string;
  /** 同城配送起始时间 */
  deliveryStartTime?: string;
}

interface RecommendLayoutConfig {
  layout: number;
  imageFillStyle: number;
}

interface DialogRecommendLayoutConfig {
  imageFillStyle: number;
  layout: number;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.res.format.UMPInfoFormatDTO
 */
interface IUMPInfoFormatDTO {
  /** 红包抵扣信息 */
  redPacketInfo?: IRedPacketInfoDTO;
  /** 总共减少金额 */
  decreaseAmount?: number;
  /** 官方补贴 */
  platformSubsidyInfo?: IPlatformSubsidyInfoDTO;
  /** 商品可享受的最优优惠信息 */
  goodsAvlActivity?: IGoodsAvlActivityDTO;
  /** 订单活动清单 */
  orderActivities?: IOrderActivityDetail[];
  /** 获取订单、团购返现、满减送积分、满减送优惠券等信息 */
  preferentialInfo?: IPreferentialInfoDTO;
}

/**
 * com.youzan.ebiz.trade.api.manage.detail.dto.res.format.MarketingInfoFormatDTO
 */
interface IMarketingInfoFormatDTO {
  /** 复购券 */
  repurchaseCoupon?: IRepurchaseCouponDTO;
}

// 先用后付
interface UseBeforePayData {
  is: boolean; // 当前订单是否是先用后付的场景
  status: string | 'PENDING'; // 履约状态，PENDING=待履约，PAID=已支付
}

interface IOuterPromotion {
  isPromotionCorrect: boolean;
  otherPromotionAmount?: number;
  voucherPromotionAmount?: number;
}

type ItemList = Array<{ goodsId: number }>;
export default interface ExtensionMetadata {
  data: {
    provide: {
      orderStateInfo: OrderStateInfo;
      receiverInfo: ReceiverInfo;
      selfFetchInfo: SelfFetchInfo;
      returnValueCard: ReturnValueCard;
      service: Service;
      time: Time;
      verifyAddressInfo: VerifyAddressInfo;
      microTransferInfo: MicroTransferInfo;
      goods: IItemInfoFormatDTO;
      guaranteeOrderInfo: GuaranteeOrderInfo;
      order: IMainOrderInfoFormatDTO;
      shopInfo: IShopInfoDto;
      gift: IChildInfoFormatDTO;
      invoice: IInvoiceFormatDTO;
      orderBizExtra: IOrderBizExtra;
      giftInvite: IGiftInviteInfo;
      courses: FaceToFaceCourseInfo[];
      addressInfo: IOrderAddressInfoFormatDTO;
      recommendLayoutConfig: RecommendLayoutConfig;
      dialogRecommendLayoutConfig: DialogRecommendLayoutConfig;
      ump: IUMPInfoFormatDTO;
      marketingInfo: IMarketingInfoFormatDTO;
      useBeforePayData: UseBeforePayData;
      outerPromotion: IOuterPromotion;
      itemList: ItemList;
      orderExtra: unknown;
      payment: unknown;
      canUseTradeUmpV1: boolean;
      receivingContent: string;
      showMessage: boolean;
      bottomBtnsDisplay: unknown;
      showOrderAddress: boolean;
      showOrderStatus: boolean;
      kdtId: number;
      flowEntranceBannerBizName: string;
      requestExtraParams: unknown;
      buyerId: number;
      rxNo: string;
      bizName: 'order_detail';
      cpsConfigKey: 'cps_goods_recommend_order_detail';
      orderNo: string;
      bigOrder: {
        mainOrderInfo: unknown;
        shopInfo: unknown;
        orderBizExtra: unknown;
        paymentInfo: unknown;
      };
      rechargeInfo: {
        origin: string;
        payAmount: number;
      };
    };
  };
  process: {
    invoke: {
      setBottomBtnsDisplay: (payload: { batchRefund?: boolean; confirmHotel?: boolean }) => void;
      setReceivingContent: (payload: unknown) => void;
      setItemList: (payload: unknown) => void;
      setShowMessage: (payload: boolean) => void;
      toggleBlockDisplay: (payload: { blockName: string; display: boolean }) => void;
      getOrderEnterShopPolicy: () => void;
      getShareAppMessage: (payload: string) => void;
      waitingOrderProgress: () => void;
    };
    define: {
      afterReceiverInfoUpdated: (payload: unknown) => void;
      setReceivingContent: (payload: string) => void;
      setShowMessage: (payload: boolean) => void;
      setItemList: (payload: ItemList) => void;
      setBottomBtnsDisplay: (payload: unknown) => void;
      toggleBlockDisplay: (payload: any, isShow?: boolean) => void;
    };
  };
  event: {
    emit: {
      onLoadOrderInfo: any;
    };
    once: {
      onLoadOrderInfo: any;
    };
  };
  cloud: {
    emit: {
      onOrderLoadedEvent: () => void;
    };
  };
}
