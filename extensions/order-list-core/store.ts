import Tee from '@youzan/tee';
import Event from '@youzan/tee-event';
/* #ifdef web */
import { action as <PERSON><PERSON><PERSON><PERSON><PERSON>A<PERSON> } from '@youzan/zan-jsbridge';
/* #endif */
import { beforeConfirmReceive } from '@youzan/wsc-tee-trade-common/lib/order-utils/wx-confirm-receive-handler';

// Utils
import type { Query, CloudBtnListCondition, OrderListTabOpt, SearchBarOpt } from './extension';
import api from './utils/api';
import {
  TITLE_CONF,
  isIvr,
  isKsApp,
  isXhsApp,
  openAppConfig,
  PAGE_SIZE,
  SHARE_OPTIONS,
  DEFAULT_ACTIVITY_TAB,
  COUPON_CODE_MAP,
  isAlipayApp,
  isQQApp,
  CHECK_ORDER_DELAY_RECEIVE_STATUS_MAP,
  QUERY_PAGE_TYPE,
  CRM_OFFLINE_TYPE,
  ACTIVITY_TYPE,
  ORDER_STATUS,
  DEFAULT_POINTS_NAME,
  DEFAULT_CANCEL_ORDER_POPUP_CONFIG,
  PAGE_SCENE,
} from './utils/constants';
import {
  LOG_BULK_ADD_CART,
  LOG_CANCLE_ORDER,
  LOG_CANCLE_ORDER_CLICK,
  LOG_CANCLE_ORDER_COMPONENT_VIEW,
  LOG_CLICK_CRM_OFFLINE_ORDER,
  LOG_CLICK_DIVIDE_COUPON,
  LOG_CLICK_MARKETING_PAIDPRO,
  LOG_CONFIRM_GOODS,
  LOG_CONFIRM_HOTEL_GOODS,
  LOG_DELAY_GOODS,
  LOG_EXPRESS_REMINDER,
  LOG_GET_PAID_CLICK,
  LOG_MODIFY_GOODS_CLICK,
  LOG_NEW_BUY_AGAIN_CLICK,
  LOG_ORDER_DELETE,
  LOG_SEARCH_ORDER_CLICK,
  LOG_SELECT_REASON,
  LOG_VIEW_CANCEL_ORDER_REASON,
} from './utils/log';

// Dependencies
import args from '@youzan/utils/url/args';
import get from '@youzan/utils/object/get';
import navigate from '@youzan/tee-biz-navigate';
import ua from '@youzan/utils/browser/ua_browser';
import buildUrl from '@youzan/utils/url/buildUrl';
import { enterShopApollo, accordSkyLogger } from '@youzan/tee-chain-store';
import { checkEduShop, checkRetailShop } from '@youzan/utils-shop';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import env from '@youzan/wsc-tee-trade-common/lib/env';
import { setNavigationBarTitle, setStorage } from '@youzan/tee-api';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import chinaMobile from '@youzan/utils/validate/chinaMobile';
import skynet from '@youzan/wsc-tee-trade-common/lib/utils/skynet';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import mapKeysToCameLCase from '@youzan/utils/string/mapKeysToCamelCase';
import { navigateToRantaPage, PAGE_TYPE } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import { checkChainStore } from '@youzan/utils-shop/lib/shop-model/chain/base';
import { BTN_CONF } from './utils/order-buttons';
import { yunBtnTypeMap } from './utils';
import { errorToast } from '@youzan/tee-biz-util';

interface RepurchaseCouponInfo {
  activityAlias: string;
  endTime: number;
  id: number;
  orderNo: string;
  startTime: number;
  unitCopywriting: string;
  useThresholdCopywriting: string;
  validTimeCopywriting: string;
  validTimeGenerateType: number;
  valueCopywriting: string;
}

interface StoreModule {
  state?: {
    orderRepurchaseCouponStatus: {
      // null 表示对应的订单没有复购券，注意判断 === null 避免错误的判断 undefined
      [orderNo: string]: RepurchaseCouponInfo | null;
    };
    [key: string]: any;
  };
  getters?: Record<string, Function>;
  actions?: (ctx: any) => Record<string, Function>;
}

const { isWeixin } = ua;
const isNewHotelGood = (listItem: Record<string, any>) =>
  get(listItem, 'orderItems[0].newHotelGoods') === '1';
const getOrderItem = (listItem) => get(listItem, 'orderItems', [])[0] || {};
const buildGoodsList = (goodsList, failGoodsList) => {
  const failList = failGoodsList.map((failGoods) => {
    const goodsArr = goodsList.filter(
      (goods) => goods.sku_id === failGoods.sku_id && goods.goods_id === failGoods.goods_id
    );
    const foundGoods = goodsArr[0] || {};
    return {
      title: foundGoods.title || foundGoods.goods_info.title || '',
      num: foundGoods.num,
      sku: foundGoods.sku.map((item) => item.v).join(' '),
    };
  });

  const successList = goodsList.filter(
    (goods) =>
      !failGoodsList.some(
        (failGoods) => goods.sku_id === failGoods.sku_id && goods.goods_id === failGoods.goods_id
      )
  );

  return {
    failList,
    successList,
  };
};

const isXhsLocalLife = args.get('isXhsLocalLife');

// 获取跳转链接
const getDirectUrl = ({ listItem, btn }) => {
  const helper = () => {
    const orderItem = getOrderItem(listItem);
    if (btn && btn.url) return btn.url;

    return orderItem.detailUrl;
  };

  const url = helper();

  if (isXhsLocalLife) {
    return args.add(url, { isXhsLocalLife });
  }

  return url;
};

// 在 URL 上添加统计参数
const addAnalyticsParams = (ctx, url, query = {}) => {
  const loggerData = ctx.logger.getLogParams();
  const context = (loggerData && get(loggerData, 'context')) || {};
  return url
    ? args.add(url, {
        dc_ps: context.dc_ps || '',
        from_source: context.from_source || '',
        from_params: context.from_params || '',
        ...query,
      })
    : '';
};

// 付费商品连续包月 跳转会员支付页
const handleGoodsLevel = (listItem, directUrl = '') => {
  const payGradeCard = get(listItem, 'orderItems[0].items[0].payGradeCard', false);
  if (!payGradeCard) {
    return;
  }
  const kdtId = get(listItem, 'kdtId', '');
  const goodsId = get(listItem, 'orderItems[0].items[0].goodsId', '');
  const skuVid = get(listItem, 'orderItems[0].items[0].sku[0].vId', '');
  return api.API_GET_SCRM_LEVEL({ kdtId, goodsId }).then((res: Record<string, any> = {}) => {
    const { autoRenew, levelAlias, levelGoods = {} } = res;
    const sku = levelGoods.skuList.find((s) => s.skuVId === skuVid);
    const { goodsSkuType } = sku || [];
    // 1 付费规则 2.自动续费规则
    let weappUrl = '';
    let h5Url = '';
    if (autoRenew && goodsSkuType === 2) {
      weappUrl = args.add('/packages/shop/levelcenter/plus/index', {
        kdt_id: kdtId,
        alias: levelAlias,
      });
      /* #ifdef web */
      h5Url = buildUrl(
        args.add('/pay/wscuser_paylevel', { kdt_id: kdtId, alias: levelAlias }),
        'cashier',
        kdtId,
        { notReplaceDomain: true }
      );
      /* #endif */
    } else {
      /* #ifdef web */
      h5Url = directUrl;
      /* #endif */
    }

    navigate({
      url: weappUrl,
      /* #ifdef web */
      web: {
        type: 'znb',
        znb: {
          url: h5Url,
          weappUrl: `/pages/common/webview-page/index?src=${h5Url}`,
        },
      },
      /* #endif */
    });
  });
};

let DEFAULT_TOP_NAV_HEIGHT = 0;
/* #ifdef weapp */
DEFAULT_TOP_NAV_HEIGHT = 90;
/* #endif */

const rootStore: StoreModule = {
  state: {
    orderRepurchaseCouponStatusCount: 0,
    orderRepurchaseCouponStatus: {},
    orderListTabOpt: {
      isShow: true, // 默认展示标签栏，通过beforeSetup可以控制隐藏
      tabs: [],
    }, // orderListTabOpt: 标签页配置项，list是标签列表
    searchBarOpt: {
      isShow: true, // 默认展示顶部搜索栏，通过beforeSetup可以控制隐藏
    },
    topNavHeight: DEFAULT_TOP_NAV_HEIGHT,
    isShowRetailOrderList: false,
    directBuyAgainBtnConfig: {},
    shopMetaInfo: {},
    query: {},
    isIvr,
    isXhsApp,
    isKsApp,
    isAlipayApp,
    isQQApp,

    kdtId: 0, // 从 shop-core 中获取
    bizName: 'order_list',
    activityTab: {
      ...DEFAULT_ACTIVITY_TAB,
    },
    orderListCount: 0,
    orderList: [], // 原始订单列表数据
    renderOrderList: [], // 直接可以用来渲染UI的数据 eachPick(orderList, renderData)
    openAppConfig,
    searchCondition: {
      value: '',
    },
    canUseTradeUmpV1: undefined,
    listIndex: -1,
    listItem: {}, // 当前正在处理的订单列表项，即当前正在处理(操作)的订单

    // 分享订单
    isShowOrderSharePopup: false,
    isShowOrderSharePoster: false,
    shareOptions: SHARE_OPTIONS.filter((option) => option.key !== 'toutiao'),
    shareUrl: '',

    // 取消订单
    isShowOrderCancelPopup: false,
    cancelOrderReasonInfo: {
      buyerCloseReason: [],
      isShowItemPutInCartButton: false,
    },
    cancelOrderNo: '',
    cancelOrderPopupConfig: DEFAULT_CANCEL_ORDER_POPUP_CONFIG,

    // 再来一单 - 失败商品确认弹窗
    isShowBuyAgainFailGoodsDialog: false,
    failBuyAgainGoodsList: [],
    failBuyAgainGoodsResolve: undefined,
    pointsName: DEFAULT_POINTS_NAME,

    selfFetchInfo: {},
    isShowSelfFetchPopup: false,

    userInfo: {},

    // 开放相关的数据
    yunConfig: {
      // 确认收货时的自定义内容
      receivingContent: '',
      btnListCondition: {
        condition: { filterOrderStatus: [], activityTypes: [] },
        cloudBtnList: [],
      },
      hideButtonTypes: [],
    },
    yunDesignConfig: {},
    isShowWxVideoGuidePopup: false,
    loading: false,
    isShowWelikeEntry: false,
    isGoodsImgLoaded: false,
  },
  getters: {
    stickyHeaderHeight() {
      const isShowSearchBar = Boolean(this.searchBarOpt.isShow && !this.isDrugShop);
      const isShowOrderListTabsForWsc = Boolean(this.orderListTabOpt.isShow && !this.isDrugShop);
      const isShowOrderListTabsForRetail = Boolean(
        !this.orderListTabOpt.isShow && this.orderListTabOpt.isShowRetailTabs
      );
      const isShowBindAccount = Boolean(
        !this.userInfo.buyerId && get(this.openAppConfig, 'bindYzAccount', false)
      );
      const isShowBindPhone = Boolean(!this.userInfo.buyerId);

      const { topNavHeight } = this;
      const searchBarHeight = isShowSearchBar ? 50 : 0;
      const orderListTabsHeightForWsc = isShowOrderListTabsForWsc ? 44 : 0;
      const orderListTabsHeightForRetail = isShowOrderListTabsForRetail ? 44 : 0;

      let bindPhoneHeight = 0;
      let bindAccountHeight = 0;
      /* #ifdef web */
      bindAccountHeight = isShowBindAccount ? 22 : 0;
      /* #endif */
      /* #ifdef weapp */
      bindPhoneHeight = isShowBindPhone ? 46 : 0;
      /* #endif */

      if (this.isIvr) {
        return searchBarHeight;
      }

      return (
        topNavHeight +
        searchBarHeight +
        orderListTabsHeightForWsc +
        orderListTabsHeightForRetail +
        bindAccountHeight +
        bindPhoneHeight
      );
    },
    // 当前(tab下)是否订单列表为空
    isEmptyList() {
      return this.orderListCount === 0 && !this.loading;
    },
    // 是否需要展示空状态提示组件
    isShowEmptyTip() {
      return this.isEmptyList && !this.isShowWelikeEntry;
    },
    isRetailShop() {
      return checkRetailShop(this.shopMetaInfo);
    },
    isEduShop() {
      return checkEduShop(this.shopMetaInfo);
    },
    isChainShop() {
      return checkChainStore(this.shopMetaInfo);
    },
    pageType() {
      // NOTICE: weapp 的 query 是 pagetype
      return (
        get(window, 'query.pageType') ||
        get(this.query, 'pageType') ||
        get(this.query, 'query.pagetype') ||
        QUERY_PAGE_TYPE.SINGLE
      );
    },
    orderMark() {
      return get(window, 'query.orderMark', '');
    },
    showRecommend() {
      return this.pageType !== 'all' && !this.activityTab.hasNext;
    },
    isDrugShop() {
      return (this.query || {}).type === 'drug';
    },
  },
  actions: (ctx: any) => {
    return {
      SET_ORDER_LIST_TAB_OPT(orderListTabOpt: OrderListTabOpt) {
        this.orderListTabOpt = orderListTabOpt;
      },
      SET_SEARCH_BAR_OPT(searchBarOpt: SearchBarOpt) {
        this.searchBarOpt = searchBarOpt;
      },
      SET_IS_SHOW_WELIKE_ENTRY(isShow: boolean) {
        this.isShowWelikeEntry = isShow;
      },
      SET_DIRECT_BUY_AGAIN_BTN_CONFIG(data) {
        this.directBuyAgainBtnConfig = data;
      },
      SET_USER_INFO(userInfo: { buyerId: string }) {
        this.userInfo = userInfo;
      },
      SET_QUERY(query: Query) {
        this.query = mapKeysToCameLCase(query);
        this.searchCondition = { ...this.searchCondition, value: (query || {}).keyword || '' };
        // TODO: 这里的代码后续要优化一下，this.query.type 应该改成 query.type 改动前注意前搜索事件search-order
        let type = '';
        if (this.query.type) {
          type = this.query.type;
        }

        if (this.query.activeTab) {
          // 开放参数转换

          const valMap = {
            // 所有订单
            all: 'all',
            // 待付款的订单
            waitPay: 'topay',
            // 待发货的订单
            waitShip: 'tosend',
            // 已发货的订单
            shipped: 'send',
            // 待评价的订单
            waitEvaluate: 'toevaluate',
          };

          type = valMap[this.query.activeTab] || type;
        }

        if (type) {
          this.setActivityTab({ ...this.activityTab, type });
        }
      },
      SET_POINTS_NAME(pointsName: string) {
        this.pointsName = pointsName;
      },
      getBannerId(index = 0) {
        const pageRandomNumber = makeRandomString(8);
        const loggerSpm = ctx?.logger?.getSpm() || '';
        return `${loggerSpm}~new_buy_again_click~${index}~${pageRandomNumber}`;
      },
      /**
       * TODO: 这个方法不应该独立存在，他应该和list.json接口合并，因为他的值会影响
       * !formatOrderList，如果这个api的请求结果落后于formatOrderLIst之后就会导致
       * !formatOrderList的结果有问题，目前暂时在fetchMoreOrder和refreshOrderInfo
       * !两个方法上加了前置判断，如果后续合并到list.json接口中之后就不需要这个前置判断了
       * !可以移除掉，另外state中的canUseTradeUmpV1: undefined 是为了判断canUseTradeUmpV1
       * !是否已经被赋值过了，如果接口合并之后，这里应该调整为正常的canUseTradeUmpV1: false
       */
      async initCanUseTradeUmpV1() {
        return api
          .API_USE_TRADE_UMP_V1()
          .then((data) => {
            this.canUseTradeUmpV1 = data;
          })
          .catch((error) => {
            console.log(error);
            this.canUseTradeUmpV1 = false;
            throw new Error(error.msg || '服务器开小差了');
          });
      },
      // 重置tab信息
      resetTab() {
        const defaultType = get(this, 'orderListTabOpt.tabs[0].type', '');
        this.setActivityTab({
          ...this.activityTab,
          ...DEFAULT_ACTIVITY_TAB,
          type: defaultType,
        });
      },
      refreshList() {
        this.orderList = [];
        this.orderListCount = this.orderList.length;
        this.renderOrderList = [];
        this.fetchMoreOrder(this.activityTab.type);
      },
      appendOrderList(newOrderList) {
        newOrderList.forEach((item) => {
          this.orderList.push(item);
          this.renderOrderList.push(item.renderData);
        });
        this.orderListCount = this.orderList.length;
      },
      setActivityTab(data) {
        this.activityTab = { ...data };
      },
      updateOrderItem(listIndex, newData = []) {
        try {
          const oldList = JSON.parse(JSON.stringify(this.orderList));
          if (newData) {
            oldList.splice(listIndex, 1, ...newData);
          } else {
            oldList.splice(listIndex, 1);
          }
          this.setActivityTab({
            ...this.activityTab,
          });
          this.orderList = oldList;
        } catch (e) {
          console.error(e);
        }
      },
      async refreshOrderInfo({ orderNo }) {
        const listIndex = this.orderList.findIndex((listItem) => listItem.orderNo === orderNo);
        if (listIndex === -1) {
          console.warn('找不到需要刷新的订单信息');
          return;
        }
        if (this.canUseTradeUmpV1 === undefined) {
          await this.initCanUseTradeUmpV1();
        }
        // 待收货列表收货后直接不展示该订单
        // if (this.activityTab.type !== 'all') {
        //   this.updateOrderItem(listIndex);
        //   return;
        // }
        api
          .API_ONE_ORDER({
            orderNo,
            canUseTradeUmpV1: this.canUseTradeUmpV1,
            isDrugList: String(this.isDrugShop) as 'true' | 'false',
            // 零售订单列表传入1，会获取详细数据
            retailFulfillDetail: Number(
              ctx.scene === PAGE_SCENE.RETAIL &&
                ['current', 'history'].includes(this.activityTab.type)
            ),
          })
          .then((data: any) => {
            if (data.list.length) {
              this.updateOrderItem(listIndex, data.list);
            }
          })
          .catch((error) => {
            console.log(error);
            throw new Error(error.msg || '服务器开小差了');
          });
      },
      async fetchMoreOrder(curTabType: string) {
        this.loading = true;
        if (this.canUseTradeUmpV1 === undefined) {
          await this.initCanUseTradeUmpV1();
        }
        const { activityTab, searchCondition, pageType, orderMark } = this;

        const paramsFormater = {
          page_id: activityTab.pageId || 'wsc',
          page: activityTab.page + 1,
          page_type: pageType,
          page_size: activityTab.pageSize || 10,
          type: activityTab.type || 'all',
          orderMark,
          from: this.isIvr ? 'ivr' : '',
          isDrugList: String(this.isDrugShop) as 'true' | 'false',
          haveOfflineOrder: 2, // 表示可以获取线下门店订单，数字前后端约定好的，表示该版本小程序支持展示
          canUseTradeUmpV1: this.canUseTradeUmpV1,
          // 零售订单列表传入1，会获取详细数据
          retailFulfillDetail: Number(
            ctx.scene === PAGE_SCENE.RETAIL && ['current', 'history'].includes(activityTab.type)
          ),
        } as Record<string, any>;
        if (this.query.scene === 'onlineScanBuy') {
          paramsFormater.caller = 'online_scan_buy';
        }
        // 爱逛默认查询所有店铺订单数据
        if (!paramsFormater.page_type && orderMark === 'weapp_guang') {
          const kdtId = this.query.kdt_id;
          paramsFormater.page_type = kdtId ? 'single' : 'all';
        }
        const searchValue = get(searchCondition, 'value', '');
        if (chinaMobile(searchValue)) {
          paramsFormater.receivertel = searchValue;
        } else if (searchValue) {
          paramsFormater.keyword = searchValue;
        }
        if (this.isEduShop && (paramsFormater.type === 'going' || paramsFormater.type === 'send')) {
          // 1 表示查询知识付费订单 0 表示排除知识付费订单
          paramsFormater.knowledgeGoods = paramsFormater.type === 'going' ? 1 : 0;
        }

        if (paramsFormater.page === 1) {
          setTimeout(() => {
            /* #ifdef web */
            window.scrollTo(0, 0);
            /* #endif */
            /* #ifdef weapp */
            Tee.$native.pageScrollTo({ scrollTop: 0 });
            /* #endif */
          }, 300);
        }

        return api
          .API_GET_ORDER_LIST(paramsFormater as any)
          .then((data: Record<string, any> = {}) => {
            data.hasNext = data.pageSize === data.list.length;
            // 如果当时拉取数据是的tab类型和当前tab类型不一致，则不处理，主要用于处理tab切换场景
            if (curTabType !== this.activityTab.type) {
              return {};
            }
            return data;
          })
          .then((newData: any) => {
            if (!!Object.keys(newData).length) {
              this.setActivityTab({
                ...activityTab,
                // 为什么这里不直接使用后端返回的hasNext的原因是因为后端给的hasNext在零售场景计算有问题
                // 明明只返回了3个订单的情况，hasNext还是true
                // TODO: 这个hasNext的判断可以后续做到Node端，减少一点客户端体积
                hasNext: newData.hasNext,
                page: newData.page,
                pageId: newData.pageId,
                pageSize: newData.pageSize,
              });
              this.appendOrderList(newData.list);
              this.updateOrderRepurchaseCoupon(newData.list);
            }

            this.loading = false;
          })
          .catch((error) => {
            console.log(error);
            this.setActivityTab({
              ...this.activityTab,
              hasNext: false,
            });
            this.loading = false;
            throw new Error(error.msg || '服务器开小差了');
          });
      },

      /**
       * 异步批量获取多个订单项对应的复购券信息
       */
      updateOrderRepurchaseCoupon(orderList: any[]) {
        const params = orderList.map<{
          orderNo: string;
          status: number;
          goodsIdList: number[];
        }>((item) => {
          const orderItem = get(item, 'orderItems[0]', {});
          const goodsIdList = get(orderItem, 'items', []).map(
            (item: { goodsId: any }) => item.goodsId
          );
          return {
            orderNo: item.orderNo,
            status: orderItem.status,
            goodsIdList,
          };
        });

        api.API_GET_ORDER_REPURCHASE_COUPON({ orderList: params }).then((orderRepurchaseCoupon) => {
          Object.keys(orderRepurchaseCoupon).forEach((orderNo: string) => {
            this.orderRepurchaseCouponStatus[orderNo] = orderRepurchaseCoupon[orderNo];
          });
          this.orderRepurchaseCouponStatusCount++;
        });
      },

      // 按钮处理
      async doOrderFooterAction({ btn = {}, listIndex, listItem }) {
        const { value: btnType } = btn as Record<string, any>;
        // 非微信H5环境，找人代付页面无法支付
        if (btnType === 'peerpay' && !isWeixin()) {
          Toast('此渠道暂不支持代付');
          return;
        }

        const orderItem = getOrderItem(listItem);
        const { kdtId, orderNo } = listItem;

        // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
        await ctx.cloud.invoke('beforeOrderBtnClickAsync', {
          type: yunBtnTypeMap.getKeyByValue(btnType) ?? btnType,
          value: (btn as Record<string, any>).text,
          orderNo,
        });
        // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
        await ctx.cloud.invoke('beforeOrderActionHandle', {
          type: btnType === BTN_CONF.directBuyAgain ? BTN_CONF.buyAgain : btnType,
          orderNo,
        });
        if (btnType.startsWith('cloud_')) {
          return;
        }

        const directUrl = getDirectUrl({ listItem, btn });
        const payGradeCard = get(orderItem, 'items[0].payGradeCard', false);

        /* #ifdef weapp */
        if (!(await this.notUpdateKdtId(listItem))) {
          await this.updateKdtId(kdtId);
        }
        /* #endif */

        this.listIndex = listIndex;
        this.listItem = listItem;
        switch (btnType) {
          case 'cancel':
            this.cancelOrder({ listItem });
            break;
          case 'cancelPayOnDelivery':
            // @ts-ignore
            Dialog.alert({
              message: '商家可能已经接单配货，你需联系客服申请取消订单哟',
              confirmButtonText: '我知道了',
            });
            break;
          case 'laterReceive':
            this.laterReceive({ listItem });
            break;
          case 'confirmReceive':
          case 'confirmReceiveHotel':
            this.confirmReceive({ listIndex, listItem, btn });
            break;
          case 'buyAgain':
            this.buyAgain({ listItem });
            break;
          case 'directBuyAgain':
            this.directBuyAgain({ listItem });
            break;
          case 'deleteOrder':
            this.deleteOrder({ listItem });
            break;
          case 'expressReminder':
          case 'expressReminderHotel':
            this.expressReminder({ listIndex, listItem, btn });
            break;
          /* #ifdef weapp */
          case 'topay':
          case 'topayDeposit':
          case 'topayRetainage':
          case 'inviteHelp':
          case 'peerpay':
            this.gotoPay({ listItem });
            break;
          case 'transport':
            this.transport({ listItem });
            break;
          case 'viewCardDetail':
            this.viewCardDetail({ listItem });
            break;
          case 'grouponDetail':
            this.grouponDetail({ listItem });
            break;
          case 'lotteryResult':
            this.lotteryResult({ listItem });
            break;
          case 'evaluate':
            this.evaluate({ listItem });
            break;
          case 'selfFetchCode':
            this.onPopSelfFetchCode({ listItem });
            break;
          case 'paidPromotion':
            this.paidPromotion({ listItem });
            break;
          case 'coupon':
            LOG_CLICK_MARKETING_PAIDPRO(ctx, { type: 'coupon', orderNo });
            this.paidPromotion({ listItem });
            break;
          case 'liveQrCode':
            LOG_CLICK_MARKETING_PAIDPRO(ctx, { type: 'liveQrCode', orderNo });
            this.paidPromotion({ listItem });
            break;
          case 'fissionCoupon': // 领优惠券
            LOG_CLICK_DIVIDE_COUPON(ctx);
            this.fissionCoupon({ listItem });
            break;
          /* #else */
          case 'shared':
            this.handleOrderShare({ listItem });
            break;
          /* #endif */
          default:
            /* #ifdef web */
            if (payGradeCard) {
              // 判断是否属于续费商品
              handleGoodsLevel(listItem, directUrl);
            } else {
              navigate({
                web: {
                  type: 'safeLink',
                  safeLink: { url: directUrl },
                },
              });
            }
            /* #endif */
            break;
        }
      },

      toggleSelfFetchPopup({ isShow }) {
        this.isShowSelfFetchPopup = isShow;
      },

      onPopSelfFetchCode({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const { kdtId } = listItem;
        const { orderNo } = orderItem;
        Toast({ type: 'loading' });
        api
          .API_SELF_FETCH_DETAIL({ orderNo, kdtId })
          .then((res = {}) => {
            this.selfFetchInfo = mapKeysToCameLCase(res).selfFetchVoucher || {};
            this.toggleSelfFetchPopup({ isShow: true });
            Toast.clear();
          })
          .catch((err) => {
            Toast.clear();
            errorToast(err, { message: '获取自提信息失败' });
          });
      },

      fissionCoupon({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const orderNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderItem.orderNo;
        navigate({
          url: args.add('/packages/ump/fission/index', { sharer: 1, order_no: orderNo }),
        });
      },

      paidPromotion({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const orderNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderItem.orderNo;
        const { kdtId } = listItem;
        const h5Url = args.add('/wscump/paid-promotion/fetch', {
          kdtId,
          orderNo,
          source: 'order_list',
        });

        navigate({
          url: args.add('/pages/common/webview-page/index', { src: buildUrl(h5Url, 'h5', kdtId) }),
        });
      },

      lotteryResult({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const { activityId } = orderItem.items[0];

        navigate({
          url: args.add('/packages/collage/lottery/result/index', { activity_id: activityId }),
        });
      },

      grouponDetail({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const orderNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderItem.orderNo;

        const goodsItem = orderItem.items[0];
        const isLotteryGroup = get(orderItem, 'orderExtra.isLotteryGroup', false);
        const { goodsId, activityId, alias, activityType } = goodsItem;

        let url = '';
        if (goodsItem.goodsType === 31) {
          url = args.add('/packages/paidcontent/groupon/index', {
            orderNo,
            goodId: goodsId,
            activity_type: activityType,
          });
        } else if (isLotteryGroup) {
          url = args.add('/packages/collage/lottery/detail/index', {
            order_no: orderNo,
            activity_id: activityId,
          });
        } else {
          url = args.add('/packages/collage/groupon/detail/index', {
            orderNo,
            groupAlias: alias,
          });
        }

        navigate({ url });
      },

      viewCardDetail({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const orderNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderItem.orderNo;

        navigate({
          url: args.add('/packages/trade/cert/verify-ticket/index', { order_no: orderNo }),
        });
      },

      transport({ listItem }) {
        const orderItem = getOrderItem(listItem);
        const orderNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderItem.orderNo;

        navigate({
          url: args.add('/packages/trade/order/express/index', { orderNo }),
        });
      },

      async gotoPay({ listItem }) {
        const { kdtId, orderNo } = listItem;
        const orderItem = getOrderItem(listItem);
        const extra = get(orderItem, 'orderExtra', {});
        const { isMallGroupBuy, isFinalPayment } = extra;
        const goodsList = get(orderItem, 'items', []);
        const goodsItem = get(goodsList, '[0]', {});
        const { activityType } = goodsItem;

        const orderFormatedNo =
          get(listItem, 'orderList.length', 0) > 1 ? listItem.orderNo : orderNo;
        let url = '';

        // 待支付的快速下单订单跳转专门的支付页
        if (extra.isFastAd && orderItem.statusCode === ORDER_STATUS.TOPAY) {
          url = `/packages/order-native/fastbuy/index?orderNo=${orderItem.orderNo}`;
        } else if (+activityType === 116 && isFinalPayment) {
          // 助力定金膨胀且是支付尾款
          url = `/packages/ump/handsel-expand/index?order_no=${orderNo}&kdt_id=${kdtId}`;
        } else if (isMallGroupBuy) {
          // 群拼团订单支付按钮
          url = `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}`;
        } else {
          await handleGoodsLevel(listItem);

          navigateToRantaPage({
            pageType: PAGE_TYPE.PAY,
            query: {
              orderNo: orderFormatedNo,
            },
          });
          return;
        }

        navigate({ url });
      },

      gotoOrderSearch() {
        Event.once('search-order', ({ keyword }) => {
          if (this.activityTab.type) this.query.type = this.activityTab.type;
          this.setActivityTab({ ...this.activityTab, page: 0, hasNext: true });
          this.SET_QUERY({ ...this.query, keyword });
          this.refreshList();
        });

        const { kdtId, subKdtId } = this.query;
        LOG_SEARCH_ORDER_CLICK(ctx);
        const query: Record<string, any> = {};
        /* #ifdef weapp */
        if (kdtId) {
          query.kdt_id = kdtId;
        }
        /* #else */
        if (subKdtId || kdtId) {
          query.kdt_id = subKdtId || kdtId;
        }
        /* #endif */

        /* #ifdef web */
        const { isXhsLocalLife } = this.query;
        query.isXhsLocalLife = isXhsLocalLife;
        /* #endif */

        navigate({
          url: args.add('/packages/trade/order-search/index', query),
          web: {
            type: 'safeLink',
            safeLink: { url: args.add('/wsctrade/order/search', query) },
          },
        });
      },

      gotoOrderDetail({ listItem }) {
        /* #ifdef weapp */
        this.gotoOrderDetailByWeapp({ listItem });
        /* #else */
        this.gotoOrderDetailByWeb({ listItem });
        /* #endif */
      },

      async gotoOrderDetailByWeapp({ listItem }) {
        /* #ifdef weapp */
        if (!(await this.notUpdateKdtId(listItem))) {
          await this.updateKdtId(listItem.kdtId);
        }

        const orderItem = getOrderItem(listItem);

        if (!listItem) {
          return;
        }
        const goodsItem = orderItem.items[0];
        const cardGoodsId = goodsItem.goodsType === 20 ? goodsItem.goodsId : '';
        let url = '';
        let toPayParams = null;
        const orderExtra = orderItem.orderExtra || {};

        // 待支付的快速下单订单跳转专门的支付页
        if (orderExtra.isFastAd && orderItem.statusCode === ORDER_STATUS.TOPAY) {
          url = args.add('/packages/order-native/fastbuy/index', { orderNo: orderItem.orderNo });
        } else if (orderExtra.isMallGroupBuy) {
          url = args.add('/packages/groupbuying/buyer-trade/detail/index', {
            orderNo: orderItem.orderNo,
          });
        } else if (goodsItem.goodsType === 206) {
          url = args.add('/packages/trade/order/unicashier-result/index', {
            order_no: orderItem.orderNo,
          });
        } else if (+goodsItem.activityType === 116 && orderExtra.isFinalPayment) {
          url = args.add('/packages/ump/handsel-expand/index', {
            order_no: orderItem.orderNo,
            kdt_id: listItem.kdtId,
          });
        } else if (orderItem.statusCode === ORDER_STATUS.TOPAY) {
          // 判断 连续包月付费商品
          await handleGoodsLevel(listItem);

          toPayParams = {
            orderNo: listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo,
          };
        } else if (goodsItem.payGradeCard) {
          url = args.add('/packages/levelcenter/pay/index', { alias: goodsItem.alias });
        } else if (cardGoodsId) {
          url = args.add('/packages/card/detail/index', { goods_id: cardGoodsId });
        } else if (
          +goodsItem.activityType === 401 /* 盲盒购买订单 */ &&
          orderItem.statusCode !== 'toPay'
        ) {
          if (orderItem.statusCode === 'cancel') {
            Toast('订单已关闭');
            return;
          }
          const h5Url = buildUrl(
            args.add('/wscump/blind-box/detail', {
              kdtId: listItem.kdtId,
              orderNo: orderItem.orderNo,
            }),
            'h5',
            listItem.kdtId
          );
          navigate({
            url: args.add('/pages/common/webview-page/index', { src: h5Url }),
          });
          return;
        } else if (listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE) {
          // CRM线下门店订单
          url = args.add('/packages/trade/crm-order-detail/index', {
            order_no: orderItem.orderNo,
            kdtId: listItem.kdtId,
          });
          LOG_CLICK_CRM_OFFLINE_ORDER(ctx);
        } else {
          url = args.add('/packages/trade/order/result/index', { order_no: orderItem.orderNo });
        }

        if (toPayParams) {
          navigateToRantaPage({
            pageType: PAGE_TYPE.PAY,
            query: toPayParams,
          });
        } else {
          navigate({ url });
        }
        /* #endif */
      },

      async gotoOrderDetailByWeb({ listItem }) {
        /* #ifdef web */
        let kdtId = 0;
        const { kdtId: newKdtId = 0, kdt_id: oldKdtId = 0 } = get(window, '_global', {});
        kdtId = newKdtId || oldKdtId;
        const orderItem = getOrderItem(listItem);

        if (this.isAlipayApp || this.isQQApp || this.isXhsApp || this.isKsApp) {
          if (+orderItem.buyWay === 7 && +orderItem.status !== 99) {
            Toast('此渠道暂不支持查看代付订单');
            return;
          }
        }

        // 增加 连续包月会员商品，修改跳转链接
        const payGradeCard = get(listItem, 'orderItems[0].items[0].payGradeCard', false);
        if (payGradeCard) {
          await handleGoodsLevel(listItem);
        }
        // todo：增加判断是否为盲盒订单，修改跳转链接
        const { orderNo = '', kdtId: orderKdtId = 0, statusCode = '' } = listItem;
        if (
          (+orderItem.activityType === ACTIVITY_TYPE.blindBoxBuy ||
            +orderItem.activityType === ACTIVITY_TYPE.blindBoxVerification) &&
          statusCode !== 'topay'
        ) {
          if (statusCode === 'cancel') {
            Toast('订单已关闭');
            return;
          }
          const h5Url = buildUrl(
            `/wscump/blind-box/detail?orderNo=${orderNo}&kdtId=${orderKdtId}`,
            'h5',
            orderKdtId
          );
          navigate({
            web: {
              type: 'znb',
              znb: {
                url: h5Url,
                weappUrl: `/pages/common/webview-page/index?src=${h5Url}`,
              },
            },
          });
          return;
        }

        if (this.isAlipayApp && get(orderItem, 'orderExtra.isOfflineOrder', false)) {
          navigate({
            web: {
              type: 'znb',
              znb: {
                aliappUrl: `packages/freego/order-detail/index?kdt_id=${kdtId}&order_no=${orderItem.orderNo}`,
              },
            },
          });
          return;
        }

        // 小红书小程序需要打开新的页面
        if (this.isXhsApp || this.isKsApp) {
          navigate({
            web: {
              type: 'znb',
              znb: {
                url: orderItem.detailUrl,
                xhsUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(
                  orderItem.detailUrl
                )}`,
                ksUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(
                  orderItem.detailUrl
                )}`,
              },
            },
          });
          return;
        }

        // CRM线下门店订单详情跳转链接
        if (listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE) {
          const h5Url = buildUrl(
            `/wsctrade/order/detail-crm?order_no=${orderNo}&kdt_id=${orderKdtId}&isCrmOfflineOrder=true&tee_page=true`,
            'h5',
            orderKdtId
          );
          LOG_CLICK_CRM_OFFLINE_ORDER(ctx);
          navigate({
            web: {
              type: 'znb',
              znb: {
                url: h5Url,
                weappUrl: args.add('/packages/trade/crm-order-detail/index', {
                  order_no: orderNo,
                  kdtId: orderKdtId,
                }),
              },
            },
          });
          return;
        }

        const pdlive = args.get('pdlive', window.location.href);
        if (pdlive) {
          orderItem.detailUrl = `${orderItem.detailUrl}&pdlive=${pdlive}`;
        }

        navigate({
          web: {
            type: 'safeLink',
            safeLink: { url: orderItem.detailUrl, kdtId },
          },
        });
        /* #endif */
      },

      evaluate({ listItem }) {
        const { orderNo } = listItem;
        setStorage('order-list:processOrder', { orderNo });
        navigate({
          url: args.add('/packages/evaluation/order/create/index', { order_no: orderNo }),
        });
      },

      activityTabChange(tab: Record<string, any> = {}) {
        setNavigationBarTitle(TITLE_CONF[tab.type] || '订单列表');
        const { activityTab } = this;
        if (activityTab.type === tab.type) return;
        this.loading = false;

        this.orderList = [];
        this.setActivityTab({
          ...tab,
          hasNext: true,
          page: 0,
          pageId: ['all', 'topay'].indexOf(tab.type) >= 0 ? 'wsc' : '1',
          pageSize: PAGE_SIZE,
        });
        this.refreshList();
      },

      // 分享订单
      toggleOrderSharePopup({ isShow }) {
        this.isShowOrderSharePopup = isShow;
      },
      toggleOrderSharePoster({ isShow }) {
        this.isShowOrderSharePoster = isShow;
      },
      initShareUrl({ listItem = {} }: any) {
        const { kdtId, orderNo } = listItem;
        let link = addAnalyticsParams(
          ctx,
          args.add(buildUrl('/wsctrade/order/share', 'h5', kdtId), {
            order_no: orderNo,
            kdt_id: kdtId,
            show_share_guide: 1,
          })
        );

        if (isNewHotelGood(listItem)) {
          const firstOrderItem = listItem.orderItems[0] || {};
          link = firstOrderItem.newHotelGoodsDetailUrl || '';
        }

        this.shareUrl = link;
      },
      initShareOptions() {
        if (isXhsApp || isKsApp) {
          this.options = SHARE_OPTIONS.filter((option) => option.key === 'copylink');
        } else if (this.isNewHotelGood) {
          this.options = SHARE_OPTIONS.filter(
            (option) => !['toutiao', 'haibao'].includes(option.key)
          );
        } else {
          this.options = SHARE_OPTIONS.filter((option) => option.key !== 'toutiao');
        }
      },
      resetShareOptions(options = []) {
        if (options.length) {
          this.shareOptions = options
            .map((option) => {
              return SHARE_OPTIONS.find((item) => item.key === option);
            })
            .filter((item) => !!item);
        } else {
          this.initShareOptions();
        }
      },
      handleOrderShare({ listItem = {} as Record<string, any>, options = [] }) {
        this.resetShareOptions(options);

        this.orderNo = listItem.orderNo || '';
        this.initShareUrl({ listItem });

        this.toggleOrderSharePopup({ isShow: true });
      },

      // 取消订单
      toggleOrderCancelPopup({ isShow }) {
        // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
        ctx.cloud.invoke('beforeCancelOrderPopupToggle', { isShow }).then((res) => {
          const config = { ...DEFAULT_CANCEL_ORDER_POPUP_CONFIG };
          if (res?.config?.autoAddCart) {
            config.autoAddCart = {
              ...config.autoAddCart,
              ...res.config.autoAddCart,
            };
          }
          this.cancelOrderPopupConfig = config;
          this.isShowOrderCancelPopup = isShow;
        });
      },
      // 取消订单
      cancelOrder({ listItem }) {
        const _cancel = ({ listItem }) => {
          const { orderNo, kdtId } = listItem;
          const orderItem = getOrderItem(listItem);
          const { goodsList = [] } = orderItem;
          const goodsIds = goodsList.map(({ goodsId }) => goodsId);
          api.API_GET_REASON_LIST({ orderNo, kdtId }).then((reasonInfo) => {
            if (reasonInfo.isShowItemPutInCartButton) {
              LOG_BULK_ADD_CART(ctx, { orderNo, goodsIds });
            }
            this.cancelOrderReasonInfo = reasonInfo;
            this.cancelOrderNo = orderNo;
            LOG_VIEW_CANCEL_ORDER_REASON(ctx, { orderNo });
            this.toggleOrderCancelPopup({ isShow: true });
          });
        };

        // 是否是充值优惠订单
        const orderItem = getOrderItem(listItem);
        const isRechargeOrderFree = (orderItem.orderExtra || {}).isRechargeOrderFree || false;
        if (isRechargeOrderFree) {
          // @ts-ignore
          Dialog.confirm({
            title: '取消订单将放弃充值优惠',
            message: '充值优惠仅针对本订单，取消订单后将无法享受本次充值优惠',
            confirmButtonText: '使用优惠',
            cancelButtonText: '放弃优惠',
          }).catch(() => {
            _cancel({ listItem });
          });
        } else {
          _cancel({ listItem });
        }
      },
      confirmCancel({ reason, isBuyAgain }) {
        const { orderNo, kdtId, orderExtra } = this.listItem;
        /* #ifdef weapp */
        this.doCancelOrder({ orderNo, kdtId, reason, isBuyAgain });
        /* #else */
        const orderItem = getOrderItem(this.listItem);
        const goodsList = orderItem.items;
        const isPeerPay = get(orderExtra, 'payTool', []).includes('PEER_PAY');

        // 微信环境下，并且店铺支持代付
        if (reason === 28 && isPeerPay && isWeixin()) {
          // 无法正常支付，跳转代付
          LOG_CANCLE_ORDER_COMPONENT_VIEW(ctx, { orderNo, type: 'get_paid' });

          // @ts-ignore
          Dialog.confirm({
            title: '支付遇到问题了？',
            message: '你还可以找好友帮你代付哦',
            confirmButtonText: '找人代付',
            cancelButtonText: '取消订单',
            showCancelButton: true,
          })
            .then(() => {
              LOG_GET_PAID_CLICK(ctx, { orderNo });
              setTimeout(() => {
                const url = buildUrl(
                  args.add('/pay/peerpay/invite', { order_no: orderNo, kdt_id: kdtId }),
                  'wap',
                  kdtId,
                  { notReplaceDomain: true }
                );
                navigate({
                  web: {
                    type: 'safeLink',
                    safeLink: { url },
                  },
                });
              });
            })
            .catch(() => {
              LOG_CANCLE_ORDER_CLICK(ctx, { orderNo, type: 'get_paid' });
              this.doCancelOrder({ orderNo, kdtId, isBuyAgain, reason });
            });
        } else if (reason === 27 && goodsList.length === 1) {
          // 规格数量拍错, 单商品跳转商详
          LOG_CANCLE_ORDER_COMPONENT_VIEW(ctx, { orderNo, type: 'modify_goods' });

          // @ts-ignore
          Dialog.confirm({
            message: '你可以修改商品信息后重新下单',
            confirmButtonText: '前往修改',
            cancelButtonText: '取消订单',
            showCancelButton: true,
          })
            .then(() => {
              LOG_MODIFY_GOODS_CLICK(ctx, { orderNo, goodsList });
              setTimeout(() => {
                this._cancelOrder({ kdtId, orderNo, reason, isBuyAgain })
                  .then(() => {
                    Toast('取消订单成功');

                    const { url } = goodsList[0];
                    navigate({
                      web: {
                        type: 'safeLink',
                        safeLink: { url },
                      },
                    });
                  })
                  .catch((err) => {
                    errorToast(err, { message: '重新下单失败' });
                  });
              });
            })
            .catch(() => {
              LOG_CANCLE_ORDER_CLICK(ctx, { orderNo, type: 'modify_goods' });
              this.doCancelOrder({ orderNo, kdtId, reason, isBuyAgain });
            });
        } else {
          this.doCancelOrder({ orderNo, kdtId, reason, isBuyAgain });
        }
        /* #endif */
      },
      doCancelOrder(data: { orderNo: string; kdtId: number; reason: string; isBuyAgain: boolean }) {
        this._cancelOrder(data)
          .then(() => {
            Toast('取消订单成功');

            LOG_SELECT_REASON(ctx, { orderNo: data.orderNo, reason: data.reason });
            LOG_CANCLE_ORDER(ctx, { orderNo: data.orderNo, isBuyAgain: data.isBuyAgain });

            this.refreshOrderInfo({ orderNo: data.orderNo });
          })
          .catch((err) => {
            errorToast(err, { message: '取消订单失败' });
          });
      },
      _cancelOrder(data: { orderNo: string; kdtId: number; reason: string; isBuyAgain: boolean }) {
        Toast({ type: 'loading' });
        return new Promise((resolve, reject) => {
          api.API_CANCEL_ORDER(data).then(
            (data) => {
              Toast.clear();
              resolve(data);
            },
            (err) => {
              Toast.clear();
              reject(err);
            }
          );
        });
      },

      // 再来一单 - 失败商品确认弹窗
      toggleBuyAgainFailGoodsDialog({ isShow }) {
        this.isShowBuyAgainFailGoodsDialog = isShow;
      },
      handleBuyAgainFailGoods(payload) {
        return new Promise((resolve) => {
          this.toggleBuyAgainFailGoodsDialog({ isShow: true });
          this.failBuyAgainGoodsList = payload;
          this.failBuyAgainGoodsResolve = resolve;
        });
      },
      confirmBuyAgainFailGoods() {
        if (this.failBuyAgainGoodsResolve) {
          this.failBuyAgainGoodsResolve();
        }
      },

      // 删除订单
      deleteOrder({ listItem }) {
        const { orderNo, kdtId } = listItem;
        const orderItem = getOrderItem(listItem);
        const { buyerId } = orderItem;
        LOG_ORDER_DELETE(ctx, { orderNo, kdtId, buyerId });
        // @ts-ignore
        Dialog.confirm({
          title: '确定删除订单？',
          message: '删除订单后无法恢复，无法处理您的售后问题，请慎重考虑。',
          cancelButtonText: '取消',
          confirmButtonText: '删除',
          confirmButtonColor: '',
        })
          .then(() => {
            Toast({ type: 'loading' });
            api
              .API_DELETE_ORDER({ orderNo })
              .then(() => {
                Toast.clear();
                Toast('订单已删除');
                this.orderList.splice(this.listIndex, 1);
                this.renderOrderList.splice(this.listIndex, 1);
                this.orderListCount = this.orderList.length - 1;
                this.setActivityTab(this.activityTab);
              })
              .catch((err) => {
                Toast.clear();
                errorToast(err, { message: '订单删除失败' });
              });
          })
          .catch(() => {
            // @ts-ignore
            Dialog.close();
          });
      },

      // 再来一单 - 跳转购物车
      async buyAgain({ listItem }) {
        const { orderNo, kdtId } = listItem;
        const orderItem = getOrderItem(listItem);
        const { goodsList = [] } = orderItem;
        const goodsIds = goodsList.map(({ goodsId }) => goodsId);

        const repurchaseCoupon = get(this, `orderRepurchaseCouponStatus[${listItem.orderNo}]`, {});
        const repurchaseCouponId = get(repurchaseCoupon, 'id');
        const params = {
          last_order_no: orderNo,
          order_no: orderNo,
          goods_id: goodsIds,
          is_new: false,
          // TODO: repurchaseCoupon
          has_buyagain_coupon: !!repurchaseCouponId,
        };
        LOG_NEW_BUY_AGAIN_CLICK(ctx, params);

        Toast({ type: 'loading' });
        Promise.all([
          api.API_BUY_AGAIN({ orderNo, kdtId }),
          ...(repurchaseCouponId ? [this.claimRepurchaseCoupon(repurchaseCoupon)] : []),
        ])
          .then(([buyAgainResult, claimRepurchaseCouponResult]) => {
            Toast.clear();
            const {
              code,
              valueCopywriting = '',
              unitCopywriting = '',
            } = claimRepurchaseCouponResult;

            /* #ifdef weapp */
            const cartQuery = {
              repurchaseCouponStatus: code,
              couponValue: valueCopywriting,
              couponUnit: unitCopywriting,
            };
            navigateToRantaPage({
              pageType: PAGE_TYPE.CART,
              query: cartQuery,
            });
            /* #else */
            buyAgainResult = Array.isArray(buyAgainResult) ? buyAgainResult : [];
            const urlParams = repurchaseCoupon
              ? {
                  repurchaseCouponStatus: 0,
                  couponValue: valueCopywriting,
                  couponUnit: unitCopywriting,
                }
              : {};
            const cartUrl = args.add('/wsctrade/cart', {
              kdt_id: kdtId,
              ...urlParams,
            });
            // 返回的数据是添加到购物车失败的商品数组，全部成功时返回空数组
            if ((buyAgainResult as any[]).length === 0) {
              navigate({
                web: {
                  type: 'safeLink',
                  safeLink: { url: cartUrl, kdtId },
                },
              });
            } else {
              skynet.info('再来一单部分加购成功统计', { orderNo });
              // 商品sku与数量信息需要从订单商品列表中获取，然后构造一个新的数组
              const parsedGoodsList = buildGoodsList(goodsList, buyAgainResult);

              ctx.process
                .invokePipe('handleBuyAgainFailGoods', parsedGoodsList.failList)
                .then(() => {
                  // 商品全部添加失败时直接隐藏弹层，不跳转
                  if (goodsList.length !== (buyAgainResult as any[]).length) {
                    navigate({
                      web: {
                        type: 'safeLink',
                        safeLink: { url: cartUrl, kdtId },
                      },
                    });
                  }
                });
            }
            /* #endif */
          })
          .catch((err) => {
            console.log(err);
            Toast.clear();
            errorToast(err, { message: '再来一单请求失败。' });
          });
      },

      // 再来一单(New) - 跳转商详
      async directBuyAgain({ listItem }) {
        const { orderNo, kdtId } = listItem;
        const orderItem = getOrderItem(listItem);
        const { goodsList = [] } = orderItem;
        const goodsIds = goodsList.map(({ goodsId }) => goodsId);
        const bannerId = this.getBannerId();

        const repurchaseCoupon = get(this, `orderRepurchaseCouponStatus[${listItem.orderNo}]`, {});
        const repurchaseCouponId = get(repurchaseCoupon, 'id');
        const params = {
          banner_id: bannerId,
          buyer_id: +orderItem.buyerId,
          goods_id: goodsIds,
          order_no: orderNo,
          is_new: true,
          has_buyagain_coupon: !!repurchaseCouponId,
        };
        LOG_NEW_BUY_AGAIN_CLICK(ctx, params);

        let claimRepurchaseCouponResult: any = {};
        if (repurchaseCouponId) {
          claimRepurchaseCouponResult = await this.claimRepurchaseCoupon(repurchaseCoupon);
        }

        const { code, valueCopywriting = '', unitCopywriting = '' } = claimRepurchaseCouponResult;
        let preToastDesc = '';
        if (code !== undefined && +code === 0 && valueCopywriting && unitCopywriting) {
          preToastDesc = `已为你领取${valueCopywriting}${unitCopywriting}优惠券，下单享优惠`;
        } else if (COUPON_CODE_MAP[code]) {
          preToastDesc = COUPON_CODE_MAP[code];
        }
        api
          .API_DIRECT_BUY_AGAIN({ orderNo, kdtId, preToastDesc })
          .then((data: Record<string, any>) => {
            // 跳转商详页
            if (+data?.destination === 2) {
              const { alias } = data;
              const weappUrl = args.add('/pages/goods/detail/index', { alias });
              navigate({
                url: weappUrl,
                web: {
                  type: 'znb',
                  znb: {
                    url: buildUrl(`/wscgoods/detail/${alias}`, 'h5', kdtId),
                    weappUrl,
                  },
                },
              });
              return;
            }
            /* #ifdef weapp */
            const { book_key: bookKey } = data;
            const params = {
              bookKey,
              orderNo,
              banner_id: bannerId,
            };
            navigate({
              url: args.add('/packages/order/index', params),
            });
            /* #endif */
            /* #ifdef web */
            // 跳转下单页
            const payUrl = args.add(
              data.buy_url || get(window, '_global.url.trade', '') + data.url,
              {
                showwxpaytitle: 1,
                kdt_id: kdtId,
                book_key: data.book_key,
                banner_id: bannerId,
              }
            );
            if (env.isYouzanmars) {
              // @ts-ignore
              ZanJSBridgeAction.gotoWebview({
                url: payUrl,
                page: 'web',
              });
            } else if (isAlipayApp || isQQApp || isXhsApp) {
              navigate({
                web: {
                  type: 'znb',
                  znb: {
                    aliappUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
                    qqUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
                    xhsUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
                  },
                },
              });
            } else {
              navigate({ url: payUrl });
            }
            /* #endif */
          })
          .catch((err) => {
            errorToast(err, { message: '再来一单请求失败。' });
          });
      },

      // 领取复购券
      claimRepurchaseCoupon(repurchaseCoupon: {
        id: string;
        valueCopywriting: string;
        unitCopywriting: string;
      }) {
        return api
          .API_GET_VOUCHER({ activityId: repurchaseCoupon.id })
          .then((res: Record<string, any> = {}) => {
            return {
              ...res,
              code: 0,
              valueCopywriting: repurchaseCoupon.valueCopywriting,
              unitCopywriting: repurchaseCoupon.unitCopywriting,
            };
          })
          .catch((err) => {
            return err;
          });
      },

      // 延长收货
      laterReceive({ listItem }) {
        const { kdtId, orderNo } = listItem;
        LOG_DELAY_GOODS(ctx);
        api
          .API_CHECK_ORDER_DELAY_RECEIVE({ orderNo, kdtId })
          .then(() => {
            return new Promise<void>((resolve, reject) => {
              const { kdtId, orderNo } = listItem;
              // @ts-ignore
              Dialog.confirm({
                title: '延长收货时间',
                message: '每笔订单只能延长一次收货时间，如需多次延长请联系商家。',
                cancelButtonText: '确定延长',
                confirmButtonText: '取消',
              }).catch(() => {
                api
                  .API_DELAY_RECEIVE({ orderNo, kdtId })
                  .then((res: Record<string, any> = {}) => {
                    // @ts-ignore
                    Dialog.alert({
                      title: '延长收货时间',
                      message: res.msg,
                      confirmButtonText: '我知道了',
                    }).then(resolve);
                  })
                  .catch((err) => {
                    errorToast(err, { message: '延长收货失败，请重试' });
                    reject();
                  });
              });
            });
          })
          .catch((err) => {
            const errData = err.data;
            if (errData.code && errData.msg) {
              switch (errData.code) {
                case CHECK_ORDER_DELAY_RECEIVE_STATUS_MAP.TIME_NOT_ENOUGH:
                case CHECK_ORDER_DELAY_RECEIVE_STATUS_MAP.ALREADY_EXTENDED:
                  // @ts-ignore
                  Dialog.alert({
                    title: '延长收货时间',
                    message: errData.msg,
                    confirmButtonText: '我知道了',
                  });
                  break;
                default:
                  errorToast(err, { message: '延长收货失败，请重试' });
                  break;
              }
            }
          });
      },

      confirmReceiveAction(options: any = {}) {
        // listItem如果是空的，那就赋值为this.listItem
        options.listItem = options.listItem || this.listItem;
        const { listItem, btn, isShowConfirm } = options;
        this.confirm({ listItem, btn, isShowConfirm }).then(() => {
          // 酒店确认入住埋点
          if (btn.value === 'confirmReceiveHotel') {
            LOG_CONFIRM_HOTEL_GOODS(ctx);
          }

          this.refreshOrderInfo({
            orderNo: listItem.orderNo,
          });
        });
      },

      // 确认收货
      confirmReceive(options: any = {}) {
        const { listItem } = options;
        const { orderExtra } = getOrderItem(listItem);
        const { orderNo } = listItem || {};
        beforeConfirmReceive({ ...orderExtra, orderNo }).then((isContinue = true) => {
          if (isContinue) {
            this.confirmReceiveAction({ ...options, isShowConfirm: true });
          }
        });
      },

      // 确认收货
      confirm({ listItem, btn, isShowConfirm = true }) {
        const orderItem = getOrderItem(listItem);
        const { orderExtra } = orderItem;
        if (btn.value === 'confirmReceive') {
          LOG_CONFIRM_GOODS(ctx, orderItem.orderNo);
        }
        let message =
          orderItem.buyWay === 1
            ? '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。'
            : '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
        if (orderExtra.isHotel) {
          message =
            orderItem.buyWay === 1
              ? '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。'
              : '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。';
        }
        if (this.yunConfig.receivingContent !== '') {
          message = this.yunConfig.receivingContent;
        }
        const successMsg = orderExtra.isHotel ? '入住成功' : '收货成功';
        const title = orderExtra.isHotel ? '确认入住' : '确认收货';
        const confirmButtonText = orderExtra.isHotel ? '确认入住' : '确认收货';
        const { kdtId, orderNo } = listItem;

        return new Promise<void>((resolve, reject) => {
          const requestConfirm = () => {
            Toast({ type: 'loading' });
            api.API_CONFIRM_RECEIVE_V2({ orderNo, kdtId }).then(
              (data: Record<string, any>) => {
                Toast.clear();
                // 开启了自动跳转发布评价页则不继续查询是否会员升级
                if (data?.isAutoGoEvaluate) {
                  Toast(successMsg);
                  navigate({
                    url: args.add('/packages/evaluation/order/create/index', {
                      order_no: orderNo,
                      from: 'confirm_goods',
                    }),
                    /* #ifdef web */
                    web: {
                      type: 'safeLink',
                      safeLink: {
                        url: args.add('/wsctrade/order/evaluate/create', {
                          kdt_id: kdtId,
                          order_no: orderNo,
                          from: 'confirm_goods',
                        }),
                        kdtId,
                      },
                    },
                    /* #endif */
                  });
                  return;
                }
                // 如果没有LevelupTip，则直接resolve。
                // TODO：当前代码看着存在bug，没有挂载LevelupTip，因此下面针对LevelupTip的事件根本不会生效，在这里新增一个data做判断，如果后续挂载了LevelupTip则自动走下方的事件，否则就直接resolve
                if (!ctx.data.createLevelupTip) {
                  resolve();
                  return;
                }
                // 查询是否升级
                // eslint-disable-next-line no-template-curly-in-string
                ctx.data.levelupTipTitle = '收货成功，会员升级至${level}';
                ctx.event.emit('checkLevelupTip');
                /**
                 * 为什么不放在 index.ts？ 因为回调需要用到 resolve
                 * 如果这三个event监听存在内存泄漏，可以使用store进行治理，治理方案:
                 * store.state = { eventCbs: [] };
                 * this.eventCbs.push(ctx.event.once('xxx'));
                 *
                 * 之后在 index.ts 的 pageDestroyed 生命周期中销毁
                 * pageDestroyed() {
                 *   this.store.eventCbs.forEach(cb => cb());
                 * }
                 */
                ctx.event.once('closeLevelupTip', resolve);
                ctx.event.once('confirmLevelupTip', () => {
                  const url = buildUrl(`/wscuser/levelcenter?kdt_id=${kdtId}`, 'h5', kdtId);
                  navigate({
                    web: {
                      type: 'safeLink',
                      safeLink: { url, kdtId },
                    },
                  });
                });
                ctx.event.once('onFetchLevelupTip', ({ show, level }) => {
                  if (level !== null) {
                    if (show) {
                      Toast.clear();
                    } else {
                      Toast(successMsg);
                      resolve();
                    }
                  } else {
                    // catch
                    Toast(successMsg);
                    resolve();
                  }
                });
              },
              (err) => {
                // 收货失败
                Toast.clear();
                errorToast(err, { message: '失败，请重试' });
                reject(err);

                let level = 'error';
                // 该订单已经确认收货了
                if (err.code === 102570014) {
                  level = 'info';
                }

                skynet[level]('订单确认收货错误', {
                  error: err.toString() === '[object Object]' ? err : err.toString(),
                  data: listItem,
                });
              }
            );
          };

          // 不需要展示弹窗的话，就直接调用接口
          if (!isShowConfirm) {
            requestConfirm();
            return;
          }

          // @ts-ignore
          Dialog.confirm({
            title,
            message,
            confirmButtonText,
          })
            .then(() => {
              requestConfirm();
            })
            .catch(reject);
        });
      },

      // 提醒发货
      expressReminder({ listIndex, listItem, btn }) {
        const { kdtId, orderNo } = listItem;
        LOG_EXPRESS_REMINDER(ctx);
        const message = `提醒卖家${
          btn.value === 'expressReminderHotel' ? '接单' : '发货'
        }消息发送成功`;
        Toast({ type: 'loading' });
        api
          .API_REMIND_EXPRESS({ kdtId, orderNo })
          .then(() => {
            Toast.clear();
            Toast(message);
            if (listItem.orderPermission) {
              // TODO: orderlist 这里的本意是希望隐藏提醒发货的按钮，但是实际上没有实现
              listItem.orderPermission.isShowReminder = false;
              this.updateOrderItem(listIndex, [listItem]);
            }
          })
          .catch((err) => {
            Toast.clear();
            errorToast(err, { message: '提醒发货失败，请重试' });
          });
      },

      updateKdtId(kdtId): Promise<void> {
        return new Promise((resolve) => {
          if (kdtId === this.kdtId) {
            return resolve();
          }
          setStorage('order-list:updateKdtId:oldKdtId', this.kdtId);
          ctx.process
            .invokePipe('beforeUpdateKdtId', { kdtId })
            .then(() => {
              const mark = '[@wsc-tee-trade/order-list-core]updateKdtId';
              accordSkyLogger({
                info: `店铺列表更新kdtId,mark:${mark}`,
              });
              return ctx.process.invokePipe('setKdtId', +kdtId, mark);
            })
            .then(resolve)
            .catch((e) => {
              console.warn('setKdtId error', e);
              resolve();
            });
        });
      },

      // 不需要更新店铺场景
      async notUpdateKdtId(listItem: Record<string, any>) {
        /* #ifdef weapp */
        const isCrmOffline = listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE;
        if (!this.isChainShop) {
          return isCrmOffline;
        }
        const isTrue = await enterShopApollo('breakOrderListUpKdtId');
        const isOfflineOrder = get(listItem, 'orderList[0].isOfflineOrder', false);

        return (isOfflineOrder && isTrue) || isCrmOffline;
        /* #endif */
      },

      setReceivingContent(message: string) {
        this.yunConfig.receivingContent = message;
      },

      setCloudBtnListCondition(btnListCondition: CloudBtnListCondition) {
        this.yunConfig.btnListCondition = btnListCondition;
      },

      toggleWxVideoGuidePopup({ isShow }) {
        this.isShowWxVideoGuidePopup = isShow;
      },

      loadOrder(isInit = false) {
        // 首次初始化避免 van-list 和 watch activityTab 触发两次请求
        if (!this.activityTab.type) return;
        if (!this.activityTab.hasNext) return;
        if (this.loading) return;

        this.loading = true;

        const startTime = Date.now();

        this.fetchMoreOrder(this.activityTab.type)
          .then(() => {
            // 非初始化、非切换tab时
            if (!isInit) {
              const { mark = {} } = ctx.hummer || {};
              mark.start && mark.start('order-list-scroll-loaded', startTime);
              mark.end && mark.end('order-list-scroll-loaded');
            }
          })
          .catch((err) => {
            console.log(err);
            errorToast(err, { message: '服务器开小差了，请稍后再试' });
          });
      },

      onGoodsImgLoad() {
        // 只记录第一次
        if (!this.isGoodsImgLoaded) {
          this.isGoodsImgLoaded = true;
          ctx.hummer.mark?.log?.({ tag: 'order-list', scene: ['appLaunch', 'route'] });
        }
      },
    };
  },
};

export const storeModules = [rootStore];
