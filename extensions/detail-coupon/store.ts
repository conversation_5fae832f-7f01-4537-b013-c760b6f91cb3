import formatPrice from '@youzan/utils/money/format';
import { createStore as _createStore } from '@ranta/store';
import type { StoreModule } from '@youzan/wsc-tee-trade-common/lib/types';

const getCouponInfo = (coupon) => ({
  name: coupon.name || '',
  condition: coupon.condition || '',
  decrease: coupon.decrease || '',
  desc: `省${formatPrice(coupon.decrease)}`,
});

const rootStore: StoreModule = {
  state: {
    coupon: {},
    coupons: [],
    display: {},
    rootStyle: '',
    couponCode: {},
  },
  getters: {
    couponInfo() {
      const { coupon = {}, couponCode = {}, coupons = [], display = {} } = this;
      const showCoupon = Object.keys(coupon).length > 0;
      const showCouponCode = Object.keys(couponCode).length > 0;

      const couponInfo = {
        show: !!(showCoupon || showCouponCode || coupons.length) && display.showCouponBlock,
        list: [],
      };

      // 优惠券叠加  为了兼容之前单券的场景 coupons优先级最高
      if (coupons.length) {
        couponInfo.list = coupons.map(getCouponInfo);
      } else if (showCoupon) {
        couponInfo.list = [getCouponInfo(coupon)];
      } else if (showCouponCode) {
        couponInfo.list = [getCouponInfo(couponCode)];
      }
      return couponInfo;
    },
  },
  actions: () => ({}),
};

export default function createStore(ctx) {
  return _createStore({
    state: () => ({
      ...rootStore?.state,
    }),
    getters: {
      ...rootStore?.getters,
    },
    actions: {
      ...rootStore?.actions(ctx),
    },
  });
}
