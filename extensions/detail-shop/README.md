# @wsc-tee-trade/detail-shop

详情店铺信息

![UI呈现](https://img.yzcdn.cn/public_files/74dbbf84117cc2f462137988bcd65f6c.png)

## 调试方式

## 存疑

## Widget.Provide

| 名称           | 说明           |
| -------------- | -------------- |
| Shop `default` | 店铺信息 block |

## Widget.Consume

| 名称                | 说明                |
| ------------------- | ------------------- |
| Goods               | 商品列表            |
| GuaranteeFreightBar | 有赞担保&退货包运费 |

## Data.Consume

| 名称             | 说明     |
| ---------------- | -------- |
| themeColor       | 主题色   |
| order            | 订单详情 |
| totalPrice       | ???      |
| shopInfo         | ???      |
| miniprogram      | ???      |
| isSelfFetch      | ???      |
| isPeriodBuy      | ???      |
| isMultiPeriodBuy | ???      |
| showPriceDetail  | ???      |
| paymentInfo      | ???      |
| itemList         | ???      |
| courses          | ???      |
| points           | ???      |

## Process.Invoke

| 名称                       | 说明 |
| -------------------------- | ---- |
| handleUrlWithShopAutoEnter | ???  |
