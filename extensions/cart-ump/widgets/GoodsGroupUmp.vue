<template>
  <view :class="!onlyShowExchange ? 'ai' : ''">
    <view v-if="!onlyShowExchange" class="ai-container">
      <van-icon
        custom-class="ai__checkbox"
        size="20px"
        :color="iconColor"
        :name="iconName"
        @click.stop="chooseAllActivityGoods"
      />

      <view class="ai__text-wrap" @click="clickActivityText">
        <van-tag
          v-for="(tag, index) in activityInfo.activityTags"
          :key="index"
          custom-class="ai__tag"
          :plain="themeTag.plain"
          :hairline="themeTag.plain"
        >
          {{ tag }}
        </van-tag>

        <view ref="ai__desc" class="ai__desc">
          {{ activityInfo.activityDesc }}
        </view>

        <van-icon
          v-if="canShowPopup"
          name="info-o"
          size="var(--eo-font-size-14, 14px)"
          color="#969799"
          @click="clickActivityText"
        />
      </view>

      <view v-if="activityBtnTxt && !isPdLive" class="ai__tips theme-color" @click="toActivityUrl">
        <view class="ai__item-text">{{ activityBtnTxt }}</view>
        <van-icon name="arrow" custom-class="ai__icon" />
      </view>
    </view>

    <ump-info-popup
      v-if="canShowPopup"
      :activity-info="activityInfo"
      :show-popup="showPopup"
      :theme-general-color="themeGeneralColor"
      :theme-general-alpha-10-color="themeGeneralAlpha10Color"
      :theme-css="themeCss"
      :theme-colors="themeColors"
      :theme-tag="themeTag"
      @close="closeUmpPopup"
    />

    <!-- 换购modal -->
    <exchange-model
      v-if="activityId"
      :is-show="showExchangeModal"
      :activity-id="activityId"
      :activity-type="24"
      :kdt-id="kdtId"
      :offline-id="offlineId"
      :plus-buy-goods="plusBuyGoods"
      :activity-desc="exchangeModalDesc"
      :theme-css="themeCss"
      :theme-general-color="themeGeneralColor"
      @close="handleCloseExchangeModal"
      @add-cart="handleExchangeSucceed"
    />
  </view>
</template>
<script>
import Checkbox from '@youzan/vant-tee/dist/checkbox/index.vue';
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { whereCurrentGoodsInList } from '../utils';
import money from '@youzan/weapp-utils/lib/money';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import UmpInfoPopup from './UmpInfoPopup';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { UMP_ACTIVITY } from '@youzan/ump-biz-utils';
import { buildUrl } from '@youzan/tee-biz-util';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef web */
import { ZNB } from '@youzan/tee-biz-navigate';
/* #endif */

const WEAPP_ACTIVITY_URL = {
  [UMP_ACTIVITY.MEET_REDUCE]: '/packages/ump/meet-reduce-goods/index',
  [UMP_ACTIVITY.MEET_SEND]: '/packages/ump/meet-reduce-goods/index',
  [UMP_ACTIVITY.PACKAGE_BUY]: '/packages/ump/bundle-purchase/goods-list/index',
  [UMP_ACTIVITY.SECOND_HALF_DISCOUNT]: '/packages/ump/second-half-discount/index',
};

const ACTIVITY_TYPE_ALIAS_NUMBER = {
  PACKAGE_BUY: 104,
  MEET_REDUCE: 101,
  MEET_SEND: 261,
  PLUS_BUY: 24,
  SECOND_HALF: 115,
  MULTI_COURSE_APPLY: 10101,
};

// 活动
const ACTIVITY_TYPE_ALIAS_MAP = {
  [ACTIVITY_TYPE_ALIAS_NUMBER.PACKAGE_BUY]: UMP_ACTIVITY.PACKAGE_BUY,
  [ACTIVITY_TYPE_ALIAS_NUMBER.MEET_REDUCE]: UMP_ACTIVITY.MEET_REDUCE,
  [ACTIVITY_TYPE_ALIAS_NUMBER.MEET_SEND]: UMP_ACTIVITY.MEET_SEND,
  [ACTIVITY_TYPE_ALIAS_NUMBER.PLUS_BUY]: UMP_ACTIVITY.PLUS_BUY,
  [ACTIVITY_TYPE_ALIAS_NUMBER.SECOND_HALF]: UMP_ACTIVITY.SECOND_HALF_DISCOUNT, // 第二支半价,（secondHalfDiscount字段保持一致）
  [ACTIVITY_TYPE_ALIAS_NUMBER.MULTI_COURSE_APPLY]: 'multiCourseApply',
};

// 计算属性原逻辑
function getComputedData(keys) {
  const getIsAllActivityGoodsChoose = () => {
    let filterGoodsList = this.goodsList;
    if (!this.isEditing) {
      filterGoodsList = this.goodsList.filter((goods) =>
        this.ctx.lambdas.isGoodsCheckboxEnable(goods)
      );
      if (!filterGoodsList.length) {
        return false;
      }
    }
    return filterGoodsList.every(
      (good) => whereCurrentGoodsInList(this.checkedGoodsList, good) !== -1
    );
  };
  const isAllActivityGoodsChoose = getIsAllActivityGoodsChoose();
  const computedMaps = {
    isAllActivityGoodsChoose() {
      return isAllActivityGoodsChoose;
    },
    canShowPopup() {
      return this.activityInfo.activityDuration;
    },

    activityTypeAlias() {
      return ACTIVITY_TYPE_ALIAS_MAP[this.activityInfo.activityType];
    },

    activityId() {
      return this.activityInfo.activityId;
    },
    iconName() {
      return isAllActivityGoodsChoose ? 'checked' : 'circle';
    },
    iconColor() {
      return isAllActivityGoodsChoose ? this.themeGeneralColor : '#969799';
    },
  };
  let computedKeys = Object.keys(computedMaps);
  if (keys && Array.isArray(keys) && keys.length) {
    computedKeys = keys;
  }
  const result = computedKeys.reduce((obj, key) => {
    obj[key] = computedMaps[key].call(this);
    return obj;
  }, {});
  return {
    isAllActivityGoodsChoose,
    ...result,
  };
}

function formatSku(sku) {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    parsedSku = [];
  }

  return parsedSku;
}

// 获取加价换购商品数据
function getPlusBuyGoods(goodsList) {
  // 获取换购商品
  const plusBuyGoods = goodsList
    .filter(
      // 商品活动类型需要是加价购
      (item) => !!item.activityId && +item.activityType === ACTIVITY_TYPE_ALIAS_NUMBER.PLUS_BUY
    )
    .map((item) => {
      item = mapKeysCase.toCamelCase(item);
      item.skuData = formatSku(item.sku);
      return item;
    });
  return plusBuyGoods;
}

export default {
  name: 'goods-group-ump',

  components: {
    'van-tag': Tag,
    'van-checkbox': Checkbox,
    'van-icon': Icon,
    'ump-info-popup': UmpInfoPopup,
  },

  props: {
    kdtId: {
      type: Number,
      required: true,
    },
    offlineId: Number,
    isEditing: {
      type: Boolean,
      default: false,
    },
    activityInfo: {
      type: Object,
      default: () => ({}),
    },
    goodsList: {
      type: Array,
      default: () => [],
    },
    checkedGoodsList: {
      type: Array,
      default: () => [],
    },
    themeGeneralColor: String,
    themeGeneralAlpha10Color: String,
    themeCss: String,
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    logger: {
      type: Object,
      default: () => ({}),
    },
    onlyShowExchange: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const computedData = getComputedData.call(this);
    const activityData = this.getUmpDataByGoodsList(computedData.activityTypeAlias);
    return {
      showPopup: false,
      showExchangeModal: false,
      plusBuyGoods: [],
      activityBtnTxt: '',
      exchangeModalDesc: '',
      themeTag: {},
      ...computedData,
      ...activityData,
      isPdLive: false,
    };
  },

  watch: {
    goodsList() {
      const computedData = getComputedData.call(this, [
        'isAllActivityGoodsChoose',
        'iconName',
        'iconColor',
        'activityTypeAlias',
      ]);
      const activityData = this.getUmpDataByGoodsList(computedData.activityTypeAlias);
      const changeData = {
        ...activityData,
        ...computedData,
      };
      Object.keys(changeData).forEach((key) => {
        this[key] = changeData[key];
      });

      if (this.onlyShowExchange) {
        this.toActivityUrl();
      }
    },
    isEditing() {
      this.changeComputedData(['isAllActivityGoodsChoose', 'iconName', 'iconColor']);
    },
    checkedGoodsList() {
      this.changeComputedData(['isAllActivityGoodsChoose', 'iconName', 'iconColor']);
    },
    activityInfo() {
      this.changeComputedData(['canShowPopup', 'activityTypeAlias', 'activityId']);
    },
    themeGeneralColor() {
      this.changeComputedData(['isAllActivityGoodsChoose', 'iconName', 'iconColor']);
    },
  },

  mounted() {
    this.plusBuyGoods = getPlusBuyGoods(this.goodsList);
  },
  created() {
    mapData(this, ['themeTag']);
    /* #ifdef web */
    // 私域直播间来源的购物车，不透出跳转营销页
    this.isPdLive = !!window._global?.pdlive;
    /* #endif */
  },
  methods: {
    changeComputedData(keys) {
      const computedData = getComputedData.call(this, keys);
      Object.keys(computedData).forEach((key) => {
        this[key] = computedData[key];
      });
    },
    getUmpDataByGoodsList(activityTypeAlias) {
      this.logger?.log({
        et: 'view', // 事件类型
        ei: 'cart_activity_view', // 事件标识
        en: '购物车优惠活动曝光', // 事件名称
        params: {
          activity_type: activityTypeAlias,
          activity_id: this.activityId,
          kdt_id: this.kdtId,
        }, // 事件参数
      });
      if (
        [
          UMP_ACTIVITY.MEET_REDUCE,
          UMP_ACTIVITY.MEET_SEND,
          UMP_ACTIVITY.PACKAGE_BUY,
          UMP_ACTIVITY.SECOND_HALF_DISCOUNT,
        ].includes(activityTypeAlias)
      ) {
        return this.setCommonActivityInfo();
      }
      if ([UMP_ACTIVITY.PLUS_BUY].includes(activityTypeAlias)) {
        return this.setPlusBuyActivityInfo();
      }
      if (['multiCourseApply'].includes(activityTypeAlias)) {
        return this.setMultiCourseApplyActivityInfo();
      }
    },
    // 设置普通活动
    setCommonActivityInfo() {
      if (this.activityInfo.meet) {
        return {
          activityBtnTxt: '再逛逛',
        };
      }
      if (!this.activityInfo.meet && this.activityInfo.activityUrl) {
        return {
          activityBtnTxt: '去凑单',
        };
      }
    },

    // 设置加价购活动
    setPlusBuyActivityInfo() {
      // 获取换购商品
      this.plusBuyGoods = getPlusBuyGoods(this.goodsList);
      let activityBtnTxt = '';
      let exchangeModalDesc = '';
      const { conditionPrice } = this.activityInfo;
      if (!this.activityInfo.meet) {
        activityBtnTxt = '去凑单';
      } else if (!this.plusBuyGoods.length) {
        exchangeModalDesc = `已购满${this.moneyToYuan(conditionPrice)}元，`;
        activityBtnTxt = '去换购';
      } else {
        exchangeModalDesc = `已购满${this.moneyToYuan(conditionPrice)}元，`;
        activityBtnTxt = '重新换购';
      }
      return {
        activityBtnTxt,
        exchangeModalDesc,
      };
    },

    setMultiCourseApplyActivityInfo() {
      return {
        activityBtnTxt: '去选报',
      };
    },

    handleCloseExchangeModal() {
      this.showExchangeModal = false;
    },

    // 换购商品成功之后，需要刷新购物车
    handleExchangeSucceed() {
      this.$emit('refresh-cart-goods-list');
    },

    moneyToYuan(cent) {
      return money(cent).toYuan();
    },

    clickActivityText() {
      if (!this.canShowPopup) return;
      this.showPopup = true;
    },

    toActivityUrl() {
      const {
        activityInfo: { activityAlias, activityId, activityType },
        activityTypeAlias,
      } = this;

      // 埋点
      this.logger?.log({
        et: 'click', // 事件类型
        ei: 'cart_activity_click', // 事件标识
        en: '购物车页活动点击', // 事件名称
        params: {
          activity_type: this.activityTypeAlias,
          activity_id: this.activityId,
          kdt_id: this.kdtId,
        }, // 事件参数
      });

      if (['plusBuy'].includes(activityTypeAlias)) {
        if (this.activityBtnTxt !== '去凑单') {
          this.showExchangeModal = true;
        } else {
          let link = `/wscump/plusbuy/detail?activityId=${activityId}&kdt_id=${this.kdtId}&alias=${activityAlias}`;

          /* #ifdef weapp */
          link = `/packages/ump/plusbuy/index?activityId=${activityId}`;
          /* #endif */

          this.ctx.process.invoke('navigateFromCart', { link });
        }
        return;
      }

      if (['multiCourseApply'].includes(activityTypeAlias)) {
        // 多科连报前端拼接url
        let link = buildUrl(`/wscvis/ump/liangfan/${activityAlias}?kdt_id=${this.kdtId}`, 'h5');

        /* #ifdef weapp */
        link = `/pages/common/webview-page/index?src=${encodeURIComponent(
          buildUrl(`/wscvis/ump/liangfan/${activityAlias}?kdt_id=${this.kdtId}`, 'h5')
        )}`;
        /* #endif */
        this.ctx.process.invoke('navigateFromCart', { link, navigateType: 'navigateTo' });
        return;
      }

      let activityUrl = this.activityInfo?.activityUrl || '';

      /* #ifdef web */
      const { PLUS_BUY, MEET_REDUCE, SECOND_HALF, PACKAGE_BUY } = ACTIVITY_TYPE_ALIAS_NUMBER;
      const { isAlipayApp, isQQApp } = window._global?.miniprogram || {};
      const ACTIVITY_URL = {
        [PLUS_BUY]: '/packages/ump/plus-buy/index?',
        [MEET_REDUCE]: '/packages/ump/meet-reduce-goods/index?',
        [SECOND_HALF]: '/packages/ump/second-half-discount/index?',
        [PACKAGE_BUY]: '/packages/ump/bale/index?',
      };

      if ((isAlipayApp || isQQApp) && [MEET_REDUCE, PLUS_BUY, SECOND_HALF].includes(activityType)) {
        activityUrl = ACTIVITY_URL[activityType] + `alias=${activityAlias}&kdtId=${this.kdtId}`;

        if (PLUS_BUY === activityType) {
          activityUrl += `&activityId=${activityId}`;
        }
        ZNB.navigate({
          aliappUrl: activityUrl,
          qqUrl: activityUrl,
        });
        return;
      }

      if (isQQApp && [PACKAGE_BUY].includes(activityType)) {
        activityUrl = ACTIVITY_URL[activityType] + `alias=${activityAlias}&kdtId=${this.kdtId}`;

        ZNB.navigate({
          qqUrl: activityUrl,
        });
        return;
      }

      /* #endif */

      /* #ifdef weapp */
      activityUrl = `${WEAPP_ACTIVITY_URL[activityTypeAlias]}?alias=${activityAlias}`;
      /* #endif */

      this.ctx.process.invoke('navigateFromCart', {
        link: activityUrl,
      });
    },

    chooseAllActivityGoods() {
      const type = this.isAllActivityGoodsChoose ? 'remove' : 'add';
      let filterGoodsList = this.goodsList;
      if (!this.isEditing) {
        filterGoodsList = this.goodsList.filter(
          (goods) => !this.ctx.lambdas.isEduIosOnlineGoods(goods)
        );
        if (!filterGoodsList.length) {
          Toast('购物车里面尚未选中商品，请重新返回活动页/商品页加购');
          return;
        }
      }
      this.$emit('change-item-checked', {
        rangeType: 'batch',
        type,
        goodsList: filterGoodsList,
        isActivity: true,
      });
    },

    closeUmpPopup() {
      this.showPopup = false;
    },
  },
};
</script>
<style scoped lang="scss">
@mixin van-icon {
  display: flex;
  align-items: center;
}
.ai {
  height: 52px;
  box-sizing: border-box;
  padding: var(--theme-trade-md-gutter, 11px) var(--theme-trade-md-gutter, 10px)
    var(--theme-trade-md-gutter, 11px) var(--theme-trade-md-gutter, 10px);
  font-size: var(--eo-font-size-12, 12px);
  line-height: 1.3;
  display: flex;
  overflow: hidden;
  position: relative;

  &-container {
    display: flex;
    align-items: center;
    width: 100%;
  }

  &__tag {
    width: max-content;
    min-width: 32px;
    box-sizing: border-box;
    margin-right: 4px;
    font-size: var(--eo-font-size-12, 12px);
    line-height: 1.3;
    padding: 0 4px;
    flex-shrink: 0;
    color: var(--theme-tag-ump-color, --main-bg) !important;
    background: var(--theme-tag-ump-bg-color, --main-bg) !important;

    &::after {
      border-radius: var(--theme-radius-tag, 0) !important;
      border-color: var(--theme-tag-ump-border-color, --main-bg) !important;
    }
  }

  &__checkbox {
    width: 20px !important;
    margin-right: 12px;
    justify-content: start !important;
  }

  &__icon {
    color: var(--ump-icon, #323233);
    font-size: var(--eo-font-size-14, 14px);
  }

  &__desc {
    color: #323233;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
    font-size: var(--eo-font-size-12, 12px);
  }

  &__text-wrap,
  &__tips {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  &__text-wrap {
    flex: 1;
    overflow: hidden;
    padding-right: 12px;
  }

  &__item-text {
    display: inline-block;
    vertical-align: top;
    margin-right: 4px;
    color: var(--ump-icon, #323233);
  }
}
</style>
