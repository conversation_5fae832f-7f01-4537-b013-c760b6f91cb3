<template>
  <view class="frequently-purchase-item-container" v-if="itemList.length" :style="cssVar">
    <view class="frequently-purchase-item-title">已购商品</view>
    <van-swipe
      pid="1"
      :initial-swipe="swiperIndex"
      indicator-color="red"
      :autoplay="-1"
      :show-indicators="false"
      @change="onSwiperChange"
    >
      <van-swipe-item pid="1" v-for="(sixItems, index) in formattedLayout" :key="index">
        <view class="item-card__list">
          <view class="item-card__item" v-for="(item, sixItemsIndex) in sixItems" :key="item.alias">
            <cap-item-card
              class="goods-item"
              :_opt="item"
              :text-class="textClass"
              @item-click="itemClick(item.itemInfo, sixItemsIndex)"
            />
          </view>
        </view>
      </van-swipe-item>
    </van-swipe>
    <view
      class="progress-indicator"
      :style="progressIndicatorStyle"
      v-if="formattedLayout.length > 1"
    >
      <view class="progress-pointer" :style="progressPointerStyle"></view>
    </view>
  </view>
</template>
<script>
import CapItemCard from '@youzan/decorate-tee/src/captain/item-card/index.vue';
import VanToast from '@youzan/vant-tee/dist/toast/index';
import { Swipe, SwipeItem } from '@vant/tee';
import { mapData } from '@youzan/ranta-helper-tee';
import Tee from '@youzan/tee';
import Logger from './logs';
import makeRandomString from '@youzan/utils/string/makeRandomString';

function formatPriceToStr(price = '') {
  if (typeof price === 'string') return price;
  const priceStr = price % 100 === 0 ? Math.round(price / 100) : (price / 100).toFixed(2);
  return priceStr + '';
}

function getGoodsPageIndex() {
  let index = 0;
  /* #ifdef web */
  index = (location.hash.split('#') || [0, 0])[1];
  /* #endif */
  return index;
}
/* #ifdef web */
function setGoodsPageIndex(pageIndex) {
  location.hash = pageIndex;
}
/* #endif */

const pageIndicatorTotalLen = 48;

export default {
  components: {
    'cap-item-card': CapItemCard,
    'van-toast': VanToast,
    'van-swipe': Swipe,
    'van-swipe-item': SwipeItem,
  },
  props: {
    itemList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      layoutConfig: { type: 'three', layoutMargin: 16, itemMargin: 8 /* itemWidth: 117 */ },
      itemsNumInOnePage: 6,
      sku: {},
      skuCache: {},
      kdtId: '',
      offlineId: '',
      swiperIndex: 0,
      shop: {},
      themeColors: {},
      pageExposed: [],
      pageRandomNumber: makeRandomString(8),
    };
  },
  computed: {
    textClass() {
      let className = '';
      /* #ifdef weapp */
      className = 'fqb-text-area-weapp';
      /* #else */
      className = 'fqb-text-area-web';
      /* #endif */
      return className;
    },
    cssVar() {
      return `--general: ${(this.themeColors || {}).general}`;
    },
    general() {
      return (this.themeColors || {}).general;
    },
    progressIndicatorStyle() {
      return `width: ${pageIndicatorTotalLen}px;`;
    },
    progressPointerStyle() {
      const ptWidth = pageIndicatorTotalLen / this.formattedLayout.length;
      return `width: ${ptWidth}px; margin-left: ${this.swiperIndex * ptWidth}px; background: ${
        (this.themeColors || {}).general
      }`;
    },
    formattedItem() {
      return this.itemList.map((item) => {
        const hasTags = (item.activityInfos || {}).length;
        return {
          itemInfo: item,
          type: 'card',
          layout: 'vertical',
          layoutType: 'three',
          corner: 'circle',
          imgOpt: {
            src: item.imageUrl,
            radius: 8,
          },
          ...(item.price !== item.activityPrice && {
            oPriceOpt: {
              price: formatPriceToStr(item.price),
              fontSize: 12,
              delLine: true,
              color: '#D8D8D8',
            },
          }),
          priceOpt: {
            price: formatPriceToStr(item.activityPrice),
            fontSize: 14,
            fontWeight: 'bold',
            decimalFontWeight: 'bold',
            height: 'auto',
          },
          isTagsShow: true,
          ...(hasTags && {
            tagsOpt: {
              list: item.activityInfos
                ?.map(({ label, labelThemeType }) => ({
                  label,
                  type: labelThemeType,
                }))
                .slice(0, 1),
              marginBottom: '6px',
              marginTop: '8px',
            },
          }),
          ...(item.purchaseTimes && {
            activityOpt: {
              text: `已购${item.purchaseTimes}次`,
              bgStyle: {
                background: '#000',
                opacity: 0.5,
              },
              testStyle: {
                lineHeight: '18px',
                fontSize: '12px',
              },
            },
          }),
          btnOpt: {},
        };
      });
    },
    formattedLayout() {
      const foldedList = [];
      let pt = 0;
      const len = this.formattedItem.length;
      while (pt < len) {
        const tail = pt + this.itemsNumInOnePage > len ? len : pt + this.itemsNumInOnePage;
        const part = this.formattedItem.slice(pt, tail);
        foldedList.push(part);
        pt += this.itemsNumInOnePage;
      }
      return foldedList;
    },
  },

  watch: {
    formattedLayout(val) {
      if (val.length) {
        this.exposeBoughtGoods();
      }
    },
  },

  created() {
    mapData(this, ['kdtId', 'offlineId', 'shop', 'themeColors']);
    const pageIndex = getGoodsPageIndex() || 0;
    this.swiperIndex = pageIndex;
    Logger.enterPage();
  },

  mounted() {
    Logger.boughtGoodsComponentExposed();
  },

  methods: {
    getBannerId(index) {
      return `Already_bought_goods~${index}~${this.pageRandomNumber}`;
    },
    // 暂时采用逻辑曝光，intersection Observer在h5上tee没有抹平
    exposeBoughtGoods() {
      const pageIndex = this.swiperIndex;
      if (this.pageExposed.indexOf(pageIndex) !== -1) {
        return;
      }
      this.pageExposed.push(pageIndex);
      const pageItems = this.formattedLayout[pageIndex] || [];
      pageItems.forEach((item, index) => {
        Logger.boughtGoodsExposed(item.itemInfo, this.getBannerId(index));
      });
    },
    itemClick(goodsData, index) {
      Logger.boughtGoodsClick(goodsData, this.getBannerId(index));
      this.navigateToGoodsDetail(goodsData, this.getBannerId(index));
    },
    navigateToGoodsDetail(goodsData, bannerId) {
      const { alias, alg } = goodsData;
      const prefixedBannerId = `${Logger.pageSpm}~${bannerId}`;
      let url = '';
      /* #ifdef web */
      url = `/wscgoods/detail/${alias}?alg=${alg}&banner_id=${prefixedBannerId}`;
      /* #endif */
      /* #ifdef weapp */
      url = `/pages/goods/detail/index?alias=${alias}&alg=${alg}&banner_id=${prefixedBannerId}`;
      /* #endif */
      Tee.navigate({
        url,
        type: 'navigateTo',
      });
    },

    onSwiperChange(index) {
      this.swiperIndex = index;
      /* #ifdef web */
      setGoodsPageIndex(index);
      /* #endif */
      this.exposeBoughtGoods();
      Logger.turnPageClick();
    },
  },
};
</script>
<style lang="scss">
.frequently-purchase-item-window {
  width: 100%;
  min-width: 100%;
  overflow-x: scroll;
}
.frequently-purchase-item-swiper {
  display: flex;
  justify-content: stretch;
}

.frequently-purchase-item-title {
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  padding: 12px 16px;
  margin-bottom: -4px;
}

.frequently-purchase-item-container {
  background: #fff;
  padding-bottom: 8px;
  // h5临时解
  .cap-item-card {
    .opera {
      height: auto !important;
    }
    .cap-price .decimal {
      font-weight: bold;
    }
  }
  .progress-indicator {
    height: 4px;
    background: #ebedf0;
    border-radius: 4px;
    margin-left: auto;
    margin-right: auto;
    .progress-pointer {
      background: #ee0a24;
      height: 4px;
      border-radius: 4px;
    }
  }
}

// h5临时解
.goods-item {
  .activity-bg {
    background-color: #000;
    opacity: 0.5;
  }
  .activity-text {
    line-height: 18px;
    font-size: 12px;
  }
}
// h5临时解
.goods-tags {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}
// h5临时解
.fqb-text-area-web {
  margin: 0 !important;
  height: 64px;
}
.fqb-text-area-weapp {
  margin: 0 !important;
  height: 61px;
}

.item-card {
  &__list {
    padding: 0 16px;
    margin: 0 -4px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  &__item {
    width: 33%;
  }
}
</style>
