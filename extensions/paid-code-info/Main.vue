<template>
  <view v-if="hasPaid">
    <self-fetch-code-info v-if="showSelfFetch" />
    <voucher-card-code-info v-if="showVoucherCard" />
    <take-goods-code-info v-if="showTakeGoods" />
  </view>
</template>

<script>
import { mapState } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      payResult: {},
      hasPaid: false,
      ...mapState(this, ['showSelfFetch', 'showVoucherCard', 'showTakeGoods']),
    };
  },
  created() {
    mapData(this, ['payResult', 'hasPaid']);
  },
};
</script>
