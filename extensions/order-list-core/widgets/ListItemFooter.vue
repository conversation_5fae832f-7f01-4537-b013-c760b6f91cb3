<template>
  <view>
    <view
      v-if="actionBtns.length > 0 && !isDrugShop && !isVideoThirdComponent"
      class="list-item-footer"
    >
      <view class="list-item-footer__extend">
        <view
          v-if="hiddenBtns.length"
          class="list-item-footer__extend-icon"
          @click="toggleShowPopupExtendBtn"
        />

        <view
          v-if="showPopupExtendBtn"
          class="list-item-footer__extend-popup"
          :class="{
            'list-item-footer__extend-popup_last': listIndex === orderList.length - 1,
          }"
        >
          <view
            class="list-item-footer__extend-btn"
            v-for="btn in hiddenBtnsReverse"
            :key="btn.value"
            @click.stop="onFooterBtnClick(btn)"
            >{{ btn.text }}</view
          >
        </view>
      </view>

      <view class="list-item-footer__btns">
        <view
          class="list-item-footer__block"
          v-for="(btn, displayBtnIndex) in displayBtns"
          :key="btn.value"
        >
          <van-button
            plain
            round
            size="small"
            :icon="btn.icon"
            :disabled="btn.disabled"
            :custom-class="
              displayBtnIndex === displayBtns.length - 1
                ? 'list-item-footer__btn-primary'
                : 'list-item-footer__btn'
            "
            @click.stop="onFooterBtnClick(btn)"
            >{{ btn.text }}</van-button
          >
          <view
            v-if="
              (btn.value === 'buyAgain' || btn.value === 'directBuyAgain') &&
              btn.showCoupon &&
              hasRepurchaseCoupon
            "
            class="list-item-footer__tip"
          >
            <van-icon
              name="https://b.yzcdn.cn/public_files/e49f5b413529e624ab6a538642bf4eeb.png"
              size="16"
            />
            <text @click="onFooterBtnClick(btn)"
              >领取 {{ repurchaseCoupon.valueCopywriting
              }}{{ repurchaseCoupon.unitCopywriting }}复购券 ，下单立享优惠</text
            >
            <van-icon name="cross" color="#fff" size="12" @click="onCloseCouponTips(btn)" />
          </view>
        </view>
      </view>
    </view>

    <!-- 最新的一条物流信息 -->
    <view
      v-if="lastExpressTrace && !isRetailShop"
      class="list-item-logistics"
      @click.native.stop="onFooterBtnClick(transportBtn)"
    >
      <view class="list-item-logistics__title">
        <view class="list-item-logistics__icon" />
      </view>

      <view class="list-item-logistics__value">
        {{ lastExpressTrace }}
      </view>
    </view>

    <van-notice-bar
      v-if="isVideoThirdComponent"
      custom-class="wxvideo-notice-swipe"
      mode="link"
      @click="toggleWxVideoGuidePopup(true)"
      >视频号订单，请前往视频号订单中心操作</van-notice-bar
    >
  </view>
</template>

<script>
import { mapState } from '@ranta/store';
import get from '@youzan/utils/object/get';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import NoticeBar from '@youzan/vant-tee/dist/notice-bar/index.vue';

import {
  LOG_CLICK_DIVIDE_COUPON,
  LOG_CLICK_MARKETING_PAIDPRO,
  LOG_CLICK_TRANSPORT,
  LOG_CLOSE_BUYAGAIN_COUPON,
  LOG_VIEW_BUYAGAIN_COUPON,
  LOG_VIEW_MARKETING_PAIDPRO,
} from '../utils/log';
import { processOrderBtnsByYun } from '../open1_0';
import { cloudInvokeBeforeOrderBtnsRender } from '../open2_0';
import { processBtnLogReport } from '../utils/order-buttons';

export default {
  components: {
    'van-icon': Icon,
    'van-button': Button,
    'van-notice-bar': NoticeBar,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {
      showPopupExtendBtn: false,
      btnList: [],
      closeCouponTips: [],
      lastExpressTrace: '',
      actionBtns: [],
      transportBtn: undefined,
      displayBtns: [],
      hiddenBtns: [],
      hiddenBtnsReverse: [],
      isVideoThirdComponent: false,
      repurchaseCoupon: null,
      ...mapState(this, [
        'isDrugShop',
        'isRetailShop',
        'activityTab',
        'orderList',
        'orderMark',
        'yunConfig',
        'directBuyAgainBtnConfig',
        'orderRepurchaseCouponStatusCount',
      ]),
    };
  },

  computed: {
    hasRepurchaseCoupon() {
      return Object.keys(this.repurchaseCoupon || {}).length > 0;
    },
  },

  watch: {
    orderRepurchaseCouponStatusCount() {
      // NOTICE: 千万不要改成 mapState('orderRepurchaseCouponStatus') 因为会丢失响应
      this.repurchaseCoupon = this.store.orderRepurchaseCouponStatus[this.listItem.orderNo] || {};
    },
    listItem() {
      this.computedDisplayData();
    },
    closeCouponTips() {
      this.computedDisplayData();
    },
  },

  mounted() {
    this.computedDisplayData();
  },

  methods: {
    async computedDisplayData() {
      const actionBtnsGetter = async (payload) => {
        const { orderItem, listItem } = payload;
        let processedBtns = [...listItem.btnList];
        processedBtns = processOrderBtnsByYun({
          orderItem,
          actionBtns: processedBtns,
          yunConfig: this.yunConfig,
        });
        processedBtns = await cloudInvokeBeforeOrderBtnsRender(this.ctx, {
          orderNo: listItem.orderNo,
          actionBtns: processedBtns,
        });

        return processedBtns;
      };
      const displayBtnsGetter = (payload) => {
        const { listItem, actionBtns, closeCouponTips } = payload;
        let btns = actionBtns;
        if (btns.length > 3) {
          btns = btns.slice(-3);
        }
        const paidPromotionBtn = ['paidPromotion', 'liveQrCode', 'coupon'];
        btns.forEach((item) => {
          if (paidPromotionBtn.indexOf(item.value) > -1) {
            LOG_VIEW_MARKETING_PAIDPRO(this.ctx, {
              type: item.value,
              orderNo: listItem.orderNo,
            });
          }
          if (item.type === 'buyAgain' && listItem.repurchaseCoupon) {
            LOG_VIEW_BUYAGAIN_COUPON(this.ctx, {
              type: item.value,
              orderNo: listItem.orderNo,
            });
          }
        });
        return btns.map((item) => {
          if (item.showCoupon) {
            if (closeCouponTips.indexOf(item.value) > -1) {
              return {
                ...item,
                showCoupon: false,
              };
            }
          }
          return item;
        });
      };
      /**
       * 按钮分为 展示按钮，和更多按钮，更多按钮需要隐藏，用户操作之后需要刷新
       * @param payload
       * @returns {unknown[]|*[]}
       */
      const hiddenBtnsGetter = (payload) => {
        const { actionBtns } = payload;
        if (actionBtns.length <= 3) return [];

        return actionBtns.slice(0, -3);
      };

      const { listItem, orderMark, closeCouponTips, directBuyAgainBtnConfig } = this;
      const orderPermission = get(listItem, 'orderPermission', {});
      const orderItem = get(listItem, `orderItems[0]`, {});
      const actionBtns = await actionBtnsGetter({ orderItem, listItem, orderMark });
      const transportBtn = actionBtns.find((btn) => btn.value === 'transport');
      const displayBtns = displayBtnsGetter({ listItem, actionBtns, closeCouponTips });
      const hiddenBtns = hiddenBtnsGetter({ actionBtns });
      const hiddenBtnsReverse = hiddenBtns.slice().reverse();
      const lastExpressTrace = get(listItem, 'orderItems[0].lastExpressTrace', '');
      const isVideoThirdComponent = get(
        listItem,
        'orderItems[0].orderExtra.isVideoThirdComponent',
        false
      );

      processBtnLogReport(this.ctx, {
        orderNo: listItem.orderNo,
        orderItem,
        actionBtns,
        orderPermission,
        directBuyAgainBtnConfig,
      });

      Object.assign(this, {
        lastExpressTrace,
        actionBtns,
        transportBtn,
        displayBtns,
        hiddenBtns,
        hiddenBtnsReverse,
        isVideoThirdComponent,
      });
    },

    toggleWxVideoGuidePopup(isShow) {
      this.store.toggleWxVideoGuidePopup({ isShow });
    },

    toggleShowPopupExtendBtn() {
      this.showPopupExtendBtn = !this.showPopupExtendBtn;
    },

    onFooterBtnClick(btn) {
      if (btn.disabled) return;
      const { listItem, listIndex } = this;
      // 查看物流埋点
      if (btn.value === 'transport') {
        LOG_CLICK_TRANSPORT(this.ctx);
      }
      if (btn.value === 'fissionCoupon') {
        LOG_CLICK_DIVIDE_COUPON(this.ctx);
      }
      const paidPromotionBtn = ['paidPromotion', 'liveQrCode', 'coupon'];
      if (paidPromotionBtn.indexOf(btn.value) > -1) {
        LOG_CLICK_MARKETING_PAIDPRO(this.ctx, {
          type: btn.value,
          orderNo: listItem.orderNo,
        });
      }

      this.showPopupExtendBtn = false;
      this.store.doOrderFooterAction({
        listIndex,
        listItem,
        btn,
      });
    },

    onCloseCouponTips(btn) {
      const { orderNo } = this.listItem;
      LOG_CLOSE_BUYAGAIN_COUPON(this.ctx, { orderNo });
      this.closeCouponTips.push(btn.value);
      this.computedDisplayData();
    },
  },
};
</script>

<style lang="scss">
$width-small: 360px;

@mixin small-screen {
  @media only screen and (max-width: $width-small) {
    @content;
  }
}

.list-item-logistics {
  height: 36px;
  padding: 0 8px;
  margin: 12px 12px 0;
  background: #f7f8fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;

  &__title {
    flex-grow: 0;
    white-space: nowrap;
    color: #2da641;
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  &__icon {
    display: block;
    width: 14px;
    height: 14px;
    background: url('https://b.yzcdn.cn/public_files/fe42ba229e45d702aff92df787af233b.png')
      no-repeat center;
    background-size: 100% 100%;
  }

  &__value {
    flex-grow: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.list-item-footer {
  // 按钮变量调整，产品需要
  --button-small-height: 36px;
  --padding-xs: 15px;
  --button-round-border-radius: var(--theme-radius-button, 999px);
  --button-default-border-color: #969799;

  padding: 12px 12px 0;
  text-align: right;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;

  &__extend {
    position: relative;
    font-size: 0;
    margin-top: 3px;

    &-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('https://b.yzcdn.cn/public_files/f9ccfc06dfc71e20a4e327b475af7173.png')
        no-repeat center;
      background-size: 20px auto;
    }

    &-popup {
      width: 128px;
      position: absolute;
      left: 0;
      top: 30px;
      background: #fff;
      box-sizing: border-box;
      z-index: 999;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(50, 50, 51, 0.12);

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: -14px;
        left: 8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom-color: #fff;
      }
    }

    &-popup_last {
      top: auto;
      bottom: 30px;

      &::before {
        top: auto;
        bottom: -14px;
        transform: rotate(180deg);
      }
    }

    &-btn {
      height: 43px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &:not(:first-child)::after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        top: 0;
        left: 16px;
        right: 16px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
      }
    }
  }

  &__block {
    .t-button--small {
      height: 36px;
    }

    position: relative;
    &:not(:last-child) {
      margin-right: 8px;
    }

    @include small-screen {
      &:not(:last-child) {
        margin-right: 8px;
      }
    }

    &:first-child {
      .list-item-footer__tip {
        left: -30px;
        right: auto;
        transform: translate(0, 0);
      }
      .list-item-footer__tip::after {
        left: 70px;
        transform: translate(-6px, 0);
      }
    }

    &:last-child {
      .list-item-footer__tip {
        left: auto;
        right: 0;
        transform: translate(0, 0);
      }
      .list-item-footer__tip::after {
        left: auto;
        right: 40px;
        transform: translate(6px, 0);
      }
    }
  }

  &__btns {
    display: flex;
  }

  &__tip {
    position: absolute;
    height: 32px;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 0 8px;
    background-color: rgba($color: #000, $alpha: 0.7);
    color: #fff;
    font-size: 13px;
    white-space: nowrap;

    > span {
      margin: 0 8px 0 4px;
      flex: 1;
    }
  }

  &__tip::after {
    position: absolute;
    content: '';
    top: 32px;
    left: 50%;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top: 6px solid rgba($color: #000, $alpha: 0.7);
    transform: translate(-6px, 0);
  }

  &__btn {
    min-width: 84px !important;
    font-size: 14px !important;
    line-height: 14px !important;

    &-primary {
      color: var(--icon, #323233) !important;
      border: var(--icon, #323233) 1px solid !important;
      min-width: 84px !important;
      font-size: 14px !important;
      line-height: 14px !important;
    }

    @include small-screen {
      min-width: 70px;
      width: 70px;
      padding: 0;
    }
  }
}

.wxvideo-notice-swipe {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  margin-top: 12px;
  margin-bottom: -16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
</style>
