/**
 * 面向云开放的流程定义
 */
export default function initProcess(ctx) {
  const processMap = {
    openShare() {
      ctx.process.invoke('openShare');
    },
    openDetail() {
      ctx.process.invoke('openDetail');
    },
    openHome() {
      ctx.process.invoke('openHome');
    },
    openResult() {
      ctx.process.invoke('openResult');
    },
    openOutLink(url) {
      ctx.process.invoke('openOutLink', url);
    },
  };
  // eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
  Object.keys(processMap).forEach((key) => ctx.process.define(`ecloud:${key}`, processMap[key]));

  return () => {
    // eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
    Object.keys(processMap).forEach((key) => ctx.process.undef(`ecloud:${key}`, processMap[key]));
  };
}
