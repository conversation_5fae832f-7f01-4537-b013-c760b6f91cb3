import { formatPrice } from './utils';

import { createStore as _createStore } from '@ranta/store';
import type { StoreModule } from '@youzan/wsc-tee-trade-common/lib/types';
import { getPostageValue } from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';
import { formatTotalPrice } from '@youzan/wsc-tee-trade-common/lib/order-utils/format';

const rootStore: StoreModule = {
  state: {
    order: {},
    payment: {},
    goods: [],
    buyWayDetailUrl: '',
    showPostage: false,
  },
  getters: {
    price() {
      const { order, payment, goods, buyWayDetailUrl, showPostage } = this;
      let taxPrice = 0;
      goods.forEach((item) => {
        // 1表示不是海淘订单，2表示含税，3表示不含税
        if (item.tariffTag === 3) {
          taxPrice += item.tariffPay;
        }
      });
      const res: Record<string, any> = {
        total: '',
        postage: '',
        realPay: formatPrice(payment.buyerRealPay),
        buyWay: order.isAllowShowBuyWay ? order.buyWayStr || '' : '',
        buyWayExtendDesc: order.isAllowShowBuyWay ? order.buyWayExtendDesc || '' : '',
        buyWayDetailUrl: order.isAllowShowBuyWay ? buyWayDetailUrl || '' : '',
        payUmpDiscountMoney: payment.payUmpDiscountMoney || 0,
        prepayCardPay: '',
        taxPrice: taxPrice ? formatPrice(taxPrice, '+') : '',
        orderExtraPrices: payment.orderExtraPrices,
      };

      const { payPrice = 0 } = payment;
      const { pointsPrice = 0 } = order;
      res.total = formatTotalPrice(payPrice, pointsPrice, payment.pointsName);

      // 运费
      if (showPostage && payment.postage >= 0) {
        // res.postage = formatPrice(payment.postage, '+');
        res.postage = getPostageValue(payment.expressPayMode, payment.postage);
      }

      // 储值卡/礼品卡
      const { deductionPay = 0 } = payment;
      res.prepayCardPay = deductionPay ? formatPrice(deductionPay, '-') : '';

      res.assetPayInfos = (payment.assetPayInfos || []).map((assetInfo) => {
        return {
          ...assetInfo,
          formattedPrice: formatPrice(assetInfo.realPrice, '-'),
        };
      });

      return res;
    },
    extraPrices() {
      return (this.price.orderExtraPrices || []).map((item) => ({
        name: item.name,
        desc: item.desc,
        realPay: item.realPay,
        price: `+ ¥ ${(item.realPay / 100).toFixed(2)}`,
        showTips: item.name === '打包费',
      }));
    },
  },
  actions: () => ({}),
};

export default function createStore(ctx) {
  return _createStore({
    state: () => ({
      ...rootStore?.state,
    }),
    getters: {
      ...rootStore?.getters,
    },
    actions: {
      ...rootStore?.actions(ctx),
    },
  });
}
