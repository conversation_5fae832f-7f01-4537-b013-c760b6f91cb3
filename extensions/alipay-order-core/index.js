import Toast from '@youzan/vant-tee/dist/toast/toast';
/* #ifdef web */
import args from '@youzan/weapp-utils/lib/args';
import { ZNB } from '@youzan/tee-biz-navigate';
/* #endif */

const isAliPayTradeModuleOrder = (orderExtra) => {
  let BIZ_ORDER_ATTRIBUTE = {};
  try {
    BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}');
    return BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'ALIPAY_TRADE_MODULE';
  } catch (error) {
    return false;
  }
};

export default class Extension {
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.process.define('beforePrepay', this.beforePrepay.bind(this));
  }

  beforePrepay() {
    const { prepare = {} } = this.ctx.data;

    return new Promise((resolve, reject) => {
      /* #ifdef weapp */
      if (isAliPayTradeModuleOrder(prepare.extensions)) {
        Toast('支付宝系统限制，该订单需要取消后重新下单');
        return reject();
      }
      /* #endif */
      /* #ifdef web */
      if (isAliPayTradeModuleOrder(prepare.extensions)) {
        const { isAlipayApp = false } = _global?.miniprogram || {};
        if (isAlipayApp) {
          const { order = {} } = this.ctx.data.payState || {};
          return ZNB.navigate({
            aliappUrl: args.add('/packages/assets/ali-trade-module/index', {
              orderNo: order.outBizNo || order.orderNos[0],
            }),
            type: 'redirectTo',
          });
        }

        Toast('支付宝系统限制，该订单需要取消后重新下单');
        return reject();
      }
      /* #endif */
      resolve();
    });
  }

  static lambdas = {
    isAliPayTradeModuleOrder,
  };
}
