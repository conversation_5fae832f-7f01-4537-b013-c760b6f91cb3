import { cdnImage, buildUrl } from '@youzan/tee-biz-util';
import { BTN, TAG } from './contants';
import flatten from '@youzan/utils/array/flatten';
import formatMoney from '@youzan/utils/money/format';
/* #ifdef web */
// 赠送时长
function getGivingDuration(givingDuration, currnetSkuId) {
  const giveawayTypeEnum = ['赠$1天', '赠$1课时'];
  if (Array.isArray(givingDuration) && givingDuration.length) {
    return givingDuration
      .filter(({ skuId }) => skuId === currnetSkuId)
      .map((item) => {
        const { giveawayType, number } = item;
        const des = giveawayTypeEnum[giveawayType - 1];
        return des.replace('$1', giveawayType === 2 ? Number(number / 100).toFixed(2) : number);
      })
      .join(' ');
  }
  return '';
}
/* #endif */

// 格式化金额
const formatPrice = (price = 0, operator) => {
  const symbol = operator ? `${operator} ¥` : '¥';
  return `${symbol} ${(Math.abs(price) / 100).toFixed(2)}`;
};
// 动态计算提示信息
function goodsTips(good) {
  const tips = [];
  if (good.preSaleDate) {
    tips.push(good.preSaleDate);
  }
  if (good.expressPayMode) {
    tips.push('运费到付');
  }
  return tips;
}

function getGoodsUrl(item, order, orderBizExtra) {
  const { isUsingNewSnapshot = false } = orderBizExtra;
  const { goodsInfo = {} } = item;
  let goodsUrl = buildUrl(`/showcase/goods?alias=${goodsInfo.alias}`, 'wap', order.kdtId);

  const isCourseGoods = goodsInfo.courseGoodsMark && goodsInfo.courseGoodsMark !== 'null';
  // 课程商品链接
  if (isCourseGoods) {
    goodsUrl = buildUrl(
      `/wscvis/edu/prod-detail?alias=${goodsInfo.alias}&kdt_id=${order.kdtId}`,
      'h5',
      order.kdtId
    );
  } else if (item.snapShot && !isUsingNewSnapshot) {
    // 商品快照
    goodsUrl = buildUrl(
      `/showcase/goodsSnapRedirect/urlByOrder?order_no=${order.orderNo}&alias=${goodsInfo.alias}&goods_id=${item.goodsId}&snap_key=${item.snapShot}`,
      'wap',
      order.kdtId
    );
  }

  let disableGoodsUrl = false;
  // 收银台订单，无商品页跳转
  disableGoodsUrl = disableGoodsUrl || order.orderType === 6;
  // 佣金订单，无商品页跳转
  disableGoodsUrl = disableGoodsUrl || order.buyWay === 21;
  // 知识付费订单屏蔽跳转
  disableGoodsUrl = disableGoodsUrl || order.orderType === 75;
  if (disableGoodsUrl) {
    goodsUrl = 'javascript:;';
  }
  return goodsUrl;
}

/** 格式化金额（支持负数） */
const formatPricePlus = (price) => {
  if (!price) {
    return '';
  }
  const format = (price) => {
    return `¥${(Math.abs(price) / 100).toFixed(2)}`;
  };
  // 支持负数
  return price > 0 ? `${format(price)}` : `-${format(price)}`;
};

// 获取商品属性
const getPropertiesStr = (goods) => {
  const extra = goods.extra || {};
  // 商品属性
  const properties = extra.GOODS_PROPERTY || [];

  return properties.map((property) => property.valName).join(', ');
};

// 获取商品sku信息
const getGoodsDesc = (goods = {}) => {
  const skuStr = (goods.sku || [])
    .filter((item) => item.v)
    .map((item) => item.v)
    .join(', ');
  const propertiesStr = getPropertiesStr(goods);

  return [skuStr, propertiesStr].filter((item) => !!item).join(', ');
};

// 将价格分割成整数和小数部分
// 11 => { integer: '11', decimals: '' }
// 10.11 => { integer: '10', decimals: '11' }
const divisionPrice = (price) => {
  price = String(price);

  if (price.indexOf('.') > -1) {
    return {
      integer: price.split('.')[0],
      decimals: price.split('.')[1] !== '00' ? price.split('.')[1] : '',
    };
  }

  return {
    integer: price,
    decimals: '',
  };
};

const getRealPriceObj = (goods, pointsName) => {
  const { goodsInfo, realPay, num } = goods;
  const { pointsPrice = 0 } = goodsInfo;
  const realPrice = (realPay / 100).toFixed(2);
  const { integer, decimals } = divisionPrice(realPrice);

  return {
    realPay,
    pointsName,
    integer,
    decimals,
    pointsPrice: pointsPrice * num,
  };
};

const getOriginPriceObj = (goods) => {
  const { payPrice, originUnitPrice, num } = goods;
  const originPrice = ((originUnitPrice ? originUnitPrice * num : payPrice) / 100).toFixed(2);
  const { integer, decimals } = divisionPrice(originPrice);

  return {
    originPrice,
    integer,
    decimals,
  };
};

// 获取 goods 数据
const formatGoods = (
  goods,
  activityType,
  isShowPostponeShip,
  pointsName,
  order,
  orderBizExtra,
  env,
  stockCouponGoods,
  orderExtra
) => {
  return goods.map((item, index) => {
    let formatComboDetail = item?.comboDetail?.comboGroups?.map((comboGroup) => {
      let childrenGoods;
      if (comboGroup?.combos?.length) {
        childrenGoods = comboGroup?.combos?.map(({ gName, n, sName, properties, ap }) => {
          if (properties?.length || sName) {
            const propsDesc = properties
              ?.map((prop) => `${prop.valName}${formatPricePlus(prop.price)}`)
              ?.filter((x) => x)
              .join(';');
            if (properties?.length && sName) {
              // 既有属性又有规格
              return `${gName}（${sName}${formatPricePlus(ap)};${propsDesc}）x${n}`;
            }
            if (properties?.length && !sName) {
              // 有属性无规格
              return `${gName}（${propsDesc}）x${n}`;
            }
            if (!properties?.length && sName) {
              // 无属性有规格
              return `${gName}（${sName}${formatPricePlus(ap)}）x${n}`;
            }
          }
          return `${gName} x${n}`;
        });
      }
      return childrenGoods;
    });
    formatComboDetail = flatten(formatComboDetail).filter((x) => x);
    const res = {
      url:
        item.goodsType === 21
          ? ''
          : `/pages/goods/detail/index?alias=${item.goodsInfo.alias || ''}`,
      thumb: item.goodsInfo.imgUrl ? cdnImage(item.goodsInfo.imgUrl, '!180x180.jpg') : '',
      title: item.goodsInfo.shortTitle || item.goodsInfo.title || '',
      pointsPrice: item.goodsInfo.pointsPrice,
      price: item.unitPrice ? formatMoney(item.unitPrice) : 0,
      realPriceObj: getRealPriceObj(item, pointsName),
      originPriceObj: getOriginPriceObj(item),
      desc: getGoodsDesc(item),
      num: item.num || 0,
      itemId: item.itemId || '',
      /* #ifdef web */
      id: item.itemId + '' + index, // 满减送商品时会出现两个同样商品情况
      /* #endif */
      preSaleDate:
        (item.controlExtra && item.controlExtra.preSale && item.controlExtra.preSale.preSaleDate) ||
        '',
      skuId: item.skuId || '',
      goodsId: item.goodsId || '',
      alias: item.goodsInfo.alias || '',
      goodsType: item.goodsType || '',
      message: item.buyerMemo || {},
      safeRefund: item.safeRefund || {},
      originPrice: '',
      imgTag: {},
      tags: [],
      btns: [],
      taxTips: '',
      feedbackTips: '', // 售后维权状态文案
      refundOrderItem: item.refundOrderItem || {},
      cancelPostponeDeliveryUrl: item.cancelPostponeDeliveryUrl,
      postponeDeliveryUrl: item.postponeDeliveryUrl,
      processInfo: item.processInfo, // 商品制作进度
      isShipped: !!item.isShipped,
      isPrescriptionDrugGoods: item.extra?.IS_PRESCRIPTION_DRUG_GOODS,
      comboDetail: item?.comboDetail, // 套餐商品子商品详情
      formatComboDetail, // 格式化套餐商品子商品详情
      bizExtra: item?.bizExtra || {},
      refundAmt: formatMoney(item.refundAmt || 0), // 主动退款总金额
      refundRecord: (item.refundRecord || []).map((item) => {
        return {
          ...item,
          refundAmt: formatMoney(item.refundAmt),
        };
      }), // 主动退款记录
    };
    /* #ifdef web */
    const { giving_duration: givingDuration } = _global;
    /* #endif */

    const extra = item.extra || {};
    const usedPro = extra.USED_PRO || {};

    // 跨境商品税费计算
    // tariffTag， 1表示不是海淘订单，2表示含税，3表示不含税
    const { tariffTag, tariffPay = 0 } = item;
    if (tariffTag === 2) {
      res.taxTips = '进口税(含运费税款)：商品已含税';
    } else if (tariffTag === 3) {
      res.taxTips = `进口税(含运费税款)：¥ ${(tariffPay / 100).toFixed(2)}`;
    } else {
      res.taxTips = '';
    }

    // 商品标签
    item.controlButton.isPreSale && res.tags.push(TAG.presale);
    orderBizExtra.isSelfFetch && res.tags.push(TAG.selfFetch); // 自提
    orderBizExtra.isEnjoyBuy && res.tags.push(TAG.enjoyBuy); // 随心订
    item.controlButton.isPeriodBuy && res.tags.push(TAG.periodBuy);
    activityType === 20 && res.tags.push(TAG.fCode);
    activityType === 21 && res.tags.push(TAG.bargain);
    /* #ifdef web */
    const gdTag = getGivingDuration(givingDuration, item.skuId);
    gdTag && res.tags.push({ text: gdTag, type: 'danger', plain: true }); // 赠送时长
    /* #endif */
    item.isUseFissionUmp && res.tags.push(TAG.inSourcing);
    item.controlButton.isSeckill && res.tags.push(TAG.seckill);
    item.controlButton.isPresent && res.tags.push(TAG.present);
    item.controlButton.isTimelimitedDiscount && res.tags.push(TAG.timelimitedDiscount);
    item.controlButton.isAuction && res.tags.push(TAG.auction);
    item.controlButton.isCustomerDiscount && res.tags.push(TAG.customerDiscount);
    +usedPro.activityType === 24 && res.tags.push(TAG.plusBuy); // 加价购
    item.controlButton.isCrossBorder && res.tags.push(TAG.crossBorder);
    item.controlButton.isUseGoodsExchangeCoupon && res.tags.push(TAG.exchangeCoupon);

    const isPaidContentOrder = item.goodsType === 31;
    const isSamePrice = item.unitPrice === item.originUnitPrice;
    // 划线价
    if (
      item.controlButton.isSeckill ||
      item.controlButton.isPresent ||
      item.controlButton.isTimelimitedDiscount ||
      item.controlButton.isAuction ||
      item.controlButton.isCustomerDiscount ||
      (isPaidContentOrder && !isSamePrice)
    ) {
      res.originPrice = item.originUnitPrice ? formatPrice(item.originUnitPrice) : '';
    }
    // 如果为失效赠品，则展示失效状态
    res.isInvalid = item.controlButton.isInvalidPresent;

    /* #ifdef web */
    res.goodsUrl = getGoodsUrl(item, order, orderBizExtra); // 商品链接
    /* #endif */

    // 商品按钮-加入购物车
    // 如果存在先囤后约商品不展示加入购物车
    if (
      orderBizExtra.isShowAddCart &&
      !env.isFxZpp &&
      !stockCouponGoods &&
      item.extra?.IS_PRICE_CALENDAR !== 1
    ) {
      res.btns.push(BTN.addToCart);
    }

    // qtt 再来一单
    if (env.isFxZpp && orderExtra.ATTR_FX_ZPP_NOTE_ID) {
      res.btns.push(BTN.reorder);
    }

    // 商品按钮-查看留言
    if (item.buyerMemo && Object.keys(item.buyerMemo).length) {
      res.btns.push(BTN.message);
    }

    // 周期购顺延
    if (isShowPostponeShip) {
      res.btns.push(BTN.postpone);
    }

    // 商品按钮-退款
    const showTypes = [1, 3, 4];
    const showRefund =
      item.safeRefund &&
      Object.keys(item.safeRefund) &&
      showTypes.indexOf(item.safeRefund.buttonType) !== -1;

    if (showRefund) {
      res.btns.push({ ...BTN.refund, text: item.safeRefund.buttonText });
    }

    // 商品售后状态
    if (item?.safeRefund?.buttonType === 5) {
      res.feedbackTips = item?.safeRefund?.buttonText;
    }

    res.goodsTips = goodsTips(item);

    return res;
  });
};

export { formatGoods };
