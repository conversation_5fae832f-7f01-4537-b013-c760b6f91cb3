<template>
  <view class="bottom-btn">
    <block v-if="isOpenGift && (canGetGift || isActivityEnd)">
      <user-authorize
        :auth-type-list="['mobile']"
        v-if="canGetGift"
        class="bottom-btn-primary"
        @next="handleTakeGiftClick"
      >
        {{ gameType === 2 ? '参与抽奖' : '收下礼物' }}
      </user-authorize>
      <view v-if="isActivityEnd" class="bottom-btn-primary" @click="handleBuyAgain"
        >我也要送礼</view
      >
    </block>
    <block v-else>
      <block v-if="presenterView">
        <view
          v-if="finallyTaken || (gameType === 2 && lotteryInfo.isEnd)"
          class="bottom-btn-primary"
          @click="handleBuyAgain"
          >再送一份</view
        >
        <block v-else>
          <view class="bottom-btn-default" @click="handleCreatePoster">生成海报</view>
          <button class="bottom-btn-primary" @click="handleShareGift" open-type="share">
            送好友
          </button>
        </block>
      </block>
      <block v-if="!presenterView && giftOrderNo">
        <block v-if="showFillAddressTips">
          <user-authorize
            :auth-type-list="['nicknameAndAvatar']"
            :kdt-id="kdtId"
            @next="handleRepresentTap"
            class="bottom-btn-default"
          >
            转赠好友
          </user-authorize>
          <view @click="handleAddressTap" class="bottom-btn-primary bottom-btn-primary-more">
            <view>填收货地址</view>
            <view class="sub-text">朋友不会看到你的地址</view>
          </view>
        </block>
        <view v-else class="bottom-btn-primary" @click="handleBuyAgain">我也要送礼</view>
      </block>
      <!-- 抽奖玩法参与了且没到时间，或者抽奖结束没中奖 -->
      <block
        v-if="
          !presenterView &&
          gameType === 2 &&
          engage &&
          (!lotteryInfo.isEnd || (lotteryInfo.isEnd && lotteryInfo.userStatus !== 'GET'))
        "
      >
        <view class="bottom-btn-primary" @click="handleBuyAgain">我也要送礼</view>
      </block>
    </block>
  </view>
</template>

<script>
export default {
  props: {
    presenterView: Boolean,
    showFillAddressTips: Boolean,
    isPresenter: Boolean,
    isOpenGift: Boolean,
    isActivityEnd: Boolean,
    canGetGift: Boolean,
    useUp: Boolean,
    outDated: Boolean,
    gameType: Number,
    giftOrderNo: String,
    lotteryEnd: {
      type: Boolean,
      default: false,
    },
    finallyTaken: Boolean,
    engage: Boolean,
    lotteryInfo: Object,
  },

  data() {
    return {};
  },

  methods: {
    handleBuyAgain() {
      this.$emit('jump');
    },

    handleRepresentTap() {
      this.$emit('represent');
    },

    handleAddressTap() {
      this.$emit('address');
    },

    handleCreatePoster() {
      this.$emit('createPoster');
    },

    handleShareGift() {
      /* #ifdef web */
      this.$emit('share');
      /* #endif */
    },

    handleTakeGiftClick() {
      this.$emit('takeGiftClick');
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-btn {
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 0 16px;
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  box-sizing: border-box;
  margin-top: 4px;
  display: flex;
  border-top: 0.5px solid #e0e0e0;

  &-default,
  &-primary {
    text-align: center;
    border-radius: 8px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    margin: 8px 0;
    font-weight: 500;
  }

  &-default {
    width: 114px;
    margin-right: 12px;
    border: 1px solid #e0e0e0;
  }

  &-primary {
    color: #5f3f36;
    background: #ecc085;
    font-size: 16px;
    flex: 1;
    border: 1px solid #ecc085 !important;
    height: 52px !important;

    &-more {
      display: flex;
      flex-direction: column;
      line-height: 20px;
      padding-top: 5px;
      box-sizing: border-box;
      .sub-text {
        color: #5f3f36ab;
        font-size: 10px;
        opacity: 0.6;
      }
    }
  }
}
</style>
