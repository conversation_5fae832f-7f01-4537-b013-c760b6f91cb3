import { IStoreObject, mergeStore } from './helper';

import paidAction from './modules/paid-action/index';

// 展示渲染视图层
import { createStore } from '@ranta/store';

const defaultStore: IStoreObject = {
  state: {
    kdtId: 0,
    orderNo: '',
    orderItems: [],
    payResult: {},
    hasPaid: false,
    mpData: {},
    isHideYouzanFollow: false,
    hidePickUpCodeBtn: false,
    btnList: [],
  },
  getters: {},
  actions: {},
};

const rootStore: IStoreObject = [defaultStore, ...[paidAction]].reduce(
  (a, b) => mergeStore(a, b),
  {}
);

export default function createAddressStore(ctx) {
  return createStore({
    state: () => ({
      ...rootStore.state,
      ...paidAction.getState(ctx),
    }),
    getters: {
      ...rootStore.getters,
    },
    actions: {
      ...rootStore.actions,
      ...paidAction.getActions(ctx),
    },
  });
}
