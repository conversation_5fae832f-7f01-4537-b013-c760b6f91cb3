import { createStore } from '@ranta/store';
import { cdnImage } from '@youzan/tee-biz-util';
import { getInSourcingCoupon } from '../utils/in-sourcing-fission';
import get from '@youzan/utils/object/get';

const state = {
  // 是否展示周边好物模块，默认null为未知
  showPaidCpsGoods: null,
  // 是否展示流量互换模块，默认null为未知
  showFlowChangeUmp: null,
  // 是否展示导购券模块，默认null为未知
  /* #ifdef web */
  showGuideCoupon: null,
  /* #endif */
  hasPaid: false,
  isWxReceipt: false,
  canUseTradeUmpV1: false,
  payResult: {},
};

const getters = {
  // 全部为布尔值则为加载完成
  paidExtHolderLoaded() {
    /* #ifdef web */
    if (this.isWxReceipt) {
      return true;
    }
    /* #endif */
    return [
      this.showFlowChangeUmp,
      /* #ifdef web */
      this.showGuideCoupon,
      /* #endif */
      this.showBigDataCoupon,
      this.showPaidCpsGoods,
    ].every((val) => typeof val === 'boolean');
  },
  // 任何一个模块展示则此为true
  // TODO: 这里判断非常鸡肋，依赖组件的回调，需要优化
  emptyPaidExtHolderBlock() {
    /* #ifdef web */
    if (this.isWxReceipt) {
      return !this.showBigDataCoupon;
    }
    /* #endif */
    return [
      this.showFlowChangeUmp,
      /* #ifdef web */
      this.showGuideCoupon,
      /* #endif */
      this.showBigDataCoupon,
      this.showPaidCpsGoods,
    ].every((val) => val === false);
  },
  inSourcingFission() {
    return getInSourcingCoupon(this.payResult);
  },
  paidPromotion() {
    return this.payResult.paidPromotion || {};
  },
  // 是否展示内购券模块
  showInSourcingCoupon() {
    return (this.hasPaid || this.isWxReceipt) && this.inSourcingFission.isDisplay;
  },
  // 是否展示大数据券模块
  showBigDataCoupon() {
    // 如果展示裂变券，直接不展示此券
    if (this.isShowFissionCoupon) {
      return false;
    }
    return (
      (!this.paidPromotion || !this.paidPromotion.detailUrl) &&
      !!this.payResult?.shopCoupons?.[0]?.bigDataCoupons?.[0]?.value
    );
  },
  // 批发订单
  isWholesaleOrder() {
    return this.payResult?.isWholesaleOrder || false;
  },
  // 裂变优惠券
  fissionCoupon() {
    return get(this.payResult, 'shopCoupons[0].orderCoupons[0]', {});
  },
  // 是否展示裂变券
  isShowFissionCoupon() {
    return (
      (this.hasPaid || this.isWxReceipt) &&
      !this.canUseTradeUmpV1 &&
      this.fissionCoupon.quantity > 0
    );
  },
  weappImg() {
    /* #ifdef weapp */
    return this.payResult.weappImg || cdnImage('public_files/53007f232f165a057088c0cfe1ccd606.png');
    /* #endif */
  },
  webImg() {
    /* #ifdef web */
    return this.payResult.webImg || '';
    /* #endif */
  },
};

const actions = {
  onFlowChangeUmpView(flag) {
    this.showFlowChangeUmp = flag;
  },
  /* #ifdef web */
  onGuideCouponView(flag) {
    this.showGuideCoupon = flag;
  },
  /* #endif */
  onPaidCpsGoodsView(flag) {
    this.showPaidCpsGoods = flag;
  },
};

export default function createPaidExtHolderStore() {
  const store = createStore({
    state: () => state,
    getters,
    actions,
  });
  return store;
}
