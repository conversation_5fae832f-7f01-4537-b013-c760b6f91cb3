import { requestV2 } from '@youzan/tee-biz-request';
import { groupList } from '../utils';

const prefix = '/retail/h5/miniprogram';

// 批量校验地址是否超出配送范围
// 对超出20条的addressList做并发请求处理
export function batchCheckAddressAvailable(warehouseId, addressList) {
  if (addressList.length === 0) return Promise.resolve([]);
  const rq = (addressList) => {
    return requestV2({
      path: `${prefix}/checkUserAddressSupport.json`,
      data: {
        warehouseId,
        addressList,
      },
      method: 'POST',
    }).catch(() => []);
  };
  return Promise.all(groupList(addressList, 20)
    .map(rq))
    .then((resArr) => {
      const arr = [];
      for (let i = 0; i < resArr.length; i += 1) {
        arr.push(...resArr[i]);
      }
      return arr;
    });
}

export function getPendingOrderWaitingProgress(data) {
  return requestV2({
    path: `${prefix}/order/pendingOrderWaitingProgress.json`,
    data,
  });
}

export function getWarehouseBySupplyMode(data) {
  return requestV2({
    path: `${prefix}/getWarehouseBySupplyMode.json`,
    data,
  });
}

// 小程序订阅消息模板
export function getTemplate(data) {
  return requestV2({
    path: `${prefix}/getTemplate.json`,
    data,
  });
}
