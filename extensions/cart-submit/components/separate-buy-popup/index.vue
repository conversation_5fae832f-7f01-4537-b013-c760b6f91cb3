<template>
  <van-popup
    :show="separateBuy.show"
    custom-class="separate-buy-popup"
    position="bottom"
    @close="onClose"
  >
    <view class="title">
      选择结算商品
      <van-icon class="icon" name="cross" @click="onClose" />
    </view>
    <view class="tip">{{ TIP_MAP[separateBuy.type] }}</view>
    <view class="container">
      <view v-for="(goodItem, index) in formattedData" :key="index" class="cell">
        <view class="t-clearfix cell-title-wrap">
          <view class="cell-title">{{ goodItem.title }}</view>
          <view v-if="goodItem.isHasHaiTao" class="prompt">
            <text>（含</text>
            <!-- eslint-disable-next-line @youzan-open/tee/require-child-tag -->
            <text class="icon-haitao" />
            <text>的商品）</text>
          </view>
        </view>

        <view class="cell-content">
          <view>
            <image
              v-for="(imageItem, imageIndex) in goodItem.goodItemsFilter"
              :key="imageIndex"
              :src="imageItem.imgUrl"
              class="img-container"
              mode="aspectFit"
            />

            <text v-if="goodItem.list.length > 3" class="cell-dot">...</text>
          </view>
          <view>
            <van-button custom-class="button-wrap" @click="goPay(index)">去结算</van-button>
          </view>
        </view>

        <view class="total"
          >共 <text class="num">{{ goodItem.list.length }}</text>
          <text> {{ goodItem.title === '课程' ? '门' : '件' }}</text
          >，合计：
          <view>
            <price
              custom-class="price"
              :price="goodItem.totalPrice"
              :points-price="goodItem.totalPointsPrice"
              :points-name="pointsName"
            />
          </view>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script>
import VanPopup from '@youzan/vant-tee/dist/popup/index.vue';
import VanIcon from '@youzan/vant-tee/dist/icon/index.vue';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import { tee } from '@youzan/tee-util/lib';
import Price from '../../Price';
import each from '@youzan/weapp-utils/lib/each';
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */
import { SEPARATE_BUY_TYPE, TIP_MAP } from '../../constants';

export default {
  components: {
    'van-popup': VanPopup,
    'van-icon': VanIcon,
    'van-button': VanButton,
    price: Price,
  },

  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */

  props: {
    separateBuy: Object,
    themeCss: String,
    pointsName: String,
  },

  data() {
    return {
      TIP_MAP,
    };
  },

  computed: {
    customPopupStyle() {
      let bottomHeight = 0;
      /* #ifdef weapp */
      bottomHeight = this.popupBottom;
      if (this.safeBottom) {
        bottomHeight = this.noSafeBottom;
      }
      /* #endif */
      return `bottom: ${bottomHeight}px; ${this.themeCss}`;
    },

    showBeauty() {
      const { type } = this.separateBuy;
      return (
        [
          SEPARATE_BUY_TYPE.COURSE_MIX_TYPE,
          SEPARATE_BUY_TYPE.MIX_TYPE,
          SEPARATE_BUY_TYPE.LOGISTICS,
        ].indexOf(type) !== -1
      );
    },

    formattedData() {
      const { data } = this.separateBuy;
      const result = data.map((item) => {
        const { list } = item;
        const totalPrice = list.reduce((sum, cur) => sum + cur.payPrice * cur.num, 0);
        const totalPointsPrice = list.reduce(
          (sum, cur) => sum + (cur.pointsPrice || 0) * cur.num,
          0
        );
        return {
          ...item,
          isHasHaiTao: list.some((i) => i.settlementRule?.settlementMark === 'HAITAO'),
          goodItemsFilter: list.slice(0, 3),
          totalPrice,
          totalPointsPrice,
        };
      });
      return result;
    },
  },

  methods: {
    onClose() {
      this.$emit('close-separate-buy-popup');
    },

    goPay(index) {
      const { type, data: separateList } = this.separateBuy;
      const { list } = separateList[index];
      if ([SEPARATE_BUY_TYPE.MIX_TYPE, SEPARATE_BUY_TYPE.COURSE_MIX_TYPE].includes(type)) {
        this.$emit('buy', { list }, { bubbles: true, composed: true });
      } else {
        this.logisticsBuy(index);
      }
    },

    logisticsBuy(index) {
      const { data: separateList } = this.separateBuy;
      const { expressType, list: currentBuyGoods } = separateList[index];
      // 去下单的商品
      const currentBuyGoodsIds = currentBuyGoods.map((item) => item.cartId);
      // 排除掉去下单的剩余商品，支付完成后提示继续购买
      const waitBuyGoodsIds = [];

      each(separateList, (value, oIndex) => {
        if (index === oIndex) {
          return;
        }

        each(value.list, ({ cartId }) => {
          if (waitBuyGoodsIds.indexOf(cartId) === -1 && currentBuyGoodsIds.indexOf(cartId) === -1) {
            waitBuyGoodsIds.push(cartId);
          }
        });
      });

      if (waitBuyGoodsIds.length) {
        tee.storage.set('waitBuyGoodsIds', waitBuyGoodsIds, {
          expire: 0.02, // 30min
        });
      }

      this.$emit(
        'buy',
        {
          list: currentBuyGoods,
          expressType,
        },
        { bubbles: true, composed: true }
      );
    },
  },
};
</script>

<style lang="scss">
.separate-buy-popup {
  border-radius: var(--theme-radius-dialog, 16px) var(--theme-radius-dialog, 16px) 0 0;
  overflow: hidden;

  .logistics {
    padding-top: 50px;
  }

  .title {
    background-color: #fff;
    font-size: 16px;
    color: #323233;
    padding: 11px 0;
    text-align: center;
    position: relative;
  }

  .icon {
    position: absolute;
    right: 15px;
    top: 13px;
    color: #999;
    font-size: 20px;
  }

  .tip {
    font-size: 12px;
    padding: 0 16px;
    color: #999;
    margin-bottom: 16px;
  }

  .container {
    overflow: scroll;
    height: 65vh;
  }

  .cell {
    position: relative;
    padding: 12px;
    background: #f7f8fa;
    border-radius: var(--theme-radius-card, 8px);
    margin: 0 16px 12px;
  }

  .cell-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cell-title-wrap {
    margin-bottom: 8px;
  }

  .cell-title {
    font-weight: 500;
    font-size: 14px;
    color: #323233;
    vertical-align: middle;
    display: inline-block;
  }

  .count {
    float: left;
    margin-top: 2px;
    font-size: 12px;
    color: #9b9b9b;
  }

  .items {
    width: auto;
    margin-right: 75px;
    white-space: nowrap;
  }

  .cell-dot {
    display: inline-block;
    vertical-align: bottom;
    color: #323233;
  }

  .img-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--theme-radius-card, 4px);
    margin-right: 8px;
    vertical-align: middle;
    box-sizing: border-box;
    background-color: #fff;
    object-fit: cover;
  }

  .img-container image {
    max-width: 100%;
    max-height: 100%;
    border-radius: 4px;
    background-color: #fff;
  }

  .ellipsis {
    font-size: 14px;
  }

  .button-wrap {
    height: 24px !important;
    line-height: 24px !important;
    text-align: center;
    border-radius: var(--theme-radius-button, 12px) !important;
    width: 56px !important;
    font-size: 12px !important;
    white-space: nowrap;
    color: var(--main-text, #fff) !important;
    background: var(--main-bg, #f44) !important;
  }

  .total {
    margin-top: 10px;
    font-size: 13px;
    color: #323233;
    line-height: 17px;
    display: flex;
  }

  .price {
    font-family: Avenir;
    font-weight: var(--theme-common-price-font-weight, 600);
    color: var(--price, #f44);
  }

  .prompt {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    color: #969799;
    vertical-align: middle;
  }

  .icon-haitao {
    display: inline-flex;
    width: 28px;
    height: 16px;
    background-image: url('https://img01.yzcdn.cn/public_files/2019/08/19/fbd4c38994578e951ef1cdfd9104606d.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    vertical-align: middle;
    margin: 0 3px;
  }
}
</style>
