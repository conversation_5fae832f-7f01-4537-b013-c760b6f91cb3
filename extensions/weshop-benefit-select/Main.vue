<template>
  <view class="wrapper">
    <van-dialog
      :show="showVersionDialog"
      title="提示"
      :message="versionDialogMessage"
      confirm-button-text="升级"
      cancel-button-text="返回"
      :confirm-button-color="themeColor"
      show-cancel-button
      :overlay-style="{ backgroundColor: 'rgba(0, 0, 0, 1)' }"
      :close-on-click-overlay="false"
      @confirm="handleVersionDialogUpgrade"
      @cancel="handleVersionDialogReturn"
      @close="handleVersionDialogClose"
    />
    <van-dialog
      :show="showZeroPriceInfoDialog"
      title="提示"
      :message="zeroPriceInfoMessage"
      confirm-button-text="我知道了"
      :confirm-button-color="themeColor"
      :close-on-click-overlay="true"
      @confirm="handleCloseZeroPriceIconClick"
      @cancel="handleCloseZeroPriceIconClick"
      @close="handleCloseZeroPriceIconClick"
    />
    <van-loading v-if="isLoading" size="24px" vertical class="loading-overlay">
      加载中...
    </van-loading>
    <top-tip v-if="showTopTip" />
    <view class="container">
      <view v-for="benefit in allBenefits" :key="benefit.type">
        <view class="title">{{ benefit.title }}</view>
        <view v-for="item in benefit.list" :key="item.id">
          <coupon-item
            :coupon="item"
            :chosen-coupons="benefit.chosenList"
            :data-index="item.id"
            :coupon-overlying-superposed-total-num="couponOverlyingSuperposedTotalNum"
            @onChange="onChangeCoupon($event, benefit.type)"
            :diy-color="themeColor"
            :color="themeColor"
          />
        </view>
      </view>
      <view v-if="!allBenefits.length" class="coupon-list__empty-text"> 暂无可用权益 </view>
    </view>
    <view class="footer">
      <view class="footer__total">
        共减 ¥
        <view class="footer__total-price">{{ ' ' }}{{ totalPrice }}</view>
        <van-icon
          v-if="isZeroPriceOrder"
          name="question-o"
          class="zero-price-icon"
          @click="handleZeroPriceIconClick"
        />
      </view>
      <view :color="themeColor" class="footer__btn" @click="submitOrder" :disabled="isLoading"
        >确认</view
      >
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Loading from '@youzan/vant-tee/dist/loading/index';
import VanDialog from '@youzan-open/vant-tee/dist/dialog/index.vue';
import VanIcon from '@youzan-open/vant-tee/dist/icon/index.vue';
import TopTip from './components/TopTip.vue';
import CouponItem from './components/coupon-item/index.vue';
import { fetchAllBenefits } from './api';
import compareVersion from 'shared/utils/compare-version';
import { getSystemInfoSync } from '@youzan/tee-api';

const { SDKVersion } = getSystemInfoSync();

// * 三种权益类型枚举定义
const benefitTypeMap = {
  COUPON: 'COUPON',
  POINTS: 'POINTS',
  MEMBER: 'MEMBER',
};

export default {
  components: {
    'top-tip': TopTip,
    'coupon-item': CouponItem,
    'van-loading': Loading,
    'van-dialog': VanDialog,
    'van-icon': VanIcon,
  },
  data() {
    return {
      totalPrice: 0,
      themeColor: '#fe623a', // * 按照设计稿的固定值
      couponOverlyingSuperposedTotalNum: 3,
      options: {},
      extraData: {},
      showTopTip: false,
      allBenefits: [],
      isLoading: false, // 用于控制 loading 状态
      showVersionDialog: false, // 新增：控制版本提示Dialog的显示
      versionDialogMessage:
        '你的微信版本过低，不支持切换会员权益。可点击"返回"使用默认会员权益下单，或点击"升级"跳转至最新版微信下载页面升级后再购买', // 新增：Dialog消息
      isZeroPriceOrder: false, // 新增：是否0元订单
      showZeroPriceInfoDialog: false, // 新增：控制0元订单提示Dialog的显示
      zeroPriceInfoMessage:
        '由于微信小店不能0元下单，所以会员优惠金额过大时会按照订单实付0.01元对扣减金额进行调整。', // 新增：0元订单Dialog消息
    };
  },
  created() {
    const options =
      Tee.$native.getEnterOptionsSync?.() || Tee.$native.getLaunchOptionsSync?.() || {};
    this.options = options;

    this.ctx.event.listen('onPageShow', () => {
      this.onPageShow();
    });
  },
  destroyed() {
    this.ctx.event.remove('onPageShow', this.onPageShow);
  },
  methods: {
    onPageShow() {
      const comparisonResult = compareVersion(SDKVersion, '3.7.2');
      // 如果微信版本小于3.7.2，则显示版本提示Dialog
      if (comparisonResult < 0) {
        this.showVersionDialog = true;
      } else {
        this.showVersionDialog = false;
        this._fetchAllBenefits({ isFirstEnter: true, options: this.options });
      }
    },
    _logErrorToTracker(scene, error) {
      const errorDetails = {};
      if (error instanceof Error) {
        // 判断是否是 Error 实例
        errorDetails.errorMessage = error.message;
        errorDetails.errorName = error.name;
        errorDetails.errorStack = error.stack; // 显式包含 stack
      } else if (typeof error === 'object') {
        errorDetails.error = error;
      } else {
        // 如果不是 Error 实例，尝试将其字符串化
        errorDetails.errorMessage = String(error);
        errorDetails.errorName = 'UnknownErrorType';
      }
      // 确保 this.ctx.logger 存在且 log 是一个函数
      if (this.ctx && this.ctx.logger && typeof this.ctx.logger.log === 'function') {
        this.ctx.logger.log({
          et: 'custom', // 硬编码
          ei: 'postMessageToReferrerPageFail', // 硬编码
          en: '确认权益失败', // 硬编码
          pt: 'wxvideoShopBenefit', // 硬编码
          params: {
            scene, // 场景描述，用于区分不同错误
            error: errorDetails,
          },
        });
      } else {
        // 如果 logger 不可用，可以降级到 console.error
        console.error('[Logger Service Unavailable] Scene:', scene, 'Error:', error);
      }
    },

    _fetchAllBenefits(params) {
      this.isLoading = true;
      fetchAllBenefits(params)
        .then(this.handleResponse)
        .catch((err) => {
          this._logErrorToTracker('获取权益列表失败', err);
          Toast.fail(err?.msg || '获取权益失败');
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    handleResponse(response) {
      this.extraData = response.extraData;
      this.allBenefits = response.parsedBenefitList;
      this.couponOverlyingSuperposedTotalNum = response.couponOverlyingSuperposedTotalNum;
      this.totalPrice = this.getTotalPrice(response);
      this.isZeroPriceOrder = response.isZeroPriceOrder || false; // 更新 isZeroPriceOrder
      // * 只在仅会员权益的时候展示顶部提示（ruleType: 3）
      if (response.ruleType === 3) {
        this.showTopTip = true;
      } else {
        this.showTopTip = false;
      }
    },
    getTotalPrice(response) {
      const originPrice = response?.extraData?.vip_discounted_info?.vip_discounted_price || 0;
      return (originPrice / 100).toFixed(2);
    },
    onChangeCoupon(type, $event) {
      const id = $event.detail;

      const targetList = this.allBenefits.find((item) => item.type === type);
      const targetItem = targetList.list.find((item) => item.id === id);
      const targetChosenList = this.allBenefits.find((item) => item.type === type).chosenList;
      const targetChosenItem = targetChosenList.find((item) => item.id === id);

      if (type === benefitTypeMap.COUPON) {
        if (targetChosenItem) {
          // 如果已选中，则移除
          targetChosenList.splice(targetChosenList.indexOf(targetChosenItem), 1);
        } else {
          // 检查已选中的优惠券是否可叠加
          const isChosenCouponsCanOverlying = targetChosenList.some(
            (coupon) => coupon.canOverlying
          );
          // 当前选中的优惠券是否可叠加
          const isCurrentChosenCanOverlying = targetItem.canOverlying;

          if (!isChosenCouponsCanOverlying || !isCurrentChosenCanOverlying) {
            // 如果已选中的不可叠加或当前选中的不可叠加，则清空列表并添加当前选中
            targetChosenList.length = 0;
            targetChosenList.push(targetItem);
          } else {
            // 如果已选中的和当前选中的都可叠加，则添加到列表
            targetChosenList.push(targetItem);
          }
        }
      } else if (targetChosenItem) {
        // 会员权益和积分抵现保持单选逻辑
        targetChosenList.splice(targetChosenList.indexOf(targetChosenItem), 1);
      } else {
        targetChosenList.length = 0;
        targetChosenList.push(targetItem);
      }

      // 提交选中结果
      this.submitChosenBenefits();
    },
    submitChosenBenefits() {
      this._fetchAllBenefits({
        isFirstEnter: false,
        options: this.options,
        allBenefits: this.allBenefits,
      });
    },
    submitOrder() {
      /* #ifdef weapp */
      const self = this; // 保存 this 上下文
      try {
        Tee.$native.postMessageToReferrerPage({
          extraData: this.extraData,
          success() {
            self.ctx.logger.log({
              et: 'custom', // 事件类型
              ei: 'postMessageToReferrerPageSuccess', // 事件标识
              en: '微信小店消息成功回传', // 事件名称
              pt: 'wxvideoShopBenefit', // 页面类型

              params: {
                scene: 'postMessageToReferrerPage Success', // 场景描述
                extraData: self.extraData, // 事件参数
              }, // 事件参数
            });
            try {
              Tee.$native.exitMiniProgram({
                success() {},
                fail(error) {
                  self._logErrorToTracker('退出小程序失败(postMessage成功后)', error);
                  Toast.fail('退出小程序失败');
                },
              });
            } catch (e) {
              self._logErrorToTracker('退出小程序异常(postMessage成功后)', e);
              Toast.fail('退出小程序异常');
            }
          },
          fail(error) {
            self._logErrorToTracker('确认权益失败(postMessageToReferrerPage调用失败)', error);
            Toast.fail('确认权益失败');
          },
        });
        // * 安卓机也会出现 not use tab 的问题，这里也直接调用退出小程序
        // if (isIOS) {
        try {
          Tee.$native.exitMiniProgram({
            success() {},
            fail(error) {
              self._logErrorToTracker('iOS退出小程序失败(直接调用)', error);
              Toast.fail('iOS退出小程序失败');
            },
          });
        } catch (e) {
          self._logErrorToTracker('iOS退出小程序异常(直接调用)', e);
          Toast.fail('iOS退出小程序异常');
        }
        // }
      } catch (e) {
        self._logErrorToTracker('确认权益前置逻辑异常', e);
        Toast.fail('操作失败，请稍后重试');
      }
      /* #endif */
    },
    // 新增：处理版本Dialog的升级操作
    handleVersionDialogUpgrade() {
      // @ts-ignore
      wx.updateWeChatApp();
      this.showVersionDialog = false;
    },
    // 新增：处理版本Dialog的返回操作
    handleVersionDialogReturn() {
      Tee.$native.exitMiniProgram();
    },
    handleVersionDialogClose() {
      this.showVersionDialog = false;
    },
    // 新增：处理0元订单问号图标点击
    handleZeroPriceIconClick() {
      this.showZeroPriceInfoDialog = true;
    },
    handleCloseZeroPriceIconClick() {
      this.showZeroPriceInfoDialog = false;
    },
  },
};
</script>

<style lang="scss">
.wrapper {
  height: 100vh;
  overflow: auto;
  background-color: #f7f8fa;

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    z-index: 9999;
    font-size: 16px;
  }
}

.container {
  padding-bottom: calc(80px + constant(safe-area-inset-bottom));
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
  .title {
    margin: 16px 0 12px 12px;
    font-size: 14px;
    color: #333;
  }
  .coupon-list__empty-text {
    margin: 16px auto;
    text-align: center;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &__total {
    display: flex;
    align-items: baseline;
    margin-left: 12px;

    .zero-price-icon {
      margin-left: 4px;
      color: #969799; // 与van-icon默认颜色保持一致或自定义
      font-size: 16px; // 可根据需要调整大小
    }

    &-price {
      font-size: 24px;
      font-weight: 800;
      margin-left: 6px;
    }
  }

  &__btn {
    width: 110px;
    height: 44px;
    margin: 12px;
    background-color: #fe623a;
    color: #fff;
    font-size: 16px;
    line-height: 44px;
    text-align: center;
    border-radius: 10px;
    cursor: pointer;

    &:hover {
      background-color: darken(#fe623a, 10%);
    }
  }
}
</style>
