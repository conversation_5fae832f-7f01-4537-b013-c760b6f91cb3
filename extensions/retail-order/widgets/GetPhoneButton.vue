<template>
  <view class="btn" :style="{ customStyle }" @click="getPhone">
    <user-authorize
      v-if="!phone"
      :kdt-id="kdtId"
      custom-style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
      :auth-type-list="['mobile']"
      :need-update-nickname-and-avatar="false"
      @next="getAuhPhone"
    ></user-authorize>
    自动填写
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { isPhone } from '@youzan/wsc-tee-trade-common/lib/order-utils/address-validate';

export default {
  props: {
    customStyle: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      kdtId: 0,
      phone: '',
    };
  },

  created() {
    mapData(this, ['kdtId']);
    mapData(this, {
      retailSelfFetchContact: (val) => {
        const phone = val?.phone;
        if (phone && isPhone(phone)) {
          this.phone = phone;
        }
      },
    });
  },

  methods: {
    getAuhPhone(result) {
      this.$emit('getPhone', result?.mobile || '');
    },
    getPhone() {
      this.$emit('getPhone', this.phone);
    },
  },
};
</script>

<style lang="scss" scoped>
.btn {
  position: relative;
  display: flex;
  height: 24tpx;
  font-size: 12tpx;
  /*color: var(--general); */
  color: #333;
  justify-content: center;
  align-items: center;
  border-radius: 12tpx;
}
</style>
