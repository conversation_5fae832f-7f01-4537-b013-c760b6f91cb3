<template>
  <!-- 交易组件3.0订单引导弹窗,后面会下掉 -->
  <van-popup
    :show="isShowWxVideoGuidePopup"
    round
    position="bottom"
    @close="toggleWxVideoGuidePopup(false)"
  >
    <view class="wxvideo-guide-pop">
      <view class="pop-header">查看/处理视频号订单</view>
      <image
        src="https://b.yzcdn.cn/public_files/b4bd57493510a13fe591b1c6e8bd65f0.png"
        class="wxvideo-guide-img"
        mode="aspectFit"
      />
      <view class="wxvideo-guide-btn" @click="toggleWxVideoGuidePopup(false)">我知道了</view>
    </view>
  </van-popup>
</template>

<script>
import { mapState } from '@ranta/store';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

export default {
  components: {
    'van-popup': Popup,
  },

  data() {
    return {
      ...mapState(this, ['isShowWxVideoGuidePopup']),
    };
  },

  methods: {
    toggleWxVideoGuidePopup(isShow) {
      this.store.toggleWxVideoGuidePopup({ isShow });
    },
  },
};
</script>

<style scoped lang="scss">
.wxvideo-guide-pop {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pop-header {
    font-weight: 500;
    font-size: 16px;
    line-height: 44px;
    margin-bottom: 8px;
  }

  .wxvideo-guide-img {
    width: 318px;
    height: 500px;
  }

  .wxvideo-guide-btn {
    width: 91.4%;
    margin-bottom: 5px;
    height: 40px;
    margin-top: 16px;
    background: linear-gradient(270deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 20px;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    color: #fff;
  }
}
</style>
