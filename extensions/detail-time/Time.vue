<template>
  <view class="time" :style="rootStyle">
    <view
      v-if="isUsingNewSnapshot && !isFxZpp"
      class="time__item snapshot"
      @click="handleGoSnapshotClick"
    >
      交易快照：
      <text class="time__item__text__color">发生交易争议时，可作为判断依据</text>
      <van-icon custom-class="snapshot-icon" name="arrow" />
    </view>
    <!-- #ifdef web -->
    <view v-if="fxZppNoteInfo.participateNo" class="time__item">
      跟团号：
      <text class="time__item__text__color">{{ fxZppNoteInfo.participateNo }}</text>
    </view>
    <!-- #endif -->

    <view class="time__item time__item__order-no">
      <view class="time__item__order-no__text">
        订单编号：<text class="time__item__text__color">{{ orderNo }}</text>
      </view>
      <van-button size="mini" custom-class="time__item__copy" @click="handleCopyBtnClick">
        复制
      </van-button>
    </view>
    <view v-if="!!time.createTime" class="time__item">
      创建时间：
      <text class="time__item__text__color">{{ formatTime.createTime }}</text>
    </view>
    <view v-if="!!time.payTime" class="time__item">
      {{ time.payTitle }}：
      <text class="time__item__text__color">{{ formatTime.payTime }}</text>
    </view>
    <view v-if="!!time.expressTime && !isEduOrder" class="time__item">
      发货时间：
      <text class="time__item__text__color">{{ formatTime.expressTime }}</text>
    </view>
    <view v-if="!!time.successTime" class="time__item">
      完成时间：
      <text class="time__item__text__color">{{ formatTime.successTime }}</text>
    </view>
    <view v-if="showYZQuestionUrl && !isTradeComponent3 && !isTTApp" class="time__action">
      <view v-if="orderBizUrl.yzGuaranteeUrl" class="time__action__item" @click="toYzGuaranteeUrl">
        联系放心购客服
      </view>
      <block v-else>
        <!-- 对此订单有疑问先下线，由于登录态问题 -->
        <view
          v-if="orderBizUrl.orderQuestionUrl && !orderBizUrl.moneyToWhereUrl"
          class="time__action__item"
          @click="toOrderQuestion"
        >
          对此订单有疑问？
        </view>
        <view v-if="!!orderBizUrl.moneyToWhereUrl" class="time__action__item money-to-where">
          温馨提示：货款已通过微信支付付给商家。
          <text class="time__action__item__link" @click="toMoneyToWhere">钱款去向</text>
        </view>
      </block>
    </view>
    <van-toast ref="van-toast" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Button from '@youzan/vant-tee/dist/button/index';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import { setClipboardData } from '@youzan/tee-api';
import { url, date, object } from '@youzan/tee-util';
import { buildUrl } from '@youzan/tee-biz-util';
import navigate from '@youzan/tee-biz-navigate';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-icon': Icon,
    'van-button': Button,
    'van-toast': VanToast,
  },

  data() {
    return {
      rootStyle: {},
      order: {},
      orderExtra: {},
      kdtId: 0,
      orderNo: '',
      time: {},
      orderBizUrl: {},
      isUsingNewSnapshot: false,
      miniprogram: {},
      isEduOrder: false,
    };
  },

  computed: {
    isFxZpp() {
      let isFxZpp = false;
      /* #ifdef web */
      isFxZpp = object.get(window, '_global.env.isFxZpp', false);
      /* #endif */
      return isFxZpp;
    },
    isTTApp() {
      let isTTApp = false;
      /* #ifdef web */
      isTTApp = !!window._global?.miniprogram?.isTTApp;
      /* #endif */

      return isTTApp;
    },
    formatTime() {
      const { time } = this;
      const formateTime = {};
      ['createTime', 'payTime', 'expressTime', 'successTime'].forEach((key) => {
        formateTime[key] = time[key] ? date.formatDate(time[key], 'YYYY-MM-DD HH:mm:ss') : '';
      });
      return formateTime;
    },

    showYZQuestionUrl() {
      const { miniprogram } = this;
      let isFxZpp = false;
      /* #ifdef web */
      isFxZpp = _global?.env?.isFxZpp || false;
      /* #endif */

      // 支付宝 QQ 快手 环境下 订单详情页隐藏掉联系有赞客服的需求
      return !miniprogram.isAlipayApp && !miniprogram.isQQApp && !isFxZpp && !miniprogram.isKsApp;
    },

    // 交易组件3.0屏蔽相关操作后面会下线!
    isTradeComponent3() {
      const { orderExtra } = this;
      console.log('orderExtra: ', orderExtra);

      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse((orderExtra || {}).BIZ_ORDER_ATTRIBUTE || '{}');
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION === 'TRADE_COMPONENT_3_0' ||
          BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN'
        );
      } catch (error) {
        return false;
      }
    },
    /* #ifdef web */
    // 群团团相关字段
    fxZppNoteInfo() {
      const { isFxZpp, orderExtra } = this;

      if (!isFxZpp) {
        return {};
      }

      return {
        participateNo:
          orderExtra?.ATTR_FX_ZPP_TRADE_PARTICIPATE_NO ||
          url.args.get('participateNo', location.href) ||
          '', //  跟团号
      };
    },
    /* #endif */
  },

  created() {
    mapData(this, [
      'rootStyle',
      'order',
      'kdtId',
      'orderNo',
      'time',
      'orderBizUrl',
      'isUsingNewSnapshot',
      'miniprogram',
      'isEduOrder',
      'orderExtra',
    ]);
  },

  methods: {
    handleGoSnapshotClick() {
      const { kdtId, orderNo } = this;
      this.ctx.logger.log({
        et: 'click',
        ei: 'click_history',
        en: '进入交易快照',
        si: kdtId,
      });
      const path = '/wsctrade/order/snapshot';
      const weappUrl = `${path}?order_no=${orderNo}&kdt_id=${kdtId}`;

      const snapshotUrl = url.args.add(buildUrl(path, 'h5'), {
        order_no: orderNo,
        kdt_id: kdtId,
      });

      navigate({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(weappUrl)}`, // 跳转URL
        type: 'navigateTo',
        web: {
          type: 'safeLink',
          safeLink: {
            url: snapshotUrl,
          },
        },
      });
    },

    handleCopyBtnClick() {
      setClipboardData(this.orderNo).then(() => {
        /* #ifdef web */
        Toast.success('已复制');
        /* #endif */
      });
    },

    toMoneyToWhere() {
      // 钱款去向
      /* #ifdef web */
      Tee.navigate({ url: this.orderBizUrl.moneyToWhereUrl });
      /* #endif */

      /* #ifdef weapp */
      // openWebView(this.data.moneyToWhereUrl, { title: '钱款去向' });
      let weappUrl = this.orderBizUrl.moneyToWhereUrl;
      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      if (weappUrl.startsWith('http://')) {
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        weappUrl = weappUrl.replace('http://', 'https://');
      }
      navigate({
        url: `/pages/common/webview-page/index?src=${weappUrl}&title=钱款去向`,
        type: 'navigateTo',
      });
      /* #endif */
    },

    toOrderQuestion() {
      if (this.isTradeComponent3) {
        Toast('视频号订单，请咨询腾讯客服 400-670-0700');
        return;
      }

      const { orderNo } = this;
      const { wxChannelsOrder } = this.order;
      const fromScene = wxChannelsOrder ? 'wechatChannel' : 'default';
      const commonUrl = url.args.add(this.orderBizUrl.orderQuestionUrl, {
        from_scene: fromScene,
        order: orderNo,
      });
      /* #ifdef web */
      Tee.navigate({ url: commonUrl });
      /* #endif */

      /* #ifdef weapp */
      // openWebView(this.data.orderQuestionUrl);
      navigate({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(commonUrl)}`,
        type: 'navigateTo',
      });
      /* #endif */
    },

    toYzGuaranteeUrl() {
      const { wxChannelsOrder } = this.order;
      const fromScene = wxChannelsOrder ? 'wechatChannel_Youzan' : 'Youzan_danbao';
      this.ctx.logger.log({
        et: 'click',
        ei: 'click_guarantee_cs',
        en: '点击有赞担保专属客服',
        si: this.kdtId,
        params: {
          from_scene: fromScene,
        },
      });

      let targetUrl = url.args.add(this.orderBizUrl.yzGuaranteeUrl, {
        from_biz: 'wsc',
        from_scene: fromScene,
        order: this.orderNo,
      });

      /* #ifdef web */
      Tee.navigate({
        url: targetUrl,
      });
      /* #endif */
      /* #ifdef weapp */
      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      if (targetUrl.startsWith('http://')) {
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        targetUrl = targetUrl.replace('http://', 'https://');
      }
      navigate({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(targetUrl)}`,
        type: 'navigateTo',
      });
      /* #endif */
    },
  },
};
</script>

<style lang="scss">
.time {
  margin-top: 10px;
  font-size: 14px;
  line-height: 22px;
  background: #fff;
  overflow: hidden;
}

.time__item {
  display: flex;
  line-height: 28px;
  padding: 0 16px;
  color: #969799;
}

.time__item:first-child {
  padding-top: 10px;
}

.time__item__text__color {
  color: #323233;
}

.time__item__order-no {
  display: flex;
  align-items: center;
}

.time__item__order-no__text {
  margin-right: 10px;
}

.time__item__copy {
  height: 18px !important;
  line-height: 16px !important;
  min-width: 30px !important;
  justify-content: center !important;
  font-size: 12px;
}

.time__action {
  margin-top: 10px;
  display: flex;
  position: relative;
}

.time__action::after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  transform-origin: center;
  top: -50%;
  bottom: -50%;
  left: 16px;
  right: 16px;
  transform: scaleY(0.5);
  border-top: 1px solid #eee;
}

.time__action .time__action__item {
  flex: 1;
  padding: 14px 0;
  color: var(--link, #576b95);
  text-align: center;
  vertical-align: middle;
  position: relative;
  font-size: 14px;
}

.time__action .time__action__item.money-to-where {
  color: #646566;
  text-align: left;
  padding: 0 15px;
}

.time__action .time__action__item__link {
  color: #1989fa;
  padding: 4px;
}

.time__action__item + .time__action__item::after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  transform-origin: center;
  height: 50%;
  top: 25%;
  left: 0;
  right: -50%;
  bottom: 25%;
  transform: scaleY(0.5);
  border-left: 1px solid #eee;
}

.snapshot {
  display: flex;
  align-items: center;
}

.snapshot-icon {
  display: block !important;
  margin-left: 4px;
}
</style>
