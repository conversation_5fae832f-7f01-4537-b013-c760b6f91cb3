<template>
  <view>
    <van-dialog ref="van-dialog-detail-action" />

    <van-action-sheet
      :show="showActionSheet"
      :actions="allBtns"
      class="order-detail-more-btn-sheet"
      cancel-text="取消"
      @cancel="toggleActionSheet"
      @close="toggleActionSheet"
      @select="handleBtnClick"
    />
    <fail-goods-dialog
      :show="showFailGoodsDialog"
      :goods="failGoodsList"
      @close="toggleFailGoodsDialog(false)"
      @confirm="confirmFailGoodsDialog"
    />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import { mapState, mapActions } from '@ranta/store';
import { ZNB } from '@youzan/tee-biz-navigate';
import VanDialog from '@youzan/vant-tee/dist/dialog/index';
import ActionSheet from '@youzan/vant-tee/dist/action-sheet/index';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';
import { BOTTOM } from './constants';
import FailGoodsDialog from './FailGoodsDialog.vue';

export default {
  components: {
    'van-dialog': VanDialog,
    'van-action-sheet': ActionSheet,
    'fail-goods-dialog': FailGoodsDialog,
  },

  data() {
    return {
      order: {},
      goods: {},
      itemList: [],
      guaranteeOrderInfo: {},
      isIVR: false,
      isIvrOwner: false,
      ivrRedirectUrl: '',
      miniprogram: {},
      ...mapState(this, ['allBtns', 'showActionSheet', 'showFailGoodsDialog', 'failGoodsList']),
    };
  },

  watch: {
    allBtns(value, oldValue) {
      /* #ifdef weapp */
      if (
        this.order.shareCouponStyleVersion === 'v2' &&
        value.length > 0 &&
        oldValue.length === 0
      ) {
        const haveCoupon = value.filter((x) => x.value === BOTTOM.coupon.value);
        if (haveCoupon.length) {
          this.ctx.logger.log({
            et: 'view', // 事件类型
            ei: 'view', // 事件标识
            en: '曝光', // 事件名称
            params: {
              page_view: 1,
              component: 'divide_coupon_floating_window',
            }, // 事件参数
          });
        }
      }
      /* #endif */
    },
  },

  created() {
    mapData(this, [
      'order',
      'itemList',
      'goods',
      'guaranteeOrderInfo',
      'isIVR',
      'isIvrOwner',
      'ivrRedirectUrl',
      'miniprogram',
    ]);
    mapProcess(this, {
      viewEvaluate: () => {
        this.store.handleBtnNavigate(BOTTOM.viewEvaluate.value);
      },
      evaluate: () => {
        this.store.handleBtnNavigate(BOTTOM.evaluate.value);
      },
      showBatchRefund: () => {
        this.store.handleBtnNavigate(BOTTOM.batchRefund.value);
      },
      shareOrder: this.store.openShareSheet,
      extendOrder: this.store.extendOrder,
      cancelOrder: this.store.cancelOrder,
      buyAgain: this.store.buyAgain,
      directBuyAgain: this.store.directBuyAgain,
      handleBtnClick: this.store.handleBtnClick,
    });
    mapActions(this, ['toggleActionSheet', 'confirmFailGoodsDialog']);
  },

  mounted() {
    Tee.setGlobal('van-dialog-detail-action', this.$refs['van-dialog-detail-action']);
    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    }).catch((e) => {
      console.log(e);
    });
    /* #endif */
    // 裂变优惠券的曝光埋点
    const haveCoupon = this.allBtns.filter((x) => x.value === BOTTOM.coupon.value);
    if (haveCoupon.length) {
      if (this.order.shareCouponStyleVersion === 'v1') {
        this.ctx.logger.log({
          et: 'view',
          ei: 'view_coupon',
          en: '底部领取优惠券按钮曝光',
          pt: 'od',
          params: {},
        });
      } else if (this.order.shareCouponStyleVersion === 'v2') {
        /* #ifdef web */
        this.ctx.logger.log({
          et: 'view', // 事件类型
          ei: 'view', // 事件标识
          en: '曝光', // 事件名称
          params: {
            page_view: 1,
            component: 'divide_coupon_floating_window',
          }, // 事件参数
        });
        /* #endif */
      }
    }
    this.logNewBuyAgaginView();
    this.logDeleteOrderView();
  },

  methods: {
    toggleFailGoodsDialog(isShow) {
      this.store.toggleFailGoodsDialog({ isShow });
    },

    // NOTICE: handleBtnClick 必须保留，因为在template中使用，'btn' 入参直接来自 emit 提供的数据
    handleBtnClick(btn) {
      this.store.handleBtnClick(btn);
    },

    logNewBuyAgaginView() {
      const haveBuyAgain = this.allBtns.filter((x) => x.value === BOTTOM.buyAgain.value);
      const haveDirectBuyAgain = this.allBtns.filter(
        (x) => x.value === BOTTOM.directBuyAgain.value
      );
      if (haveBuyAgain.length <= 0 && haveDirectBuyAgain.length <= 0) {
        return;
      }
      const { orderNo } = this.order;
      const goodsIds = [];
      this.goods.forEach((item) => {
        goodsIds.push(item.goodsId);
      });
      const params = {
        goods_id: goodsIds,
        order_no: orderNo,
        is_new: false,
      };
      // 新再来一单
      if (haveDirectBuyAgain.length > 0) {
        params.is_new = true;
      }
      // 旧再来一单
      if (haveBuyAgain.length > 0) {
        params.is_new = false;
      }

      this.ctx.logger.log({
        et: 'view',
        ei: 'new_buy_again_view',
        en: '新再来一单曝光',
        pt: 'od',
        params,
      });
    },

    logDeleteOrderView() {
      const {
        order: { orderNo },
      } = this;
      // 删除订单
      const haveDeleteOrder = this.allBtns.filter((x) => x.value === BOTTOM.deleteOrder.value);
      if (haveDeleteOrder.length) {
        this.ctx.logger.log({
          et: 'view', // 事件类型
          ei: 'order_delete_expo', // 事件标识
          en: '删除订单按钮曝光', // 事件名称
          params: {
            order_no: orderNo,
          },
        });
      }
    },
  },
};
</script>
