import { mapData } from '@youzan/ranta-helper-tee';

import Main from './Main.vue';

export default class Extension {
  constructor(options) {
    this.ctx = options.ctx;

    // this.getEduGrouponLink();
  }

  getEduGrouponLink() {
    console.log('define getEduGrouponLink in js');
    mapData(this, {
      orderNo: (orderNo) => {
        if (orderNo) {
          console.log('data js', this.ctx.data, JSON.stringify(this.ctx.data));
          const res = {
            goods: this.ctx.data.goods,
            orderNo: this.ctx.data.orderNo,
            kdtId: this.ctx.data.kdtId,
            activityType: this.ctx.data.activityType,
          };
          let link = '';
          if (res.goods.length > 0) {
            const goodsItem = res.goods[0] || {};

            if (goodsItem.goodsType == 31) {
              // 知识付费拼团
              const alias = (goodsItem.goodsInfo || {}).alias;
              link = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
                `https://h5.youzan.com/wscvis/ump/groupon/groupon-detail?order_no=${res.orderNo}&alias=${alias}&kdt_id=${res.kdtId}&activity_type=${res.activityType}`
              )}`;
            }
          }
          console.log('define getEduGrouponLink', link);
          this.ctx.data.grouponLink = link;
        }
      },
    });
  }

  static widgets = {
    Main,
  };
}
