# @wsc-tee-trade/detail-trade-share

分享

![UI呈现](https://img.yzcdn.cn/public_files/e0211a6f8dc65aeadd9fc506f72c882f.png) ![分享海报弹窗](https://img.yzcdn.cn/public_files/d76c1e15d67ed7450c7c61954b286f6e.png)

## 调试方式

- 下单

## 存疑

- ifdef 应该可以更加精确
- 是否可以作为一个 Widget 提供？

## Widget.Provide

| 名称                 | 说明 |
| -------------------- | ---- |
| TradeShare `default` | ???  |

## Event.Listen

| 名称                 | 说明 |
| -------------------- | ---- |
| showTradeShareDialog |      |

## Process.Define

| 名称               | 说明 |
| ------------------ | ---- |
| getShareAppMessage | ???  |

## Data.Consume

| 名称              | 类型      | 说明                                                  |
| ----------------- | --------- | ----------------------------------------------------- |
| isAllowShareOrder | _boolean_ | 是否允许分享 `_global.orderInfo.isAllowShareOrder`    |
| isNewHotelGood    | _boolean_ | 是否是新的酒店商品 在 page-setup 中的 format 中有逻辑 |
| orderNo           | _string_  | 订单号                                                |
| offlineId         | ???       | ???                                                   |
| kdtId             | _number_  | 店铺 ID                                               |
| isMultiStore      | _boolean_ | 是否是多门店                                          |
| miniprogram       | ???       | 小程序环境变量                                        |
| goodsList         | ???       | ???                                                   |
| orderBizUrl       | ???       | ???                                                   |
