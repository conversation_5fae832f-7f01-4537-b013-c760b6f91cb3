# @wsc-tee-trade/detail-order-tips

订单状态

![UI呈现](https://img.yzcdn.cn/public_files/b6c54d7e212de05f64bc336a5fe5d7a4.png)

## 调试方式

- 实物商品下单

## 存疑

## widget.Provide

| 名称                  | 说明     |
| --------------------- | -------- |
| OrderStatus `default` | 订单状态 |

## Widget.Consume

| 名称           | 说明     |
| -------------- | -------- |
| WaitingProcess | 等待进度 |

## Data.Consume

| 名称 | 类型 | 说明 |
| --- | --- | --- |
| orderStateInfo | ??? | 订单状态信息，由 `detail-page-setup` format 提供 |
| groupon | ??? | 拼团信息，`_global.orderInfo.groupBuy` 由 Node 侧 `GroupBuyOrderHandler.handler` 提供 |
| orderBizExtra | ??? | 订单业务扩展消息，`_global.orderBizExtra` |
| order | ??? | 订单信息，`_global.orderInfo` |
