<template>
  <van-cell-group custom-class="id-card" :border="false" v-if="showIdCard">
    <van-cell
      label="身份证号"
      title-class="id-card__title"
      label-class="id-card__label"
      value-class="id-card__value"
      :value="idCardNumber"
      :border="false"
    />
  </van-cell-group>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },

  data() {
    return {
      showIdCard: false,
      idCardNumber: '',
    };
  },

  created() {
    mapData(this, ['showIdCard', 'idCardNumber']);
  },
};
</script>

<style lang="scss">
.id-card {
  margin-top: 10px;
}

.id-card__title {
  flex: none !important;
  margin-right: 12px;
}

.id-card__label {
  color: #323233 !important;
}

.id-card__value {
  flex: none !important;
}
</style>
