/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import Tee from '@youzan/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { cdnImage } from '@youzan/tee-biz-util';

// 小程序上暂时支持的支付有礼活动类型
export const SupportedActivity = {
  tradeincard: 'tradeincard',
  promocode: 'promocode',
  couponpackage: 'couponpackage',
  seller: 'seller',
  seniorseller: 'seniorseller',
  wheel: 'wheel',
  feature: 'feature',
  present: 'present',
  activitiesQrCode: 'activitiesQrCode',
};

export const PROMO_TYPE_ACTION = {
  tradeincard({ orderNo, type = 'promocard' }) {
    Tee.navigate({
      url: `/packages/user/coupon/detail/index?orderNo=${orderNo}&type=${type}&from=payresult`,
    });
  },

  promocode(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'promocode',
    });
  },

  couponpackage(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'couponpackage',
    });
  },

  seller() {
    Tee.navigate({
      url: '/packages/salesman/tutorial/index',
    });
  },

  wheel(options) {
    const detailUrl = encodeURIComponent(options.detailUrl);
    Tee.navigate({
      url: `/pages/common/webview-page/index?src=${detailUrl}`,
      success: () => {
        // do nothing
      },
      error: () => {
        Toast('打开失败');
      },
    });
  },

  seniorseller() {
    PROMO_TYPE_ACTION.seller();
  },

  feature({ detailUrl }) {
    const match = detailUrl.match(/feature\/([^?/]+)/);
    Tee.navigate({
      url: `/packages/home/<USER>/index?alias=${match[1]}`,
    });
  },

  present({ detailUrl, present }) {
    const match = detailUrl.match(/goods\/([^?/]+)/);
    Tee.navigate({
      url: `/packages/goods/present/index?alias=${match[1]}&type=present&activityId=${present}`,
    });
  },
};

export const ConcatButtonConfig = {
  msgImg: cdnImage('upload_files/2021/05/07/Fl_JhHoGUlPwN1jMcRux-W5KVhAs.png'), // 客服消息上面的图片
  h5Logo: cdnImage('weapp/wsc/1fxuP9X.png'),
  baseSharePath: '/pages/home/<USER>/index',
};

export const PROMOTION_TYPES = {
  /** 优惠券 */
  Tradeincard: 'tradeincard',
  /** 优惠券礼包 */
  Couponpackage: 'couponpackage',
  /** 优惠码 */
  Promocode: 'promocode',
  /** 赠品 */
  Present: 'present',
  /** 刮刮乐 */
  Guaguale: 'guaguale',
  /** 疯狂猜 */
  Crazyguess: 'crazyguess',
  /** 幸运大抽奖 */
  Wheel: 'wheel',
  /** 生肖翻翻看 */
  Zodiac: 'zodiac',
  /** 投票调查 */
  Survey: 'survey',
  /** 微页面 */
  Feature: 'feature',
  /** 分销员 */
  Seller: 'seller',
  /** 二级分销员 */
  Seniorseller: 'seniorseller',
  /** 活码 */
  ActivitiesQrCode: 'activitiesQrCode',
};

const NEW_PROMOTION_IMG_URL = {
  [PROMOTION_TYPES.Guaguale]: 'upload_files/2023/09/13/Fiy3PmvhNRPJBjG7RHnJVo-sDtgI.png',
  [PROMOTION_TYPES.Crazyguess]: 'upload_files/2023/09/13/Fvg8HA3_EwfAvY89MiPCZtlNMolT.png',
  [PROMOTION_TYPES.Zodiac]: 'upload_files/2023/09/13/FrqSVMf-pjG43ItIXKV5JR-LRGXC.png',
  [PROMOTION_TYPES.Survey]: 'public_files/a3658561fc74be479944d2738723f136.png',
  [PROMOTION_TYPES.Feature]: 'public_files/02f1542513680804e866af586239488c.png',
  [PROMOTION_TYPES.Seller]: 'public_files/e483107500d617e430949f2f55b31603.png',
};

/** 是否是默认图片 */
export const PromotionImgType = {
  Default: 1,
  Custom: 2,
};

export const isDefaultPromotion = (imgType) => {
  return imgType === PromotionImgType.Default;
};

/** 处理新老图片的映射 */
export const getNewPromotionUrl = ({ imgType, type, imageUrl }) => {
  if (imgType === PromotionImgType.Custom) return imageUrl;

  imageUrl = NEW_PROMOTION_IMG_URL[type] || imageUrl;

  return imageUrl;
};

export const COUPON_STATE = {
  FETCH_SUCCESS: 1, // 领取成功
  HAS_FETCHED: 2, // 已经领取过
  NONE: 3, // 抢光了
  ACT_ENDED: 4, // 活动已结束
  LIMIT_EXCEEDED: 5, // 已超过最多领取次数
  ACT_INVALID: 6, // 活动已失效
  LINK_INVALID: 7, // 链接已失效
  COUPON_INVALID: 8, // 优惠券已失效
};

export const COUPON_STATE_FAIL_TEXT = {
  [COUPON_STATE.NONE]: '优惠券已抢光',
  [COUPON_STATE.ACT_ENDED]: '活动已结束', // 活动已结束
  [COUPON_STATE.LIMIT_EXCEEDED]: '已超过最多领取次数', // 已超过最多领取次数
  [COUPON_STATE.ACT_INVALID]: '活动已失效', // 活动已失效
  [COUPON_STATE.LINK_INVALID]: '链接已失效', // 链接已失效
  [COUPON_STATE.COUPON_INVALID]: '优惠券已失效', // 优惠券已失效
};

export const RECEIVE_MAP = {
  RECEIVED: 1,
  UN_RECEIVED: 0,
};

export const ActivityQrCodeTypes = {
  Person: 1,
  Group: 2,
};

export const CashbackType = {
  Money: 1, // 现金
  Store: 2, // 储值金
};
