<template>
  <van-popup
    v-if="status == 'ALL_PAID'"
    v-model="show"
    closeable
    style="height: 100%; width: 100%"
    @close="closeHandler"
  >
    <view>
      <image :src="subscriptionInfo.qrcode" alt class="subscription-dialog__img" />
      <view v-if="subscriptionInfo.text" class="subscription-dialog__des">
        {{ subscriptionInfo.text }}
      </view>
    </view>
  </van-popup>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import get from '@youzan/utils/object/get';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { generateMpAccount, generateWxCode } from './api';
import { cdnImage } from '@youzan/tee-biz-util';

export default {
  components: {
    'van-popup': Popup,
  },

  data() {
    return {
      show: false,
      loading: false,
      subscriptionInfo: {},
      payResult: {},
      kdtId: '',
    };
  },

  created() {
    mapData(this, ['kdtId', 'payResult']);
    mapEvent(this, {
      openSubscription: () => {
        this.init();
      },
    });
  },
  methods: {
    status() {
      return get(this.payResult, 'payResultVO.payState');
    },
    closeHandler() {
      this.show = false;
    },
    init() {
      this.ctx.process.invoke('logger', 'view', 'show_fuchuang_dingyuewuliu', '“订阅物流”浮窗曝光');
      if (Object.keys(this.subscriptionInfo).length) {
        this.show = true;
        return;
      }
      const showPayResult = get(this.payResult, 'showPayResult');
      const mpData = get(showPayResult, 'mpData', {});
      if (mpData.mpId) {
        // 判断店铺是否绑定了公众号
        return generateMpAccount(
          this.kdtId,
          mpData.fans ? `访问店铺公众号，查看订单物流` : '扫码关注店铺公众号，查看订单物流'
        ).then((result) => {
          this.subscriptionInfo = result;
          this.show = true;
        });
      }
      if (mpData.followWeChatMp) {
        // 判断店铺是否绑定了小程序
        return generateWxCode(this.kdtId).then((result) => {
          this.subscriptionInfo = result;
          this.show = true;
        });
      }
      if (mpData.followYouZanMp) {
        // 判断用户是否关注了有赞公众号
        return generateMpAccount(
          371189,
          `长按访问有赞公众号，查看订单物流`,
          cdnImage('public_files/860b6e9dfd967e8b9a9fa88c2a4662f9.png')
        ).then((result) => {
          this.subscriptionInfo = result;
          this.show = true;
        });
      }
      return generateMpAccount(
        371189,
        `长按关注有赞公众号，查看订单物流`,
        cdnImage('public_files/860b6e9dfd967e8b9a9fa88c2a4662f9.png')
      ).then((result) => {
        this.subscriptionInfo = result;
        this.show = true;
      });
    },
  },
};
</script>

<style lang="scss">
$border-color: #e5e5e5;
$qrnum-border-color: #dcdee0;
$qrnum-text-color: #7d7e80;
$text-num-color: #333;

.subscription-dialog {
  text-align: center;

  &__img {
    width: 180px;
    height: 180px;
    user-select: none;
    display: block;
    border: 1px solid #dcdee0;
    margin: 56px auto 0;
  }

  &__des {
    text-align: center;
    color: #333;
    font-size: 14px;
    margin-bottom: 20px;
    margin-top: 16px;
    line-height: 20px;
    padding: 0 24px;
    user-select: none;
  }
}

.t-overlay {
  background-color: transparent !important;
}
</style>
