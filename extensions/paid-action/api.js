import { requestV2 } from '@youzan/tee-biz-request';

export const getPopConfig = function ({ keys, popPosition }) {
  return requestV2({
    path: '/wscshop/shopad/pop-config-with-keys.json',
    data: {
      popPosition,
      queryJson: JSON.stringify({ keys }),
    },
  });
};

export const getIsCrmShop = () =>
  requestV2({
    path: '/wscuser/scrm/api/isCrmShop.json',
    method: 'GET',
  });

/** 查询订单信息，辅助判断评价逻辑 */
export const queryOrderInfoByNo = (orderNo) => {
  return requestV2({
    path: '/wsctrade/order/getOrderInfoByTrade.json',
    method: 'GET',
    data: {
      order_no: orderNo,
    },
  });
};

export function fetchYouzanFollowWhiteList() {
  return requestV2({
    origin: 'cashier',
    withCredentials: true,
    path: '/wsctrade/apollo-gray.json',
    method: 'GET',
    data: {
      key: 'showYouzanFollow',
    },
  });
}
