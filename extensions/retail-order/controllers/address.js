import { batchCheckAddressAvailable } from '../api';
import { EXPRESS_VALUE } from '../contants';

// 批量校验地址是否超出配送范围
function onBatchCheckAddressAvailable(warehouseId, addressList = []) {
  const availableAddressMap = {};
  // 针对历史数据没有lon/lat的场景过滤
  const pendingAddressList = addressList.reduce((addressList, item) => {
    const {
      id: addressId,
      lat,
      lon,
      province = '',
      city = '',
      county = '',
      addressDetail = '',
    } = item;
    if (lat && lon) {
      addressList.push({
        addressId,
        lat,
        lon,
        addressStr: `${province}${city}${county}${addressDetail}`,
      });
    } else {
      availableAddressMap[addressId] = false;
    }
    return addressList;
  }, []);
  return batchCheckAddressAvailable(warehouseId, pendingAddressList).then((availableAddressList) => {
    return availableAddressList.reduce((map, { addressId, isSupport = false }) => {
      map[addressId] = isSupport;
      return map;
    }, availableAddressMap);
  });
}

// 格式化零售地址，如果是同城配送就格式化为校验地址是否超出配送范围，如果是快递就格式化为不校验
export async function formatRetailAddress({ currentExpressType, warehouseId, address = {} }) {
  const addressList = address.list || [];
  // 同城配送校验地址范围
  if (currentExpressType === EXPRESS_VALUE.CITY_EXPRESS && warehouseId) {
    const availableAddressMap = await onBatchCheckAddressAvailable(warehouseId, addressList);
    const { activeList, inactiveList } = addressList.reduce(
      (data, item) => {
        availableAddressMap[item.id] ? data.activeList.push(item) : data.inactiveList.push(item);
        return data;
      },
      {
        activeList: [],
        inactiveList: [],
      }
    );
    address.activeList = activeList;
    address.inactiveList = inactiveList;
    return address;
  }

  // 如果是快递，需要判断数据是否是超出配送范围处理过的，如果是处理过的数据，需要进行还原
  if (currentExpressType === EXPRESS_VALUE.EXPRESS && address.activeList) {
    address.activeList = [];
    address.inactiveList = [];
  }
  return address;
}

export function initHooks(ctx) {
  ctx.process.define('callHookBeforeUpdateAddress', (address) => {
    const { warehouseId, isRetailOrderScene, postage } = ctx.data;
    // 地址更新时，重新计算地址是否超出配送范围
    if (isRetailOrderScene && postage) {
      return formatRetailAddress({
        currentExpressType: postage.currentExpressType,
        warehouseId,
        address,
      });
    }
    return address;
  });
}

export function watchPrepareData(ctx, data) {
  const { tradeConfirmation: { extra: { HOUSE_NUMBER_REQUIRED } = {} } = {} } = data;
  ctx.data.houseNumberRequired = !!+HOUSE_NUMBER_REQUIRED;
}
