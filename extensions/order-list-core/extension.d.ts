import type { Goods, ToggleDialogParams } from '@youzan-cloud/cloud-biz-types';

export type CamelCase<T> = {
  [K in keyof T as Uncapitalize<K & string>]: T[K];
};

/**
 * query中的初始tab类型
 * 'all'        = 全部
 * 'topay'      = 待付款
 * 'tosend'     = 待发货
 * 'send'       = 待收货
 * 'toevaluate' = 待评价
 * 'going'      = 已付款(仅在教育店铺中)
 */
enum QueryTypeEnum {
  ALL = 'all',
  topay = 'topay',
  tosend = 'tosend',
  send = 'send',
  toevaluate = 'toevaluate',
  going = 'going',
}
interface WebQuery {
  type?: QueryTypeEnum;
  pagetype?: 'single' | 'all';
  keyword?: string;
  // eslint-disable-next-line camelcase
  kdt_id?: string;
  // eslint-disable-next-line camelcase
  sub_kdt_id?: string;
}

interface WeappQuery {
  type?: QueryTypeEnum;
  pagetype?: 'single' | 'all';
  scene?: string;
  refresh?: boolean;
  isDrug?: boolean;
  keyword?: string;
  // eslint-disable-next-line camelcase
  kdt_id?: string;
  navigateType?: 'navigateTo' | 'redirectTo';
}

export type Query = WebQuery | WeappQuery;

export interface ProcessOrder {
  orderNo: string;
  listIndex: number;
}

/**
 * 订单操作类型
 * 'cancel' = 取消订单
 * 'topay' = 立即付款
 * 'transport' = 查看物流
 * 'topayDeposit' = 支付定金
 * 'topayRetainage' = 支付尾款
 * 'confirmReceive' = 确认收货
 * 'confirmReceiveHotel' = 确认入住
 * 'laterReceive' = 延长收货
 * 'buyAgain' = 再来一单
 * 'evaluate' = 立即评价
 * 'lotteryResult' = 抽奖结果
 * 'inviteHelp' = 邀请助力
 * 'deleteOrder' = 删除订单
 * 'expressReminderHotel' = 提醒接单
 * 'expressReminder' = 提醒发货
 * 'paidPromotion' = 支付有礼
 * 'coupon' = 领优惠券
 * 'fissionCoupon' = 抢优惠券
 * 'grouponDetail' = 拼团详情(仅小程序支持)
 * 'viewCardDetail' = 查看卡券(仅小程序支持)
 * 'selfFetchCode' = 核销码(仅小程序支持)
 * 'viewEvaluate' = 查看评价(仅H5支持)
 * 'gift' = 查看礼单(仅H5支持)
 * 'groupDetail' = 查看拼团(仅H5支持)
 * 'courseDetail' = 查看课程(仅H5支持)
 * 'hotelComboUse' = 立即预约(仅H5支持)
 * 'shared' = 我要晒单(仅H5支持)
 * 'peerpay' = 代付(仅H5支持)
 */
enum OrderActionEnum {
  CANCEL = 'cancel',
  TOPAY = 'topay',
  TRANSPORT = 'transport',
  TOPAY_DEPOSIT = 'topayDeposit',
  TOPAY_RETAINAGE = 'topayRetainage',
  CONFIRM_RECEIVE = 'confirmReceive',
  CONFIRM_RECEIVE_HOTEL = 'confirmReceiveHotel',
  LATER_RECEIVE = 'laterReceive',
  BUY_AGAIN = 'buyAgain',
  EVALUATE = 'evaluate',
  LOTTERY_RESULT = 'lotteryResult',
  INVITE_HELP = 'inviteHelp',
  DELETE_ORDER = 'deleteOrder',
  EXPRESS_REMINDER_HOTEL = 'expressReminderHotel',
  EXPRESS_REMINDER = 'expressReminder',
  PAID_PROMOTION = 'paidPromotion',
  COUPON = 'coupon',
  FISSION_COUPON = 'fissionCoupon',
  GROUPON_DETAIL = 'grouponDetail',
  VIEW_CARD_DETAIL = 'viewCardDetail',
  SELF_FETCH_CODE = 'selfFetchCode',
  VIEW_EVALUATE = 'viewEvaluate',
  GIFT = 'gift',
  GROUP_DETAIL = 'groupDetail',
  COURSE_DETAIL = 'courseDetail',
  HOTEL_COMBO_USE = 'hotelComboUse',
  PEERPAY = 'peerpay',
}

interface BeforeCancelOrderPopupToggleReturnConfigAutoAddCart {
  /* 初始勾选状态，默认勾选 */
  isChecked: boolean;
  /* 是否禁用，默认允许 */
  isDisabled: boolean;
  /* 是否展示，默认展示 */
  isShow: boolean;
}
export interface BeforeCancelOrderPopupToggleReturnConfig {
  /* 自动加入购物车Cell配置项 */
  autoAddCart: BeforeCancelOrderPopupToggleReturnConfigAutoAddCart;
}
interface BeforeCancelOrderPopupToggleReturn {
  /* 取消订单弹窗配置项 */
  config: BeforeCancelOrderPopupToggleReturnConfig;
  /* 订单号 */
  orderNo: string;
}
export interface BeforeCancelOrderPopupToggle {
  (payload: ToggleDialogParams): Promise<BeforeCancelOrderPopupToggleReturn>;
}
interface OrderAction {
  /* 按钮名称 */
  name: string;
  /* 按钮类型，自定义按钮类型传入 ('cloud_' + <name>) */
  type: OrderActionEnum;
}
interface BeforeOrderBtnsRenderPayload {
  /* 将会展示的订单按钮 */
  btns: OrderAction[];
  /* 订单号 */
  orderNo: string;
}
export interface BeforeOrderBtnsRenderReturn {
  /* 处理后需要展示的按钮列表 */
  btns: OrderAction[];
}
export interface BeforeOrderBtnsRender {
  (payload: BeforeOrderBtnsRenderPayload): Promise<BeforeOrderBtnsRenderReturn>;
}

// -----------------------------------------------------------------------------
/*
 * 订单列表标签页类型
 * 'all' = 所有订单
 * 'waitPay' = 待付款的订单
 * 'waitShip' = 待发货的订单
 * 'shipped' = 已发货的订单
 * 'waitEvaluate' = 待评价的订单
 */
export enum OrderListTabTypeEnum {
  /** 所有订单 */
  ALL = 'all',
  /** 待付款的订单 */
  WAIT_PAY = 'waitPay',
  /** 待发货的订单 */
  WAIT_SHIP = 'waitShip',
  /** 已发货的订单 */
  SHIPPED = 'shipped',
  /** 待评价的订单 */
  WAIT_EVALUATE = 'waitEvaluate',
}

/**
 * 订单列表标签页
 */
export interface OrderListTab {
  /* 标签页类型 */
  type: OrderListTabTypeEnum;
  /* 标签页名称 */
  text: string;
  /* 是否为当前选中的标签页，默认false */
  isSelected: boolean;
}
/**
 * 订单列表标签页开放数据
 */
export interface OrderListTabs {
  /* 订单列表标签页列表 */
  tabs: OrderListTab[];
}

export type CloudExtensionInfo = Record<string, any>;
/**
 * 'all' = 全部订单
 * 'book' = 待确认
 * 'topay' = 待付款
 * 'totuan' = 待接单
 * 'paid' = 已付款
 * 'tosend' = 待发货
 * 'send' = 已发货
 * 'sign' = 已签收
 * 'success' = 交易完成
 * 'pay_success' = 付款成功
 * 'cancel' = 已取消
 * 'closed' = 已关闭
 * 'refund' = 已退款
 * 'refunding' = 退款中
 */
enum OrderStatusEnum {
  ALL = 'all',
  BOOK = 'book',
  TOPAY = 'topay',
  TOTUAN = 'totuan',
  PAID = 'paid',
  TOSEND = 'tosend',
  SEND = 'send',
  SIGN = 'sign',
  SUCCESS = 'success',
  PAY_SUCCESS = 'pay_success',
  CANCEL = 'cancel',
  CLOSED = 'closed',
  REFUND = 'refund',
  REFUNDING = 'refunding',
}
export interface OrderListItemGoods extends Goods {
  /* 商品数量 */
  num: number;
  /* 格式化后的sku信息 */
  skuText: string;
}
interface OrderListItemShopInfo {
  /* 订单所在的店铺名称 */
  shopName: string;
  /* 订单所在的店铺ID */
  kdtId: number;
}
interface OrderListItemOrderInfoPayInfo {
  /* 付款描述文案 */
  amountDesc: string;
  /* 付款金额 */
  payAmount: number;
  /* 付款描述后缀 */
  lastDesc: string;
}
interface OrderListItemOrderInfo {
  /* 订单号 */
  orderNo: string;
  /* 订单状态 */
  status: OrderStatusEnum;
  /* 订单状态文案 */
  statusText: string;
  /* 取货码(仅限零售订单，其他返回空字符串) */
  pickUpCode: string;
  /* 支付信息 */
  payInfo: OrderListItemOrderInfoPayInfo;
}
interface OrderListItemBottomBtn {
  name: string;
  type: OrderActionEnum;
}
interface OrderListOrderInfo {
  shopInfo: OrderListItemShopInfo;
  orderInfo: OrderListItemOrderInfo;
  goodsList: OrderListItemGoods[];
  bottomBtns: OrderListItemBottomBtn[];
}
export interface OrderListInfo {
  /* 订单列表 */
  orderList: OrderListOrderInfo[];
  /* 是否还有下一页 */
  hasNext: boolean;
  /* 是否加载中 */
  isLoading: boolean;
}
export interface HandleOrderActionParams {
  /* 订单操作类型 */
  type: OrderActionEnum;
  /* 需要操作的订单号 */
  orderNo: string;
}
export interface HandleOrderAction {
  (params: HandleOrderActionParams): void;
}

export interface SwitchOrderListTabParams {
  /* 需要切换的标签页类型 */
  type: OrderListTabTypeEnum;
}

export interface SwitchOrderListTab {
  (params: SwitchOrderListTabParams): void;
}
export interface RefreshOrderInfoParams {
  /* 需要刷新的订单号 */
  orderNo: string;
}

export interface RefreshOrderInfo {
  (params: RefreshOrderInfoParams): void;
}
/**
 * 订单操作按钮类型
 * 'topay' = '付款'
 * 'transport' = '查看物流'
 * 'review' = '评价'
 * 'reviewed' = '查看评价'
 * 'peerpay' = '代付'
 * 'gift' = '礼单'
 * 'cancel' = '取消'
 * 'express-reminder' = '提醒发货'
 * 'extend-receive' = '延长收货'
 * 'confirm-receive' = '确认收货'
 * 'confirm-receive-hotel' = '确认入住'
 * 'cancel-pay-on-delivery' = '取消'
 * 'buy-again' = '再来一单'
 * 'direct-buy-again' = '再来一单'
 * 'topay-presale-downpayment' = '支付定金'
 * 'topay-presale-finalpayment' = '支付尾款'
 * 'before-presale-finalpayment' = '支付尾款'
 * 'invite-help' = '邀请助力'
 * 'lottery-result' = '抽奖结果'
 * 'group-detail' = '拼团详情'
 * 'course-detail' = '查看课程'
 * 'evaluate' = '评价'
 * 'view-card-detail' = '查看卡券
 */
enum BtnTypeEnum {
  TOPAY = 'topay',
  TRANSPORT = 'transport',
  REVIEW = 'review',
  REVIEWED = 'reviewed',
  PEERPAY = 'peerpay',
  GIFT = 'gift',
  CANCEL = 'cancel',
  EXPRESS_REMINDER = 'express-reminder',
  EXTEND_RECEIVE = 'extend-receive',
  CONFIRM_RECEIVE = 'confirm-receive',
  CONFIRM_RECEIVE_HOTEL = 'confirm-receive-hotel',
  CANCEL_PAY_ON_DELIVERY = 'cancel-pay-on-delivery',
  BUY_AGAIN = 'buy-again',
  DIRECT_BUY_AGAIN = 'direct-buy-again',
  TOPAY_PRESALE_DOWNPAYMENT = 'topay-presale-downpayment',
  TOPAY_PRESALE_FINALPAYMENT = 'topay-presale-finalpayment',
  BEFORE_PRESALE_FINALPAYMENT = 'before-presale-finalpayment',
  INVITE_HELP = 'invite-help',
  LOTTERY_RESULT = 'lottery-result',
  GROUP_DETAIL = 'group-detail',
  COURSE_DETAIL = 'course-detail',
  EVALUATE = 'evaluate',
  VIEW_CARD_DETAIL = 'view-card-detail',
}

export interface BtnClickHandleParams {
  /* 操作类型 */
  type: BtnTypeEnum;
  /* 需要操作的订单下标 */
  listIndex: number;
}

export interface BtnClickHandleParamsWeapp {
  /* 按钮展示文案 */
  text: string;
  /* 按钮标识，说明调用哪个按钮能力 */
  value: BtnTypeEnum;
  /* 按钮样式类型，默认default */
  type: string;
  /* 当前 tab 的 id */
  listType: string;
  /* 列表的下标 */
  listIndex: number;
}

/** 订单操作类型 */
export enum OrderListOrderActionEnum {
  CANCEL = 'cancel',
  BUY_AGAIN = 'buyAgain',
  DELETE_ORDER = 'deleteOrder',
  COUPON = 'coupon',
  EVALUATE = 'evaluate',
  VIEW_EVALUATE = 'viewEvaluate',
  COURSE_DETAIL = 'courseDetail',
  CONFIRM = 'confirm',
  CONFIRM_HOTEL = 'confirmHotel',
  EXTEND = 'extend',
  SHARE = 'share',
  TOPAY = 'topay',
  TRANSPORT = 'transport',
  TOPAY_DEPOSIT = 'topayDeposit',
  TOPAY_RETAINAGE = 'topayRetainage',
  LOTTERY_RESULT = 'lotteryResult',
  INVITE_HELP = 'inviteHelp',
  EXPRESS_REMINDER_HOTEL = 'expressReminderHotel',
  EXPRESS_REMINDER = 'expressReminder',
  PAID_PROMOTION = 'paidPromotion',
  FISSION_COUPON = 'fissionCoupon',
  GROUPON_DETAIL = 'grouponDetail',
  VIEW_CARD_DETAIL = 'viewCardDetail',
  SELF_FETCH_CODE = 'selfFetchCode',
  GIFT = 'gift',
  GROUP_DETAIL = 'groupDetail',
  HOTEL_COMBO_USE = 'hotelComboUse',
  PEERPAY = 'peerpay',
}
/** “调用订单级操作”的方法参数 */

interface BeforeOrderBtnClickAsyncPayload {
  /* 当前操作的按钮类型 */
  type: BtnTypeEnum;
  /* 按钮文件文案 */
  value: string;
  /* 订单号 */
  orderNo: string;
}
export interface BeforeOrderBtnClickAsync {
  (payload: BeforeOrderBtnClickAsyncPayload): Promise<void>;
}

export type CloudBtnListCondition = {
  condition: { filterOrderStatus: number[]; activityTypes: number[] };
  cloudBtnList: Array<{ key: string; value: string }>;
};

export interface SetGlobalBtnsDisplayParams {
  /* 确认入住，入参为false时隐藏（暂不支持隐藏后动态显示） */
  confirmReceiveHotel: boolean;
  type: string[];
}

export type OrderListTab = {
  type: string;
  text: string;
  // isSelected: boolean
};
export type OrderListTabOpt = {
  isShow: boolean;
  tabs: OrderListTab[];
};
export type SearchBarOpt = {
  isShow: boolean;
};
type BeforeSetupPayload = {
  orderListTabOpt: OrderListTabOpt;
};
type BeforeSetupReturn = BeforeSetupPayload;
interface BeforeSetup {
  (payload: BeforeSetupPayload): Promise<BeforeSetupReturn>;
}

export default interface ExtensionMetadata {
  cloud: {
    invoke: {
      beforeOrderBtnsRender: () => Promise<void>;
    };
  };
  process: {
    invoke: {
      /**
       * 订单列表初始化前调用，用于外部ext重新设置一些参数
       * 如果其他ext绑定了beforeSetup，无论是否处理，请返回payload
       *
       * @param {BeforeSetupPayload} payload - 订单列表初始化前的参数
       *
       * @example
       * // 示例1: 基本使用
       * // 返回: 无论是否处理，请返回payload
       * this.ctx.process.define(
       *   'beforeSetupRetail',
       *   (payload) => {
       *     // process payload...
       *     return payload;
       * });
       */
      beforeSetup: BeforeSetup;
    };
  };
}
