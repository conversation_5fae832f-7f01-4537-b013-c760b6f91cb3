<template>
  <view class="msg" ref="paid-msg">
    <view style="min-height: 76px">
      <image v-if="logo" :src="logo" alt="店铺" class="msg__img" />
    </view>
    <!-- 支付金额 -->
    <view class="msg__success-text">
      <text class="msg__success-tips">{{ shopName }}</text>
    </view>
    <view class="msg__price">
      <text>{{ statusText }}</text>
      <text>¥</text>
      <text class="msg__price-pay">{{ realPayAmount }}</text>
    </view>
  </view>
</template>

<script>
import { cdnImage } from '@youzan/tee-biz-util';
import moneyFormat from '@youzan/utils/money/format';
import get from '@youzan/utils/object/get';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      payResult: {},
    };
  },

  computed: {
    payResultVO() {
      return this.payResult.payResultVO || {};
    },

    realPayAmount() {
      return moneyFormat(this.payResultVO.realPayAmount || 0, true, false);
    },

    shopName() {
      return this.payResultVO.shopName;
    },

    logo() {
      return cdnImage(this.payResultVO.logo, '!180x180.jpg');
    },

    statusText() {
      const status = get(this.payResultVO, 'payState');
      return status !== 'ALL_PAID' ? '支付中' : '支付成功';
    },
  },

  created() {
    mapData(this, ['payResult']);
  },

  mounted() {
    this.$nextTick(() => {
      const height = (this.$refs['paid-msg'] && this.$refs['paid-msg'].offsetHeight) || 0;
      this.ctx.data.paidMsgHeight = height;
    });
  },
};
</script>

<style lang="scss">
$text-color: #323233;

.msg {
  position: relative;
  padding: 16px 0 12px;
  background: #fff;
  text-align: center;

  &__success-text {
    color: $text-color;
    margin-bottom: 8px;
    margin-top: 8px;
  }

  &__img {
    height: 56px;
    width: 56px;
    border-radius: 100%;
    margin-top: 20px;
  }

  &__success-icon {
    display: block;
    font-size: 32px;
    margin-bottom: 8px;
    font-weight: 500;
  }

  &__success-tips {
    font-size: 16px;
    font-weight: 500;
  }

  &__price {
    font-size: 14px;
    color: #323233;

    &-pay {
      font-weight: var(--theme-common-price-font-weight, 0);
      font-family: Avenir, sans-serif;
    }
  }

  &__tips {
    margin-top: 5px;
  }

  &__pay-detail {
    color: #909090 !important;
    padding: 15px 0 10px;
    font-size: 12px;

    a {
      color: #38f;
    }
  }
}

.msg.simple {
  padding: 64px 0 48px;
}

.msg.simple .msg__price > text {
  font-size: 40px;
}
</style>
