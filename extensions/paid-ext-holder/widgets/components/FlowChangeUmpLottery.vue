<template>
  <view class="fc-ump_lottery">
    <image v-if="lotteryImg" class="fc-ump_lottery-main" :src="lotteryImg" @click="handleOpen" />
    <van-popup custom-class="fc-ump_lottery-popup" :close-on-click-overlay="true" :show="visible">
      <view class="fc-icon-close" @click="handleClose" />
      <view v-if="secKillAdContent" class="fc-ump_lottery-popup-container-goods">
        <view class="sub-title"
          >获得 [<text class="text">{{ secKillAdContent.cooperateKdtName }}</text
          >] 的低价商品</view
        >
        <view class="goods-container" @click="handleClick">
          <image class="image" :src="secKillAdContent.picture" />
          <view class="info">
            <view class="title">{{ secKillAdContent.goodsTitle }}</view>
            <view class="price fc-flex-baseline"
              >秒杀价 ¥
              <!-- #ifdef weapp -->
              <native-text class="price-weight fc-flex-baseline"
                ><native-text class="price-weight-big">{{ secKillAdContent.bigPrice }}</native-text
                >.{{ secKillAdContent.centPrice }}</native-text
              >
              <!-- #else -->
              <text class="price-weight"
                ><text class="price-weight-big">{{ secKillAdContent.bigPrice }}</text
                >.{{ secKillAdContent.centPrice }}</text
              >
              <!-- #endif -->
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="couponAdContent" class="fc-ump_lottery-popup-container-coupon">
        <view class="sub-title"
          >获得 [<text class="text">{{ couponAdContent.cooperateKdtName }}</text
          >] 的优惠券</view
        >
        <view class="coupon-container">
          <view class="coupon" @click="handleClick">
            <!-- #ifdef weapp -->
            <native-text class="quota fc-flex-baseline" :style="couponFontSize">
              <native-text class="unit" v-if="couponAdContent.isShowMax">最高</native-text
              >{{ couponAdContent.couponQuota
              }}<native-text class="unit" v-if="couponAdContent.isShowUnit">元</native-text
              ><native-text class="unit" v-if="couponAdContent.isShowDiscount">折</native-text>
            </native-text>
            <!-- #else -->
            <view class="quota" :style="couponFontSize">
              <text class="unit" v-if="couponAdContent.isShowMax">最高</text
              >{{ couponAdContent.couponQuota
              }}<text class="unit" v-if="couponAdContent.isShowUnit">元</text
              ><text class="unit" v-if="couponAdContent.isShowDiscount">折</text>
            </view>
            <!-- #endif -->
            <view class="coupon-message">
              <text class="coupon-message-money">{{
                couponAdContent.preferentialCopyWriting
              }}</text>
              <text class="coupon-message-date">距到期仅剩 3 天</text>
            </view>
          </view>
          <view class="divider">
            <view class="divider-line" />
            <text class="divider-text">购买以下商品可用</text>
            <view class="divider-line" />
          </view>
          <view class="goods-list" @click="handleClick">
            <view v-for="item in availableGoods" :key="item.alias" class="goods">
              <image class="goods-image" :src="item.logo" />
              <view class="goods-price">
                <view class="container fc-flex-baseline"
                  >¥
                  <!-- #ifdef weapp -->
                  <native-text class="price-weight fc-flex-baseline"
                    ><native-text class="price-weight-big">{{ item.bigPrice }}</native-text
                    >.{{ item.centPrice }}</native-text
                  >
                  <!-- #else -->
                  <text class="price-weight"
                    ><text class="price-weight-big">{{ item.bigPrice }}</text
                    >.{{ item.centPrice }}</text
                  >
                  <!-- #endif -->
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <van-button custom-class="fc-button" round size="small" @click="handleClick"> </van-button>
    </van-popup>
  </view>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index';
import Button from '@youzan/vant-tee/dist/button/index';

import {
  adType,
  couponType,
  lotteryResultType,
  getCouponValueFromDialogFontSize,
} from '../../utils/flow-change';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'flow-change-ump-lottery',

  components: {
    'van-button': Button,
    'van-popup': Popup,
  },

  props: {
    originData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      visible: false,
      couponType,
      couponKey: lotteryResultType.default,
    };
  },

  computed: {
    resourceResponse() {
      return this.originData.resourceResponse || {};
    },
    couponAdContent() {
      if (this.originData.couponAdContentList?.length > 0) {
        return this.originData.couponAdContentList[0];
      }
      return null;
    },
    secKillAdContent() {
      if (this.originData.secKillAdContentList?.length > 0) {
        const item = this.originData.secKillAdContentList[0];
        return {
          ...item,
          picture: fullfillImage(item.picture, 'small'),
        };
      }
      return null;
    },
    availableGoods() {
      return this.couponAdContent?.availableGoods || [];
    },
    lotteryImg() {
      const { scheduleDetails = [] } = this.resourceResponse;
      if (scheduleDetails.length > 0) {
        const { scheduleValues = [] } = scheduleDetails[0];
        if (scheduleValues.length > 0) {
          const { picUrl } = scheduleValues[0];
          return picUrl;
        }
      }
      return '';
    },
    couponFontSize() {
      const {
        couponType: type,
        couponValueFontSize = '',
        couponQuota,
      } = this.couponAdContent || {};
      let fontSize = couponValueFontSize;
      if (type === couponType.random) {
        let { length } = couponQuota;
        if (couponQuota.includes('.')) {
          length -= 1;
        }
        fontSize = getCouponValueFromDialogFontSize(length);
        return `font-size: ${fontSize}px`;
      }
      return fontSize;
    },
  },

  created() {
    this.logger('view');
    let key = lotteryResultType.default;
    if (this.couponAdContent) {
      key = lotteryResultType.coupon;
    }
    if (this.secKillAdContent) {
      key = lotteryResultType.seckill;
    }
    this.couponKey = key;
  },

  methods: {
    handleClose() {
      this.visible = false;
    },
    handleOpen() {
      this.visible = true;
      // 抽奖图片点击埋点
      this.logger('click');
      // 抽奖结果弹窗曝光埋点
      this.logger('view', this.couponKey);
    },
    handleClick() {
      // 抽奖结果弹窗点击埋点
      this.logger('click', this.couponKey, true);
    },
    logger(eventType, type = adType.lottery, isNavigator = false) {
      const data = this.couponAdContent || this.secKillAdContent;
      if (data) {
        this.$emit('onLogger', {
          data,
          type,
          eventType,
          isNavigator,
        });
      }
    },
  },
};
</script>

<style lang="scss">
.fc-ump_lottery-popup {
  padding: 48px 0;
  width: 310px;
  max-width: 310px;
  min-height: 370px;
  background-color: transparent !important;
}
.fc-flex-baseline {
  display: flex;
  align-items: baseline;
}

.fc-ump {
  &_lottery {
    /* #ifdef web */
    width: 100%; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    height: 100%; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    /* #endif */
    /* #ifdef weapp */
    width: 700rpx; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    height: 400rpx; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    /* #endif */
    &-main {
      width: 100%;
      height: 100%;
    }
    &-popup-container {
      @mixin container {
        width: 100%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        border-radius: 17px;
        text-align: center;
        padding: 0 12px 12px 12px;
        box-sizing: border-box;
        .sub-title {
          display: flex;
          margin: 58px auto 12px;
          font-size: 16px;
          color: white;
          .text {
            font-weight: bold;
          }
        }
      }
      &-goods {
        @include container();

        min-height: 210px;
        background-image: url(https://b.yzcdn.cn/flow-change-ump/lottery-popup-bg-2.x.x.png);
        .goods-container {
          background: white;
          border-radius: 8px;
          padding: 12px;
          box-sizing: border-box;
          display: flex;
          .image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            margin-right: 8px;
          }
          .info {
            text-align: left;
            .title {
              font-size: 14px;
              width: 174px;
              height: 38px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .price {
              font-size: 14px;
              margin-top: 16px;
              color: #f44;
              &-weight {
                font-weight: bold;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 105px;
                &-big {
                  font-size: 20px;
                }
              }
            }
          }
        }
      }
      &-coupon {
        @include container();

        min-height: 366px;
        background-image: url(https://b.yzcdn.cn/flow-change-ump/lottery-popup-bg-3.x.x.png);
        .coupon-container {
          background: white;
          border-radius: 8px;
          padding: 12px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          .coupon {
            width: 262px;
            height: 80px;
            border-radius: 5px;
            background: rgba(255, 68, 68, 0.1);
            border: 1px rgba(238, 10, 36, 0.1) solid;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding-left: 16px;
            padding-right: 23px;
            .quota {
              width: 86px;
              font-size: 30px;
              font-weight: bold;
              color: #f44;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              .unit {
                font-size: 12px;
                font-weight: normal;
              }
            }
            &-message {
              display: flex;
              flex-direction: column;
              text-align: left;
              margin-left: 25px;
              &-money {
                font-size: 14px;
              }
              &-date {
                font-size: 12px;
                color: #9b9b9b;
                margin-top: 10px;
              }
            }
          }
          .divider {
            display: flex;
            align-items: center;
            margin: 12px 0;
            &-line {
              width: 73px;
              height: 0;
              display: flex;
              border-color: #ebedf0;
              border-style: solid;
              border-width: 0;
            }
            &-line::before {
              content: '';
              display: block;
              -webkit-box-flex: 1;
              -webkit-flex: 1;
              flex: 1;
              box-sizing: border-box;
              height: 1px;
              border-color: inherit;
              border-style: inherit;
              border-width: 1px 0 0;
            }
            &-text {
              font-size: 12px;
              color: #969799;
              margin: 0 8px;
            }
          }
          .goods-list {
            display: flex;
            justify-content: space-between;
            .goods {
              width: 82px;
              height: 118px;
              display: flex;
              flex-direction: column;
              &-image {
                width: 82px;
                height: 82px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
              }
              &-price {
                width: 82px;
                height: 36px;
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #f44;
                background: #f7f8fa;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
                .container {
                  justify-content: center;
                  width: inherit;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  .price-weight {
                    font-weight: bold;
                    margin-left: 3px;
                    &-big {
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .fc-icon-close {
      position: absolute;
      right: 16px;
      top: 0;
      width: 32px;
      height: 32px;
      background-image: url(https://img.yzcdn.cn/public_files/2018/05/16/c29879b23772965a3af7f5689ee61f58.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .fc-button {
      display: block;
      height: 40px;
      width: 240px;
      margin: 16px auto 0;
      padding: 0 12px;
      border: none;
      background-image: url(https://img01.yzcdn.cn/upload_files/2021/08/06/FutKN9BG4KGgwI1TVAyYTYFi7NTF.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-color: transparent;
      animation: breath 800ms linear infinite;
    }

    @keyframes breath {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(0.95);
      }
      100% {
        transform: scale(1);
      }
    }
  }
}
</style>
