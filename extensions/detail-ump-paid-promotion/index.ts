import PaidPromotion from './PaidPromotion.vue';
import { cloud } from '@youzan/ranta-helper';
import type { PaidPromotionData } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class PaidPromotionExtension {
  ctx: any;

  /**
   * paidPromotion
   * @desc 支付有礼
   * @type {PaidPromotion}
   */
  @cloud('paidPromotion', 'data')
  paidPromotion: PaidPromotionData;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['paidPromotion'], {
      callback: () => {
        const { paidPromotion = [] } = this.ctx.data;
        const newOpenData = {
          paidPromotion: cloudData.getPaidPromotion({ paidPromotion }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    PaidPromotion,
  };
}
