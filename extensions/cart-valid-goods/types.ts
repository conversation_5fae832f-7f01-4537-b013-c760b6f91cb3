import type { SelectAll, SetGoodsNumParams, TradeCartGoods } from '@youzan-cloud/cloud-biz-types';

interface GoodsV1 {
  cartId: number;
  kdtId: number;
  goodsId: number;
  skuId: number;
  sku: unknown;
  checked: boolean;
  activityId: number;
  storeId: number;
  channelId: number;
  canyinId: number;
  alias: string;
  title: string;
  attachmentUrl: string;
  num: number;
  payPrice: unknown;
  originPrice: unknown;
  stock: unknown;
  limitNum: number;
  activityType: unknown;
  activityAlias: string;
  messages: string;
  unique: unknown;
  revive: unknown;
  weight: unknown;
  imgUrl: string;
  /** 仅web端生效 */
  stockNum?: number;
  /** 仅web端生效 */
  thumbUrl?: string;
}
interface GoodsGroupList {
  goodsList: GoodsV1[];
  groupActivityInfo: unknown;
}
export interface ShopList {
  goodsGroupList: GoodsGroupList;
  kdtId: number;
  shopName: string;
}
interface Item {
  kdtId: number;
  goodsId: number;
  skuId: number;
  propertyIds: unknown;
  properties: unknown;
  sku: unknown;
  checked: boolean;
  activityId: number;
  storeId: number;
  channelId: number;
  canyinId: number;
  alias: string;
  title: string;
  attachmentUrl: string;
  num: number;
  payPrice: unknown;
  originPrice: unknown;
  stock: unknown;
  limitNum: number;
  activityType: unknown;
  activityAlias: string;
  messages: string;
  unique: unknown;
  revive: unknown;
  weight: unknown;
  imgUrl: string;
  /** 仅web端生效 */
  stockNum?: number;
  /** 仅web端生效 */
  thumbUrl?: string;
}
export interface GoodsList {
  activities: unknown;
  items: Item[];
  kdtId: number;
  shopName: string;
}

export interface TradeCartSetGoodsNumParams extends SetGoodsNumParams {
  /** 商品加购记录ID */
  cartId: number;
}

export type SelectGoods = {
  /** 选中的商品列表 */
  goodsList: TradeCartGoods[];
};
export type TradeCartGoodsChangePayload = SelectAll | SelectGoods;
