/**
 * 购物车异步数据拆分处理，不影响购物车核心数据展示，根据异步请求数据更新购物车列表。
 */
import { requestV2 } from '@youzan/tee-biz-request';
import cloneDeep from '@youzan/utils/object/clone-deep';

// 请求接口
const getCartAsyncData = (postData) => {
  return requestV2({
    method: 'post',
    path: '/wsctrade/cart/cart-async-data.json',
    data: postData,
    options: {
      rawResponse: true,
    },
  });
};

/**
 * 根据购物车列表数据获取商品的id
 * @param {array} clientCartWrap 购物车列表数据
 */
const getItemListParams = (clientCartWrap) => {
  const groupList = clientCartWrap?.goodsGroupList || [];
  return groupList
    .map((groupItem) => {
      return (groupItem.goodsList || []).map((item) => {
        const { goodsId, skuId, alias, cartId, num, comboDetail } = item;
        const format = {
          itemId: goodsId,
          skuId,
          alias,
          uniqueKey: String(cartId),
          num,
          comboDetail,
        };
        if (comboDetail) {
          format.comboDetail = comboDetail;
        }
        return format;
      });
    })
    .flat();
};

// 遍历shopCart并且对每个商品执行handler函数格式化
const traversalAndFormat = (clientCartWrap, handler) => {
  const newCart = cloneDeep(clientCartWrap);
  newCart.goodsGroupList.forEach((groupItem) => {
    groupItem.goodsList.forEach(handler);
  });
  return newCart;
};

// 购物车异步数据数据格式化
const formatShopCart = (shopCart, resData = {} as any) => {
  const { securedItems = [], itemDispatchInfoList = [] } = resData;

  return traversalAndFormat(shopCart, (goods) => {
    // 有赞担保数据
    if (securedItems.length) {
      const itemData = securedItems.find((item) => item.alias === goods.alias) || {};
      goods.yzGuarantee = itemData.yzSecured || false;
    }
    // 预计送达时间数据
    if (itemDispatchInfoList) {
      const itemDispatch =
        itemDispatchInfoList.find((item) => item.uniqueKey === String(goods.cartId)) || {};
      goods.deliveryTimeStr = itemDispatch.estimateDeliveryTimeDesc || '';
    }
  });
};

const asyncDataHelper = {
  // 初始数据更新
  init(shopCart, ctx) {
    try {
      const newCart = ctx.asyncResult ? formatShopCart(shopCart, ctx.asyncResult.data) : shopCart;
      ctx.data.shopCart = newCart;
    } catch (err) {
      ctx.data.shopCart = shopCart;
      console.log('asyncDataHelper init err: ', err);
    }
  },
  // 更新数据
  update(shopCart, ctx) {
    const { currentAddress, currentExpressType, currentLocation } = ctx.data;
    const itemInfoList = getItemListParams(shopCart);

    // 没有地址或者没有商品列表不需要请求接口
    if (!itemInfoList.length) {
      ctx.data.shopCart = shopCart;
      return;
    }
    // 所有异步请求需要的信息传到node端
    const params = {
      itemInfoList,
      currentAddress,
      currentExpressType,
      currentLocation,
    };

    return getCartAsyncData(params)
      .then((res: any) => {
        ctx.asyncResult = res;
        const newCart = formatShopCart(shopCart, res.data);
        return newCart;
      })
      .catch((err) => {
        console.log('err', err);
        // 报错则返回原来的购物车信息
        return shopCart;
      })
      .then((newCart) => {
        ctx.data.shopCart = newCart;
      });
  },
};

export default asyncDataHelper;
