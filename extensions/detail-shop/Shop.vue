<template>
  <view class="shop" :style="themeVars">
    <view v-if="!isFxZpp" class="shop__title" @click="handleShopNameClick">
      <van-icon class="shop__title__icon" name="shop-o" size="18px" />
      {{ shopName }}
    </view>

    <!-- #ifdef web -->
    <van-cell
      v-if="fxZppNoteInfo.noteTitle && !fxZppNoteInfo.isVirtualNote"
      is-link
      title="团购标题"
      title-width="80px"
      value-class="note-cell__text"
      title-class="note-cell__text"
      :value="fxZppNoteInfo.noteTitle"
      @click="handleZppNoteTitleClick"
    />
    <!-- #endif -->

    <!-- 商品卡片坑位 -->
    <goods />

    <van-cell-group :border="false">
      <!-- 周期购 -->
      <period />

      <!-- 资产-有赞担保（包括退货包运费） -->
      <guarantee-freight-bar />

      <!-- 合计 -->
      <van-cell
        v-if="!isWscSingleStore"
        :border="false"
        value-class="total-price__desc"
        :is-link="showPriceDetail"
        @click.stop="onPriceDetailClick"
      >
        <!-- showPriceDetail 含义是 是不是酒店商品 -->
        <view v-if="!showPriceDetail">
          商品小计：<text class="total-price__value">{{ totalPriceValue }}</text>
        </view>

        <view v-if="showPriceDetail" class="total-price--hotel">
          <text
            >商品小计：<text
              :style="{
                color: generalColor,
              }"
              class="total-price__value"
              >{{ totalPriceValue }}</text
            ></text
          >
          <text class="goods-info-price__detail-link">明细</text>
        </view>
        <!-- 费用明细 -->
        <van-action-sheet
          :show="showDetailPopup"
          @close="onClose"
          title="费用明细"
          class="goods-info-price__detail"
        >
          <price-detail
            :price="price"
            :points-name="points"
            :points="order.pointsPrice"
            :item-list="itemList"
            :main-color="generalColor"
          />
        </van-action-sheet>
      </van-cell>
    </van-cell-group>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import ActionSheet from '@youzan/vant-tee/dist/action-sheet/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import { formatTotalPrice } from '@youzan/wsc-tee-trade-common/lib/order-utils/format';

import { buildUrl } from '@youzan/tee-biz-util';
import PriceDetail from './components/PriceDetail';
import { object, string } from '@youzan/tee-util';
import { checkWscSingleStore } from '@youzan/utils-shop';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef web */
import { getUrlLinkRedirectUrl } from './api';
/* #endif */

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-icon': Icon,
    'van-action-sheet': ActionSheet,
    'price-detail': PriceDetail,
  },

  data() {
    return {
      order: {},
      totalPrice: '',
      shopInfo: {},
      isFxZpp: false,
      showPriceDetail: false,
      showDetailPopup: false,
      paymentInfo: {},
      courses: [],
      itemList: [],
      themeColor: {},
      themeVars: '',
      orderExtra: {},
    };
  },

  computed: {
    /** 是否为微商城单店 */
    isWscSingleStore() {
      return checkWscSingleStore(this.shopInfo);
    },
    generalColor() {
      return this.themeColor.general || '#f44';
    },
    shopName() {
      const { order, shopInfo } = this;
      return shopInfo.offlineStoreName || order.shopName || '';
    },

    totalPriceValue() {
      return formatTotalPrice(this.price, this.order.pointsPrice, this.points);
    },

    shopLink() {
      let result = '';
      /* #ifdef web */
      const _global = window._global || {};
      // 门店的总店ID，普通店铺headKdtId == kdtId
      const {
        kdtId: newKdtId = 0,
        kdt_id: oldKdtId = 0,
        orderInfo: { headKdtId = 0 },
        isOfflineOrder,
      } = _global;
      const kdtId = newKdtId || oldKdtId;
      let enterKdtId = kdtId;
      // 只有门店订单，优先使用headKdtId
      // 为了解决网店订单用headKdtId进入时，会默认进入上次访问的网店的问题
      if (isOfflineOrder) {
        enterKdtId = headKdtId || enterKdtId;
      }
      const homePageUrl = buildUrl(`/wscshop/showcase/homepage?kdt_id=${enterKdtId}`, 'h5', kdtId);
      const openAppConfig = string.mapKeysToCamelCase(object.get(_global, 'open_app_config'));
      result = openAppConfig.hideShopLink ? 'javascript:;' : homePageUrl;
      /* #endif */
      return result;
    },
    // 商品金额
    price() {
      // 线下报名价格需要自己累加
      // eslint-disable-next-line max-len
      if (this.courses && this.courses.length > 0)
        return this.courses.reduce(
          (acc, cur) => acc + object.get(cur, 'extPriceDTO.currentTotalAmount', 0),
          0
        );
      return this.paymentInfo.payPrice;
    },
    // 群团团相关字段
    /* #ifdef web */
    fxZppNoteInfo() {
      const { isFxZpp, orderExtra } = this;

      if (!isFxZpp || !orderExtra) {
        return {};
      }

      let noteTitle;
      let qttNote = {};
      try {
        noteTitle = JSON.parse(orderExtra.ATTR_QTT_ORDER_EXPORT)?.noteTitle;
        qttNote = JSON.parse(orderExtra.QTT_NOTE);
      } catch (error) {}

      return {
        noteTitle, // 团购标题
        noteId: orderExtra.ATTR_FX_ZPP_NOTE_ID, // 团购id
        participateNo: orderExtra.ATTR_FX_ZPP_TRADE_PARTICIPATE_NO, //  跟团号
        isVirtualNote: qttNote.IS_VIRTUAL_NOTE === 'true',
      };
    },
    /* #endif */
  },

  created() {
    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    }).catch((e) => {
      console.log(e);
    });
    this.isFxZpp = _global?.env?.isFxZpp || false;
    /* #endif */
    mapData(this, [
      'order',
      'totalPrice',
      'shopInfo',
      'miniprogram',
      'itemList',
      'showPriceDetail',
      'paymentInfo',
      'courses',
      'points',
      'themeColor',
      'themeVars',
      'orderExtra',
    ]);
  },

  methods: {
    handleShopNameClick() {
      const { miniprogram } = this;
      const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isKsApp } = miniprogram;
      const shouldSwitchTab = isAlipayApp || isQQApp || isXhsApp || isKsApp;
      const forbidLink = this.isFxZpp || isTTApp;

      /* #ifdef web */
      // 赞拼拼订单不跳转
      if (forbidLink) {
        return;
      }
      /* #endif */

      // this.ctx.process.invoke('handleUrlWithShopAutoEnter', url) 对跳转链接进行处理：是否需要加进店标识
      if (shouldSwitchTab) {
        /* #ifdef web */
        navigate({
          web: {
            type: 'znb',
            znb: {
              aliappUrl: this.ctx.process.invoke(
                'handleUrlWithShopAutoEnter',
                '/pages/home/<USER>/index'
              )[0],
              qqUrl: this.ctx.process.invoke(
                'handleUrlWithShopAutoEnter',
                '/pages/home/<USER>/index'
              )[0],
              xhsUrl: this.ctx.process.invoke(
                'handleUrlWithShopAutoEnter',
                '/pages/home/<USER>/index'
              )[0],
              ksUrl: this.ctx.process.invoke(
                'handleUrlWithShopAutoEnter',
                '/pages/home/<USER>/index'
              )[0],
              type: shouldSwitchTab ? 'switchTab' : '',
            },
          },
        });
        /* #endif */
      } else {
        navigate({
          url: this.ctx.process.invoke(
            'handleUrlWithShopAutoEnter',
            '/packages/home/<USER>/index'
          )[0], // 跳转URL
          type: 'navigateTo',
          web: {
            type: 'safeLink',
            safeLink: {
              url:
                this.shopLink === 'javascript:;'
                  ? this.shopLink
                  : this.ctx.process.invoke('handleUrlWithShopAutoEnter', this.shopLink)[0],
            },
          },
        });
      }

      // ZNB.getEnv()
      //   .then((env) => {
      //     const shouldSwitchTab = env.platform === 'aliapp' || env.platform === 'qqapp';
      //     ZNB.navigate({
      //       aliappUrl: `/pages/home/<USER>/index`,
      //       qqUrl: `/pages/home/<USER>/index`,
      //       url: this.shopLink,
      //       type: shouldSwitchTab ? 'switchTab': ''
      //     });
      //     return;
      //   });

      /* #ifdef weapp */
      // Tee.navigate({ url: '/packages/home/<USER>/index' });
      /* #endif */
    },

    onPriceDetailClick() {
      if (this.showPriceDetail) {
        this.showDetailPopup = true;
      }
    },
    onClose() {
      this.showDetailPopup = false;
    },
    /* #ifdef web */
    // 跳转至团购详情
    handleZppNoteTitleClick() {
      const { noteId } = this.fxZppNoteInfo || {};

      if (!noteId) return;

      const url = `/packages/detail-page/view/index?noteId=${noteId}`;
      const isWeapp = window._global?.miniprogram?.isWeapp;

      if (isWeapp) {
        ZNB.navigate({
          weappUrl: url,
        });
      } else {
        getUrlLinkRedirectUrl({
          url: url.substr(1),
          expiredTime: new Date().getTime() + 1000 * 60 * 5,
        }).then((res) => {
          if (res && res.yzLongLink) {
            location.href = res.yzLongLink;
          }
        });
      }
    },
    /* #endif */
  },
};
</script>

<style lang="scss">
$text-color: #323233;

.goods-info-price {
  &__detail-link {
    float: right;
    color: #38f;
  }
  &__detail {
    max-height: 80%;
    overflow-y: auto;
  }
}

.shop {
  margin-top: 10px;
  background: #fff;
}

.shop__title {
  position: relative;
  padding: 10px 16px 10px 42px;
  font-size: 14px;
  font-weight: bold;
}

.shop__title__icon {
  position: absolute !important;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0;
}

.total-price__desc {
  color: $text-color !important;
}
.total-price--hotel {
  display: flex;
  justify-content: space-between;
}

.total-price__value {
  font-weight: bold;
  color: var(--icon, #323233);
}

.t-c-gray-darker {
  color: #666;
}

.note-cell__text {
  color: #969799 !important;

  .tee-view {
    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
