<template>
  <view v-if="evaluate && evaluate.length > 0" class="order-pay-prompt-evaluate">
    <view class="order-pay-prompt-evaluate__list">
      <view
        :key="item.attitude"
        v-for="item in evaluate"
        :style="{ backgroundColor: themeColors['vice-bg'] }"
        class="order-pay-prompt-evaluate__item"
      >
        {{ item.attitude }}({{ item.count }})
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    evaluate: {
      type: Array,
      // 提供默认的评价数据
      default: () => [],
    },
    themeColors: {
      type: Object,
      // 提供默认的主题颜色
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" scoped>
.order-pay-prompt-evaluate {
  width: 100%;
  margin-bottom: 12px;
  padding: 0 10px;
  box-sizing: border-box;

  &__list {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__item {
    background-color: rgba(227, 20, 54, 0.1);
    border-radius: var(--theme-radius-tag, 16px);
    margin: 4px 6px;
    height: 24px;
    padding: 4px 8px;
    box-sizing: border-box;
    font-size: 12px;
    color: #323233;
  }
}
</style>
