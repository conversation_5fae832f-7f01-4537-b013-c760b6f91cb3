import {
  GoodsTypeV1,
  OrderDetailUrlV1,
  OrderNoV1,
  OrderTypeV1,
  PayResultV1,
  PayWayStrV1,
  VirtualTicketDetailUrlV1,
} from '../types';
import type {
  OrderPaidOrderInfo,
  OrderPaidPayment,
  OrderPaidPayStateEnum,
  OrderTypeEnum,
} from '@youzan-cloud/cloud-biz-types';
import toCamelCase from '@youzan/utils/string/toCamelCase';
import { PayWayEnum } from '@youzan-cloud/cloud-biz-types';

const payWayEnum = new Map([
  [1, { value: 'wxpay', desc: '微信自有支付' }],
  [3, { value: 'alipay', desc: '支付宝wap' }],
  [28, { value: 'ecard', desc: '有赞E卡支付' }],
  [4101, { value: 'electronicBankPay', desc: '银联网银支付' }],
  [36, { value: 'creditCardUnionpay', desc: '信用卡银联支付' }],
  [25, { value: 'prepaidCard', desc: '储值卡' }],
  [35, { value: 'unifiedPrepaidCard', desc: '会员余额' }],
  [9, { value: 'codpay', desc: '货到付款' }],
  [7, { value: 'peerpay', desc: '代付' }],
]);

const orderTypeEnum = new Map([
  [0, { value: 'normal', desc: '普通订单' }],
  [2, { value: 'peerpay', desc: '代付' }],
  [1, { value: 'gift', desc: '送礼订单' }],
  [3, { value: 'fxCaigoudan', desc: '分销采购单' }],
  [4, { value: 'present', desc: '赠品' }],
  [5, { value: 'wish', desc: '心愿单' }],
  [100, { value: 'pf', desc: '批发' }],
  [10, { value: 'group', desc: '拼团' }],
  [35, { value: 'hotel', desc: '酒店' }],
]);

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  getGoodsTypeV1: ({ goodsType }): GoodsTypeV1 => goodsType,
  getPayResultV1: ({ payResult }): PayResultV1 => ({
    payWayPrompt: payResult.payWayStr,
    realPayAmount: payResult.realPayAmount,
  }),
  getOrderDetailUrlV1: ({ orderDetailUrl }): OrderDetailUrlV1 => orderDetailUrl,
  getOrderTypeV1: ({ orderType }): OrderTypeV1 => orderType,
  getOrderNoV1: ({ orderNo }): OrderNoV1 => orderNo,
  getVirtualTicketDetailUrlV1: ({ virtualTicketDetailUrl }): VirtualTicketDetailUrlV1 =>
    virtualTicketDetailUrl,
  getPayWayStrV1: ({ payWayStr }): PayWayStrV1 => payWayStr,

  getOrderInfo: ({ orderNo, orderType, payResult }): OrderPaidOrderInfo => {
    return {
      orderNo,
      orderType: (orderTypeEnum.get(orderType)?.value || 'unknown') as OrderTypeEnum,
      payState: toCamelCase(
        payResult?.payState?.toLocaleLowerCase() || ''
      ) as OrderPaidPayStateEnum,
    };
  },
  getPayment: ({ payResult }): OrderPaidPayment => {
    return {
      payWay: (payWayEnum.get(payResult.payWay)?.value || 'unknown') as PayWayEnum,
      payWayDesc: payResult.payWayStr,
      realPay: payResult?.realPayAmount,
    } as OrderPaidPayment;
  },
};
