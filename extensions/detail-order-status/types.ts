/**
 * 订单状态
 */
enum OrderStateEnum {
  CREATED = 1, // 已下单
  WAIT_PAY = 10, // 待支付
  PAID = 20, // 已支付
  WAIT_CONFIRM = 30, // 待接单
  CONFIRMED = 40, // 已接单
  WAIT_SHIPPED = 50, // 待发货
  SHIPPED = 60, // 已发货
  WAIT_RECEIVED = 70, // 待收货
  RECEIVED = 80, // 已收货
  CLOSED = 99, // 已关闭
  SUCCESS = 100, // 已完成
}
/**
 * 订单状态进度条
 */
interface ProgressBar {
  /** 是否到达该状态 */
  arrive: boolean;
  /** 订单状态文案 */
  type: string;
}

/**
 * 订单关闭状态
 * 0 = 未关闭
 * 1 = 已关闭
 */
enum CloseTypeEnum {
  OPEN = 0,
  CLOSE = 1,
}
export interface OrderStatusData {
  /** 订单状态 */
  orderState: OrderStateEnum;
  /** 订单状态文案 */
  orderStateTitle: string;
  /** 自动收货时间(秒级时间戳) */
  autoReceiveOrderTime: number;
  /** 关闭类型 1 已关闭 0 未关闭 */
  closeType: CloseTypeEnum;
  /** 关闭类型文案 */
  closeTypeText: string;
  /** 订单进度条 */
  progressBar: ProgressBar[];
}
