// Widgets
import AfterSale from './AfterSale.vue';

// Utils
import createStore from './store';
import { cloudData, isSameObject } from './utils';

// Dependencies
import { cloud, bridge } from '@youzan/ranta-helper';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';
import type { AfterSaleData } from '@youzan-cloud/cloud-biz-types';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';

export default class AfterSaleExtension {
  static widgets = {
    AfterSale,
  };

  ctx: any;

  store: any;

  /**
   * callAftersalePhone
   * @desc 拨打售后电话
   */
  @cloud('callAftersalePhone', 'method', { allowMultiple: true })
  callAftersalePhone() {
    this.store.handleMakePhoneCall();
  }

  /**
   * afterSale
   * @desc 售后信息
   */
  @cloud('afterSale', 'data')
  afterSale: AfterSaleData;

  /**
   * showIM
   * @deprecated 从 2.0 开始
   * @desc 处理“在线客服”
   */
  @bridge('showIM', 'process')
  handleShowIM() {
    /* #ifdef weapp */
    console.warn('小程序暂不支持调用 showIM');
    /* #endif */
    /* #ifdef web */
    this.store.handleContactIM();
    /* #endif */
  }

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.data.showIM = false;
    this.store = createStore(this.ctx);

    mapCtxData(this, [
      'env',
      'kdtId',
      'contact',
      'orderNo',
      'goodsList',
      'orderExtra',
      'showAfterSaleMobile',
    ]);
    mapStoreToCtx(this, ['showIM']);
    mapData(this, ['showIM'], {
      isSetData: false,
      callback: () => {
        const { showIM = false } = this.ctx.data;
        const newOpenData = {
          afterSale: cloudData.getAfterSale({ showIM }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
    mapProcess(this, {
      handleContactIM: this.store.handleContactIM,
      handleMakePhoneCall: this.store.handleMakePhoneCall,
    });
  }

  beforePageMount() {
    this.store.initFxZppData();
  }
}
