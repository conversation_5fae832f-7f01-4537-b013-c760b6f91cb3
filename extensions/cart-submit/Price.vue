<template>
  <view class="price--block" :style="themeVars">
    <view class="price custom-class">
      <view class="price--unit">¥</view>
      <view class="price--integer">{{ formatPriceArr[0] }}</view>
      <view v-if="formatPriceArr[1]" class="price--decimal">.{{ formatPriceArr[1] }}</view>
      <block v-if="pointsPrice">
        +
        <view class="price--integer">{{ pointsPrice }}</view>
        <view class="price--unit">{{ pointsName }}</view>
      </block>
    </view>
  </view>
</template>

<script>
import money from '@youzan/weapp-utils/lib/money';

export default {
  props: {
    price: {
      type: [String, Number],
      required: true,
      default: 0,
    },
    themeVars: {
      type: String,
    },
    pointsPrice: {
      type: [String, Number],
      default: 0,
    },
    pointsName: {
      type: String,
      default: '积分',
    },
  },
  data() {
    const formatPriceArr = this.getFormatPriceArr();
    return {
      rootClass: {
        root: `custom-class`,
      },
      formatPriceArr,
    };
  },

  externalClasses: ['custom-class'],

  watch: {
    price() {
      this.formatPriceArr = this.getFormatPriceArr();
    },
  },
  methods: {
    getFormatPriceArr() {
      let newPrice = this.price.toString();
      if (newPrice.indexOf(',') === -1) {
        newPrice = money(newPrice).toYuan();
      }

      const formatPriceArr = newPrice.split('.');
      // 小数去 0
      const decimalReverse = +formatPriceArr[1].split('').reverse().join('');
      formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

      if (!+formatPriceArr[1]) {
        formatPriceArr.splice(1, 1);
      }

      return formatPriceArr;
    },
  },
};
</script>

<style lang="scss" scoped>
.price {
  color: var(--price, #323233);
  display: inline-flex;
  align-items: baseline;
  justify-content: flex-end;
  font-family: Avenir;
  flex-wrap: wrap;
  font-weight: var(--theme-common-price-font-weight, 600);
  &--block {
    view {
      display: inline-block;
    }
  }

  &--unit {
    font-size: var(--eo-font-size-12, 12px);
    margin-right: 2px;
  }

  &--integer {
    font-size: var(--eo-font-size-16, 16px);
    font-family: Avenir;
  }

  &--decimal {
    font-size: var(--eo-font-size-12, 12px);
    font-family: Avenir;
  }

  &--origin {
    font-size: 12px;
    color: #969799;
    margin-left: 5px;
    text-decoration: line-through;
    font-family: Avenir;
  }
}
</style>
