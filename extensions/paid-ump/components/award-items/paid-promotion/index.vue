<template>
  <!-- 因为默认的优惠券在上面已经展示掉了，这里不会出现 -->
  <!-- 默认的赠品 -->
  <award-present
    v-if="currentType === PROMOTION_TYPES.Present"
    :info="promotionInfo"
    :theme-color="themeColor"
    @pure-log="pureLog"
    @click-handle="clickImage"
  />
  <!-- 活码，只有默认的 -->
  <award-activity-code
    v-else-if="currentType === PROMOTION_TYPES.ActivitiesQrCode"
    @pure-log="pureLog"
    :info="promotionInfo"
  />
  <!-- 默认的幸运大抽奖，但是目前小程序是不支持这个的，具体可以看constants下的 SupportedActivity -->
  <award-lucky-game
    v-else-if="currentType === PROMOTION_TYPES.Wheel"
    :prizes="promotionInfo.prizes"
    :detail-url="promotionInfo.detailUrl"
    :navigate-to="navigateTo"
    @click-handle="clickImage"
  />
  <award-simple-show
    v-else-if="currentType === DEFAULT_SHOW"
    :theme-color="themeColor"
    :info="promotionInfo"
    @pure-log="pureLog"
    @click-btn="clickImage"
  />
  <!-- 如果是非默认的活码，需要走特定的逻辑 -->
  <concat-button
    v-else-if="isCustomActivityQrcode"
    @after-contact="afterContact"
    custom-class="ump-concat-button"
    :h5-link="promotionInfo.detailUrl"
  >
    <image class="custom-pic" :src="customPic" mode="aspectFit" />
  </concat-button>
  <!-- 如果是非默认的幸运大抽奖/赠品，就都直接展示默认的图片 -->
  <award-container
    custom-style="padding: 0"
    @click="clickImage"
    v-else-if="currentType === ImageShow"
  >
    <image class="custom-pic" :src="customPic" mode="aspectFit" />
  </award-container>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Present from './Present';
import ActivityCode from './ActivityCode';
import LuckyGame from './LuckyGame';
import AwardContainer from '../Container';
import ConcatButton from '../../widgets/ConcatButton';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import args from '@youzan/weapp-utils/lib/args';
import SimpleShow from './Simple';
import { PROMOTION_TYPES, isDefaultPromotion, PROMO_TYPE_ACTION } from '../../../constants';
import { promotionAttachLog } from '../paid-promotion-utils';

/** 默认行为不是展示图片的 act_type */
const DefaultSupportedActivity = [
  PROMOTION_TYPES.Tradeincard,
  PROMOTION_TYPES.Couponpackage,
  PROMOTION_TYPES.Promocode,
  PROMOTION_TYPES.Present,
  PROMOTION_TYPES.Wheel,
  PROMOTION_TYPES.ActivitiesQrCode,
];

/** 需要控制展示的部分act */
const DefaultButtonedActivity = [
  PROMOTION_TYPES.Survey,
  PROMOTION_TYPES.Feature,
  PROMOTION_TYPES.Seller,
  PROMOTION_TYPES.Seniorseller,
];

const ImageShow = 'imageShow';
const DEFAULT_SHOW = 'default-show';

export default {
  name: 'award-paid-promotion',
  components: {
    'award-present': Present,
    'award-activity-code': ActivityCode,
    'award-lucky-game': LuckyGame,
    'award-container': AwardContainer,
    'concat-button': ConcatButton,
    'award-simple-show': SimpleShow,
  },
  props: {
    promotionInfo: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
    navigateTo: Function,
    orderNo: String,
  },
  data() {
    return {
      PROMOTION_TYPES,
      ImageShow,
      DEFAULT_SHOW,
    };
  },
  computed: {
    customPic() {
      return this.promotionInfo?.imgUrl;
    },
    isCustomActivityQrcode() {
      const { promotionType } = this.promotionInfo;
      // 自定义活码比较特殊，需要唤起客服
      return promotionType === PROMOTION_TYPES.ActivitiesQrCode && this.currentType === ImageShow;
    },
    currentType() {
      const { promotionType, imageType } = this.promotionInfo;

      if (promotionType === DefaultSupportedActivity.activitiesQrCode)
        return PROMOTION_TYPES.ActivityCode;
      if (DefaultSupportedActivity.includes(promotionType) && isDefaultPromotion(imageType)) {
        return promotionType;
      }
      if (isDefaultPromotion(imageType) && DefaultButtonedActivity.includes(promotionType))
        return DEFAULT_SHOW;
      return ImageShow;
    },
  },
  mounted() {
    const { promotionType, imageType } = this.promotionInfo;
    // 如果是优惠券，就不触发这里的埋点
    if (
      [
        PROMOTION_TYPES.Tradeincard,
        PROMOTION_TYPES.Couponpackage,
        PROMOTION_TYPES.Promocode,
      ].includes(promotionType)
    ) {
      return;
    }
    promotionAttachLog({
      type: promotionType,
      isCustom: !isDefaultPromotion(imageType),
      track: 'view',
      logFn: this.pureLog,
    });
  },
  methods: {
    pureLog(payload) {
      this.$emit('log-attach', payload);
    },
    afterContact() {
      const { promotionType, imageType } = this.promotionInfo;

      promotionAttachLog({
        type: promotionType,
        isCustom: !isDefaultPromotion(imageType),
        track: 'click',
        logFn: this.pureLog,
      });
    },
    clickImage() {
      const { promotionType, detailUrl, imageType } = this.promotionInfo;

      promotionAttachLog({
        type: promotionType,
        isCustom: !isDefaultPromotion(imageType),
        track: 'click',
        logFn: this.pureLog,
      });

      const action = PROMO_TYPE_ACTION[promotionType];
      if (!action) {
        Toast(promotionType + '跳转异常，请稍后重试');
      } else {
        action({
          orderNo: this.orderNo,
          detailUrl,
          ...mapKeysCase.toCamelCase(args.getAll(detailUrl)),
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-pic {
  width: calc(100vw - 48px);
  height: 88px;
}
.ump-concat-button {
  width: 100%;
  height: 88px !important;
  padding: 0 !important;
  border: none !important;
}
</style>
