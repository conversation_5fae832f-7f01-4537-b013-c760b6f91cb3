<template>
  <van-popup
    round
    closeable
    :show="show"
    position="bottom"
    @close="handleCancel"
    safe-area-inset-bottom
  >
    <view class="receiver-modify">
      <view class="receiver-modify-title">修改收货信息</view>
      <!-- TODO: 由于小程序存在样式问题，这里自定义展示cell内容，样式与ReceiverInfo.vue有些重复，可优化 -->
      <view class="cell-item" @click="handleAddressClick">
        <view class="custom-cell">
          <view class="custom-cell-left">收货地址</view>
          <view class="custom-cell-content">
            <view class="custom-cell-title">{{ addressDetail }}</view>
            <view class="custom-cell-label">{{ receiverDetail }}</view>
          </view>
          <view class="custom-cell-arrow">
            <van-icon name="arrow" />
          </view>
        </view>
      </view>
      <view class="cell-item" v-if="showDeliveryTime">
        <view
          class="custom-cell"
          :class="{ 'custom-cell-disabled': hasNotNewTime }"
          @click="handleTimeClick"
        >
          <view class="custom-cell-left">送达时间</view>
          <view class="custom-cell-content">
            <view v-if="timeText" class="custom-cell-title">
              预计 <text class="text-strong">{{ timeText }}</text>
            </view>
            <view v-else class="custom-cell-title" style="color: #999"> 请选择... </view>
          </view>
          <view v-if="!hasNotNewTime" class="custom-cell-arrow">
            <van-icon name="arrow" />
          </view>
        </view>
      </view>
      <button-group :btns="BUTTONS" @click="handleClick" />
    </view>
  </van-popup>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import buttonGroup from '../../components/button-group.vue';
import { mapActions, mapState } from '@ranta/store';

const BUTTONS = [
  {
    type: 'cancel',
    primary: false,
    text: '取消',
  },
  {
    type: 'save',
    primary: true,
    text: '保存修改',
  },
];

export default {
  components: {
    'van-cell': Cell,
    buttonGroup,
    'van-popup': Popup,
    'van-icon': Icon,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      BUTTONS,
      ...mapState(this, ['address', 'timeConfig', 'showDeliveryTime', 'hasNotNewTime']),
    };
  },
  computed: {
    addressDetail() {
      return this.address.addressDetail || '';
    },
    receiverDetail() {
      const { userName = '', tel = '' } = this.address;
      return `${userName} ${tel}`;
    },
    timeText() {
      return this.timeConfig.text || '';
    },
  },
  created() {
    mapActions(this, ['handleSaveChange']);
  },
  methods: {
    handleCancel() {
      this.ctx.data.showReceiverModifyPopup = false;
    },
    handleAddressClick() {
      this.ctx.data.showAddressModifyPopup = true;
    },
    handleClick({ type }) {
      if (type === 'cancel') {
        this.handleCancel();
      } else if (type === 'save') {
        // 保存修改
        this.handleSaveChange();
      }
    },
    handleTimeClick() {
      if (this.hasNotNewTime) {
        return;
      }
      this.ctx.data.showTimeModifyPopup = true;
    },
  },
};
</script>

<style lang="scss">
.receiver-modify {
  width: 100%;
  height: 80vh;
  background: #f7f8fa;

  &-title {
    font-weight: 600;
    font-size: 16px;
    height: 52px;
    line-height: 52px;
    text-align: center;
    background: #fff;
  }

  .cell-item {
    margin: 12px;
    border-radius: 8px;
    overflow: hidden;
  }
  .cell-label {
    color: #666;
  }
  .cell-left {
    margin-right: 18px;
  }

  .text-strong {
    font-weight: 500;
  }

  .custom-cell {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    position: relative;

    &-disabled {
      opacity: 0.6;
    }

    &-left {
      margin-right: 18px;
      color: #323233;
      font-size: 14px;
      flex-shrink: 0;
    }

    &-content {
      flex: 1;
      min-width: 0;
    }

    &-title {
      font-size: 14px;
      color: #323233;
      line-height: 20px;
    }

    &-label {
      font-size: 12px;
      color: #666;
      line-height: 16px;
      margin-top: 2px;
    }

    &-arrow {
      margin-left: 8px;
      color: #969799;
      font-size: 14px;
    }
  }
}
</style>
