<template>
  <view>
    <block>
      <view class="retail-goods-card" v-for="(item, index) in goodsListFormatted" :key="index">
        <view class="retail-left">
          <image class="retail-card__thumb" :src="item.thumb" mode="aspectFill" />
        </view>
        <view class="retail-center">
          <view class="retail-title card__title">
            <view v-if="item.isShipped" class="card__title__shipped-tag">已发货</view>
            <image
              v-if="item.isPrescriptionDrugGoods"
              class="goods-card__title-tag"
              :src="drug"
              alt="处方药"
            />
            <text class="card__title__goods-name">{{ item.title }}</text>
          </view>
          <view class="retail-desc">
            <view v-if="item.skuStr" class="van-card__desc">{{ item.skuStr }}</view>
            <view class="card__presale-date" v-if="item.preSaleExpressTimeDesc">
              {{ item.preSaleExpressTimeDesc }}
            </view>
          </view>
          <view class="retail-tag">
            <van-tag
              v-for="(tagItem, index) in item.tagList"
              :key="index"
              color="var(--ump-tag-bg, #f2f2ff)"
              text-color="var(--ump-tag-text, #323233)"
              round
              class="card__tag-item"
            >
              {{ tagItem.text }}
            </van-tag>
            <view class="card__hotel" v-if="hotel.show">
              <view>入住：{{ hotel.checkInTime }}</view>
              <view>离店：{{ hotel.checkOutTime }}，共{{ hotel.num }}晚</view>
            </view>
          </view>
        </view>
        <view class="retail-right">
          <view class="card-price__price">
            <text class="card-price__price_desc">¥</text>
            <text class="card-price__price_pr">
              {{ item.priceStr }}
            </text>
          </view>
          <view class="card-price__num">x {{ item.num }}</view>
        </view>
      </view>
      <view class="goods-list__total-bar">
        <view class="goods-list__ctrl" @click.stop="toggleShowMore" v-if="goodsList.length > 1">
          <view v-if="!showMore"
            >共{{ goodsList.length }}件
            <van-icon class="arrow-icon" name="arrow-down" color="#969799" />
          </view>
          <view v-else>收起 <van-icon class="arrow-icon" name="arrow-up" color="#969799" /></view>
        </view>
        <view v-else> 共1件 </view>

        <view class="goods-list__total-amount">实付 {{ payPrice }}</view>
      </view>
    </block>
  </view>
</template>
<script>
// Components
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Card from '@youzan/vant-tee/dist/card/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'goods-card',

  components: {
    'van-tag': Tag,
    'van-card': Card,
    'van-icon': Icon,
  },

  props: {
    hotel: {
      type: Object,
      default: () => ({}),
    },
    payPrice: String,
    goodsList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      drug: 'https://b.yzcdn.cn/path/to/cdn/dir/isDrugTag_3x.png',
      showMore: false,
    };
  },

  computed: {
    // 因为缩略图需要格式化才可以用所以需要用这个计算属性
    goodsListFormatted() {
      return this.goodsList.slice(0, this.showMore ? this.goodsList.length : 1).map((item) => {
        return {
          ...item,
          thumb: fullfillImage(item.image, '!200x0.jpg'),
        };
      });
    },
  },
  methods: {
    toggleShowMore() {
      this.showMore = !this.showMore;
    },
  },
};
</script>
<style lang="scss" scoped>
.card__tag-item {
  margin-right: 5px;
}

.card__title {
  line-height: 20px;
  display: flex;
  align-items: center;
}

.card__title__goods-name {
  word-break: break-all;
  font-size: 16px;
}

.goods-card__title-tag {
  width: 43px;
  height: 16px;
  margin-right: 5px;
}

.card__title__shipped-tag {
  color: #faab0c;
  float: right;
}

.card__presale-date {
  font-size: 12px;
  color: #ed6a0c;
  margin: 5px 0;
}

.card__hotel {
  font-size: 12px;
  color: #666;
  line-height: 20px;
}

.goods-card-index--card__price {
  color: #323233;
}

$height: 68px;

.retail-goods-card {
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
  background-color: #fff !important;
  color: #323233;
  font-size: 12px;
  padding: 8px 16px;
  position: relative;
  min-height: $height;
  border-radius: 8px;

  .retail-left {
    width: 72px;
    height: $height;
    flex: none;

    &.retail-left__more {
      width: auto;
      flex: 1;
      display: flex;
      overflow: auto;
      white-space: nowrap;
    }

    .retail-card__thumb {
      width: 54px;
      height: 54px;
      flex: none;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 12px;
      position: relative;
      display: inline-block;
      margin-top: 8px;

      &_img {
        width: 100%;
        height: 100%;
      }

      &_num {
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 14px;
        height: 20px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
      }
    }
  }

  .retail-right {
    width: auto;
    flex: none;
    padding-left: 10px;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    font-size: 14px;
    border-radius: 8px;
    min-height: $height;
    justify-content: center;
    background-color: #fff;

    .card-price__integral {
      font-size: 14px;
      font-weight: 500;
    }

    .card-price__price {
      font-weight: 600;
      margin-bottom: 3px;

      &_pr {
        font-size: 16px;
      }

      &_desc {
        margin-right: 1px;
      }
    }

    .card-price__num {
      color: #969799;
    }
  }

  .retail-center {
    flex: 1;
    padding-top: 8px;

    .van-card__desc {
      color: #969799;
      margin-top: 4px;
      font-size: 14px;
      line-height: 16px;
    }
  }
}

.more-goods-icon {
  width: 30px;
}
.goods-list {
  &__total-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 12px 16px;
    background-color: #fff;

    .arrow-icon {
      margin-left: 4px;
      font-size: 12px;
    }
  }
  &__total-amount {
    font-weight: 500;
  }
}
</style>
