<template>
  <view :class="rootClass.root" :style="customStyle" @click="click">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'award-container',
  externalClasses: ['custom-class'],
  props: {
    customStyle: {
      type: String,
      default: '',
    },
  },
  computed: {
    rootClass() {
      return {
        root: `custom-class award-container`,
      };
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.award-container {
  border-radius: 8px;
  width: calc(100vw - 48px);
  box-sizing: border-box;
  margin-top: 12px;
}

.award-container + .award-container {
  margin-top: 12px;
}
</style>
