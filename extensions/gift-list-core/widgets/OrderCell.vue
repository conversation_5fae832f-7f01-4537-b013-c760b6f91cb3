<template>
  <view class="order-cell">
    <view @click="doCellClick">
      <van-card
        thumb-class="thumb-class"
        v-for="(item, index) in displayedItems"
        :key="index"
        :thumb="item.thumb || ''"
        :title="item.title || ''"
        :price="item.price || ''"
        :num="(mode === 'donate' && item.num) || ''"
      >
        <view v-if="action && item.status" class="van-card__detail-row" slot="desc">
          <view class="van-card__detail-status">{{ item.status }}</view>
        </view>
      </van-card>
    </view>
    <view v-if="status" class="order-cell__status">
      <view class="order-cell__status-text">{{ status }}</view>
      <view class="order-cell__status-action" @click="doCellAction">
        {{ action }}
      </view>
    </view>
    <view v-if="items.length > 1 && !showAllItems" class="order-cell__more" @click="checkMoreItems">
      查看全部{{ items.length }}件商品
    </view>
  </view>
</template>

<script>
import Card from '@youzan-open/vant-tee/dist/card/index.vue';

export default {
  components: {
    'van-card': Card,
  },

  props: {
    mode: String, //  模式：receive donate
    items: {
      type: Array,
      default: () => [],
    },
    giftAlias: String,
    status: String,
    action: String,
    actionType: String,
  },

  data() {
    return {
      showAllItems: false,
      displayedItems: [],
    };
  },

  watch: {
    items() {
      this.computeDisplayedItems();
    },
  },

  created() {
    this.computeDisplayedItems();
  },

  methods: {
    checkMoreItems() {
      const _showAllItems = !this.showAllItems;
      this.showAllItems = _showAllItems;
      if (_showAllItems) {
        this.computeDisplayedItems(_showAllItems);
      }
    },

    computeDisplayedItems(showAllItems) {
      if (this.items.length > 0) {
        if (showAllItems || this.showAllItems) {
          this.displayedItems = this.items;
        } else {
          this.displayedItems = [this.items[0]];
        }
      }
    },

    doCellAction() {
      this.$emit('cellaction', {
        actionType: this.actionType || '',
        alias: this.giftAlias || '',
      });
    },

    doCellClick() {
      this.$emit('celltap', {
        actionType: this.actionType || '',
        alias: this.giftAlias || '',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$COLOR_BLUE: #3283fa;
.order-cell {
  margin-top: 10px;
  font-size: 14px;
  background: #fff;
  padding-top: 13px;

  &__more {
    color: $COLOR_BLUE;
    line-height: 40px;
    text-align: center;
    position: relative;
  }
  &__status {
    font-size: 12px;
    line-height: 20px;
    padding: 10px 15px;
    overflow: hidden;
    color: #666;
    position: relative;

    &-text {
      float: left;
    }
    &-action {
      float: right;
      color: $COLOR_BLUE;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      transform: scale(0.5);
      transform-origin: 0 0;
      pointer-events: none;
      box-sizing: border-box;
      border-bottom: 1px solid #e5e5e5;
    }
  }
}

.thumb-class {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: auto !important;
  height: auto !important;
  max-width: 100%;
  max-height: 100%;
}

.van-card__detail-row {
  overflow: hidden;
  line-height: 20px;
  min-height: 20px;
  margin-top: 25px;
}

.van-card__detail-status {
  font-size: 12px;
  color: #666;
}
</style>
