# cloud 目录说明

这个目录下放的都是和 开放 1.0 / 开放 2.0 相关的内容

标品的业务逻辑请不要放在这里目录里（包括一些工具函数也不要放在这里，这个目录下的[utils.ts](./utils.ts)只服务于开放）

## 目录文件说明

- [CloudExtension.ts](./CloudExtension.ts) --- 这个是一个 class，被外部的 index.ts 继承
- [types.ts](./types.ts) ------------ 这里放的是和开放相关的一些类型声明
- [utils.ts](./utils.ts) ------------ 这里放的是和开放相关的工具函数

在 [CloudExtension.ts](./CloudExtension.ts) 中你会看到一个 SuperExtension 的类，这个是超类，不要去动他，单独拆出来只是为了和开放更明确的拆分开。

另外你还会看到 [`ModuleContextCtx`](./CloudExtension.ts) 和 [`ModuleContextOptions`](./CloudExtension.ts)这个不需要去动他所有 extension 中都差不多是一样的。

另外，如果 ts 出现了报错，比如某某 ctx 上的定义找不到之类的报错，请到外部的 [extension.d.ts](../extension.d.ts) 中进行定义
