{"extensionId": "@wsc-tee-trade/paid-ext-holder", "name": "@wsc-tee-trade/paid-ext-holder", "version": "1.0.1", "bundle": "<builtin>", "widget": {"default": "Main", "provide": ["Main", "FlowChangeUmp"], "consume": ["FlowChangeUmp", "FissionCouponUmp", "PaidPromotionBanner", "GuideCouponBlock", "BigdataCoupon", "InSourcingCouponUmp", "ProtocolPopupWeapp", "PaidCpsGoodsRecommend"]}, "data": {"consume": {"hasPaid": ["r"], "payResult": ["r"], "canUseTradeUmpV1": ["r"], "isWxReceipt": ["r"], "realHeight": ["r"], "kdtId": ["r"], "returnUrl": ["r"]}, "provide": {"showExtHolder": ["r"], "emptyPaidExtHolderBlock": ["r"], "paidExtHolderLoaded": ["r"]}}, "event": {"emit": ["SubscribeNotice"]}, "process": {"define": ["getInSourcingCouponShare"], "invoke": ["navigateTo", "logger", "ready"]}, "platform": ["weapp", "web"]}