<template>
  <view class="combo-goods" @click.stop="$emit('open-combo-popup')">
    <view class="title">
      <view class="left">套餐包含以下商品</view>
      <view class="right">
        <text>详情</text>
        <van-icon name="arrow" />
      </view>
    </view>
    <scroll-view
      scroll-x
      class="wrapper"
      @touchstart="$emit('change-swipe-status', true)"
      @touchend="$emit('change-swipe-status', false)"
    >
      <view class="combo-list">
        <view class="item" v-for="(item, index) in comboDetail" :key="index">
          <image class="item-img" mode="aspectFill" lazy-load :src="item.src" />
          <text class="item-number">x{{ item.num }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

export default {
  components: {
    'van-icon': Icon,
    'van-popup': Popup,
  },
  props: {
    comboDetail: {
      type: [Array],
      required: true,
    },
  },
};
</script>

<style scoped lang="scss">
.combo-goods {
  margin-top: 8px;
  .title {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #323233;
    .right {
      color: #969799;
    }
  }
  .wrapper {
    height: 60px;
    .combo-list {
      display: flex;
      flex: 1;
      margin-top: 4px;
      .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 40px;
        margin-right: 4px;
        &:last-of-type {
          margin-right: 0;
        }
        &-img {
          width: 40px;
          height: 40px;
          border-radius: 2px;
        }
        &-number {
          color: #969799;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>
