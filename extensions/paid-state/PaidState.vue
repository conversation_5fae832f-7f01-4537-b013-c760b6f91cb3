<template>
  <view>
    <view v-if="hasPaid" :class="['msg', bgTransparent ? 'trade-ump-v1' : '']">
      <view class="paid-success-info">
        <view class="success-tips">{{
          showPriorUse ? '提交订单成功' : displayPrompt.payPrompt
        }}</view>
        <view class="price">
          <text class="num">{{ realPayAmount }}</text>
        </view>
      </view>
      <text v-if="displayPrompt.payPromptSubTitle" class="pay-prompt-sub_title">{{
        displayPrompt.payPromptSubTitle
      }}</text>
      <view v-if="showPriorUse" class="prior-use">
        <i class="icon" />
        <text class="label prior-pay">微信支付分<text class="vertical">|</text>先用后付</text>
        <text class="label">下单成功，确认收货后自动扣款</text>
      </view>
      <view v-if="payTip" class="detail">
        {{ payTip.prefixText }}
        <view class="detail__text" @click="openPayDetailWv">{{ payTip.urlText }}</view>
      </view>
    </view>
    <oversale-msg v-else-if="showOversale" />
    <error v-else-if="showError" :loading="isRefreshing" @click="refresh" />
    <!-- 未完成支付待支付显示组件 -->
    <!-- 暂时隐藏 -->
    <!-- <wait
      v-else
      :order-no="orderNo"
      :display-prompt="displayPrompt"
      :loading="isRefreshing"
      @refresh="refresh"
    /> -->
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { args } from '@youzan/tee-util/lib/common/url';
import moneyHelper from '@youzan/weapp-utils/lib/money';
import Error from './components/Error';
import OversaleMsg from './components/OversaleMsg';
import Tee from '@youzan/tee';

function parseJSON(data, defaultValue = {}) {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (e) {
      return defaultValue;
    }
  }
  return defaultValue;
}

function parseAggregatedRechargeData(payload) {
  const {
    extensions: {
      AGGREGATED_PAY_TYPE: aggregatedPayType,
      AGGREGATED_PAY_INFO: aggregatedPayInfo,
    } = {},
  } = payload;

  let payInfo = null;
  if (aggregatedPayType === 'RECHARGE') {
    const afterParsed = parseJSON(aggregatedPayInfo, null);
    payInfo = Array.isArray(afterParsed) ? afterParsed[0] : afterParsed;
  }
  return {
    // 充值信息
    rechargeInfo:
      aggregatedPayType === 'RECHARGE'
        ? {
            origin: aggregatedPayInfo,
            ...payInfo,
          }
        : null,
  };
}
export default {
  components: {
    error: Error,
    'oversale-msg': OversaleMsg,
  },

  data() {
    return {
      kdtId: 0,
      orderNo: '',
      hasPaid: false,
      showError: false,
      showOversale: false,
      isRefreshing: false,
      useTransparentBg: true,
      realPayAmount: 0,
      displayPrompt: {},
      hasSelfFetchOrder: false,
      showPriorUse: false,
      paidCodeInfoIsShow: false,
      emptyPaidExtHolderBlock: false,
    };
  },

  computed: {
    payTip() {
      return this.displayPrompt?.payTip ?? '';
    },
    bgTransparent() {
      return this.paidCodeInfoIsShow || this.useTransparentBg || !this.emptyPaidExtHolderBlock;
    },
  },

  created() {
    mapData(this, [
      'kdtId',
      'orderNo',
      'hasPaid',
      'isRefreshing',
      'paidCodeInfoIsShow',
      'emptyPaidExtHolderBlock',
    ]);
    mapData(
      this,
      {
        payResult: (val) => {
          const displayPrompt = val.displayPrompt || {};
          // UI走查问题，先处理掉，后续交给后端处理
          if (displayPrompt.payPrompt === '订单支付成功') {
            displayPrompt.payPrompt = '支付成功';
          }
          this.displayPrompt = displayPrompt;
          const { rechargeInfo } =
            parseAggregatedRechargeData({
              extensions: val.extension,
            }) || {};

          // 处理金额
          this.realPayAmount =
            typeof val.realPayAmount === 'number'
              ? moneyHelper(
                  (rechargeInfo ? rechargeInfo.payAmount : 0) + val.realPayAmount
                ).toYuan()
              : '';
          this.showError = !!val.showError;
          this.showOversale = !!val.showOversale;
          this.hasSelfFetchOrder = !!val.hasSelfFetchOrder;
          // 展示先用后付标
          if (val.payWay === 49) {
            this.showPriorUse = true;
          }
        },
        // 定义钩子通过回调被告知是否使用透明背景，产品思维，子模块的业务条件需要时告知我
        showAwardV2Block: (showAwardV2Block) => {
          this.useTransparentBg = !!showAwardV2Block;
        },
      },
      { isSetData: false }
    );
  },
  methods: {
    openPayDetailWv() {
      let installmentUrl = this.displayPrompt?.payTip?.url;
      if (installmentUrl) {
        this.ctx.logger.log({
          et: 'click',
          ei: 'installment_detail',
          en: '分期详情',
          si: this.kdtId,
        });

        /* #ifdef weapp */
        // args.add默认会做url编码
        installmentUrl = args.add('/pages/common/webview-page/index', {
          src: installmentUrl,
          title: '分期详情',
        });
        /* #endif */

        Tee.navigate({ url: installmentUrl });
      }
    },

    refresh() {
      this.ctx.event.emit('checkPay');
    },
  },
};
</script>

<style lang="scss">
.msg {
  position: relative;
  padding: 24px;
  background: #fff;
  text-align: center;
  color: #323233;
}

.trade-ump-v1 {
  background: transparent;
}

.pay-prompt-sub_title {
  margin-top: 8px;
  display: block;
  font-size: 12px;
  font-weight: 400;
  color: #969799;
  line-height: 16px;
}

.paid-success-info {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}
.success-tips {
  font-size: var(--eo-font-size-18, 18px);
  margin-right: 8px;
}

.price .num {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--eo-font-size-20, 20px);
  line-height: 27px;
  transform: translateY(1px);
  font-weight: var(--theme-common-price-font-weight, 800);
  font-family: var(--price-font-family, Avenir, sans-serif);
}

.price .num::before {
  content: '¥';
  font-size: var(--eo-font-size-16, 16px);
  margin-right: 2px;
  display: block;
  transform: translateY(1px);
}

.tips {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.detail {
  font-size: 12px;
  padding: 10px 0;
  color: #323233;
  display: flex;
  justify-content: center;

  &__text {
    color: #323233;
    margin-left: 2px;
  }
}
.prior-use {
  font-size: 14px;
  line-height: 20px;
  margin-top: 6px;
  color: #666;
  .prior-pay {
    margin-right: 4px;
  }
  .icon {
    display: inline-block;
    vertical-align: text-top;
    margin-right: 4px;
    width: 16px;
    background-size: cover;
    height: 16px;
    background-image: url(https://b.yzcdn.cn/assets-cashier/icon/prior-use-wechat2.png);
    position: relative;
    top: 1px;
  }
  .vertical {
    margin: 0 2px;
    position: relative;
    bottom: 1px;
    font-size: 12px;
  }
}
</style>
