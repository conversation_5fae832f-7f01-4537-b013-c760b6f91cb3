{"extensionId": "@wsc-tee-trade/cart-shop-header", "name": "@wsc-tee-trade/cart-shop-header", "version": "1.3.1", "bundle": "<builtin>", "widget": {"default": "ShopHeader"}, "data": {"provide": {"editMode": ["r", "w"], "currentAddress": ["r", "w"], "currentExpressType": ["r", "w"], "currentLocation": ["r", "w"]}, "consume": {"shopTitle": ["r"], "shopList": ["r", "w"], "showEmptyCart": ["r"], "shopMetaInfo": ["r"]}}, "process": {"invoke": ["navigateFromCart", "updateCartList", "tryLocation"]}, "event": {"emit": ["updateCartAsyncData"]}, "platform": ["web", "weapp"]}