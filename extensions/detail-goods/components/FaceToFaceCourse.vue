<template>
  <!-- 线下报名课程信息 -->
  <view class="order-detail__face2face">
    <view class="order-detail__face2face-des">该线下报名订单包含以下课程：</view>
    <van-card
      v-for="(item, index) in courses"
      :key="index"
      :desc="formatSku(item.extItemSkuDTO)"
      :title="item.goodsTitle"
      :price="formatPrice(item)"
      :origin-price="formatOriginPrice(item)"
      :num="item.num || 1"
      :thumb="item.imageUrl"
    >
      <view v-if="formatTag(item.alias)" slot="tags">
        <van-tag plain type="danger">
          <view class="tagClass">
            {{ formatTag(item.alias) }}
          </view>
        </van-tag>
      </view>
    </van-card>
  </view>
</template>

<script>
import { object } from '@youzan/tee-util';
import Card from '@youzan/vant-tee/dist/card/index.vue';
import formatMoney from '@youzan/utils/money/format';

export default {
  name: 'face-to-face-course-info',

  components: {
    'van-card': Card,
  },
  props: {
    order: Object,
  },
  computed: {
    courses() {
      return object.get(this.order, 'faceToFaceCourseInfo.courses', []);
    },
  },

  methods: {
    formatSku(sku) {
      if (sku && typeof sku === 'object') {
        return Object.keys(sku)
          .filter((item) => /^k\d+$/.test(item) && sku[item])
          .join(', ');
      }
      sku = Array.isArray(sku) ? sku : sku && typeof sku === 'string' ? JSON.parse(sku) : [];
      return sku
        .filter((item) => item.v)
        .map((item) => item.v)
        .join(', ');
    },

    formatPrice(item) {
      return formatMoney(object.get(item, 'extPriceDTO.currentTotalAmount', 0), true);
    },

    // 格式化原价，如果原价和优惠价相同则不显示了
    formatOriginPrice(item) {
      const price = object.get(item, 'extPriceDTO.currentTotalAmount', 0);
      const originPrice = object.get(item, 'extPriceDTO.originTotalAmount', 0);
      return price === originPrice ? null : formatMoney(originPrice, true);
    },

    // 格式化课程数量，规则200 -> 2, 240 => 2.4, 241 => 2.14
    formatCourseTime(num) {
      if (!num) return 0;
      const zeros = num.toString().split('').reverse();
      let count = 0;
      for (let i = 0; i < zeros.length; i++) {
        if (zeros[i] === '0') {
          count += 1;
        } else {
          break;
        }
      }
      if (count === 0) {
        return Math.abs(num / 100).toFixed(2);
      }
      if (count === 1) {
        return Math.abs(num / 100).toFixed(1);
      }
      return Math.abs(num / 100);
    },

    formatTag(alias) {
      const giveaways = object.get(this.order, 'faceToFaceCourseInfo.giveaways', []);
      const curGiveaways = giveaways.filter((item) => item.productAlias === alias);
      return curGiveaways
        .map((item) => {
          if (item.giveawayType === 1 && item.number) {
            // 赠品类型为 1 的时候 number 表示赠送天数
            return `赠${item.number}天`;
          }
          if (item.giveawayType === 2 && item.number) {
            // 赠品类型为 2 的时候 number 表示赠送课时数
            return `赠${this.formatCourseTime(item.number)}课时`;
          }
          return undefined;
        })
        .join(' ');
    },
  },
};
</script>

<style lang="scss">
.order-detail__face2face {
  &-des {
    font-size: 12px;
    padding: 12px 16px 3px 16px;
  }
}
.tagClass {
  color: #4990e2 !important;
  border-radius: 2px;
  border: 1px solid #4990e2 !important;
  background-color: #fff;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  padding: 0 4px;
  line-height: 16px;
}
</style>
