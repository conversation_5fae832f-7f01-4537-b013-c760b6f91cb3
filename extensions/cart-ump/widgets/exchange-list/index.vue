<template>
  <view :style="themeCss">
    <view class="exchange-list">
      <van-checkbox
        v-for="(item, index) in formatGoodsList"
        :key="index"
        :name="item.goodsId"
        :data-goods-id="item.goodsId"
        :value="item.checked"
        :checked-color="themeGeneralColor"
        @change="onChange(item.goodsId, /* #ifdef web */ { detail: $event } /* #endif */)"
        custom-class="list"
        label-class="exchange-label"
      >
        <view class="list__content">
          <view class="img-wrap">
            <image
              class="img"
              mode="aspectFit"
              :src="item.picture"
              @click.stop="goDetail(item.alias)"
            />
          </view>
          <view class="info">
            <view class="title">
              {{ item.title }}
            </view>
            <view v-if="item.desc" class="goods-sku">
              <view
                class="sku-info"
                :class="[item.thinSkus.length > 1 ? 'multi-sku' : '']"
                @click.stop="handleClickSku(item)"
              >
                <text class="sku-info__text">{{ item.desc }}</text>
                <van-icon
                  v-if="item.thinSkus.length > 1"
                  class="multi-sku-icon"
                  size="10px"
                  name="arrow-down"
                />
              </view>
            </view>
            <view class="goods-price" :style="priceStyle[index]">
              <price
                :price="item.exchangePrice"
                :theme-css="themeCss"
                :origin-price="item.originalPrice"
                :is-pop="isPop"
              />
            </view>
          </view>
        </view>
      </van-checkbox>
    </view>
    <van-toast ref="van-toast" />
  </view>
</template>

<script>
import Checkbox from '@youzan/vant-tee/dist/checkbox/index.vue';
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import { cdnImage, errorToast } from '@youzan/tee-biz-util';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import get from '@youzan/utils/object/get';
import money from '@youzan/weapp-utils/lib/money';
import { getGoodSkusJson } from '../../api';
import {
  getSelectedSkuFromSkuData,
  parseGoodsProperties,
  getSkuData,
  parseGoodsPropertyIdsByProperties,
  getPropPrice,
} from '../../utils';

export default {
  name: 'exchange-list',

  components: {
    'van-checkbox': Checkbox,
    'van-toast': VanToast,
  },

  props: {
    supportMulti: {
      type: Boolean,
    },
    goodsList: {
      type: Array,
      default: () => [],
    },
    currentSelectedSku: Object,
    themeCss: String,
    themeGeneralColor: String,
  },

  data() {
    return {
      themeClass: '',
      priceStyle: [],
      isShowSkuPopup: false,
      skuData: {}, // sku数据
      initialSku: {},
      goodsProperties: [],
      skuDataGoods: {}, // 当前显示sku商品
      exchangePrice: 0, // 换购价
      skuMessageConfig: {},
      skuExtraData: {
        useCustomHeaderPrice: true,
      },
    };
  },

  computed: {
    formatGoodsList() {
      return this.goodsList.map((item) => ({
        ...item,
        picture: cdnImage(item.picture, '!300x300.jpg'),
        desc: item.skuStr,
        checked: !!item.selected,
      }));
    },
  },

  watch: {
    currentSelectedSku(value) {
      if (value) {
        /** 属性加上 */
        if (typeof value.pluginsResult?.goodsAttributes === 'object' && value.skuData) {
          value.skuData.propertyIds = Object.values(value.pluginsResult.goodsAttributes)?.flat();
        }
        this.onConfirmSku(value.skuData || {});
      } else {
        this.skuData = null;
        this.skuDataGoods = null;
        this.initialSku = null;
      }
    },
  },

  methods: {
    onConfirmSku(selectedSku) {
      const { skuData: skuInfo, skuDataGoods } = this;
      if (skuInfo && skuDataGoods) {
        const skuArr = getSelectedSkuFromSkuData(skuInfo.tree, selectedSku.selectedSkuComb);
        const skuData = getSkuData(selectedSku.selectedSkuComb);
        const skuStr = skuArr.map((sku) => sku.name).join(';');

        const propStr = [];
        if (skuInfo.itemSalePropList?.length > 0) {
          for (const p of skuInfo.itemSalePropList) {
            if (!p || !Array.isArray(p.v) || p.v.length === 0) {
              continue;
            }
            for (const i of p.v) {
              if (!i) {
                continue;
              }
              if ((selectedSku.propertyIds || []).includes(i.id) && i.name) {
                propStr.push(i.name);
              }
            }
          }
        }
        const properties = get(selectedSku.selectedSkuComb, 'properties', []);

        const picture = (skuArr[0] && skuArr[0].imgUrl) || skuDataGoods.picture;

        const { messages, selectedSkuComb, propertyIds } = selectedSku;
        const { id: skuId, price, noneSku } = selectedSkuComb;
        const formatMessages = Object.keys(messages).map((key) => messages[key]);

        // https://jira.qima-inc.com/browse/ONLINE-618310
        // 单SKU商品price是string类型(67.00), 多SKU商品price是number类型(6700) 需要统一单位
        // 单SKU转分 多SKU不变
        const formatPrice = noneSku ? money(price).toCent() : price;

        let propPrice = 0;
        if (propertyIds?.length > 0) {
          propPrice = getPropPrice(skuInfo?.itemSalePropList, propertyIds);
        }

        const originalPrice = skuDataGoods?.thinSkus?.find(
          (sku) => sku.id === selectedSku.selectedSkuComb?.id
        )?.originalPrice;

        this.updateGoodsListData(skuDataGoods, {
          skuStr: skuStr + (propStr?.length > 0 ? '，' + propStr.join('，') : ''),
          properties: parseGoodsProperties(properties),
          propertyIds: selectedSku?.propertyIds,
          messages: formatMessages.length ? JSON.stringify(formatMessages) : '',
          skuId,
          skuData,
          exchangePrice: parseInt(Math.round(formatPrice + propPrice), 10),
          originalPrice: originalPrice
            ? parseInt(Math.round(originalPrice + propPrice), 10)
            : originalPrice,
          selected: true,
          picture,
        });

        this.$emit('close-sku');
        this.skuDataGoods = null;
        this.skuData = null;
        this.initialSku = null;
      }
    },

    // 获取商品sku
    fetchGoodsSku(goods) {
      const goodsSkuIds = goods.thinSkus.map((item) => item.id);

      getGoodSkusJson({
        alias: goods.alias,
      })
        .then((skuData) => {
          skuData = mapKeysCase.toCamelCase(skuData);
          const goodsProperties = get(skuData, 'itemSalePropList', []);

          const isShowSkuPopup =
            !skuData.noneSku ||
            !!(skuData.messages && skuData.messages.length) ||
            !!goodsProperties.length;

          // 过滤不可用sku
          skuData.list = skuData.list.filter((item) => goodsSkuIds.indexOf(item.id) > -1);

          // ================ 设置sku换购价
          const thinSku = goods.thinSkus.find((item) => item.id === skuData.collectionId) || {};
          skuData.price = thinSku.exchangePrice
            ? money(thinSku.exchangePrice).toYuan()
            : skuData.price;

          skuData.list.forEach((sku) => {
            const thinSku = goods.thinSkus.find((item) => item.id === sku.id) || {};
            sku.price = thinSku.exchangePrice || sku.price;
          });

          // ================ 设置sku换购价 end

          if (!isShowSkuPopup) {
            this.updateGoodsListData(goods, {
              selected: true,
            });
          }

          this.skuDataGoods = goods;
          this.skuData = skuData;
          this.goodsProperties = goodsProperties;

          // 换购商品的商品属性不参与最后的价格计算
          const goodsAttributes = skuData.itemSalePropList || [];

          // goods.properties为空，formatPropertyIds就是空的，反而goods.propertyIds是有数据的，原先这里这么写是错误的
          // 影响：购物车场景下的换购商品如果带有商品属性，就无法展示
          const formatPropertyIds = parseGoodsPropertyIdsByProperties(goods.properties);

          let propertyIds = goods.propertyIds || [];

          // 担心有其他地方会用到formatPropertyIds，如果formatPropertyIds有值就用formatPropertyIds，如果没有formatPropertyIds就用goods.propertyIds
          if (formatPropertyIds.length > 0) {
            propertyIds = formatPropertyIds;
          }

          // 如果选择完会更新goodsList，messages的格式不对，如果是数组需要转换一下
          const goodsMessages = JSON.parse(goods.messages || '{}');
          let initialMessages = {};

          if (goodsMessages instanceof Array) {
            (skuData.messages || []).forEach((item, index) => {
              initialMessages[item.name] = goodsMessages[index];
            });
          } else {
            initialMessages = goodsMessages;
          }

          const data = {
            sku: {
              ...skuData,
              limit: { quota: 1 },
            },
            goodsAttributes,
            goods: {
              ...goods,
              propertyIds,
              id: goods.id || goods.goodsId,
            },
            messageConfig: {
              initialMessages,
            },
            skuConfig: {
              buyText: '确定',
              skuSence: 'buy',
            },
          };

          const initialSku = skuData.list?.find((item) => item.id === goods.skuId) ?? {};

          this.$emit('change-sku-show', {
            ...data,
            initialSku: {
              ...initialSku,
              propertyIds,
              properties: goods.properties,
              selectedNum: 1,
            },
            event: {
              skuScene: 'buy',
              skuOptions: {
                resetSku: true,
              },
            },
            goodsAttributes: { reset: true },
          });
        })
        .catch((error) => {
          errorToast(error, { message: '商品信息获取失败，请稍后重试' });
        });
    },

    // 选择换购商品 e: checked
    onChange(goodsId, { detail: checked }) {
      const currentGoods = this.goodsList.find((goods) => goods.goodsId === goodsId);

      if (checked) {
        if (!this.supportMulti) {
          this.goodsList.forEach((goods) => {
            this.updateGoodsListData(goods, {
              selected: false,
            });
          });
        }
        currentGoods && this.fetchGoodsSku(currentGoods);
      } else {
        currentGoods &&
          this.updateGoodsListData(currentGoods, {
            skuStr: '',
            exchangePrice: currentGoods.reserveExchangePrice,
            originalPrice: currentGoods.reserveOriginalPrice,
            selected: false,
          });
      }
    },

    // 添加已选换购商品
    updateGoodsListData(currentGoods, options = {}) {
      this.$emit('updateGoodsListData', { ...currentGoods, ...options });
    },

    handleClickSku(item) {
      if (item.thinSkus.length <= 1) return;
      this.fetchGoodsSku(item);
    },

    // 跳转到商品详情页
    goDetail(alias = '') {
      let link = `/pages/goods/detail/index?alias=${alias}`;

      /* #ifdef web */
      link = `/wscgoods/detail/${alias}`;
      /* #endif */

      this.$emit('to-goods-detail', { link });
    },
  },
};
</script>

<style lang="scss" scoped>
.exchange-list {
  height: 56vh;
  min-height: 56vh;
  overflow: scroll;
}
.exchange-label {
  flex: 1;
  width: 100%;
}
.list {
  margin-bottom: 12px;
  border-radius: var(--theme-radius-card, 8px);
  background: #fff;
  display: flex;
  align-items: center;
  padding: 12px 0 12px 12px;

  &__content {
    display: flex;
    align-items: flex-start;

    .img-wrap {
      width: 96px;
      height: 96px;
      float: left;
      position: relative;
      margin-left: auto;
      margin-right: auto;
      overflow: hidden;
      background: #fff;
      background-size: cover;
      border-radius: 4px;

      .img {
        width: 96px;
        height: 96px;
      }
    }

    .info {
      flex: 1;
      position: relative;
      width: 100%;
      margin-left: 8px;
      padding-right: 12px;
      height: 96px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &:not(:last-child) {
        border-bottom: 1px solid #f2f2f2;
      }

      .title {
        margin-bottom: 8px;
        line-height: 17px;
        color: #323233;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods-sku {
        margin-bottom: 4px;
        color: #969799;

        .sku-info {
          display: inline-block;
          line-height: 16px;
          font-size: 12px;
          max-width: 200px;

          &__text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            width: 100%;
            vertical-align: middle;
          }

          &.multi-sku {
            padding: 4px 8px;
            background: #f7f8fa;
            border-radius: 4px;
          }

          text {
            vertical-align: middle;
          }

          .multi-sku-icon {
            margin-left: 8px;
            vertical-align: middle;
          }
        }
      }
    }

    .goods-tag {
      border: 1px solid #ee0a24;
      border-radius: var(--theme-radius-card, 8px);
      font-size: 10px;
      color: #ee0a24;
      line-height: 14px;
      padding: 0 4px;
      text-align: center;
    }

    .goods-price {
      padding-right: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .exchange-price {
        font-size: 14px;
        color: #f44;
        margin-right: 5px;
      }

      .original-price {
        text-decoration: line-through;
        color: #999;
      }
    }
  }

  .msg-item {
    background-color: #f7f8fa;

    &:not(:last-child) {
      .t-cell {
        border-bottom: 1px solid #f2f2f2;
      }
    }

    .t-cell {
      margin-left: 15px;
      padding: 10px 15px 10px 0;
      background-color: #f7f8fa;

      &.t-cell--required::before {
        left: -7px;
      }
    }
  }
}
</style>
