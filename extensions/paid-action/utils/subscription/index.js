/* eslint-disable no-use-before-define */
import { requestV2 } from '@youzan/tee-biz-request';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import env from '@youzan/wsc-tee-trade-common/lib/env';
import { buildUrl, cdnImage } from '@youzan/tee-biz-util';

const defaultTitle = '订阅物流';
export const YOUZAN_MARS_MODEL = {
  title: defaultTitle,
  type: 'youzanmars',
  content: [
    [] /* 前置文案 */,
    [cdnImage('pay-result/images/09acc6205b98bb2681d5d71b5f5793e26b515a.png')] /* 图片 */,
    ['下载有赞精选 App，查看订单物流状态'] /* 后置文案 */,
  ],
};

function generateMpAccount({ kdtId, text, imageUrl, retryCount = 0 }) {
  return new Promise((resolve) => {
    requestV2({
      path: '/v3/weapp/mp-account.json',
      data: { kdt_id: kdtId },
    })
      .then((data) => {
        if (data) {
          let qrcode = '';
          if (imageUrl) {
            qrcode = imageUrl;
          } else if (data.qrcodeUrl) {
            qrcode = buildUrl(
              `/v2/weixin/scan/wximg.jpeg?s=${encodeURIComponent(data.qrcodeUrl)}`,
              'h5'
            );
          }

          resolve({
            title: defaultTitle,
            type: 'focusMpAccount',
            content: [[] /* 前置文案 */, [qrcode] /* 图片 */, [text] /* 后置文案 */],
          });
        } else {
          resolve(delayContinue({ type: 'mp-account', kdtId, text, imageUrl, retryCount }));
        }
      })
      .catch(() => {
        resolve(delayContinue({ type: 'mp-account', kdtId, text, imageUrl, retryCount }));
      });
  });
}

function generateWxCode({ kdtId, retryCount = 0 }) {
  return new Promise((resolve) => {
    requestV2({
      url: '/wsctrade/poster/generate-wxcode.json',
      method: 'POST',
      data: {
        kdtId,
        page: 'pages/home/<USER>/index',
      },
    })
      .then((data) => {
        if (data) {
          const qrcode = 'data:image/png;base64,' + data;
          resolve({
            title: defaultTitle,
            type: 'addMiniProgram',
            content: [
              [] /* 前置文案 */,
              [qrcode] /* 图片 */,
              ['长按访问小程序，查看订单物流'] /* 后置文案 */,
            ],
          });
        } else {
          resolve(delayContinue({ type: 'wxcode', kdtId, retryCount }));
        }
      })
      .catch(() => {
        resolve(delayContinue({ type: 'wxcode', kdtId, retryCount }));
      });
  });
}

function delayContinue({ type, kdtId, imageUrl, text, retryCount = 0 }) {
  // 轮询接口，响应后每秒一次，最多3次

  return new Promise((resolve) => {
    if (++retryCount < 3) {
      setTimeout(() => {
        const fn = type === 'wxcode' ? generateWxCode : generateMpAccount;
        resolve(fn({ kdtId, text, imageUrl, retryCount }));
      }, 1000);
    } else {
      Toast(type === 'wxcode' ? '获取小程序二维码失败' : '获取公众号二维码失败');
      resolve();
    }
  });
}

export function buildSubscriptionModel({ mpData = {}, kdtId }) {
  let model;

  /* #ifdef web */
  // 支付宝小程序
  if (env.isAlipayApp) {
    model = {
      title: defaultTitle,
      type: 'aliPayApp',
      content: [
        [] /* 前置文案 */,
        [cdnImage('upload_files/2021/07/14/FoAeSWXiX1kBOi8cKYcLXj6TmdGj.gif')] /* 图片 */,
        ['收藏店铺小程序，到支付宝首页我的小程序，查看订单物流'] /* 后置文案 */,
      ],
    };
  } else if (env.isQQApp) {
    // QQ小程序
    model = {
      title: defaultTitle,
      type: 'aliPayApp',
      content: [
        [] /* 前置文案 */,
        [cdnImage('public_files/90883fa2a839d50bd38d31d14437e9b3.gif')] /* 图片 */,
        ['收藏店铺小程序，到支付宝首页我的小程序，查看订单物流'] /* 后置文案 */,
      ],
    };
  } else if (env.isKuaiShou) {
    // 快手APP
    model = {
      title: defaultTitle,
      type: 'kuaiShou',
      content: [
        ['您可以在“设置”-“快手小店”-“我的订单”中查看订单物流状态'] /* 前置文案 */,
        [cdnImage('pay-result/images/kaishou.png')] /* 图片 */,
        [] /* 后置文案 */,
      ],
    };
  } else if (env.isAlipay) {
    // 支付宝APP
    model = {
      title: defaultTitle,
      type: 'alipay',
      content: [
        ['您可以在支付宝中搜索关注“有赞”生活号，查看订单物流状态'] /* 前置文案 */,
        [cdnImage('pay-result/images/alipay.png')] /* 图片 */,
        [] /* 后置文案 */,
      ],
    };
  } else if (env.isXmlyApp) {
    // 喜马拉雅APP
    model = {
      title: defaultTitle,
      type: 'xmly',
      content: [
        // eslint-disable-next-line
        ['你可以在“账号-我的订单-更多订单-其他订单“中查看订单物流状态'] /* 前置文案 */,
        [cdnImage('upload_files/2020/03/24/FmPAiLQNL0LUbA_mGi2uY0LTf5Mk.png')] /* 图片 */,
        [] /* 后置文案 */,
      ],
    };
  } else if (env.isDouYinApp) {
    // 抖音APP
    model = {
      title: defaultTitle,
      type: 'douyin',
      content: [
        ['长按保存二维码，打开微信扫码，查看订单物流'] /* 前置文案 */,
        [
          cdnImage('upload_files/2020/04/02/Fop5s6FiRp5RwtcasAiogm-EojJ8.png'),
          cdnImage('upload_files/2020/04/02/FtuhN0pPLBaM4Y3lE5_Am7ezv2XQ.png'),
        ] /* 图片 */,
        [] /* 后置文案 */,
      ],
    };
  } else if (env.isYouzanmars || env.isYouzanwxd || env.isWeibo || env.isZhihu) {
    // 有赞精选APP，微小店，微博，知乎等按照精选的规则展示
    model = {
      ...YOUZAN_MARS_MODEL,
    };
  } else {
    // 端比较完了，进入业务比较环节

    let promise;
    Toast.loading();
    if (mpData.followWeChatMp) {
      // 判断店铺是否绑定了小程序
      // isSubscriptionShow模式不展示这块内容，有点悬疑 TODO @陈佳明
      promise = generateWxCode({ kdtId });
    } else if (mpData.mpId) {
      // 判断店铺是否绑定了公众号
      promise = generateMpAccount({
        kdtId,
        text: mpData.fans
          ? `${env.isWeixin ? '长按' : ''}访问店铺公众号，查看订单物流`
          : `${env.isWeixin ? '长按' : '扫码'}关注店铺公众号，查看订单物流`,
      });
    } else {
      // 没有绑定商家公众号/小程序则报错提示
      promise = Promise.resolve(null);
    }

    return promise.then((res) => {
      Toast.clear();
      return res;
    });
  }
  /* #endif */

  /* #ifdef weapp */
  model = {
    title: defaultTitle,
    type: 'miniProgram',
    content: [
      ['添加店铺小程序到我的小程序，查看订单物流'] /* 前置文案 */,
      [cdnImage('upload_files/2020/08/04/FnELPhb4UriBAY1CQ9M6eavAYo25.gif')] /* 图片 */,
      [] /* 后置文案 */,
    ],
  };
  /* #endif */

  return Promise.resolve(model);
}
