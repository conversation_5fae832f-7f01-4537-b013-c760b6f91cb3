import FissionCouponUmp from './components/FissionCouponUmp';
import InSourcingCouponUmp from './components/InSourcingCouponUmp';
import FlowChangeUmp from './components/FlowChangeUmp';
import PaidPromotionBanner from './components/PaidPromotionBanner';
import NewAwardBlock from './components/NewAwardBlock';
import ActivateBenefitcardDialog from './components/ActivateBenefitcardDialog';

export default class PaidUmpBlock {
  constructor(options) {
    this.ctx = options.ctx;
  }

  static component = {
    FissionCouponUmp,
    InSourcingCouponUmp,
    FlowChangeUmp,
    PaidPromotionBanner,
    NewAwardBlock,
    ActivateBenefitcardDialog,
  };
}
