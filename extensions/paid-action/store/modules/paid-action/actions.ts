import toCamelCase from '@youzan/utils/string/toCamelCase';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import get from '@youzan/utils/object/get';
import cloneDeep from '@youzan/weapp-utils/lib/clone-deep';
import env from '@youzan/wsc-tee-trade-common/lib/env';
import Tee from '@youzan/tee';
import { args } from '@youzan/tee-util/lib/common/url';
import {
  BIZ_TYPE,
  BTNS_MAP,
  BUTTON_CLASS,
  GROUP_BUY_DEFAULT_BTNS,
  STANDARD_BTN_GROUP,
} from '../../../constans';
import { getButtonClass } from '../../../util';
import { buyAgain } from '../../../utils/buy-again';
/* #ifdef web */
import { fetchYouzanFollowWhiteList } from '../../../api';

const hasPrescriptionDrugGood = get(window, '_global.hasPrescriptionDrugGood', false);
/* #endif */

export default function (ctx: any) {
  return {
    fetchYouzanFollowWhiteList() {
      /* #ifdef web */
      fetchYouzanFollowWhiteList().then((res) => {
        this.isHideYouzanFollow = res;
      });
      /* #endif */
    },
    processPaidAction({ type, btnBizType = BIZ_TYPE.DEFAULT, url }) {
      if (type === BTNS_MAP.BUY_AGAIN || type === BTNS_MAP.DIRECT_BUY_AGAIN) {
        buyAgain({
          ctx,
          type,
          kdtId: this.kdtId,
          orderNo: this.orderNo,
          orderItems: this.orderItems,
          logger: ctx.logger,
        });
      } else if (type === BTNS_MAP.DETAIL) {
        // 查看详情
        ctx.logger.log({
          et: 'click',
          ei: 'click_button_chakandingdan',
          en: '“查看订单”按钮点击',
          si: this.kdtId,
        });

        ctx.event.emit('gotoOrderDetail');
      } else if (type === BTNS_MAP.SUBSCRIPTION) {
        // 订阅物流
        ctx.logger.log({
          et: 'click',
          ei: 'click_button_wuliu',
          en: '“订阅物流”按钮点击',
          si: this.kdtId,
        });
        /* #ifdef weapp */
        if (ctx.data.showWeappSubscribe) {
          return ctx.event.emit('handlerWeappSubscription');
        }
        /* #endif */

        ctx.event.emit('showSubscriptionDialog');
      } else if (type === BTNS_MAP.SAVE_PICK_UP_CODE) {
        // 查看提货码
        if (btnBizType === BIZ_TYPE.DEFAULT) {
          ctx.event.emit('saveSelfFetchPoster');
        }
      } else if (type === BTNS_MAP.SAVE_CARD_VOUCHER) {
        // 查看卡券
        ctx.event.emit('saveVoucherCardPoster');
      } else if (type === BTNS_MAP.SHARE) {
        // 分享一下
        if (btnBizType === BIZ_TYPE.DEFAULT) {
          console.log('emit showTradeShareDialog');
          ctx.event.emit('showTradeShareDialog');
        }
      } else if (type === BTNS_MAP.TAKE_GOODS_CODE) {
        // 查看取货口令
        ctx.event.emit('showTakeGoodsDialog');
      } else if (
        [
          BTNS_MAP.GIVE_AWAY,
          BTNS_MAP.EXHIBITION_RESERVE_SHARE,
          BTNS_MAP.CARD_VOUCHER_EXHIBITION_DETAIL,
        ].includes(type)
      ) {
        // 活动预定详情支持小程序
        if (
          type === BTNS_MAP.CARD_VOUCHER_EXHIBITION_DETAIL ||
          type === BTNS_MAP.EXHIBITION_RESERVE_SHARE
        ) {
          // 路径示例 https://h5.youzan.com/wscshop/booking?kdt_id=92732278#/detail?activityId=5022800&appIdentity=14200&voucherAlias=n20e20zx
          const res = url.split('?');
          const kdtIdParam = res?.[1]?.split('#')?.[0];
          const restParam = res[2];
          const weappUrl = `/packages/retail-shelf/wine-tasting-detail/index?${kdtIdParam}&${restParam}`;

          /* #ifdef web */
          const isWeapp = window?._global?.miniprogram?.isWeapp;
          if (isWeapp) {
            // @ts-ignore
            ZNB.navigate({
              url,
              weappUrl,
              type: 'redirectTo',
            }).then(() => {
              // @ts-ignore
              ZNB.navigate({ url });
            });
            return;
          }
          /* #endif */
          /* #ifdef weapp */
          Tee.$native.navigateTo({
            url: weappUrl,
          });
          return;
          /* #endif */
        }

        // 立即赠送，默认行为直接跳转出去，只有H5环境需要处理
        /* #ifdef web */
        navigate({
          web: {
            type: 'safeLink',
            safeLink: {
              url,
            },
          },
        });
        /* #endif */
      } else if (type === BTNS_MAP.PURCHASE_TAKE) {
        ctx.event.emit('gotoIndentorShipment');
      } else if (type === BTNS_MAP.PURCHASE_CENTER) {
        ctx.event.emit('gotoIndentorCenter');
      } else if (type === BTNS_MAP.KEEP_SHOPPING) {
        ctx.dmc.switchTab('Home', {
          kdt_id: this.kdtId,
        });
      }
    },
    // 获取业务按钮配置
    getSourceBtnMap() {
      const { buttonGroup: buttons = {}, isGroupBuy } = this.payResult;
      let btnMap = cloneDeep(buttons);
      // 社区团购订单目前buttonGroup做特殊处理
      if (isGroupBuy) {
        btnMap = GROUP_BUY_DEFAULT_BTNS;
      }

      let directBuyAgainBtnConfig;
      /* #ifdef web */
      directBuyAgainBtnConfig = get(window, '_global.directBuyAgainBtnConfig', {});
      /* #endif */
      /* #ifdef weapp */
      directBuyAgainBtnConfig = this.payResult.directBuyAgainBtnConfig || {};
      /* #endif */
      // 存在DIRECT_BUY_AGAIN，则不展示BUY_AGAIN
      const { orderItems = [] } = this.payResult;
      const goodsIds = [];
      orderItems.forEach((item) => {
        goodsIds.push(item.goodsId);
      });
      const logParams = {
        et: 'view',
        ei: 'new_buy_again_view',
        en: '新再来一单曝光',
        pt: 'ol',
        params: {
          order_no: this.orderNo,
          goods_id: goodsIds,
        },
      };
      if (btnMap?.DIRECT_BUY_AGAIN && directBuyAgainBtnConfig.show && !env.isYouzanmars) {
        // 埋点
        (logParams.params as Record<string, any>).is_new = true;
        ctx.logger.log(logParams);
        delete btnMap.BUY_AGAIN;
      } else {
        // 存在老版本再来一单时曝光埋点
        if (!!btnMap.BUY_AGAIN) {
          (logParams.params as Record<string, any>).is_new = false;
          ctx.logger.log(logParams);
        }
        delete btnMap.DIRECT_BUY_AGAIN;
      }

      // 未支付场景，只显示查看详情，这段逻辑可以扔给后端 - TODO
      if (!this.hasPaid) {
        delete btnMap.SUBSCRIPTION;
      }

      return btnMap;
    },
    // 业务场景屏蔽按钮逻辑
    filterBtns() {
      let buttonGroups = STANDARD_BTN_GROUP.slice();

      /* #ifdef weapp */
      const appOptions = Tee.getAppOptions() as Record<string, any>;
      // 视频号场景值下不展示分享按钮1177（视频号直播间）和1175 （视频号profile页）
      if (appOptions && [1177, 1175, 1195].includes(appOptions.scene)) {
        buttonGroups = buttonGroups.filter((key) => key !== BTNS_MAP.SHARE);
      }
      // 云定制 默认为false
      if (this.hidePickUpCodeBtn) {
        buttonGroups = buttonGroups.filter((item) => item !== 'SAVE_PICK_UP_CODE');
      }
      /* #endif */

      /* #ifdef web */

      // 赞拼拼订单不展示任何按钮
      if ((env as Record<string, any>)?.isFxZpp) {
        return [];
      }

      // 有赞精选不展示订阅按钮,百度小程序,APP环境不展示订阅按钮
      if (
        env.isYouzanmars ||
        env.isSwanApp ||
        env.isThirdApp ||
        (env as Record<string, any>).isXhsApp
      ) {
        buttonGroups = buttonGroups.filter((item) => item !== 'SUBSCRIPTION');
      }
      // 喜玛朗雅不展示订单分享按钮
      if (env.isXmlyApp) {
        buttonGroups = buttonGroups.filter((item) => item !== 'SHARE');
      }
      // 处方订单不展示查看提货码
      if (hasPrescriptionDrugGood) {
        buttonGroups = buttonGroups.filter((item) => item !== 'SAVE_PICK_UP_CODE');
      }
      // 有赞客、有赞热卖小程序、头条小程序、新有赞精选APP和新有赞精选小程序不展示订阅物流、再来一单、分享一下
      const url = window.location.href;
      const zzChannel = args.get('zzChannel', url) || args.get('zz_channel', url);
      const isYzRm = zzChannel === 'yzrm'; // 是否是有赞热卖小程序
      const isNewYzMars = ['app-mars', 'weapp-mars'].indexOf(zzChannel) > -1; // 是否是新有赞精选APP和新有赞精选小程序
      if (
        env.isYouzanke ||
        isYzRm ||
        env.isTTApp ||
        isNewYzMars ||
        (env as Record<string, any>).isKsApp
      ) {
        buttonGroups = buttonGroups.filter(
          (item) => ['SUBSCRIPTION', 'BUY_AGAIN', 'SHARE', 'DIRECT_BUY_AGAIN'].indexOf(item) < 0
        );
      }
      /* #endif */

      return buttonGroups;
    },
    updateBtnList() {
      const { isGroupBuy, orderTags = {} } = this.payResult;
      // 获取业务数据的按钮配置项
      const orderTypeButton = this.getSourceBtnMap();
      // 按照业务场景过滤按钮
      const BTN_GROUP = this.filterBtns();

      const btnList = BTN_GROUP.filter((key) => !!orderTypeButton[key]).map((key, index, arr) => {
        const className = getButtonClass(arr, key);
        const btnBizType = isGroupBuy
          ? BIZ_TYPE.GROUPBUY
          : orderTags.SOLITAIRE_BUY
          ? BIZ_TYPE.SOLITAIRE_BUY
          : BIZ_TYPE.DEFAULT;
        const btnItem = orderTypeButton[key];

        return {
          ...btnItem,
          block: className === BUTTON_CLASS.primary,
          btnBizType,
          className,
        };
      });
      const renderBtns = Array.from(
        new Set(
          btnList.map(({ type }) => {
            if (type === BTNS_MAP.DIRECT_BUY_AGAIN) {
              return BTNS_MAP.BUY_AGAIN.toLocaleLowerCase();
            }
            return type.toLocaleLowerCase();
          })
        )
      );
      ctx.cloud
        .invoke('beforeDisableOrderPaidBtns', {
          btns: renderBtns,
        })
        .then(({ disableBtns = [] } = {}) => {
          this.btnList = btnList.filter((_) => {
            if (disableBtns.includes('buyAgain')) {
              // 如果存在buyAgain,则把directBuyAgain也加入进去
              disableBtns.push('directBuyAgain');
            }
            return !disableBtns.includes(toCamelCase(_.type.toLocaleLowerCase()));
          });
        })
        .catch((e) => {
          this.btnList = btnList;
          console.log('hook:beforeDisableOrderPaidBtns三方报错', e);
        });
    },
  };
}
