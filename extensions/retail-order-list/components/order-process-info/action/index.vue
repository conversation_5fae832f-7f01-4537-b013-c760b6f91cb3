<template>
  <view class="action-item" @click.stop="onClick">
    <view class="action-item-icon">
      <van-icon :name="icon" size="20px" />
    </view>
    <view>{{ text }}</view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { actionHandler } from './actionHelper';

export default {
  name: 'action-item',
  components: {
    'van-icon': Icon,
  },
  props: {
    icon: String,
    text: String,
    type: String,
    opt: Object,
  },

  methods: {
    onClick() {
      actionHandler(this.type, this.opt);
    },
  },
};
</script>

<style lang="scss" scoped>
.action-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-left: 16px;
  line-height: 16px;
  &-icon {
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f6f6f6;
    border-radius: 50%;
    text-align: center;
    color: #000;
    font-size: 14px;
    padding-top: 4px;
    box-sizing: border-box;
    margin-bottom: 2px;
  }
}
</style>
