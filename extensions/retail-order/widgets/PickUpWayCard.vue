<template>
  <view class="pick-up-way" v-if="pickUpWays.length">
    <view class="pick-up-way-list" v-if="pickUpWays.length === 2">
      <view class="pick-up-way-item" v-for="item in pickUpWays" :key="item.value">
        <express-way-card
          :mode="'card'"
          :active="item.value === value"
          :theme-colors="themeColors"
          :icon="item.icon"
          :title="item.name"
          @onSelect="onSwitchPickUpWay(item.value)"
        />
      </view>
    </view>
    <!--一种提货方式有效-->
    <view v-else-if="pickUpWays.length === 1">
      <!-- 仅支持一种配送方式：快递/同城 -->
      <express-way-card
        :mode="'line'"
        :theme-colors="themeColors"
        :icon="pickUpWays[0].icon"
        :title="pickUpWays[0].name"
      />
    </view>
    <view v-if="showRetailPickUpWayErrorToast" class="error">请选择提货方式</view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      value: this.ctx.data.retailPickUpWayValue || '',
      pickUpWays: this.ctx.data.retailPickUpWays || [],
      showRetailPickUpWayErrorToast: false,
      themeColors: {},
    };
  },

  created() {
    mapData(this, ['showRetailPickUpWayErrorToast', 'themeColors']);
  },

  methods: {
    onSwitchPickUpWay(val) {
      if (val === this.value) return;
      this.value = val;
      this.ctx.data.showRetailPickUpWayErrorToast = false;
      const pickUpWay = this.pickUpWays.find((item) => item.value === val);
      this.ctx.process.invoke('updatePickUpWay', pickUpWay);
    },
  },
};
</script>

<style lang="scss" scoped>
.pick-up-way {
  background: #fff;
}
.pick-up-way-list {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px 16px;
  .pick-up-way-item {
    width: calc(50% - 6px);
  }
}

.error {
  padding: 0 12px;
  font-size: 12tpx;
  font-weight: 400;
  line-height: 20tpx;
  text-align: right;
  color: #f00;
}
</style>
