import { isGoodsCheckboxEnable, isEduIosOnlineGoods } from '../../../utils';
import hexToRgba from '@youzan/utils/string/hexToRgba';

export default {
  kdtId() {
    return this.shopCart.kdtId;
  },
  // 编辑模式启用 0为非编辑模式
  currentEditKdtId() {
    if (this.editMode === 'edit') {
      return this.shopGoods.kdtId;
    }
    return 0;
  },
  themeGeneralColor() {
    return this.themeColors.general;
  },
  themeGeneralAlpha10Color() {
    return hexToRgba(this.themeGeneralColor, 0.1);
  },
  isEditing() {
    return !!this.currentEditKdtId;
  },
  hasGoods() {
    return (this.shopGoods.goodsGroupList || []).some(
      (goodsGroup) => !!goodsGroup.goodsList.length
    );
  },
  computedGoodsGroupList() {
    return this.hasGoods
      ? this.shopGoods.goodsGroupList.map((goodsGroup, i) => ({
          ...goodsGroup,
          uniqId: i,
        }))
      : [];
  },
  provideData() {
    if (!this.shopGoods) {
      return {
        checkedGoodsList: [],
        isCheckedAll: false,
      };
    }
    const checkedGoodsList = [];
    let isCheckedAll = true;

    if (this.isEditing) {
      (this.shopGoods.goodsGroupList || []).forEach((goodsGroup) => {
        (goodsGroup.goodsList || []).forEach((goods) => {
          // 编辑状态不判断是否可选
          if (this.editCheckedGoods[goods.cartId + '']) {
            checkedGoodsList.push(goods);
          } else {
            isCheckedAll = false;
          }
        });
      });
    } else {
      (this.shopGoods.goodsGroupList || []).forEach((goodsGroup) => {
        const filterGoodsList = (goodsGroup.goodsList || []).filter(
          (goods) => !isEduIosOnlineGoods(goods)
        );
        if (!filterGoodsList.length) {
          return;
        }
        filterGoodsList.forEach((goods) => {
          if (goods.checked) {
            // 不可选不算在全选范围内
            if (isGoodsCheckboxEnable(goods)) checkedGoodsList.push(goods);
          } else {
            isCheckedAll = false;
          }
        });
      });
    }

    return {
      checkedGoodsList,
      isCheckedAll: checkedGoodsList.length ? isCheckedAll : false,
    };
  },
  checkedGoodsList() {
    return this.provideData.checkedGoodsList;
  },
  showEstimatedPrice() {
    return this.shopCart.isShowEstimatedPrice;
  },
};
