import { ALL_BTN_TYPES, BTN_CONF } from './utils/order-buttons';

export const cloudInvokeBeforeOrderBtnsRender = (ctx, payload) => {
  const { orderNo, actionBtns } = payload;
  let processedBtns = [...actionBtns];
  return (
    ctx.cloud
      // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
      .invoke('beforeOrderBtnsRender', {
        btns: processedBtns.map((_) => {
          return {
            name: _.text,
            type: _.value === BTN_CONF.directBuyAgain ? BTN_CONF.buyAgain : _.value,
          };
        }),
        orderNo,
      })
      .then((beforeOrderBtnsRenderReturn) => {
        if ((beforeOrderBtnsRenderReturn || {}).btns) {
          const filterBtns = beforeOrderBtnsRenderReturn.btns.filter(
            (_) => ALL_BTN_TYPES.includes(_.type) || _.type.startsWith('cloud_')
          );
          processedBtns = filterBtns
            .map((item) => {
              if (item.type.startsWith('cloud_')) {
                return {
                  text: item.name,
                  value: item.type,
                };
              }
              const matched = processedBtns.find((_) => {
                if (
                  item.type === BTN_CONF.buyAgain &&
                  [BTN_CONF.buyAgain, BTN_CONF.directBuyAgain].includes(_.value)
                ) {
                  return true;
                }
                return _.value === item.type;
              });
              return matched;
            })
            .filter((_) => _ !== undefined);
        }
      })
      .catch((e) => {
        console.warn(e);
      })
      .then(() => processedBtns)
  );
};
