{"extensionId": "@wsc-tee-trade/paid-page-setup", "name": "@wsc-tee-trade/paid-page-setup", "version": "2.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageCreate", "onShareAppMessage", "pageDestroyed"], "asyncInit": true, "data": {"provide": {"kdtId": ["r", "w"], "buyerId": ["r"], "title": ["r", "w"], "hasPaid": ["r", "w"], "pageSize": ["r", "w"], "requestExtraParams": ["r", "w"], "bizName": ["r", "w"], "cpsConfigKey": ["r"], "orderNo": ["r"], "isRefreshing": ["r"], "fetching": ["r"], "payResult": ["r", "w"], "payWayStr": ["r"], "showChangePriceDialog": ["r"], "waitBuyDialog": ["r", "w"], "phasePaymentStage": ["r"], "orderItems": ["r"], "changeSubscribe": ["r"], "miniprogram": ["r"], "mpAccount": ["r"], "platformInfo": ["r"], "goodsType": ["r"], "orderDetailUrl": ["r"], "orderType": ["r"], "virtualTicketDetailUrl": ["r"], "navigationTitle": ["r", "w"], "isSkyline": ["r", "w"]}, "consume": {"groupbuyShareData": ["r"], "showSolitaire": ["r"], "solitaireShareData": ["r"]}}, "event": {"listen": ["checkPay"], "emit": ["checkPay"]}, "process": {"define": ["beforeGoToUrl"], "invoke": ["getInSourcingCouponShare", "getFissionShare"]}, "platform": ["weapp", "web"], "widget": {"default": "Main"}, "component": {"consume": ["UserAuthorizePopup"]}}