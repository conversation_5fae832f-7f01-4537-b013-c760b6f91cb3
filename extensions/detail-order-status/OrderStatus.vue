<template>
  <view class="wrapper" v-if="isShow">
    <view :class="'status status--' + orderStateClass">
      <view class="status__title">{{ orderStateTitle }}</view>
      <!-- #ifdef web -->
      <view v-if="(isTTApp || isXhsApp) && buyFirst" class="status__desc">
        <view class="order-inline-text">{{ buyFirstOrderStateStr }}</view>
      </view>
      <view v-else>
        <!-- #endif -->
        <view v-if="orderStateDescInfo.showOrderStatusSended" class="status__desc">
          <view v-if="orderStateDescInfo.marketingOrderSource === 'KUAI_SHOU'">
            物流显示签收后自动确认完成
          </view>
          <view v-else-if="orderStateDescInfo.leftTime">
            <view class="order-inline-text">还剩</view>
            <van-count-down
              class="order-count-down"
              use-slot
              :time="orderStateDescInfo.leftTime * 1000"
              @change="onSendedLeftTimeChange"
            >
              <view class="order-inline-text count-down-item">{{ sendedTimeStr }}</view>
            </van-count-down>
            <view class="order-inline-text">自动确认完成</view>
          </view>
        </view>
        <view
          v-else-if="orderStateDescInfo.showOrderStatusToGroup && groupLeftTime"
          class="status__desc"
        >
          <view class="order-inline-text">拼团单需在</view>
          <van-count-down
            class="order-count-down"
            use-slot
            :time="groupLeftTime * 1000"
            @change="onGroupLeftTimeChange"
            @finish="finishCountDown"
          >
            <view class="order-inline-text count-down-item">{{ groupTimeStr }}</view>
          </van-count-down>
          <view class="order-inline-text">内邀请{{ groupLeftPeople }}位好友参团</view>
        </view>
        <view v-else-if="orderStateDescInfo.closeTypeStr" class="status__desc">
          <view class="order-inline-text">{{ orderStateDescInfo.closeTypeStr }}</view>
        </view>
        <view v-else-if="crmOrderStateStr" class="status__desc">
          <view class="order-inline-text">{{ crmOrderStateStr }}</view>
        </view>
        <!-- #ifdef web -->
      </view>
      <!-- #endif -->

      <!-- #ifdef web -->
      <view v-if="isTTApp" class="tt-message-contact" @click="handleContactIM">
        <van-icon name="service-o" size="20" />
      </view>
      <!-- #endif -->
    </view>

    <!-- steps -->
    <van-steps
      v-if="orderSteps && orderSteps.list.length"
      custom-class="steps"
      :steps="orderSteps.list"
      :active="orderSteps.active"
    />

    <block v-if="!orderBizExtra.isSelfFetch">
      <waiting-process />
    </block>
  </view>
</template>

<script>
import CountDown from '@youzan/vant-tee/dist/count-down/index.vue';
import Steps from '@youzan/vant-tee/dist/steps/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

/* #ifdef web */
import { ZNB } from '@youzan/tee-biz-navigate';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Bot from '@youzan/im-base-pure/lib/core/Bot';
import args from '@youzan/utils/url/args';
import * as versions from '@youzan/utils/versions';
import { requestV2 } from '@youzan/tee-biz-request';
import formatDate from '@youzan/utils/date/formatDate';

ZNB.init({
  kdtId: window._global.kdtId,
}).catch(() => ({}));
/* #endif */

// 获取生成订单状态描述的相关参数
const getOrderStateDescInfo = (order) => {
  const nowTime = new Date().getTime() / 1000;
  const successTime = order.autoReceiveOrderTime || 0;

  return {
    showOrderStatusToGroup: order.showOrderStatusToGroup || false,
    showOrderStatusSended: order.showOrderStatusSended || false,
    marketingOrderSource: order.marketingOrderSource,
    leftTime: successTime > nowTime ? successTime - nowTime : 0,
    closeTypeStr: order.closeType > 0 ? order.closeTypeStr : '',
  };
};

// 获取订单状态 steps
const getOrderSteps = (order) => {
  const steps = {
    list: [],
    active: 0,
  };
  steps.list = (order.progressBar || []).map((item, index, list) => {
    // 由于字段名称不一致的问题，这里要根据接口返回的字段取值，有的接口返回 isArrive 有的返回 arrive
    const diffArrive = ['isArrive', 'arrive'];
    const arriveIndex = item.isArrive ? 0 : 1;
    const arriveName = diffArrive[arriveIndex];
    if (item[arriveName] && (!list[index + 1] || !list[index + 1][arriveName])) {
      steps.active = index;
    }

    return {
      text: item.type,
    };
  });
  return steps;
};

export default {
  components: {
    'van-count-down': CountDown,
    'van-steps': Steps,
    /* #ifdef web */
    'van-icon': Icon,
    /* #endif */
  },

  data() {
    return {
      order: '',
      orderState: '',
      orderStateTitle: '',
      orderSteps: {
        list: [],
        active: 0,
      },
      orderStateDescInfo: {
        showOrderStatusToGroup: false,
        showOrderStatusSended: false,
        leftTime: 0,
        closeTypeStr: '',
      },
      groupLeftTime: 0,
      groupLeftPeople: 0,
      groupTimeData: {},
      sendedTimeData: {},
      orderBizExtra: {},
      isShow: true, // 有赞云使用
      kdtId: 0,
      contact: {},
    };
  },

  computed: {
    isTTApp() {
      let isTTApp = false;
      /* #ifdef web */
      isTTApp = !!window._global?.miniprogram?.isTTApp;
      /* #endif */

      return isTTApp;
    },
    isXhsApp() {
      let isXhsApp = false;
      /* #ifdef web */
      isXhsApp = !!window._global?.miniprogram?.isXhsApp;
      /* #endif */

      return isXhsApp;
    },
    orderStateClass() {
      /* #ifdef web */
      if (this.isTTApp && this.buyFirst) {
        return 'buy-first';
      }
      /* #endif */

      let orderStateClass = '';
      // 状态60已发货，订单类型31代表教育订单
      if (this.order.state === 60 && this.order.orderGoodsType === 31) {
        // 教育订单 没有已发货状态 状态图标特殊处理
        orderStateClass = 'edu-60';
      } else {
        orderStateClass = '' + this.order.state;
      }
      return orderStateClass;
    },

    sendedTimeStr() {
      const { days, hours, minutes, seconds } = this.sendedTimeData;
      const times = [`${days}天`, `${hours}时`, `${minutes}分`, `${seconds}秒`];
      if (days === 0 && hours === 0 && minutes === 0 && seconds === 0) {
        return times.join('');
      }
      if (days > 0) {
        return times.slice(0, 2).join('');
      }
      if (hours > 0) {
        return times.slice(1).join('');
      }
      if (minutes > 0) {
        return times.slice(2).join('');
      }
      return times.slice(3).join('');
    },

    groupTimeStr() {
      const { days, hours, minutes, seconds } = this.groupTimeData;
      const times = [`${days}天`, `${hours}时`, `${minutes}分`, `${seconds}秒`];
      if (days === 0 || days === '0') {
        times.shift();
      }
      if ((days === 0 || days === '0') && (hours === 0 || hours === '0')) {
        times.shift();
      } else {
        times.pop();
      }
      return times.join('');
    },
    crmOrderStateStr() {
      return this.orderBizExtra?.extra?.offlineOrderStateStr;
    },
    /* #ifdef web */
    buyFirst() {
      return _global.itemInfo[0].extra?.ATTR_RETAIL_COUPON_GOODS_RELATED_GOODS;
    },
    buyFirstOrderStateStr() {
      if (!(this.stockCouponGoods?.canReserveNum > 0)) {
        return '';
      }

      const { type, days, endDate } =
        _global.itemInfo[0].extra?.ATTR_RETAIL_COUPON_GOODS_RELATED_GOODS;
      const { payTime } = _global.orderInfo;
      const newPayTime = new Date(formatDate(new Date(payTime), 'YYYY/MM/DD')).getTime();
      let nowTime = new Date().getTime();
      if (type === 1) {
        nowTime = (newPayTime + days * 24 * 3600 * 1000 - new Date().getTime()) / 1000;
      }
      if (type === 0) {
        // 小红书 webview 不支持：YYYY-MM-DD HH:mm:ss
        const parsedEndDate = new Date(endDate.replace(/-/g, '/'));
        nowTime = (parsedEndDate - new Date().getTime()) / 1000;
      }

      const day = (nowTime / (24 * 3600)) >> 0;
      const hour = ((nowTime % (24 * 3600)) / 3600) >> 0;
      const min = ((nowTime % 3600) / 60) >> 0;

      let timeStr = '';
      if (day) {
        timeStr += `${day}天`;
      }
      if (hour) {
        timeStr += `${hour}小时`;
      }
      if (min) {
        timeStr += `${min}分`;
      }

      return `请在 ${timeStr} 内使用，过期自动退`;
    },
    /* #endif */
  },

  created() {
    mapData(this, ['orderBizExtra', 'kdtId', 'contact', 'order', 'isShow', 'stockCouponGoods']);
    mapData(
      this,
      {
        orderStateInfo: (orderStateInfo) => {
          this.orderState = orderStateInfo.orderState;
          this.orderStateTitle = orderStateInfo.orderStateTitle;
          this.orderSteps = getOrderSteps(orderStateInfo);
          this.orderStateDescInfo = getOrderStateDescInfo(orderStateInfo);
        },
        groupon: (groupon) => {
          const { groupLeftTime = 0, groupLeftPeople = 0 } = groupon || {};
          this.groupLeftTime = groupLeftTime;
          this.groupLeftPeople = groupLeftPeople;
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    onGroupLeftTimeChange(e) {
      this.groupTimeData = e;
    },
    onSendedLeftTimeChange(e) {
      this.sendedTimeData = e;
    },
    finishCountDown() {
      this.ctx.event.emit('refreshOrderInfo');
    },
    /* #ifdef web */
    handleContactIM() {
      // 是否新的版本
      requestV2({
        path: '/retail/h5/douyin/version.json',
        method: 'GET',
        data: {
          kdt_id: this.kdtId,
        },
        withCredentials: true,
      })
        .then((data) => {
          const mpVersion = data.releaseVersion;
          if (mpVersion && versions.default.compareVersions(mpVersion, '1.1.36') > 0) {
            ZNB.navigate({
              ttUrl: args.add('/pages/native-api/im/index', { kdtId: this.kdtId }),
            });
            return;
          }
          const url =
            Bot &&
            Bot.generateUrl({
              // 常用参数
              kdtId: this.kdtId, // kdt_id, 必传, 店铺id
              fromSource: this.contact?.imFromSource, // 会话来源对象, 参考 https://doc.qima-inc.com/pages/viewpage.action?pageId=69569323
              origin: window.location.href, // origin， 打开C端客服页面的来源页面地址，仅用于iframe模式时的postMessage

              // 不常用参数
              channel: 'wsc', // channel, 'wsc' (微商城、零售), 'ls' (批发)，默认是'wsc'
              version: 2, // version, 默认是2, 影响后端选择渲染模板，基本不需要传
            });

          location.href = url;
        })
        .catch((e) => {
          console.error(e);
        });
    },
    /* #endif */
  },
};
</script>

<style lang="scss">
$text-color: #323233;
$gray-darker: #646566;
$url-60-edu: 'https://img01.yzcdn.cn/public_files/cef2d3ce50912052ff88c60102ea0a5a.png';

.wrapper {
  padding: 0 16px 10px;
  background: #fff;
}

.status {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 20px 0 20px 52px;
  min-height: 40px;
  background: url('https://img.yzcdn.cn/public_files/2018/08/30/345a61fbbf62d65a3a8c528272426666.png')
    left center no-repeat;
  background-size: 40px 40px;
}

.status__title {
  font-size: 16px;
  font-weight: bold;
  color: $text-color;
}

.status__desc {
  font-size: 14px;
  color: $gray-darker;
}

.order-count-down {
  display: inline-flex;
  align-items: center;
}

.order-inline-text {
  display: inline;
}

.count-down-item {
  color: $gray-darker;
}

.steps {
  padding: 0 0 10px;

  .t-step__title {
    font-size: 14px;
  }
}

.status--100 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/6556fce0b55b6a74bc68cba692bdb1ea.png');
}

.status--99 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/85f176382a5babc1eeed69ab34eac3ab.png');
}

.status--60 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/4cb7dad7d11e401a07081ebb1f3914de.png');
}

.status--edu-60 {
  background-image: url('https://img01.yzcdn.cn/public_files/cef2d3ce50912052ff88c60102ea0a5a.png');
}

.status--50 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/017f720391c682d5cb50c8ac770e93c6.png');
}

.status--10 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/6eb5418154ef15f9454b0500c800cfcb.png');
}

.status--20 {
  background-image: url('https://img.yzcdn.cn/public_files/2018/08/31/6eb5418154ef15f9454b0500c800cfcb.png');
}
/* #ifdef web */
.status--buy-first {
  background-image: url('https://img01.yzcdn.cn/upload_files/2024/06/12/Fox-sTf6hrxHThwOHHRIP83Qmwec.png');
}
/* #endif */

.tt-message-contact {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 5px;
  border: 1px solid #ebebeb;
  border-radius: 50%;
  background: white;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
