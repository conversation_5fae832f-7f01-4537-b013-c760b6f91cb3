<template>
  <view class="order-detail__education" v-if="showEdu">
    <!-- 课程信息 -->
    <van-cell-group v-if="showCourseInfo" custom-class="order-detail__course-info">
      <van-cell
        custom-class="order-detail__education-cell"
        v-if="education.courseAttend.courseTime"
        title="上课时间"
        :value="education.courseAttend.courseTime"
      />
      <van-cell
        custom-class="order-detail__education-cell"
        v-if="education.courseAttend.address"
        title="上课地点"
        :value="education.courseAttend.address"
      />
    </van-cell-group>

    <!-- 学员信息 -->
    <van-cell-group v-if="true" custom-class="order-detail__student-info">
      <van-cell
        v-for="(item, index) in eduAttributeItems"
        :key="index"
        :title="item.name"
        :value="item.value"
        :border="index !== eduAttributeItems.length - 1"
      />
    </van-cell-group>

    <van-cell-group v-else class="order-detail__student-info">
      <van-cell
        v-if="education.name"
        custom-class="order-detail__education-cell"
        title="学员姓名"
        :value="education.name"
      />
      <van-cell
        v-if="education.phoneNumber"
        custom-class="order-detail__education-cell"
        title="手机号"
        :value="education.phoneNumber"
      />
      <van-cell
        v-if="education.wechatAccount"
        custom-class="order-detail__education-cell"
        title="微信号"
        :value="education.wechatAccount"
      />
      <van-cell
        v-if="education.gender"
        custom-class="order-detail__education-cell"
        title="性别"
        :value="education.gender === 1 ? '男' : '女'"
      />
      <van-cell
        v-if="education.bornDate"
        custom-class="order-detail__education-cell"
        title="生日"
        :value="education.bornDate"
      />
      <van-cell
        v-if="education.grade"
        custom-class="order-detail__education-cell"
        title="年级"
        :value="education.grade"
      />
      <van-cell
        v-if="education.address"
        custom-class="order-detail__education-cell"
        title="联系地址"
        :value="education.address"
      />
    </van-cell-group>
  </view>
</template>

<script>
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

function isAttrValid(item) {
  const { value } = item || {};
  const isValid = !!value;
  if (isValid && Array.isArray(value)) {
    return value.length > 0;
  }
  return isValid;
}

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },
  data() {
    return {
      eduAttributeItems: [],
      education: {},
      showEdu: false,
    };
  },

  computed: {
    showCourseInfo() {
      const courseAttend = (this.education && this.education.courseAttend) || {};
      return courseAttend.courseTime || courseAttend.address;
    },
    showStuInfo() {
      return this.eduAttributeItems.length > 0;
    },
  },

  created() {
    mapData(this, ['education']);
    mapData(
      this,
      {
        showEducation: (showEducation) => {
          this.showEdu = showEducation;
        },
        eduAttributeItems: (eduAttributeItems) => {
          this.eduAttributeItems = eduAttributeItems
            .map((item) => ({
              id: item.attributeId,
              name: item.attributeTitle,
              value: item.value,
              isValid: isAttrValid(item),
            }))
            .filter((o) => o.isValid);
        },
      },
      { isSetData: false }
    );
  },
};
</script>

<style lang="scss">
.order-detail__course-info {
  margin-top: 10px;
}

.order-detail__education-cell .t-cell__title {
  max-width: 80px;
}

.order-detail__education-cell .t-cell__value {
  color: #666;
}

.order-detail__student-info {
  margin-top: 10px;
}

.order-detail__student-info .t-cell__title {
  max-width: 80px;
}
</style>
