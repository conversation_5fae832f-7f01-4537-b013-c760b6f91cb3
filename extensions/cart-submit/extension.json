{"extensionId": "@wsc-tee-trade/cart-submit", "name": "@wsc-tee-trade/cart-submit", "version": "2.0.2", "bundle": "<builtin>", "widget": {"default": "Main", "provide": ["Submit", "BestPriceBar"], "consume": ["Submit", "BestPriceBar"]}, "data": {"consume": {"editMode": ["r"], "isDrugShop": ["r"], "kdtId": ["r"], "isCheckedAll": ["r"], "checkedGoodsList": ["r"], "presentData": ["r"], "themeCSS": ["r"], "themeStyle": ["r"], "bookKey": ["r", "w"], "showEmptyCart": ["r"], "submitData": ["r"], "isTabPage": ["r"], "dataLoaded": ["r"], "shopCart": ["r"], "isHasSelfFetchGoodsSelected": ["r"], "navHeight": ["r"], "selectedPromotions": ["r"], "currentAddress": ["r"], "numChangeInvalid": ["r"]}, "provide": {"cartBottomData": ["r"]}}, "event": {"emit": ["toggleCheckedAll", "deleteCartItems", "updateCartGoodsListWithActivity", "reward:show"]}, "process": {"invoke": ["navigateToTradeBuy", "navigateFromCart", "createAndGoOrder", "changeSubmitText", "beforeBuyWithGoods"], "define": ["createAndGoOrder", "changeSubmitText", "changeDiscountDetailVisible"]}, "lambda": {"consume": ["setDb"]}, "platform": ["web", "weapp"]}