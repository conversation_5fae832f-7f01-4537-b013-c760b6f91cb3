import Tee from '@youzan/tee';
import { supportedActivity } from './constant';

/* #ifdef weapp */
const app = getApp();
/* #endif */

export function isSimpleStyle(payResult) {
  const { shopCoupons, isShowRecommendGoods } = payResult;
  const hasActivity =
    payResult.paidPromotion &&
    supportedActivity.indexOf(payResult.paidPromotion.promotionType) > -1;

  const hasCoupon = shopCoupons && shopCoupons.quantity > 0;
  let result = false;
  const showWxSubscribe =
    payResult.wxSubscribeScenesList &&
    payResult.wxSubscribeScenesList.length > 0 &&
    Tee.$native.canIUse('requestSubscribeMessage');

  if (!hasActivity && !hasCoupon && !isShowRecommendGoods && !showWxSubscribe) {
    result = true;
  }
  /* #ifdef weapp */
  app.trigger('ranta-paid-page', result);
  /* #endif */
  return result;
}

export function formatPayResult(payResult) {
  // 扫码购订单
  payResult.isScanBuyOrder = payResult.orderMark === 'online_scan_buy';
}
