{"extensionId": "@wsc-tee-trade/cart-valid-goods", "name": "@wsc-tee-trade/cart-valid-goods", "version": "2.0.4", "bundle": "<builtin>", "widget": {"default": "ValidGoods", "consume": ["PresentGoods", "Present", "GoodsGroupUmp", "GoodsItemUmp", "UmpInfoPopup", "CustomGoodsListHeader", "CustomGoodsItem", "GoodsGroupItem", "GoodsItem", "ComboDetailPopup", "Goods", "Price"], "provide": ["Present", "GoodsGroupItem", "GoodsItem", "Goods", "ComboDetailPopup"]}, "data": {"provide": {"checkedGoodsList": ["r", "w"], "isCheckedAll": ["r", "w"], "presentData": ["r", "w"], "presentPopupInfo": ["r", "w"], "isValidGoodsLoadFinish": ["r"], "numChangeInvalid": ["r"], "globalMarkId": ["r"]}, "consume": {"editMode": ["r"], "shopCart": ["r", "w"], "shopList": ["r", "w"], "themeCSS": ["r"], "themeColors": ["r"], "themeStyle": ["r"], "themeTag": ["r"], "canSelectPresent": ["r"], "hasResponseValidGoods": ["r"], "offlineId": ["r"], "memberConfig": ["r"]}}, "event": {"emit": ["changePresentSku", "updateCartGoodsList", "cartGoodsSku:afterSubmit", "cartPresentSku:afterSubmit", "cartPresentSku:fetch"], "listen": ["toggleCheckedAll", "deleteCartItems", "presentPopupClose", "presentPopupConfirm", "cartGoodsListDidUpdate", "onPullDownRefresh", "stopPullDownRefresh", "cartGoodsSku:hide", "cartGoodsSku:submit", "cartPresentSku:submit", "cartPresentSku:selected"]}, "lambda": {"provide": ["isGoodsCheckboxEnable", "isEduIosOnlineGoods"], "consume": ["hexToRgb"]}, "process": {"define": ["updateSelectGoods", "emptyCart", "deleteCartGoods", "batchDeleteCartGoods", "selectCartGoods", "cancelSelectCartGoods", "setCartGoodsNum", "reselectGoods", "showCartActivityPopup", "showExchangeModal", "changeGoodsSku"], "invoke": ["navigateFromCart", "emptyCart", "beforeCartClearHook", "setSkuInfo"]}, "platform": ["web", "weapp"], "lifecycle": ["onPageShow"]}