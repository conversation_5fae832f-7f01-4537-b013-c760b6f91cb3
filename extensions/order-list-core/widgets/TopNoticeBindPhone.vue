<template>
  <view v-if="isShowBind && inited" class="page-phone" :style="rootStyle">
    <!-- 绑定手机号 -->
    <view class="bind__container">
      <view>找不到订单？绑定手机号试试</view>
      <view>
        <user-authorize :auth-type-list="['mobile']" @next="getAuhPhone">
          <view> 去登录<van-icon class="bind__icon" name="arrow" /> </view>
        </user-authorize>
      </view>
    </view>
  </view>
</template>

<script>
// Dependencies
import { mapData } from '@youzan/ranta-helper-tee';
import { mapState } from '@ranta/store';

import { Icon } from '@vant/tee';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      userInfo: { buyerId: 0 },
      inited: false, // 解决闪一下的问题
      ...mapState(this, ['isShowRetailOrderList']),
    };
  },

  computed: {
    isShowBind() {
      return !this.userInfo.buyerId;
    },
    rootStyle() {
      // 零售订单中心距离固定UI的上间距为129px，其他为103px
      return `top: ${this.isShowRetailOrderList ? '129' : '103'}px`;
    },
  },

  created() {
    mapData(this, ['userInfo'], {
      callback: () => {
        this.inited = Object.keys(this.userInfo).length;
      },
    });
  },

  methods: {
    getAuhPhone(result) {
      this.userInfo.buyerId = result.buyerId;
    },
  },
};
</script>

<style scoped lang="scss">
.page-phone {
  position: absolute;
  width: calc(100% - 32px);
  margin: 0 16px;
  font-size: 14px;
  color: #ed6a0c;
}
.bind__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background-color: #faf3e1;
  padding: 0 14px 0 18px;
  border-radius: 8px;
}

.bind__icon {
  margin-left: 5px;
}
</style>
