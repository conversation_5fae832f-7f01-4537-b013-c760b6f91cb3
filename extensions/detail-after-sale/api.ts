import { requestV2 } from '@youzan/tee-biz-request';

export default {
  // qtt 获取当前订单的团长partyId
  getAttributeOrderInfo(orderNo) {
    return requestV2({
      path: '/wscfenxiao/order/getAttributeOrderInfo.json',
      method: 'GET',
      data: {
        orderNo,
      },
    });
  },
  // 查询店铺基本信息
  getShopInfo() {
    return requestV2({
      path: '/wscshop/shop/shop_baseinfo.json',
      method: 'GET',
    });
  },
  // 查询消息成交开关配置
  querySwitch(data) {
    return requestV2({
      path: '/wscfenxiao/im/querySwitch.json',
      method: 'GET',
      data,
    });
  },
  // 承接入口保存关系
  saveRelation(data) {
    return requestV2({
      path: '/wscfenxiao/im/saveRelation.json',
      method: 'POST',
      data,
    });
  },
  // 查询店铺是否开通微信客服
  queryCustomerService(data) {
    return requestV2({
      path: '/wscfenxiao/im/queryCustomerService.json',
      method: 'POST',
      data,
    });
  },
};
