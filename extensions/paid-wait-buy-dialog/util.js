import get from '@youzan/utils/object/get';

/**
 * 获取商品属性ID列表
 * @param {Array} properties
 */
function getPropertyIds(properties = []) {
  const propertyIds = [];
  properties.forEach((property) => {
    const propValueList = get(property, 'propValueList', []);
    propValueList.forEach((value) => {
      propertyIds.push(value.propValueId);
    });
  });

  return propertyIds;
}

export function generateBuyGoodsList(goodsList = []) {
  const order = goodsList.map((goods) => {
    let bizTracePointExt = '';
    let message = '';
    try {
      const extraAttribute = JSON.parse(goods.extraAttribute);
      bizTracePointExt = extraAttribute.bizData || '';
      message = JSON.parse(goods.messages || null);
    } catch (e) {
      console.warn(e);
    }
    const result = {
      activityAlias: '',
      activityId: 0,
      activityType: 0,
      message,
      num: goods.num,
      price: goods.payPrice,
      skuId: goods.skuId,
      goodsId: goods.goodsId,
      kdtId: goods.kdtId,
      bizTracePointExt,
      propertyIds: getPropertyIds(goods.properties),
    };
    if (+goods.activityType === 24) {
      Object.assign(result, {
        activityId: goods.activityId,
        activityType: 24,
      });
    }
    return result;
  });
  return order;
}
