<template>
  <view class="gift-share__goods" @click="handleGoodsClick">
    <view v-if="goodsList.length" class="gift-share__goods-list">
      <view class="gift-share__goods-list-header">
        共<text class="goods-num">{{ giftNum }}</text
        >件礼物
      </view>
      <view class="divider"></view>
      <view class="gift-share__goods-list-body">
        <view class="goods-item" v-for="(item, index) in goodsList" :key="index">
          <view class="img-wrap">
            <image class="img-wrap-item" :src="item.goodsImg" mode="aspectFit" />
          </view>
          <view class="goods-detail">
            <view class="goods-title-cell">
              <view class="goods-title">{{ item.goodsTitle }}</view>
              <view class="goods-price" v-if="isShowPrice">￥{{ item.price }}</view>
            </view>
            <view class="goods-sub-title-cell">
              <view>{{ item.sku }}</view>
              <view v-if="isShowPrice">x{{ item.num }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

export default {
  components: {
    'van-icon': Icon,
  },
  props: {
    goodsList: {
      type: Array,
      default: () => [],
    },
    isShowPrice: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    giftNum() {
      return (this.goodsList || []).reduce((total, pre) => {
        total += pre.num;
        return total;
      }, 0);
    },
  },

  methods: {
    handleGoodsClick() {
      this.$emit('jump');
    },
  },
};
</script>

<style lang="scss" scoped>
.divider {
  height: 1px;
  transform: scaleY(0.5);
  background: #e0e0e0;
  margin: 16px 0;
}
.gift-share {
  &__goods-list {
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    margin: 12px;

    &-header {
      font-size: 16px;
      font-weight: 500;

      .goods-num {
        color: #ecc085;
        margin: 4px;
      }
    }

    &-body {
      .goods-item {
        margin-top: 9px;
        margin-bottom: 16px;
        position: relative;
        height: 60px;
        display: flex;
        align-items: center;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .img-wrap {
          height: 60px;
          flex: 0 0 60px;
          background: #f8f8f8;
          position: relative;

          &-item {
            position: absolute;
            margin: auto;
            max-height: 100%;
            max-width: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            border-radius: 4px;
          }
        }

        .goods-detail {
          flex: 1;
          padding: 4px 0 4px 10px;
          overflow: hidden;
        }

        .goods-title-cell {
          display: flex;
          justify-content: space-between;
          font-size: 16px;
          font-weight: 500;
        }

        .goods-title {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .goods-price {
          text-align: right;
          flex-grow: 1;
          padding-left: 5px;
        }

        .goods-sub-title-cell {
          display: flex;
          justify-content: space-between;
          color: #666;
          font-size: 14px;
          margin-top: 6px;
        }
      }
    }

    &-footer-title {
      font-size: 14px;
    }
  }
}
</style>
