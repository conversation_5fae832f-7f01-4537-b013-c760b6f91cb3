<template>
  <van-cell v-if="isRetailMinappShelfOrder && takeGoodsCode" class="take-goods-code">
    <view class="take-goods-code__title">取货口令</view>
    <view class="take-goods-code__content">{{ takeGoodsCode }}</view>
    <view class="take-goods-code__tip">请凭取货口令前往柜台取货</view>
  </van-cell>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { object } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-cell': Cell,
  },

  data() {
    return {
      orderMark: '',
      orderBizExtra: {},
    };
  },

  computed: {
    isRetailMinappShelfOrder() {
      return this.orderMark === 'retail_minapp_shelf';
    },

    takeGoodsCode() {
      return object.get(this.orderBizExtra, 'takeGoodsCode');
    },
  },

  created() {
    mapData(this, ['orderMark', 'orderBizExtra']);
  },
};
</script>

<style lang="scss" scoped>
.take-goods-code {
  &__title {
    font-weight: bold;
    color: #323233;
    line-height: 20px;
    padding-bottom: 4px;
    text-align: center;
  }

  &__content {
    font-weight: bolder;
    font-size: 34px;
    color: #323233;
    line-height: 44px;
    padding-bottom: 4px;
    text-align: center;
  }

  &__tip {
    font-size: 12px;
    color: #969799;
    line-height: 18px;
    text-align: center;
  }
}
</style>
