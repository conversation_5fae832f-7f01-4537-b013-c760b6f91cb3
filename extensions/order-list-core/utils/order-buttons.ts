import get from '@youzan/utils/object/get';
import { LOG_NEW_BUY_AGAIN_VIEW } from './log';

export const BTN_CONF = {
  cancel: 'cancel', // 取消订单
  cancelPayOnDelivery: 'cancelPayOnDelivery', // 取消订单
  topay: 'topay', // 立即付款
  transport: 'transport', // 查看物流
  topayDeposit: 'topayDeposit', // 支付定金
  topayRetainage: 'topayRetainage', // 支付尾款
  topayRetainageDisabled: 'topayRetainageDisabled', // 支付尾款
  confirmReceive: 'confirmReceive', // 确认收货
  confirmReceiveHotel: 'confirmReceiveHotel', // 确认入住
  laterReceive: 'laterReceive', // 延长收货
  buyAgain: 'buyAgain', // 再来一单
  directBuyAgain: 'directBuyAgain', // 再来一单
  evaluate: 'evaluate', // 立即评价
  lotteryResult: 'lotteryResult', // 抽奖结果
  inviteHelp: 'inviteHelp', // 邀请助力
  deleteOrder: 'deleteOrder', // 删除订单
  expressReminderHotel: 'expressReminderHotel', // 提醒接单
  expressReminder: 'expressReminder', // 提醒发货
  paidPromotion: 'paidPromotion', // 支付有礼
  coupon: 'coupon', // 领优惠券
  fissionCoupon: 'fissionCoupon', // 抢优惠券
  liveQrCode1: 'liveQrCode', // 专属客服
  liveQrCode2: 'liveQrCode', // 加粉丝群
  /* #ifdef weapp */
  grouponDetail: 'grouponDetail', // 拼团详情
  viewCardDetail: 'viewCardDetail', // 查看卡券
  selfFetchCode: 'selfFetchCode', // 核销码
  /* #endif */
  /* #ifdef web */
  viewEvaluate: 'viewEvaluate', // 查看评价
  gift: 'gift', // 查看礼单
  groupDetail: 'groupDetail', // 查看拼团
  courseDetail: 'courseDetail', // 查看课程
  hotelComboUse: 'hotelComboUse', // 立即预约
  shared: 'shared', // 我要晒单
  peerpay: 'peerpay', // 代付
  /* #endif */
};
export const ALL_BTN_TYPES = Object.values(BTN_CONF);

interface PushButtonReturn {
  (params: Record<string, any>): Promise<boolean>;
}
export const pushButton = (
  actionBtns: any[],
  btn,
  customBtnConfig: Record<string, any> = {}
): PushButtonReturn => {
  if (btn === undefined) return () => Promise.resolve(false);
  const { condition = false, ...other } = btn || {};
  const btnConf = { ...other, ...customBtnConfig };
  if (typeof condition === 'boolean' && condition) {
    actionBtns.push(btnConf);
    return () => Promise.resolve(true);
  }
  if (typeof condition === 'function') {
    return (params): Promise<boolean> => {
      return new Promise((resolve) => {
        if (condition(params)) {
          actionBtns.push(btnConf);
          return resolve(true);
        }
      });
    };
  }
  return () => Promise.resolve(false);
};

const isExistButton = (actionBtns, btnValue) => actionBtns.some((btn) => btn.value === btnValue);

/** ************************************************************************************************
 * 处理按钮埋点上报，如果按钮有展示就上报埋点
 * 这个代码实际上不应该在这里，而是应该在真正展示按钮的时候，也就是UI层上做判断，后续可以做优化。优化的时候也要注意，
 * 不要把埋点逻辑打散在各个.vue文件里，要聚合在一起，可以是一个文件也可以是一个类，统一处理。
 ************************************************************************************************ */
export const processBtnLogReport = (ctx, payload): void => {
  try {
    const { orderNo, orderItem, actionBtns, orderPermission, directBuyAgainBtnConfig } = payload;
    const items = get(orderItem, 'items', []);

    const goodsIds = [];
    items.forEach((item) => {
      goodsIds.push(item.goodsId);
    });
    const params: Record<string, any> = {
      goods_id: goodsIds,
      order_no: orderNo,
    };

    if (isExistButton(actionBtns, BTN_CONF.directBuyAgain)) {
      params.is_new = true;
      // TODO: AB应该已经没用了应该移除这行代码，移除前需要在确认下
      params.abTraceId = (directBuyAgainBtnConfig || {}).abTraceId || null;
      LOG_NEW_BUY_AGAIN_VIEW(ctx, params);
    }
    if (isExistButton(actionBtns, BTN_CONF.buyAgain)) {
      params.is_new = false;
      if (orderPermission.isAllowDirectBuyAgain) {
        // TODO: AB应该已经没用了应该移除这行代码，移除前需要在确认下
        params.abTraceId = (directBuyAgainBtnConfig || {}).abTraceId || null;
      }
      LOG_NEW_BUY_AGAIN_VIEW(ctx, params);
    }
  } catch (e) {
    console.error(e);
  }
};
