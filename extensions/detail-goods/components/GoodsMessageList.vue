<template>
  <van-popup
    v-if="hasMessage"
    :show="show"
    position="bottom"
    safe-area-inset-bottom
    class="goods-ml"
    @close="onClose"
  >
    <view class="goods-ml_header t-hairline--bottom">商品留言</view>

    <view class="goods-ml_content">
      <van-cell v-for="(item, index) in formattedMsgs" :key="index" :title="item.label">
        <image
          v-if="item.type === 'img'"
          class="goods-ml_content-img"
          mode="widthFix"
          :src="item.value"
          @click="preview(item.value)"
        />

        <text v-else-if="item.type === 'text'">{{ item.value || '无' }}</text>
      </van-cell>
    </view>

    <van-button size="large" type="danger" square @click="onClose">关闭</van-button>
  </van-popup>
</template>

<script>
import VanPopup from '@youzan/vant-tee/dist/popup/index.vue';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import VanCell from '@youzan/vant-tee/dist/cell/index.vue';
import fullfillImage from '@youzan/utils/url/fullfillImage';
import { ImagePreview } from '@youzan/vant-tee/image-preview/image-preview';

export default {
  name: 'goods-message-list',
  components: {
    'van-popup': VanPopup,
    'van-button': VanButton,
    'van-cell': VanCell,
  },

  props: {
    show: { type: Boolean, default: false },
    messages: [Object, Array, String],
  },

  computed: {
    formattedMsgs() {
      const msgs = [];
      Object.keys(this.messages || {}).forEach((key) => {
        const value = this.messages[key];
        msgs.push({
          label: key,
          type: /^\s*http(s)*:\/\/.+/.test(value) ? 'img' : 'text',
          value,
        });
      });

      return msgs;
    },

    hasMessage() {
      return this.formattedMsgs?.length;
    },
  },
  methods: {
    onClose() {
      this.$emit('close');
    },

    preview(value) {
      ImagePreview([fullfillImage(value, '!180x180.jpg')]);
    },
  },
};
</script>

<style lang="scss">
.goods-ml {
  width: 100%;
  min-height: 70%;
  max-height: 85%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  &_header {
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    font-weight: normal;
  }

  &_content {
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    &-img {
      width: 120px;
      display: inline-block;
    }

    .t-cell {
      &__title {
        max-width: 90px;
      }

      &__value {
        text-align: left;
      }
    }
  }
}
</style>
