import { object, string, url } from '@youzan/tee-util';
import { buildUrl } from '@youzan/tee-biz-util';

import {
  getDeliveryAddress,
  getSelfFetchAddress,
  getSelfFetchLngLat,
  getVerifyAddress,
  getVerifyAddressLngLat,
} from '../common/address';

import { formatPrice } from '../common/price';
import { getIsNewHotelGood } from '../common/hotel';
import { parseItemInfo, formatStockCouponGoods } from '../common/format';
import { formatDeliveryPostage } from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';
import fullfillImage from '@youzan/utils/url/fullfillImage';

// 格式化数据
export function formatOrder() {
  const {
    orderInfo: order,
    orderAddressInfo: address,
    paymentInfo: payment,
    itemInfo: goods,
    invoiceInfo: invoice,
    shopInfo = {},
    gift = {},
    orderBizUrl = {},
    orderBizExtra = {},
    ump = {},
    marketingInfo = {},
    microTransferInfo = {},
    env = {},
    miniprogram = {},
    paidPromotion = {},
    isIVROwner = false,
    buyerInfo = {},
    giving_duration: givingDuration,
    mp_account: mpAccount,
    isFans,
    buyAgainCouponAvailable,
    platformInfo,
    directBuyAgainBtnConfig = {},
    sourceInfo = {},
    privacyWaybill = {},
    forQttOrderExtra = {},
    forOrderExtra = {},
    outerPromotion = {},
  } = _global;
  order.directBuyAgainBtnConfig = directBuyAgainBtnConfig;
  order.buyerInfo = buyerInfo;
  const customerType = object.get(buyerInfo, 'customerType');
  const buyerPhone = `${object.get(buyerInfo, 'buyerPhone', '')}`;
  const hidePhone = buyerPhone ? buyerPhone.substr(0, 3) + '****' + buyerPhone.substr(7, 11) : '';
  const customerName = object.get(buyerInfo, 'customerName', '');

  const goodsPackages = order.goodsPackages || [];
  const attributeItems = order.attributeItems || [];

  const logistics = goodsPackages[0] || {};
  const { selfFetchResult = {}, selfFetchInfo = {} } = address;
  const selfFetchInfoToCamelCase = string.mapKeysToCamelCase(selfFetchInfo);
  const localDelivery = address.localDeliveryScope || {};
  const { storeAddress = {} } = shopInfo;

  const { cashBackUmp = {}, couponUmp = {}, couponUmps = [], couponCodeUmp = {} } = order.ump || {};

  const isNewHotelGood = getIsNewHotelGood(goods);

  // 多周期购判断，新希望独有
  const isMultiPeriodBuy =
    order.goodsPackages &&
    order.goodsPackages.length > 1 &&
    goods.length > 0 &&
    goods[0].goodsType === 24;

  const courses = object.get(order, 'faceToFaceCourseInfo.courses', []);

  const itemList = parseItemInfo(
    goods,
    order,
    orderBizExtra,
    goodsPackages,
    order.periodDetail,
    givingDuration,
    isMultiPeriodBuy,
    orderBizExtra.isKosTokenVerified || false
  );

  // 先囤后约券商品
  const stockCouponGoods = formatStockCouponGoods(goods, order.kdtId, forOrderExtra);

  const fromSource = url.args.get('source', window.location.href);

  const MAP_CPS_CONFIG_KEY = {
    cart: 'cps_goods_recommend_shopping_cart',
    'order-detail': 'cps_goods_recommend_order_detail',
    'order-list': 'cps_goods_recommend_order_list',
    'order-pay-result-new': 'cps_goods_recommend_pay_success',
    'order-pay-result': 'cps_goods_recommend_pay_success',
    'login-tips': 'cps_goods_recommend_shopping_cart',
  };

  const useBeforePay = _global['@cashier/prior-use'] || {};
  const formatOrderData = {
    // 先用后付
    useBeforePayData: {
      is: useBeforePay.show || false, // 当前订单是否是先用后付的场景
      status: useBeforePay.status || 'PENDING', // 履约状态，PENDING=待履约，PAID=已支付
    },

    wrapperType: 'card',

    // 加购后推荐商品区
    recommendTitle: '更多精选商品',
    dialogRecommendTitle: '为你推荐相关优惠商品',
    recommendPageSize: 20,
    dialogRecommendPageSize: 6,
    recommendLayoutConfig: {
      layout: 1,
      imageFillStyle: 1,
    },
    dialogRecommendLayoutConfig: {
      imageFillStyle: 1,
      layout: 5,
    },
    requestExtraParams: ['note', 'coupon'],
    dialogRequestExtraParams: [],

    orderNo: order.orderNo,
    kdtId: order.kdtId,
    headKdtId: shopInfo.headKdtId || shopInfo.kdtId,
    showLogisticsInfo: !!(goodsPackages && goodsPackages.length),
    isMultiPeriodBuy,
    showVirtualTicket: orderBizExtra.showVirtualTicket || false,
    showShopVerifyAddress: order.showShopVerifyAddress || false,
    showReceiverInfo: !!(orderBizExtra.showAddress && !orderBizExtra.isSelfFetch),
    showSelfFetch: !!(orderBizExtra.showAddress && orderBizExtra.isSelfFetch),
    showRefundInfo: order.isShowRefundInfo || false,
    showQrCode: !!order.virtualResponse && Object.keys(order.virtualResponse).length > 0,
    showGrouponCollection: order.showGroupAgencyReceive || false,
    showIdCard: order.showIdCard || false,
    showPresaleSteps: !!(payment.phasePays && payment.phasePays.length),
    showCheckDeliveryScope: order.showCheckDeliveryScope || false,
    showDeliveryType: orderBizExtra.showDeliveryType || false,
    showDeliveryTime: order.showDeliveryTime || false,
    showAfterSaleMobile: order.showAfterSaleMobile || false,
    // showWxShoppingList: !!(
    //   order.extra &&
    //   order.extra.WECHAT_SYNC_SHOPPING_LIST === 1 &&
    //   goods[0].goodsType != 31 &&
    //   order.state > 4
    // ),
    showEducation: order.showEducation || false,
    showLookCoupon: orderBizExtra.hasPayCouponsOrder || false,
    isUsingNewSnapshot: orderBizExtra.isUsingNewSnapshot || false,

    // 是否是新酒店商品
    isNewHotelGood,

    // H5 顶部信息
    mpAccount,
    // 从微信订单列表过来的查询
    isSourceFromWx: fromSource === 'wx',
    isFans,

    // 订单状态
    orderStateInfo: {
      orderState: order.state,
      orderStateTitle: order.stateStr,
      autoReceiveOrderTime: order.autoReceiveOrderTime,
      showOrderStatusToGroup: order.showOrderStatusToGroup,
      showOrderStatusSended: order.showOrderStatusSended,
      closeType: order.closeType,
      closeTypeStr: order.closeTypeStr,
      progressBar: order.progressBar,
      marketingOrderSource: order.marketingOrderSource,
    },

    // 物流
    logistics,

    // 电子卡券
    virtualTicket: order.virtualTicketResponse || {},

    // 核销网点信息
    verifyAddressInfo: {
      name: storeAddress.name,
      address: getVerifyAddress(storeAddress),
      ...getVerifyAddressLngLat(storeAddress),
    },

    // 收货人或自提信息
    receiverInfo: {
      receiverName: address.receiverName,
      receiverTel: address.receiverTel,
      receiverLabel: orderBizExtra.showVirtualTicket ? '联系人' : '收货人',
      deliveryAddress: getDeliveryAddress(address),
    },

    // 获取自提信息
    selfFetchInfo: {
      qrcode: selfFetchResult.qrCodeBase64 || '',
      fetchNo: selfFetchResult.selfFetchNo || '',
      fetcher: selfFetchInfoToCamelCase.userName + selfFetchInfoToCamelCase.userTel || '',
      time: selfFetchInfoToCamelCase.userTime || '',
      tel: selfFetchInfoToCamelCase.tel || '',
      address: getSelfFetchAddress(selfFetchInfoToCamelCase),
      ...getSelfFetchLngLat(selfFetchInfoToCamelCase),
      shopName: order.shopName,
      name: selfFetchInfoToCamelCase.name || '',
    },

    // 知识付费送礼退款
    giftInvite: gift.giftInvite || {},
    gift,

    // 虚拟商品二维码
    qrCode: order.virtualResponse || {},

    // 拼团
    groupon: order.groupBuy || {},

    // 身份证号
    idCardNumber: order.idCardNumber || '',

    // 活动类型
    activityType: order.activityType || '',

    // 返现
    cashBack: cashBackUmp,

    // 返储值金
    returnValueCard: {
      assertBusinessDetail: object.get(orderBizExtra, 'assertBusinessDetail', []),
      cardAssetIsActive: orderBizExtra.cardAssetIsActive,
    },

    // 优惠券信息
    coupon: couponUmp,
    // 优惠券叠加
    coupons: couponUmps,
    // 优惠码
    couponCode: couponCodeUmp,
    ump,
    buyAgainCouponAvailable,

    marketingInfo,

    courses,
    itemList,
    stockCouponGoods,
    // 价格相关
    payment,
    showPostage: orderBizExtra.showPostage,
    buyWayDetailUrl: orderBizUrl.buyWayDetailUrl,

    activitiesList: ump.orderActivities,
    deliveryActivitiesList: ump.orderDeliveryActivities,
    decreaseAmount: ump.decreaseAmount,
    // 外部订单优惠信息
    outerPromotion,

    // 定金预售
    presaleSteps: payment.phasePays || [],

    // 订单时间相关
    time: {
      createTime: order.createTime,
      payTitle: order.buyWay === 9 ? '下单时间' : '付款时间',
      payTime: order.payTime,
      expressTime: order.expressTime,
      successTime: order.successTime,
    },

    orderBizUrl,

    order,
    addressInfo: address,
    shopInfo,
    totalPrice: formatPrice(payment.payPrice),
    isPeriodBuy: orderBizExtra.isPeriodBuy,
    isSelfFetch: orderBizExtra.isSelfFetch,
    isShowPostponeShip: order.isShowPostponeShip,
    isHotel: orderBizExtra.isHotel,

    // service
    service: {
      postage: formatDeliveryPostage(payment.postage, payment.expressPayMode),
      expressType: order.expressTypeDesc || '',
      localDeliveryDesc: localDelivery.desc || '',
      localDeliveryImg: fullfillImage(localDelivery.attachPic, 'middle'),
      deliveryTime: address.deliveryTimeDisplay || '',
      buyerMemo: order.buyerMemo || '无',
      founderToMemberMark: forQttOrderExtra.founderToMemberMark || '无',
      showHotelUnsubscribeRemind: orderBizExtra.isHotel,
      serviceTel: object.get(goods[0], 'goodsInfo.serviceTel') || '',
    },

    // 教育信息
    education: order.education,
    eduAttributeItems: attributeItems,

    // invoice
    invoice,
    invoiceStatus: invoice.status,

    goods,

    // 售后联系
    contact: order.afterSaleContact || {},

    // 运费险
    freight: order.freight,
    isOrderPage: false, // 是否是下单页
    isChoosedCard: false, // 是否选择了预付卡
    // 有赞担保
    guaranteeOrderInfo: {
      aliases: goods.map((res) => res.goodsInfo.alias),
      feedback: order.feedback, // 是否售后中
      hasYzSecured: order.yzGuarantee, // 是否是有赞担保订单
      orderNo: order.orderNo, // E单号
      orderSuccessTime: order.successTime, // 订单完成时间
      orderStatus: order.state, // 订单状态
      orderPayTime: order.payTime, // 订单支付时间
      payWay: order.buyWay, // 支付方式
      orderCreateTime: order.createTime, // 订单创建时间
    },
    // 有赞担保
    webviewPath: '', // 有且仅有小程序需要，H5不需要
    isPrePayPage: false, // 待支付页面需要 true, 其他 H5 页面为 false

    // 分享订单items
    isAllowShareOrder: order.isAllowShareOrder,
    orderBizExtra,

    // 订单是否使用内购券
    fissionTicketNum: object.get(orderBizExtra, 'fissionTicketNum', 0),

    // 积分自定义名称
    pointsName: payment.pointsName,

    // 小额打款信息
    microTransferInfo,
    // styleSet: 2,

    env,
    miniprogram,
    isEduOrder: goods[0].goodsType === 31,
    showPaidPromotion: paidPromotion && paidPromotion.detailUrl,
    paidPromotion,
    shopName: order.shopName,
    payTime: order.payTime,
    isIVR: url.args.get('from', window.location.href) === 'ivr',
    isIvrOwner: isIVROwner,
    // 当前用户无权 操作 IVR订单时，跳转地址; customerType === 16940 快手订单
    IVRRedirectUrl: buildUrl(
      `/wsctrade/order/guide?shop=${customerType === 16940 ? 'KSShop' : 'YZShop'}&shopname=${
        order.shopName
      }&from=ivr&phone=${hidePhone}&customername=${customerName}`,
      'h5',
      order.kdtId
    ),

    isShowStoreInfo: false,
    pageBgColor: '',
    platformInfo,
    // 是否是批发订单
    isWholesaleOrder: order?.isWholesaleOrder || false,
    // 线下三方门店，外部订单编号
    outBizNo: order.outBizNo,
    isRetailOrder: order.isRetailOrder,
    sourceInfo,
    // 隐私面单-号码保护
    privacyWaybill,
  };

  // 批发订单、赞拼拼订单详情不展示CPS周边好物入口 & 推荐商品模块
  if (!order?.isWholesaleOrder && !env?.isFxZpp) {
    formatOrderData.cpsConfigKey = MAP_CPS_CONFIG_KEY['order-detail'];
    formatOrderData.recommendBizName = 'order_detail';
    formatOrderData.dialogRecommendBizName = 'od~sg';
  }
  return formatOrderData;
}

/**
 * 根据地址修改ReceiverInfo
 * @param address http://zanapi.qima-inc.com/site/service/view/2808 返回的地址数据格式
 * @returns {{receiverTel: *, receiverLabel: (string), deliveryAddress, receiverName: *}}
 */
export function formatReceiverInfo(address) {
  const { orderAddressInfo, orderBizExtra } = _global;
  return {
    receiverInfo: {
      receiverName: address.userName,
      receiverTel: address.tel,
      receiverLabel: orderBizExtra.showVirtualTicket ? '联系人' : '收货人',
      deliveryAddress: getDeliveryAddress({
        ...orderAddressInfo,
        deliveryCountry: address.country,
        deliveryProvince: address.province,
        deliveryCity: address.city,
        deliveryDistrict: address.county,
        deliveryStreet: address.addressDetail + address.houseNumber,
      }),
    },
  };
}
