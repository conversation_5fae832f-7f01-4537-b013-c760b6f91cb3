import { requestV2 } from '@youzan/tee-biz-request';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { buildUrl } from '@youzan/tee-biz-util';

let index = 0;

export function delayContinue(type, kdtId, text) {
  // 轮询接口，响应后每秒一次，最多3次
  Toast.clear();
  if (++index < 3) {
    setTimeout(
      // eslint-disable-next-line no-use-before-define
      type === 'wxcode' ? () => generateWxCode(kdtId) : () => generateMpAccount(kdtId, text),
      1000
    );
  } else {
    Toast(type === 'wxcode' ? '获取小程序二维码失败' : '获取公众号二维码失败');
  }
}

export function generateMpAccount(kdtId, text, imageUrl) {
  Toast.loading();
  return requestV2({
    path: '/v3/weapp/mp-account.json',
    data: { kdt_id: kdtId },
  })
    .then((data) => {
      if (data) {
        let qrcode = '';
        if (imageUrl) {
          qrcode = imageUrl;
        } else if (data.qrcodeUrl) {
          qrcode = buildUrl(
            `/v2/weixin/scan/wximg.jpeg?s=${encodeURIComponent(data.qrcodeUrl)}`,
            'h5'
          );
        }
        Toast.clear();
        return {
          title: '订阅物流',
          type: 'focusMpAccount',
          qrcode,
          text,
        };
      }
      delayContinue('mp-account', kdtId, text);
    })
    .catch(() => {
      delayContinue('mp-account', kdtId, text);
    });
}

export function generateWxCode(kdtId) {
  Toast.loading();
  return requestV2({
    path: '/wsctrade/poster/generate-wxcode.json',
    data: {
      kdtId,
      page: 'pages/home/<USER>/index',
    },
  })
    .then((data) => {
      if (data) {
        Toast.clear();
        return {
          title: '订阅物流',
          type: 'addMiniProgram',
          qrcode: 'data:image/png;base64,' + data,
        };
      }
      delayContinue('wxcode');
    })
    .catch(() => {
      delayContinue('wxcode');
    });
}

export function getGiftId(orderNo) {
  return requestV2({
    path: '/wscump/gift/giftid.json',
    data: { orderNo },
  }).then((resp) => {
    if (resp && resp.alias) {
      return resp.alias;
    }
  });
}
