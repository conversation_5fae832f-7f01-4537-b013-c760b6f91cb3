<template>
  <van-dialog
    :show="show"
    use-slot
    show-confirm-button
    show-cancel-button
    cancel-button-text="更换门店"
    :confirm-button-color="general"
    :z-index="zIndex || 52"
    @confirm="onCreateOrder"
    @cancel="onSelectShop"
  >
    <view class="validate" v-if="show">
      <view class="title">请确认取货门店</view>
      <view class="content">
        <view class="name">{{ name }}</view>
        <view class="detail">{{ detail }}</view>
        <view :style="{ color: general }" class="distance">距离你 {{ distance }}</view>
      </view>
    </view>
  </van-dialog>
</template>

<script>
import Dialog from '@youzan/vant-tee/dist/dialog';
import Button from '@youzan/vant-tee/dist/button';
import Icon from '@youzan/vant-tee/dist/icon';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-dialog': Dialog,
    'van-button': Button,
    'van-icon': Icon,
  },

  props: {
    detail: {
      type: String,
      default: '',
    },
    distance: {
      type: String,
      default: '',
    },
    zIndex: String,
    name: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      show: false,
      expressType: '',
      general: '',
    };
  },

  created() {
    mapData(this, {
      themeColors: (val) => {
        this.general = val?.general;
      },
    });
    mapData(this, ['extra', 'expressType']);
    this.ctx.process.define('validateSelfFetchShop', () => this.validate());
  },

  methods: {
    validate() {
      // this.isOpened 避免校验过程中重复弹出
      if (
        this.expressType !== 'self-fetch' ||
        this.extra?.RETAIL_MINAPP_ORDER_SECOND_CONFIRM !== '1' ||
        this.isOpened
      ) {
        return true;
      }
      return new Promise((resolve, reject) => {
        this.show = true;
        this.$on('select', (val) => {
          this.show = false;
          this.isOpened = val;
          if (val) {
            resolve(true);
            return;
          }
          reject('重新选择店铺自提点');
        });
      });
    },

    onSelectShop() {
      this.$emit('select-address');
      this.$emit('select', false);
    },

    onCreateOrder() {
      this.$emit('select', true);
    },
  },
};
</script>

<style lang="scss" scoped>
.validate {
  padding: 24tpx;
}

.title {
  margin-bottom: 8tpx;
  font-weight: 500;
  font-size: 16tpx;
  line-height: 22tpx;
  text-align: center;
  color: #323233;
}

.content {
  padding: 16tpx;
  background: url('https://b.yzcdn.cn/public_files/2022/7/23/second_confirm_dia2.png');
  background-size: cover;
  border-radius: 8tpx;
}

.name {
  font-weight: 500;
  font-size: 14tpx;
  line-height: 22tpx;
}

.detail {
  margin-top: 4tpx;
  font-weight: 400;
  font-size: 13tpx;
  line-height: 18tpx;
  color: #999;
}

.distance {
  margin-top: 8tpx;
  font-weight: 600;
  font-size: 13tpx;
  line-height: 18tpx;
}
</style>
