<template>
  <view class="self-fetch">
    <view class="self-fetch__cell">
      <view class="self-fetch__title">自提信息</view>
      <view>
        <view class="self-fetch__content__info">
          <view v-if="selfFetchInfo.time" class="self-fetch__content__info_detail">
            <view class="self-fetch__content__info_title" :class="isTTApp ? 'w72px' : ''">
              提货时间：
            </view>
            <view class="self-fetch__content__info_item">
              {{ selfFetchInfo.time }}
            </view>
          </view>
          <view v-if="selfFetchInfo.fetcher" class="self-fetch__content__info_detail">
            <view class="self-fetch__content__info_title" :class="isTTApp ? 'w72px' : ''">
              提货人：
            </view>
            <view class="self-fetch__content__info_item">
              {{ selfFetchInfo.fetcher }}
            </view>
          </view>
          <view v-if="selfFetchInfo.address" class="self-fetch__content__info_detail">
            <view class="self-fetch__content__info_title" :class="isTTApp ? 'w72px' : ''">
              提货地址：
            </view>
            <view class="self-fetch__content__info_item item_address">
              <view class="address_text">{{ selfFetchInfo.address }}</view>
              <!-- #ifdef weapp -->
              <view
                v-if="selfFetchInfo.lng && selfFetchInfo.lat"
                class="address_icon"
                @click="onLocationWeappClick"
              />
              <!-- #endif -->
              <!-- #ifdef web -->
              <!-- 抖音增加定位图标 -->
              <view
                v-if="isTTApp && selfFetchInfo.lng && selfFetchInfo.lat"
                class="address_icon"
                @click="onLocationDouyinClick"
              />
              <!-- #endif -->
            </view>
          </view>
        </view>
      </view>

      <view class="self-fetch__action">
        <view
          v-if="order.deliveryAllowShow && order.deliveryAllowShow.allowShowModifyTimeBuckets"
          class="self-fetch__action__call"
          @click="handleModifyTime"
          style="margin-right: 12px"
        >
          <text>修改时间</text>
        </view>
        <view class="self-fetch__action__call" v-if="selfFetchInfo.tel" @click="onContactBtnClick">
          <view class="self-fetch__action__call__icon" />
          <text>联系提货点</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Loading from '@youzan/vant-tee/dist/loading/index.vue';
import { makePhoneCall } from '@youzan/tee-api';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import args from '@youzan/utils/url/args';

export default {
  components: {
    'van-loading': Loading,
  },

  props: {
    selfFetchInfo: Object,
    isPeriodBuy: Boolean,
    orderNo: String,
    kdtId: Number,
    isWeixin: Boolean,
    itemInfo: {
      type: Array,
      default: [],
    },
    rxStatus: Object,
    miniprogram: {},
    order: Object,
  },

  data() {
    return {};
  },

  computed: {
    hasPrescriptionDrugGood() {
      return this.itemInfo.some((item) => item.extra.IS_PRESCRIPTION_DRUG_GOODS);
    },
    isTTApp() {
      return this.miniprogram?.isTTApp;
    },
  },

  created() {},

  methods: {
    async onLocationDouyinClick() {
      const { lng, lat, shopName, address } = this.selfFetchInfo;
      if (lng && lat) {
        await ZNB.init();
        const tt = await ZNB.getTT();
        tt.miniProgram.openLocation({
          address,
          latitude: lat,
          longitude: lng,
          name: shopName,
        });

        // 直接使用抖音api报错：k.publish is not a function，估计是项目中znb哪里初始化影响导致，所以用了中间页跳转
        // ZNB.navigate({
        //   // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        //   url: args.add('https://h5.youzan.com/wsctrade/tt/transfer-page', {
        //     ttApi: 'openLocation',
        //     ...{
        //       address,
        //       latitude: lat,
        //       longitude: lng,
        //       name: shopName,
        //       orderNo: this.orderNo,
        //       orderKdtId: this.kdtId,
        //     },
        //   }),
        // });
      }
    },
    onLocationWeappClick() {
      const { lng, lat, shopName, address } = this.selfFetchInfo;

      if (lng && lat) {
        Tee.$native.openLocation({
          address,
          latitude: lat,
          longitude: lng,
          name: shopName,
        });
      }
    },

    onLocationWebClick() {
      const { lng, lat, shopName, addressDetail } = this.selfFetchInfo;
      const longitude = Number(lng) - 0.0065;
      const latitude = Number(lat) - 0.006;
      if (this.isWeixin) {
        ZNB.openLocation({
          latitude, // 纬度，浮点数，范围为90 ~ -90
          longitude, // 经度，浮点数，范围为180 ~ -180。
          name: shopName, // 位置名
          address: addressDetail, // 地址详情说明
          scale: 14, // 地图缩放级别,整形值,范围从1~28。默认为最大
        });
      }
    },

    onContactContentClick() {
      const { isPeriodBuy, orderNo, kdtId } = this;
      let selfFetchUrl = '';
      // 周期购自提
      if (isPeriodBuy) {
        selfFetchUrl = `/v2/trade/order/periodselffetchcode?order_no=${orderNo}`;
      } else {
        selfFetchUrl = `/wsctrade/order/selffetch/detail?orderNo=${orderNo}&kdtId=${kdtId}`;
      }
      // openWebView
      navigate({ url: selfFetchUrl, query: { title: '自提订单提货凭证' } });
    },

    onContactBtnClick() {
      makePhoneCall({ phoneNumber: this.selfFetchInfo.tel });
    },
    handleModifyTime() {
      this.$emit('onModifyTime');
    },
  },
};
</script>

<style lang="scss" scoped>
.self-fetch {
  margin-top: 10px;
  background: #fff;
  &__tips {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #636566;
    margin-top: 12px;
  }
}

.self-fetch__cell {
  padding: 15px;
}

.self-fetch__title {
  color: #323233;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #ebedf0;
  padding-bottom: 11px;
}

.self-fetch__content__info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: #646566;
}

.self-fetch__content__info .self-fetch__content__info_detail {
  line-height: 20px;
  padding-top: 11px;
  display: flex;
  .self-fetch__content__info_title {
    font-size: 14px;
    color: #969799;
    flex-basis: 70px;
    text-align: start;
    &.w72px {
      flex-basis: 72px;
    }
  }
  .self-fetch__content__info_item {
    flex: 1;
    font-size: 14px;
    color: #323233;
  }

  .item_address {
    display: inline-flex;
    .address_text {
      flex: 1;
      border-right: 1px solid #ebedf0;
      padding-right: 3px;
    }
    .address_icon {
      height: 24px;
      width: 28px;
      background: url('https://img.yzcdn.cn/public_files/2018/12/17/b3caca272aa807ce5b3056ea043df7be.png')
        no-repeat right center;
      background-size: 14px 18px;
    }
  }
}

.self-fetch__content__info .self-fetch__content__info__title {
  color: #969799;
  font-size: 14px;
}

.self-fetch__action {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.self-fetch__action__call {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 8px;
  min-width: 60px;
  font-size: 14px;
  line-height: 28px;
  color: #323233;
  background-color: #fff;
  border: 1px solid #eee;
  box-sizing: border-box;
  border-radius: 15px;
}

.self-fetch__action__call__icon {
  margin-right: 6px;
  display: inline-block;
  height: 12px;
  width: 12px;
  background: url('https://img.yzcdn.cn/public_files/2018/12/17/c7e95ec374fd995d4dd7694d7345cebb.png')
    no-repeat center center;
  background-size: 12px;
}
</style>
