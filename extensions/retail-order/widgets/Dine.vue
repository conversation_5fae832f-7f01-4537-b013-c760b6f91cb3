<template>
  <view v-if="show" class="wrapper">
    <view class="title">
      {{ tableNo }}
    </view>
    
    <view v-if="tabs.length" class="container">
      <view class="container-title">取餐方式</view>
      <view v-if="tabs.length === 1" class="single">
        {{ tabs[0].text }}
      </view>
      <view v-else class="btn-wrapper">
        <radio
          class="radio"
          custom-class="radio--custom"
          label-class="radio__text"
          v-for="item in tabs"
          :key="item.id"
          use-icon-slot
          :name="item.text"
          @change="onSwitchTab(item.id)"
        >
          {{ item.text }}
          <view slot="icon" class="icon" :class="{ 'icon--active': item.id === id }" />
        </radio>
      </view>
    </view>
  </view>
</template>

<script>
import Radio from '@youzan/vant-tee/dist/radio';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    Radio,
  },
  data() {
    return {
      show: false,
      id: '',
      tableNo: '',
      tabs: [],
    };
  },
  created() {
    mapData(this, {
      dine: (val) => {
        if (val?.isDineOrder) {
          this.ctx.event.emit('dine:afterBlockToggle');
          this.show = true;
          this.tableNo = val.tableNumber;
          this.ctx.data.tradeAddressVisible = false;
          let tabs = [];
          const { supportedPickUpWays } = this.ctx.data.pickUpWay;
          if (supportedPickUpWays) {
            tabs = Object.keys(supportedPickUpWays)
              .map((item) => {
                return {
                  id: +item,
                  text: supportedPickUpWays[item],
                };
              })
              .reverse();
          }
          this.tabs = tabs;
        }
      }
    });
  },
  methods: {
    onSwitchTab(id) {
      if (id === this.id) return;
      this.id = id;
      this.ctx.process.invoke('updateDinePickUpWay', this.tabs.find((i) => i.id === id));
      this.ctx.process.invoke('fetchShow');
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  overflow: hidden;
  margin-top: 10px;
}
.title {
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  padding: 24px 0;
  box-sizing: border-box;
  font-weight: 500;
  font-size: 32tpx;
  line-height: 40tpx;
  color: #323233;
}

.container {
  height: 48px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 12px;
    right: 12px;
    height: 1px;
    background-color: #dcdee0;
  }
}
.container-title,
.single {
  color: #323233;
  font-size: 14px;
}
.single {
  font-family: PingFangSC-Medium;
}
.btn-wrapper {
  display: flex;
}
.radio {
  margin-right: 25tpx;

  &__text {
    font-size: 14tpx;
  }

  &--custom {
    margin-bottom: 0;
  }
}
.radio:last-child {
  margin-right: 16tpx;
}
.icon {
  display: flex;
  position: relative;
  width: 16tpx;
  height: 16tpx;
  border: 1tpx solid #7d7e80;
  border-radius: 50%;
  box-sizing: border-box;
}

.icon--active {
  border-color: var(--theme-main-bg, #ee0a24);
}

.icon--active::after {
  content: '';
  position: absolute;
  top: 3tpx;
  left: 3tpx;
  width: 8tpx;
  height: 8tpx;
  border-radius: 50%;
  background-color: var(--general);
}
</style>
