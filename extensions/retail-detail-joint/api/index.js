import { requestV2 } from '@youzan/tee-biz-request';

// 获取拼单详情
export const getJointDetail = ({ orderNo, jointId, kdtId }) => {
  if (jointId) {
    return requestV2({
      path: '/retail/h5/miniprogram/orderPool/getParticipantDetail.json',
      data: {
        orderNo,
        // jointId: '1699607522312192',
        jointId,
        kdtId,
      },
    }).catch(() => []);
  }
  return Promise.resolve([]);
};
