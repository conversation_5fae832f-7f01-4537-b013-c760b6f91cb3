<!-- eslint-disable max-len -->
<template>
  <van-popup
    :show="couponPopupShow"
    closeable
    position="bottom"
    safe-area-inset-bottom
    custom-style="border-radius: var(--theme-radius-dialog,20px) var(--theme-radius-dialog,20px) 0 0; min-height:50%; max-height:80%;"
    @close="changeCouponPopupShow(false)"
  >
    <view class="coupon-popup__title">优惠券</view>
    <view class="coupon-popup__content">
      <view v-for="(coupon, index) in avlCouponList" :key="index" class="cap-couponbox">
        <coupon-card :coupon="coupon" :is-super="coupon.isVip" :vip-tag-text="vipTagText">
          <view
            v-if="!coupon.got"
            :class="['btn-box', coupon.isVip && 'btn-box-vip']"
            slot="coupon-action"
          >
            <user-authorize
              :kdt-id="kdtId"
              :auth-type-list="['mobile']"
              @next="handleAuthorizeNext(coupon)"
            >
              <view class="btn" :style="validOverArea(coupon)">{{ coupon.btnName }}</view>
            </user-authorize>
          </view>
        </coupon-card>
      </view>
    </view>
  </van-popup>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import { mapState, mapActions } from '@ranta/store';
import TakeIcon from './components/TakeIcon.vue';
import { sendCoupon, getGuideInfo } from './api';
import CouponCard from '@youzan/wsc-tee-goods-common/components/promotion-block/promotion-pop/components/CouponCard.vue';
import { navigateToMember } from '@youzan/wsc-tee-goods-common/components/promotion-block/promotion-pop/utils/navigate';
import Tee from '@youzan/tee';
import { GUIDE_TYPE } from './index';
import { errorToast } from '@youzan/tee-biz-util';

export default {
  name: 'coupon-popup',

  components: {
    'van-popup': Popup,
    'take-icon': TakeIcon,
    'coupon-card': CouponCard,
  },

  data() {
    this.firstPopup = true;

    return {
      kdtId: 0,
      sendedCoupons: [],
      couponList: [],
      themeColors: {},
      displayType: 'flat',
      panelBgStyle: '',
      valuesColorStyle: '',
      memberConfig: {},
      ...mapState(this, ['couponPopupShow']),
    };
  },

  computed: {
    avlCouponList() {
      return this.couponList.map((coupon) => ({
        ...coupon,
        headValue: coupon.value_copywriting,
        unit: coupon.unit_copywriting,
        condition: coupon.use_threshold_copywriting,
        title: coupon.title,
        valid: coupon.valid_time_copywriting,
        actionText: '',
        got: !this.sendedCoupons.every((activityId) => activityId !== coupon.id),
        btnName: coupon.need_guide && coupon.isVip ? '入会领取' : '立即领取',
      }));
    },

    vipTagText() {
      return `${this.memberConfig.name}专享`;
    },
  },

  watch: {
    couponPopupShow(value) {
      value ? this.showPopup() : this.handleClose();
    },
  },

  created() {
    mapData(this, ['kdtId', 'themeCSS', 'couponList', 'memberConfig']);
    mapData(this, {
      themeColors: (val) => {
        const mainBgRGB = this.ctx.lambdas.hexToRgb(val.general);
        val['general-alpha10'] = `rgba(${mainBgRGB[0]}, ${mainBgRGB[1]}, ${mainBgRGB[2]}, .1)`;
        this.themeColors = val;
      },
    });
    mapActions(this, ['changeCouponPopupShow']);
  },

  methods: {
    handleClose() {
      if (this.sendedCoupons.length === 0) {
        Toast('你有可用优惠券还未领取');
      }
    },

    validOverArea(coupon) {
      if (coupon.valid.length > 15) {
        return 'margin-top: -8px;';
      }
    },

    showPopup() {
      if (this.firstPopup) {
        this.firstPopup = false;

        this.couponList.forEach((coupon) => {
          this.ctx.logger &&
            this.ctx.logger.log({
              et: 'view', // 事件类型
              ei: 'view', // 事件标识
              en: '领券列表的券曝光', // 事件名称
              params: {
                item_type: 'coupon',
                item_id: coupon.id,
                component: 'takeCoupon',
                kdt_id: this.kdtId,
              }, // 事件参数
            });
        });
      }
    },

    handleSendCoupon(coupon) {
      this.ctx.logger?.log({
        et: 'click', // 事件类型
        ei: 'take_coupon_click_take', // 事件标识
        en: '领券列表的券点击领取', // 事件名称
        params: {
          item_type: 'coupon',
          item_id: coupon.id,
          component: 'takeCoupon',
          kdt_id: this.kdtId,
        }, // 事件参数
      });

      sendCoupon({ alias: coupon.alias })
        .then((res) => {
          const failText = coupon.fastJoin
            ? `入会成功，但领券失败，${res.msg || '请稍后重试'}`
            : res.msg || '领取失败';
          if (res?.couponId) {
            Toast(coupon.fastJoin ? '已成功入会并领券' : '领取成功');
            this.sendedCoupons = [...this.sendedCoupons, coupon.id];
          } else {
            Toast(failText);
          }
        })
        .catch((err) => {
          errorToast(err, { message: '领取失败' });
        })
        .finally(() => {
          if (coupon.fastJoin) {
            setTimeout(() => {
              /* #ifdef web */
              window.location.reload();
              /* #else */
              Tee.$native.startPullDownRefresh();
              /* #endif */
            }, 500);
          }
        });
    },

    handleAuthorizeNext(args) {
      const { need_guide: needGuide, id, isVip } = args;
      if (needGuide && isVip) {
        getGuideInfo({
          guideType: 'coupon',
          voucherActivityId: id,
        }).then((data) => {
          // 快速入会
          if (data.guideType === GUIDE_TYPE.FREE) {
            this.ctx.process.invoke('launchFastJoinSDK', {
              hiddenToast: true,
              successCallback: () => {
                this.handleSendCoupon({ ...args, fastJoin: true });
              },
            });
          } else if (data.needGuide) {
            navigateToMember(data, this.kdtId);
          }
        });
      } else {
        this.handleSendCoupon(args);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.coupon-popup {
  &__title {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    text-align: center;
    color: #323233;
    padding-top: 11px;
    padding-bottom: 11px;
  }

  &__content {
    padding: 16px 16px 50px;
    overflow: auto;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
    position: relative;
    min-height: 45vh;
    max-height: calc(75vh - env(safe-area-inset-bottom));
    .cap-couponbox {
      margin-bottom: 12px;
      transition: height 0.2s linear;
      .btn-box {
        display: flex;
        align-items: center;
        padding-right: 12px;
        .btn {
          padding: 0 6px;
          color: var(--ump-main-text, #fff);
          background-color: var(--ump-main-bg, #323233);
          border-radius: var(--theme-radius-button, 12px);
          font-family: PingFangSC-Regular, sans-serif;
          font-size: 12px;
          text-align: center;
          height: var(--theme-ump-coupon-button-height, 20px);
          line-height: var(--theme-ump-coupon-button-height, 20px);
        }
      }
      .btn-box-vip {
        .btn {
          background-color: #e7c5a5;
          color: #674531;
        }
      }
    }
  }
}
</style>
