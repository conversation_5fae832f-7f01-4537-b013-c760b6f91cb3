export default function initProcess(ctx, store) {
  const processMap = {
    openShare() {
      ctx.process.invoke('navigateTo', store.shareUrl);
    },
    openDetail() {
      const detailUrl = store.buttonGroup.DETAIL && store.buttonGroup.DETAIL.url;
      ctx.process.invoke('navigateTo', detailUrl);
    },
    openHome() {
      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      const homeUrl = `https://h5.youzan.com/v2/showcase/homepage?kdt_id=${store.kdtId}`;
      ctx.process.invoke('navigateTo', homeUrl);
    },
    openResult() {
      ctx.process.invoke('navigateTo', store.returnUrl);
    },
    // 提供跳转外链的process
    openOutLink(url) {
      ctx.process.invoke('navigateTo', url);
    },
  };
  // eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
  Object.keys(processMap).forEach((key) => ctx.process.define(key, processMap[key]));

  return () => {
    // eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
    Object.keys(processMap).forEach((key) => ctx.process.undef(key, processMap[key]));
  };
}
