<template>
  <!-- h5微信环境下特有组件 -->
  <view v-show="showWxSubscribe" :style="themeCSS">
    <view class="wx-subscription" :style="'background-color: ' + bgColor + ';'">
      <image
        src="https://b.yzcdn.cn/pay-result/images/weixin2.png"
        alt="wechat"
        class="wx-subscription__image"
      />
      <view class="wx-subscription__text">
        <view class="main">订阅微信通知</view>
        <view class="des">及时获取订单进度提醒</view>
      </view>
      <!-- 外部注入组件 -->
      <msg-subscribe
        :scene="scene"
        @subscribe-success="successFn"
        @subscribe-error="errorFn"
        @get-subscribe-comp-visible="getSubscribeCompVisibleFn"
      >
        <script type="text/wxtag-template">
          <style>
            .wx-subscription__dy {
              height: 28px;
              width: 76px;
              font-size: 12px;
              font-weight: 400;
              line-height: 28px;
              text-align: center;
              margin: 0 12px;
              background: #fff;
              {{ btnStyle }}
            }
          </style>
          <div class="wx-subscription__dy">
            {{ text }}
          </div>
        </script>
      </msg-subscribe>
    </view>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import env from '@youzan/wsc-tee-trade-common/lib/env';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  props: {
    plainBtn: {
      type: Boolean,
      default: true,
    },
    text: {
      type: String,
      default: '立即订阅',
    },
    bgColor: {
      type: String,
      default: '#fff',
    },
  },

  data() {
    return {
      // h5场景由原生组件通知
      showWxSubscribeFlag: false,
      themeGeneralColor: '',
      mpData: {},
      scene: 'afterPaySuccess',
      themeCSS: '',
      themeColors: {},
    };
  },

  computed: {
    btnStyle() {
      return this.plainBtn
        ? `color: ${this.themeColors.icon}`
        : `background: ${this.themeColors['main-bg']}; color: ${
            this.themeColors['main-text']
          }; border-radius: ${this.ctx.data?.themeRadius?.button || '15px'}`;
    },
    showWxSubscribe() {
      // 微信 && 店铺绑定公众号
      return env.isWeixin && !!this.mpData.mpId && this.showWxSubscribeFlag;
    },
  },

  created() {
    mapData(this, ['themeCSS']);
    mapData(this, {
      themeColors: (val) => {
        const themeGeneralColor = val.general;
        this.themeColors = val;
        this.themeGeneralColor = themeGeneralColor;
      },
      payResult: (payResult) => {
        this.mpData = payResult.mpData ?? {};
      },
    });
  },

  methods: {
    // 获取订阅组件的可见性（服务号是否认证、模板列表是否可用）
    getSubscribeCompVisibleFn(status) {
      this.showWxSubscribeFlag = status;
      this.$emit('psmshow', status);
      if (this.showWxSubscribeFlag) {
        this.ctx.logger?.log({
          et: 'view',
          ei: 'Subscribe_logistics_view',
          en: '曝光订阅物流',
          pt: 'paySuccess',
        });
      }
    },

    successFn(msg) {
      if (msg.status) {
        Toast('订阅成功，继续订阅可获得多次提醒');
        this.ctx.logger?.log({
          et: 'click',
          ei: 'allow_click',
          en: '点击允许',
          params: {
            component: 'subscribe_order',
          },
        });
        this.ctx.logger?.log({
          et: 'view',
          ei: 'Subscribe_success_toast_view',
          en: '订阅成功提示曝光',
          pt: 'paySuccess',
        });
      } else {
        this.ctx.logger?.log({
          et: 'click',
          ei: 'cancle_click',
          en: '点击取消',
          params: {
            component: 'subscribe_order',
          },
        });
      }
      this.ctx.event.emit('hideSubscriptionDialog');
    },

    errorFn(e) {
      if (!this.showWxSubscribe) return;
      if (e.code === 'NO_TEMPLATE') {
        this.showWxSubscribeFlag = false;
      } else {
        // 一些微信侧报出的错误，可以更加errCode排查
        // https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_Open_Tag.html#%E5%BC%80%E6%94%BE%E6%A0%87%E7%AD%BE
        Toast('订阅失败，code:' + e.errCode);
      }
    },
  },
};
</script>

<style lang="scss">
// 微信订阅弹窗样式
.wx-subscription {
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  margin: 12px;
  border-radius: var(--theme-radius-card, 12px);

  &__image {
    width: 52px;
    height: 40px;
    margin: 8px;
  }

  &__text {
    flex: 1;
    margin-left: 8px;
    text-align: left;

    .main {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      line-height: 20px;
    }

    .des {
      font-size: 12px;
      color: #7d7e80;
      font-weight: 400;
      line-height: 16px;
      margin-top: 4px;
    }
  }

  &__btn {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin-right: 8px;
  }
}

.overlay-wrapper {
  position: fixed;
  width: 200px;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;

  &--img {
    width: 200px;
    height: 146px;
    border-radius: 4px;
  }
}
</style>
