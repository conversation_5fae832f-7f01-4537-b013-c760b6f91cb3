<template>
  <view class="take-icon" :style="outerStyle">
    <view class="take-icon-inner" :style="innerStyle">已领</view>
  </view>
</template>

<script>
export default {
  props: {
    mainColor: String,
  },

  computed: {
    innerStyle() {
      let style = `color: var(--ump-icon, #323233);border-color: var(--ump-border, #323233);background-color: var(--ump-coupon-bg, #f2f2ff);`;

      /* #ifdef weapp */
      style += `margin-top: 15px;margin-left: 12px;`;
      /* #endif */

      return style;
    },
    outerStyle() {
      return `border-color: var(--ump-border, #323233);`;
    },
  },
};
</script>

<style lang="scss">
.take-icon {
  font-size: 10tpx;
  display: inline-block;
  width: 44tpx;
  height: 44tpx;
  border-radius: 50%;
  border: 2px solid red;
  opacity: 0.5;
  /* #ifdef web */
  &-inner {
    margin: 4px;
  }
  /* #endif */
}
</style>
