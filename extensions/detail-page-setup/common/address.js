import { baiduToGcj } from '@youzan/shop-tee-shared';

const ADDRESS_KEYS = [
  'country',
  'province',
  'city',
  'county',
  'community',
  'addressDetail',
  'address_detail', // 兼容旧版数据
  'houseNumber',
];

const stringifyAddress = (address, start = 0, end = ADDRESS_KEYS.length) => {
  const keys = ADDRESS_KEYS.slice(start, end);

  // 判断省市是否相同
  if (address[keys[1]] && address[keys[1]] === address[keys[2]]) {
    keys.splice(1, 1);
  }

  // 中国不需要展示
  if (address[keys[0]] === '中国') {
    keys.shift(0);
  }

  return keys
    .filter((key) => address[key])
    .map((key) => address[key])
    .join('');
};

const getDeliveryAddress = (address) => {
  let [country, province, city, county, addressDetail] = new Array(5).fill('');

  if (!address.selfFetchInfo) {
    country = address.deliveryCountry || '';
    province = address.deliveryProvince || '';
    city = address.deliveryCity || '';
    county = address.deliveryDistrict || '';
    addressDetail = address.deliveryStreet || '';
  }

  return stringifyAddress({
    country,
    province,
    city,
    county,
    addressDetail,
  });
};

const getSelfFetchAddress = (selfFetchInfo) => {
  const { country = '', province = '', city = '', county = '', addressDetail = '' } = selfFetchInfo;

  return stringifyAddress({
    country,
    province,
    city,
    county,
    addressDetail,
  });
};

const getSelfFetchLngLat = (selfFetchInfo) => {
  const lng = selfFetchInfo.lng || selfFetchInfo.lon || '';
  const lat = selfFetchInfo.lat || '';

  if (lng && lat) {
    return baiduToGcj(lng, lat);
  }
  return { lng: '', lat: '' };
};

const getVerifyAddress = (addressInfo) => {
  const { province = '', city = '', area = '', address = '' } = addressInfo;
  return `${province}${city}${area}${address}`;
};

const getVerifyAddressLngLat = (addressInfo) => {
  const { lng = '', lat = '' } = addressInfo;
  if (lng && lat) {
    return baiduToGcj(lng, lat);
  }
  return { lng: '', lat: '' };
};

export {
  getDeliveryAddress,
  getSelfFetchAddress,
  getSelfFetchLngLat,
  getVerifyAddress,
  getVerifyAddressLngLat,
};
