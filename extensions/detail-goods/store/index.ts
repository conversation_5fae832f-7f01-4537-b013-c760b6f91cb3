import { IStoreObject, mergeStore } from './helper';

import goods from './modules/goods';

// 展示渲染视图层
import { createStore } from '@ranta/store';

const defaultStore: IStoreObject = {
  state: {},
  getters: {},
  actions: {},
};

const rootStore: IStoreObject = [defaultStore, ...[goods]].reduce((a, b) => mergeStore(a, b), {});

export default function createAddressStore(ctx) {
  return createStore({
    state: () => ({
      ...rootStore.state,
      ...goods.getState(ctx),
    }),
    getters: {
      ...rootStore.getters,
      ...goods.getters,
    },
    actions: {
      ...rootStore.actions,
      ...goods.getActions(ctx),
    },
  });
}
