/**
 * com.youzan.ump.voucher.core.api.dto.goods.GoodsAvlActivityDTO
 * 商品可用优惠活动_DTO
 */
interface IGoodsAvlActivityDTO {
  /** 优惠方式 */
  preferential_mode?: number;
  /** 有效期生成类型 */
  valid_time_generate_type?: number;
  /** 使用门槛文案  - 满_xx元使用  - 无使用门槛 */
  use_threshold_copywriting?: string;
  /** 店铺id */
  kdt_id?: number;
  /** 随机券面额范围上限（单位：分） */
  max_value?: number;
  /** 优惠上限文案，最多减xx */
  upper_limit_copywriting?: string;
  /** 是否新人券 */
  is_newcomer_voucher?: boolean;
  /** 使用门槛+优惠面额文案  - 满_xx减xx  - 无门槛减xx  - 无门槛打_x折  - 满_xx打_x折 */
  use_threshold_and_value_copywriting?: string;
  /** 优惠活动名称 */
  title?: string;
  /** 单位文案，元、万、折 */
  unit_copywriting?: string;
  /** 优惠面额文案  已做好单位换算，与unit_copywriting组合使用 */
  value_copywriting?: string;
  /** 有效时间文案  1. 绝对可用时间时的展示文案：2019-01-01 00:00:00 - 2019-01-02 01:00:00  2. 相对可用时间时的展示文案：领券当/次日起x天内可用 */
  valid_time_copywriting?: string;
  /** 随机券面额范围下限（单位：分） */
  min_value?: number;
  /** 满件 */
  threshold_piece?: number;
  /** 活动别名 */
  alias?: string;
  /** 活动开始时间 */
  start_time?: string;
  /** 活动_id */
  id?: number;
  /** 剩余库存 */
  remain_stock?: number;
  /** 活动结束时间 */
  end_time?: string;
  /** 只有固定面额或折扣有效,优惠面额（单位：分）, 折扣 （例：88，8.8折） */
  value?: number;
  /** 是否受限制的券  true:受限制  false：不受限制 */
  has_sending_rule_limit?: boolean;
  /** 使用门槛类型 0：无门槛 1：满元 2：满件 */
  threshold_type?: number;
  /** 核销门槛 */
  use_threshold?: number;
}

interface ThemeColors {
  general: string;
  'main-bg': string;
  'main-text': string;
  'vice-bg': string;
  'vice-text': string;
  'start-bg': string;
  'end-bg': string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.LocalDeliveryPostageVO.PreferentialInfo
 */
interface IPreferentialInfo {
  /** 满 XXX 元 */
  meet?: number;
  /** 减免  YYY 元 */
  decrease?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.LocalDeliveryPostageVO
 */
interface ILocalDeliveryPostageVO {
  /** 当前同城商品价格和 */
  totalPrice?: number;
  /** 即将享受的优惠减免、为空则下一阶段的减免优惠 */
  nextPreferentialInfo?: IPreferentialInfo;
  /** 当前享受的优惠减免、为空则没有优惠 */
  currentPreferentialInfo?: IPreferentialInfo;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.CartDeliveryVO
 */
interface ICartDeliveryVO {
  /** 同城配送邮费信息 */
  localDeliveryPostage?: ILocalDeliveryPostageVO;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartActivityVO.PresentGoodsInfo
 */
interface IPresentGoodsInfo {
  /** 是否可用 */
  isAvailable?: boolean;
  /** 不可用原因 */
  reason?: string;
  /** 图片信息 */
  attachmentUrl?: string;
  goodsId?: number;
  /** 数量 */
  num?: number;
  /** 商品alias */
  alias?: string;
  /** 限购原因 */
  limitReason?: string;
  /** 赠品对应商品金额 */
  originPrice?: number;
  /** 商品名称 */
  title?: string;
  /** sku信息 */
  sku?: string;
  skuId?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.SkuCompositeIdVO
 */
interface ISkuCompositeIdVO {
  activityId?: number;
  goodsId?: number;
  cartId?: number;
  skuId?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartActivityVO.SimpleGoodsDTO
 */
interface ISimpleGoodsDTO {
  /** 购物车商品优惠是否有效  注: 并不表示商品失效, 是否失效由业务方决定  优惠套餐, 优惠失效则商品失效  营销商品, 优惠失效, 则商品失效. */
  valid?: boolean;
  /** 用以标识唯一条目, 无活动商品默认为0 */
  activityId?: number;
  /** 关联商品, 若为空, 则表示商品存在性与关联商品无关 */
  relatedGoods?: ISkuCompositeIdVO[];
  goodsId?: number;
  /** 活动价 */
  price?: number;
  /** 购物车记录唯一ID */
  cartId?: number;
  skuId?: number;
  /** 购物车商品优惠失效原因 */
  invalidReason?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartActivityVO
 */
interface ICartActivityVO {
  totalPresentNum?: number;
  /** 活动赠品 */
  presentList?: IPresentGoodsInfo[];
  /** 换购金额 */
  exchangePrice?: number;
  /** 是否满足条件 */
  hasMeet?: boolean;
  /** 活动时间 */
  activityDuration?: string;
  selectablePresents?: IPresentGoodsInfo[];
  /** 该商品存在的门槛金额, 非0表示关联商品的价格需要大于等于该金额 */
  conditionPrice?: number;
  /** 优惠描述 */
  activityDesc?: string;
  /** 优惠ID */
  activityId?: number;
  /** 活动alias */
  activityAlias?: string;
  /** 跳转链接 */
  activityUrl?: string;
  /** 参与优惠活动商品列表 */
  activityItems?: ISimpleGoodsDTO[];
  /** 优惠类型 */
  activityType?: number;
  /** 优惠 */
  activityTags?: string[];
  selectablePresentNum?: number;
  /** 加价购可换购商品数量 */
  totalExchangeSize?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.CartGroupActivityInfoVO.PresentInfo
 */
interface IPresentInfo {
  /** 赠品是否可用 */
  isAvailable?: boolean;
  /** 不可用原因 */
  reason?: string;
  /** 图片信息 */
  attachmentUrl?: string;
  goodsId?: number;
  /** 数量 */
  num?: number;
  isSelected?: boolean;
  /** 商品alias */
  alias?: string;
  /** 赠品对应商品金额 */
  originPrice?: number;
  /** 赠品id */
  id?: number;
  /** 商品名称 */
  title?: string;
  /** sku信息 */
  sku?: string;
  skuId?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.CartGroupActivityInfoVO
 * 分组活动信息
 */
interface ICartGroupActivityInfoVO {
  /** 总赠品数量 */
  totalPresentNum?: number;
  /** 活动赠品 */
  presentList?: IPresentInfo[];
  /** 换购金额 */
  exchangePrice?: number;
  /** 活动时间 */
  activityDuration?: string;
  /** 可选赠品 */
  selectablePresents?: IPresentInfo[];
  /** 该商品存在的门槛金额, 非0表示关联商品的价格需要大于等于该金额 */
  conditionPrice?: number;
  /** 优惠描述 */
  activityDesc?: string;
  /** 优惠ID */
  activityId?: number;
  /** 活动alias */
  activityAlias?: string;
  /** 跳转链接 */
  activityUrl?: string;
  /** 是否满足条件 */
  meet?: boolean;
  /** 优惠类型 */
  activityType?: number;
  /** 优惠标签 */
  activityTags?: string[];
  /** 可选择赠品数量 */
  selectablePresentNum?: number;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.SettlementRuleVO
 * 分开结算规则
 */
interface ISettlementRuleVO {
  /** 是否支持跨店结算 */
  crossStore?: boolean;
  /** 单独结算标识  目前包括:  普通商品, 海淘商品, 周期购商品  COMMON, HAITAO, PERIOD_BUY; */
  settlementMark?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartItemPropertyValueVO
 * Created by dsk Date: 2020/2/12 11:13
 */
interface ICartItemPropertyValueVO {
  /** 交易商品属性值ID */
  propValueId?: number;
  /** 属性价格 */
  price?: number;
  /** 交易商品属性值名字 */
  propValueName?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartItemPropertyVO
 * Created by dsk Date: 2020/2/12 11:12
 */
interface ICartItemPropertyVO {
  /** 交易商品属性项ID */
  propId?: number;
  /** 交易商品属性值列表 */
  propValueList?: ICartItemPropertyValueVO[];
  /** 交易商品属性项名字 */
  propName?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.SubComboVO
 * 套餐子商品信息
 */
interface ISubComboVO {
  /** 图片地址(前端使用此字段) */
  attachmentUrl?: string;
  goodsId?: number;
  groupId?: number;
  num?: number;
  propertyIds?: number[];
  addPrice?: number;
  /** 商品名称 */
  title?: string;
  price?: number;
  /** 商品别名 */
  alias?: string;
  /** sku 描述信息 */
  skuDesc?: string;
  /** 压缩后图片地址 */
  thumbUrl?: string;
  skuId?: number;
  properties?: ICartItemPropertyVO[];
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.ComboGroupVO
 * 商品套餐分组信息
 */
interface IComboGroupVO {
  /** 套餐子商品信息 */
  subComboList?: ISubComboVO[];
  /** 套餐分组规则 */
  rule?: string;
  /** 套餐分组id */
  id?: number;
  title?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.ComboVO
 * 套餐商品
 */
interface IComboVO {
  /** 商品套餐类型 0 固定套餐 1 自选套餐 */
  comboType?: number;
  groupList?: IComboGroupVO[];
}

/**
 * java.util.Map
 */
interface IMap<T1, T2> {}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.BizMarkExtensionVO
 * 扩展信息（如爱逛直播id）
 */
interface IBizMarkExtensionVO {
  cartBizMark?: IMap<string, string>;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.CartGroupGoodsInfoVO
 * 新分组购物车商品信息
 */
interface ICartGroupGoodsInfoVO {
  /** 置灰商品选中按钮 */
  disableSelect?: boolean;
  /** 商品分组信息 */
  groupIdList?: number[];
  /** 商品数量 */
  num?: number;
  /** 商品原价 */
  originPrice?: number;
  /** 营销活动id */
  activityId?: number;
  /** 是否包含多规格 */
  hasMultiSku?: boolean;
  /** 商品优惠活动 */
  activityAlias?: string;
  /** 预估到手价 */
  estimatedPrice?: number;
  /** 商品库存 */
  stockNum?: number;
  /** 加购时间 */
  createdTime?: number;
  /** 库存紧张提示 */
  isShowStockShort?: boolean;
  /** 商品是否复活（多规格商品售罄复活） */
  revive?: boolean;
  /** 商品sku */
  sku?: string;
  /** 库存id */
  skuId?: number;
  /** 商品限购数量 */
  limitNum?: number;
  /** 更新时间 */
  updatedTime?: number;
  /** 埋点相关 */
  extraAttribute?: string;
  /** 店铺id */
  kdtId?: number;
  /** 购物车记录id */
  cartId?: number;
  /** 分开结算规则 */
  settlementRule?: ISettlementRuleVO;
  /** 库存余量提示 */
  isShowStockNum?: boolean;
  /** 商品重量 */
  weight?: number;
  /** ebiz 餐饮购物车记录Id（用来更新，删除） */
  canyinId?: number;
  /** 套餐信息 */
  combo?: IComboVO;
  /** 有赞担保标记 */
  yzGuarantee?: boolean;
  /** 是否支持分期 */
  isInstallment?: boolean;
  /** 商品类型 */
  goodsType?: number;
  /** 活动结束时间 */
  activityEndTime?: string;
  /** 关税费 */
  tariffPrice?: number;
  /** 商品留言 */
  messages?: string;
  /** 置灰商品选中按钮 */
  disableSelectMsg?: string;
  /** 待开售时间 */
  startSoldTime?: number;
  /** 商品id */
  goodsId?: number;
  /** 购物车商品属性值ID列表 */
  propertyIds?: number[];
  /** 活动结束时间 */
  activityStartTime?: string;
  /** 商品名称 */
  title?: string;
  /** 扩展信息（如爱逛直播id） */
  bizExtension?: IBizMarkExtensionVO;
  /** 优惠后的价格 */
  payPrice?: number;
  /** 物流配送方式列表: [NORMAL_EXPRESS, SELF_TAKE, LOCAL_DELIVERY]  NORMAL_EXPRESS:普通快递  SELF_TAKE: 到店自提  LOCAL_DELIVERY: 同城配送 */
  logisticsTypeList?: string[];
  /** 营销活动标签(商品级) */
  activityTag?: string;
  /** 商品别名 */
  alias?: string;
  /** 商品是否勾选 */
  selectState?: number;
  ingredientInfoList?: ICartGroupGoodsInfoVO[];
  /** 渠道id */
  channelId?: number;
  /** 是否支持七天无理由退换货 */
  isSevenDayUnconditionalReturn?: boolean;
  /** 图片地址 */
  attachmentUrl?: string;
  /** 门店id */
  storeId?: number;
  /** 商品起售数量 */
  startSaleNum?: number;
  /** 失效信息 */
  errorMsg?: string;
  /** 营销活动类型(商品级) */
  activityTypeStr?: string;
  /** 比加入时降价 */
  cutPrice?: number;
  /** 关税模式 0不含税, 1含税 */
  tariffRule?: number;
  /** 营销活动类型 */
  activityType?: string;
  /** 购物车商品属性信息 */
  properties?: ICartItemPropertyVO[];
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.CartGoodsGroupVO
 * 购物车新分组
 */
interface ICartGoodsGroupVO {
  /** 活动信息（为空则该分组无活动） */
  groupActivityInfo?: ICartGroupActivityInfoVO;
  /** 分组内最近加购时间（用于分组排序） */
  latestAddCartTime?: number;
  /** 商品列表 */
  goodsList?: ICartGroupGoodsInfoVO[];
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartBizMarkVO
 * 商品维度业务元素
 */
interface ICartBizMarkVO {
  /** 直播id */
  liveStreamingId?: string;
  /** 扩展字段 */
  cartBizMark?: IMap<string, string>;
  /** 营销活动透传字段(目前只有0元抽奖码任务商品下单有用) */
  promotionMark?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartGoodsVO
 * 购物车商品
 */
interface ICartGoodsVO {
  serviceType?: number;
  /** 置灰商品选中按钮 */
  disableSelect?: boolean;
  groupIdList?: number[];
  /** 商品数量 */
  num?: number;
  /** 错误码 */
  errorCode?: number;
  /** 已废弃 */
  discount?: number;
  /** 商品原价 */
  originPrice?: number;
  /** 营销活动id */
  activityId?: number;
  /** 商品优惠活动() */
  activityAlias?: string;
  /** 是否包含多规格 */
  hasMultiSku?: boolean;
  /** 活动标题 */
  activityTitle?: string;
  /** 商品库存 */
  stockNum?: number;
  createdTime?: number;
  /** 商品是否复活（多规格商品售罄复活） */
  revive?: boolean;
  nobody?: string;
  /** 商品sku */
  sku?: string;
  /** 库存id */
  skuId?: number;
  /** 商品ump优惠 */
  ext?: string;
  /** 商品限购数量 */
  limitNum?: number;
  updatedTime?: number;
  /** 周期购周期 */
  period?: number;
  /** 埋点相关 */
  extraAttribute?: string;
  /** 店铺id */
  kdtId?: number;
  /** 购物车条目ID */
  cartId?: number;
  /** 分开结算规则 */
  settlementRule?: ISettlementRuleVO;
  supportExpressType?: string;
  /** ebiz 餐饮购物车记录Id（用来更新，删除） */
  canyinId?: number;
  /** 商品重量,单位：g */
  weight?: number;
  /** 套餐信息 */
  combo?: IComboVO;
  /** 区分如是否是蛋糕烘焙的字段：json {"code":"10000000001"} */
  bizMark?: string;
  /** 有赞担保标记 */
  yzGuarantee?: boolean;
  /** 是否支持分期 */
  isInstallment?: boolean;
  /** 商品类型 */
  goodsType?: number;
  /** 关税费 */
  tariffPrice?: number;
  /** 商品留言 */
  messages?: string;
  subType?: number;
  /** 置灰商品选中按钮 */
  disableSelectMsg?: string;
  /** 周期购数量 */
  periodNum?: number;
  /** 待开售时间 */
  startSoldTime?: number;
  /** 商品id */
  goodsId?: number;
  /** 已废弃 */
  discountPrice?: number;
  /** 购物车商品属性值ID列表 */
  propertyIds?: number[];
  /** 商品维度业务元素, 请求体 */
  cartBizMarkDTO?: ICartBizMarkVO;
  /** 商品名称 */
  title?: string;
  platform?: string;
  /** 扩展信息返回, 返回体 */
  bizExtension?: IBizMarkExtensionVO;
  /** 优惠后的价格 */
  payPrice?: number;
  /** 物流配送方式列表: [NORMAL_EXPRESS, SELF_TAKE, LOCAL_DELIVERY]  NORMAL_EXPRESS:普通快递  SELF_TAKE: 到店自提  LOCAL_DELIVERY: 同城配送 */
  logisticsTypeList?: string[];
  /** 营销活动标签(商品级) */
  activityTag?: string;
  /** 商品别名 */
  alias?: string;
  /** 商品是否勾选 */
  selectState?: number;
  /** 压缩后图片地址 */
  thumbUrl?: string;
  ingredientInfoList?: ICartGoodsVO[];
  channelId?: number;
  /** 周期购配送时间 */
  deliverTime?: number;
  /** 是否支持七天无理由退换货 */
  isSevenDayUnconditionalReturn?: boolean;
  /** 图片地址(前端使用此字段) */
  attachmentUrl?: string;
  /** 门店id */
  storeId?: number;
  /** 来源 */
  dcps?: string;
  /** 商品起售数量 */
  startSaleNum?: number;
  errorMsg?: string;
  /** 营销活动类型(商品级) */
  activityTypeStr?: string;
  directSeller?: number;
  /** 比加入时降价 */
  cutPrice?: number;
  /** 关税模式 0不含税 ， 1含税 */
  tariffRule?: number;
  /** 营销活动类型 */
  activityType?: string;
  /** 购物车商品属性信息 */
  properties?: ICartItemPropertyVO[];
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.result.PromotionDetailVO
 * 优惠明细dto
 */
interface IPromotionDetailVO {
  /** 优惠金额，单位分 */
  preferentialValue?: number;
  /** 优惠类型 */
  appType?: number;
  /** 优惠名称 */
  appName?: string;
  /** 参与的活动id */
  activityIds?: string[];
  /** 权益id */
  benefitId?: string;
}

/**
 * com.youzan.trade.shopping.cart.api.view.vo.param.CartListVO
 */
interface ICartListVO {
  /** 店铺id */
  kdtId?: number;
  /** 选中商品的优惠金额总计（展示优惠明细时使用） */
  selectedDiscountFee?: number;
  /** 店铺名称 */
  shopName?: string;
  /** 是否开起快递配送  2018年12月11日  慕阳（许曦鸣）与前端确认没有用到可以去掉 */
  express?: boolean;
  /** 店铺访问url */
  shopUrl?: string;
  /** 购物车物流信息 */
  cartDelivery?: ICartDeliveryVO;
  /** 选中商品的优惠金额总计（订单级） */
  selectedPreferencePrice?: number;
  /** 用途：最近有添加购物车的供应商排前面 */
  latestAddCartTime?: number;
  /** 符合某种活动的商品信息   <pre>  目前只支持打包一口价  </pre> */
  activities?: ICartActivityVO[];
  /** 新可购买商品分组 */
  goodsGroupList?: ICartGoodsGroupVO[];
  /** 门店名称 */
  storeName?: string;
  /** 是否展示优惠明细 */
  showPromotionDetail?: boolean;
  /** 可购买商品列表 */
  items?: ICartGoodsVO[];
  /** 不可购买商品列表 */
  unavailableItems?: ICartGoodsVO[];
  /** 优惠明细 */
  promotionDetails?: IPromotionDetailVO[];
}

export default interface ExtensionMetadata {
  data: {
    provide: {
      editMode: 'submit' | 'edit';
    };
    consume: {
      couponList: IGoodsAvlActivityDTO[];
      themeColors: ThemeColors;
      shopCart: ICartListVO;
    };
  };
}
