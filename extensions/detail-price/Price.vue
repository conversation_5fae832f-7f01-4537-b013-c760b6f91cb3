<template>
  <van-cell-group custom-class="price-panel" :border="false" :style="themeVars">
    <!-- 支付信息 -->
    <van-cell custom-class="price-cell_margin">
      <view v-if="price.total" class="price-panel__item">
        <text class="price-panel__item__title">商品金额</text>
        <text class="price-panel__item__value">{{ price.total }}</text>
      </view>
      <view v-if="price.postage && !needShowDeliveryCouponBlock" class="price-panel__item">
        <text class="price-panel__item__title">{{ postageTitle }}</text>
        <text class="price-panel__item__value">{{ price.postage }}</text>
      </view>
      <!-- 使用了运费抵扣券的运费费 -->
      <delivery-coupon-activities
        v-if="needShowDeliveryCouponBlock"
        :postage="price.postage"
      ></delivery-coupon-activities>
      <!-- 积分兑换 -->
      <view v-if="order.pointsPrice" class="price-panel__item">
        <text class="price-panel__item__title">{{ payment.pointsName }}兑换</text>
        <text class="price-panel__item__value">-{{ order.pointsPrice + payment.pointsName }}</text>
      </view>
      <view v-if="price.taxPrice" class="price-panel__item">
        <text class="price-panel__item__title">进口税（含运费税款）</text>
        <text class="price-panel__item__value">{{ price.taxPrice }}</text>
      </view>

      <popup-activities />

      <!-- 内购券 -->
      <view v-if="fissionTicketNum" class="price-panel__item">
        <text class="price-panel__item__title">内购券</text>
        <text class="price-panel__item__value">{{ fissionTicketNum }}张</text>
      </view>

      <!-- 支付优惠 -->
      <view v-if="price.payUmpDiscountMoney" class="price-panel__item">
        <text class="price-panel__item__title">支付优惠</text>
        <text class="price-panel__item__value">
          {{ formatedPayUmpDiscountMoney }}
        </text>
      </view>
      <!-- 优惠券 -->
      <coupon-activities />
      <!-- 活动 -->
      <panel-activities />

      <!-- 附加费用 -->
      <extra-fees :popup-show="popupShow" @popup-toggle="popupToggle" />

      <!-- 储值卡/礼品卡 -->
      <view v-if="price.prepayCardPay" class="price-panel__item">
        <text class="price-panel__item__title">储值卡/礼品卡</text>
        <text class="price-panel__item__value">{{ price.prepayCardPay }}</text>
      </view>

      <!-- 三方券相关信息展示 -->
      <view
        v-for="assetPayInfo in price.assetPayInfos"
        :key="assetPayInfo.payWay"
        class="price-panel__item"
      >
        <text class="price-panel__item__title">{{ assetPayInfo.payWayStr }}</text>
        <text class="price-panel__item__value">{{ assetPayInfo.formattedPrice }}</text>
      </view>

      <!-- 优惠信息 -->
    </van-cell>

    <!-- 支付方式 -->
    <van-cell
      v-if="price.buyWay"
      title="付款方式"
      center
      title-class="price-panel__buy-way__title"
      :is-link="allowBuyWayLink"
      @click="openBuyWayDetail"
    >
      <text class="price-panel__item__value">{{ price.buyWay }}</text>
      <text
        v-if="buyWayExtendDescObj.buyWayExtendDescInner"
        :class="[
          'price-panel__item__value',
          buyWayExtendDescObj.buyWayExtendDescOutLimit ? 'price-panel__buy-way__desc' : '',
        ]"
      >
        {{ buyWayExtendDescObj.buyWayExtendDescInner }}
        <van-icon
          v-if="buyWayExtraExplanation"
          @click="toggleExplanation(true)"
          size="14px"
          name="info-o"
          custom-class="new-invoice__content__title__icon"
        />
      </text>
    </van-cell>

    <!-- 合计 -->
    <van-cell v-if="isPriorUse" :border="false">
      <text class="prior-use">
        <i class="icon" />
        <text class="label">先用后付</text>
      </text>
      <text class="real-pay__title" v-if="isPriorUsePaid">
        实付款：
        <text class="real-pay__value" :style="{ color: themeColors }">
          {{ price.realPay }}
        </text>
      </text>
      <text class="real-pay__title" v-else>
        应付款：
        <text class="real-pay__value" :style="{ color: themeColors }">
          {{ price.realPay }}
        </text>
        ，确认收货后自动扣款
      </text>
    </van-cell>
    <van-cell v-else :border="true">
      <text class="real-pay__title">实付款：</text>
      <text class="real-pay__value">{{ price.realPay }}</text>
    </van-cell>

    <!-- 小额打款 -->
    <microtransfer />
    <van-dialog
      :show="showBuyWayExtraExplanation"
      confirm-button-color="#EE0A24"
      :message="buyWayExtraExplanation"
      confirm-button-text="我知道了"
      @close="toggleExplanation(false)"
    />
    <payment-info-popup
      :show="popupShow['pay-info'] || false"
      :payment-info="paymentInfo"
      @close="popupToggle('pay-info')"
    />
  </van-cell-group>
</template>

<script>
// Components
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Dialog from '@youzan/vant-tee/dist/dialog/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';

// Utils
import { getPaymentVoucher } from './api';
import { formatedPayUmpDiscountMoney } from './utils';
import paymentInfoPopup from './components/paymentInfoPopup.vue';

// Dependencies
import Tee from '@youzan/tee';
import { url } from '@youzan/tee-util';
import { mapState } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper-tee';
import { getPostageLabel } from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-icon': Icon,
    'van-dialog': Dialog,
    'payment-info-popup': paymentInfoPopup,
  },

  data() {
    return {
      order: {},
      orderBizExtra: {},
      payment: {},
      goods: [],
      buyWayDetailUrl: '',
      showPostage: false,
      fissionTicketNum: 0,
      themeColors: {},
      useBeforePayData: {},
      popupShow: {},
      showBuyWayExtraExplanation: false,
      showPaymentInfoPopup: false,
      themeVars: '',
      paymentInfo: {},
      deliveryActivitiesList: [],
      ...mapState(this, ['price', 'extraPrices']),
    };
  },

  computed: {
    isPriorUse() {
      return this.useBeforePayData.is;
    },
    isPriorUsePaid() {
      return this.useBeforePayData.is && this.useBeforePayData.status === 'PAID';
    },
    postageTitle() {
      const { payment } = this;
      return getPostageLabel(payment.expressPayMode);
    },
    formatedPayUmpDiscountMoney() {
      return formatedPayUmpDiscountMoney(this.price.payUmpDiscountMoney);
    },
    // 右侧显示箭头
    allowBuyWayLink() {
      const allowLinkList = ['分期付款', '批发线下支付'];
      return allowLinkList.includes(this?.price?.buyWay);
    },

    buyWayExtendDescObj() {
      let buyWayExtendDescOutLimit = false;
      let buyWayExtendDescInner = '';
      const { buyWayExtendDesc } = this.price;
      if (buyWayExtendDesc) {
        buyWayExtendDescOutLimit = buyWayExtendDesc.length >= 11;
        buyWayExtendDescInner = buyWayExtendDescOutLimit
          ? buyWayExtendDesc
          : `(${buyWayExtendDesc})`;
      }
      return {
        buyWayExtendDescOutLimit,
        buyWayExtendDescInner,
      };
    },

    buyWayExtraExplanation() {
      return this.orderBizExtra?.extra?.SECUREDTRANSACTIONS_PAYWAY_DIALOG_DESC || '';
    },

    needShowDeliveryCouponBlock() {
      return !!this?.deliveryActivitiesList?.length;
    },
  },

  created() {
    mapData(this, [
      'order',
      'orderBizExtra',
      'payment',
      'goods',
      'buyWayDetailUrl',
      'showPostage',
      'fissionTicketNum',
      'useBeforePayData',
      'isWholesaleOrder',
      'themeVars',
      'deliveryActivitiesList',
    ]);
    mapData(
      this,
      {
        themeColors: (value) => {
          this.themeColors = value.general || '##f44';
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    openBuyWayDetail() {
      const { buyWayDetailUrl } = this.price;
      const { kdtId } = this.ctx.data;
      // 查看批发线下支付上传凭证
      if (this.isWholesaleOrder) {
        Toast.loading({
          forbidClick: true,
        });

        getPaymentVoucher({
          orderNo: this?.order?.bizNo,
        })
          .then((res) => {
            this.paymentInfo = res || {};
            this.popupToggle('pay-info');
          })
          .finally(() => {
            Toast.clear();
          });
        return;
      }

      if (this.allowBuyWayLink && buyWayDetailUrl) {
        this.ctx.logger.log({
          et: 'click',
          ei: 'installment',
          en: '跳转到分期支付',
          si: kdtId,
        });

        const hashArray = buyWayDetailUrl.split('#');
        const domainUrl = hashArray[0].split('?')[0];

        Tee.navigate({
          url: url.args.add(domainUrl, url.args.getAll(buyWayDetailUrl)),
        });
      }
    },
    popupToggle(name) {
      if (Object.hasOwnProperty.call(this.popupShow, name)) {
        this.$set(this.popupShow, name, !this.popupShow[name]);
      } else {
        this.$set(this.popupShow, name, !this.popupShow[name]);
      }
    },
    toggleExplanation(value) {
      this.showBuyWayExtraExplanation = !!value;
    },
  },
};
</script>

<style lang="scss">
@import './style';

.price-panel {
  margin-top: 10px;
}

.price-cell_margin {
  padding-top: 4px !important;
}

.real-pay__title {
  color: #323233;
}

.real-pay__value {
  font-weight: bold;
  color: var(--icon, #323233);
}

.price-panel__buy-way__title {
  flex-grow: 0 !important;
  flex-basis: 80px !important;
}

.price-panel__buy-way__desc {
  display: block;
  color: #999;
  font-size: 12px;
  line-height: 18px;
}

.prior-use {
  font-size: 14px;
  line-height: 20px;
  color: #323233;
  margin-right: 6px;
  .icon {
    display: inline-block;
    vertical-align: text-top;
    width: 16px;
    background-size: cover;
    height: 16px;
    background-image: url(https://b.yzcdn.cn/assets-cashier/icon/prior-use-wechat2.png);
    position: relative;
    top: 1px;
    margin-right: 4px;
  }
  .vertical {
    margin: 0 2px;
    position: relative;
    bottom: 1px;
    font-size: 12px;
  }
}
</style>
