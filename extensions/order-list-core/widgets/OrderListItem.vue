<template>
  <view class="order-list-item" @click="onItemClick">
    <!-- 同城/周期购下次送达时间提示信息 -->
    <view v-if="renderOrderItem.arriveTimeDesc" class="list-item-time-tips">
      {{ renderOrderItem.pickUpCodeDesc }}
      {{ renderOrderItem.arriveTimeDesc }}
    </view>

    <!-- 商品信息展示区域 -->
    <view ref="body" class="order-list-item__body">
      <goods-card
        v-for="(item, index) in goods"
        :key="index"
        :goods="item"
        @goods-img-load="onGoodsImgLoad(index)"
      />

      <view v-if="hasShowMoreGoods" ref="more-goods" class="order-list-item__more-goods">
        <goods-card v-for="(item, index) in moreGoods" :key="index" :goods="item" />
      </view>

      <view
        v-if="moreGoods.length"
        ref="more-btn"
        class="order-list-item__more-btn"
        :style="hasShowMoreGoods ? '' : 'height: 100px; margin-top: -100px;'"
        :class="{
          'order-list-item__more-btn_has-show': hasShowMoreGoods,
        }"
        @click.stop="handleShowMoreGoods"
      >
        <view class="order-list-item__more-btn-container">
          <text class="order-list-item__more-text"
            >{{ hasShowMoreGoods ? '收起' : '查看' }}全部{{
              goods.length + moreGoods.length
            }}件商品</text
          >
          <van-icon color="#969799" :name="hasShowMoreGoods ? 'arrow-up' : 'arrow-down'" />
        </view>
      </view>
    </view>

    <view class="order-list-item__cell">
      <youzan-secured :yz-guarantee="renderOrderItem.yzGuarantee" />
    </view>

    <view v-if="orderFinalPrice.isShow" class="order-list-item__total-price">
      <text v-if="orderFinalPrice.promotionPriceDesc" class="order-list-item__discount">
        {{ orderFinalPrice.promotionPriceDesc }}，
      </text>
      <text class="order-list-item__amount-desc"> {{ orderFinalPrice.payInfo.amountDesc }} </text>
      <price
        class="order-list-item__pay-amount"
        :price="orderFinalPrice.payInfo.payAmount"
        :points-price="orderFinalPrice.realPointPay"
        :points-name="orderFinalPrice.pointsName"
      />
      <text v-if="orderFinalPrice.payInfo.last"> ，{{ orderFinalPrice.payInfo.last }} </text>
    </view>
  </view>
</template>

<script>
import Price from '../components/Price.vue';
import GoodsCard from '../components/GoodsCard';
import YouzanSecured from '../components/YouzanSecured';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import get from '@youzan/utils/object/get';

export default {
  components: {
    price: Price,
    'van-icon': Icon,
    'goods-card': GoodsCard,
    'youzan-secured': YouzanSecured,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    orderIndex: {
      type: Number,
      default: 0,
    },
    listItem: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      hasShowMoreGoods: false,
      customStyle: 'height: 0px',
    };
  },

  computed: {
    renderOrderItem() {
      return get(this, `listItem.renderData.orderItems[${this.orderIndex}]`, {});
    },
    orderFinalPrice() {
      return get(this, 'renderOrderItem.orderFinalPrice', { isShow: false });
    },
    goods() {
      return this.orderItemGoods.slice(0, 10);
    },
    moreGoods() {
      return this.orderItemGoods.slice(10);
    },
    orderItemGoods() {
      return get(this.renderOrderItem, 'goodsItems', []);
    },
  },

  methods: {
    handleShowMoreGoods() {
      this.hasShowMoreGoods = !this.hasShowMoreGoods;
      this.customStyle = `height: ${this.hasShowMoreGoods ? '300px' : '0px'}`;
    },

    onItemClick() {
      this.store.gotoOrderDetail({ listItem: this.listItem });
    },

    // 用于页面性能埋点
    onGoodsImgLoad(goodsIndex) {
      if (this.listIndex === 0 && goodsIndex === 0) {
        this.store.onGoodsImgLoad();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.list-item-time-tips {
  background: var(--tag-bg, #f2f2ff);
  border-radius: 4px;
  padding: 8px;
  margin: 12px 12px 0;
  font-size: 12px;
  color: var(--tag-text, #323233);
  line-height: 16px;
}
.order-list-item {
  line-height: 1.4;

  &__cell {
    margin-top: 8px;

    ::v-deep .t-cell:not(:last-child) {
      &::before {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        bottom: 0;
        left: 12px;
        right: 12px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
      }
    }
  }

  &__more-goods {
    overflow: hidden;
    transition: height 0.3s linear;
  }

  &__more-btn {
    line-height: 18px;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: #323233;
    letter-spacing: 0;
    font-size: 12px;
    background: linear-gradient(rgba(255, 255, 255, 0.64), #fff);

    &_has-show {
      margin-top: 12px;
    }
  }

  &__more-btn-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__more-text {
    margin-right: 4px;
  }

  .t-tag + .t-tag {
    margin-left: 4px;
  }

  &__total-price {
    height: 48px;
    padding: 0 12px;
    color: #323233;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
  }

  &__amount-desc {
    margin-right: 5px;
  }

  &__pay-amount {
    font-size: 0;

    ::v-deep .cap-price {
      color: #323233;

      &__integer {
        font-size: 18px;
      }
    }
  }
}
</style>
