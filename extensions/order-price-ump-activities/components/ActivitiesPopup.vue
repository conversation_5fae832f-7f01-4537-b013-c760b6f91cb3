<template>
  <van-popup :show="show" round position="bottom" @close="handleClose">
    <slot name="title"></slot>
    <van-cell-group :border="false" custom-class="list">
      <van-cell>
        <view v-for="(item, index) in formattedList" :key="index" class="list__item">
          <view :style="{ color: themeColors['general'] }" class="list__item__title">
            <van-tag
              v-if="item.promotionTypeName"
              :color="themeColors['general']"
              plain
              type="danger"
              custom-class="list__item__title__tag"
            >
              {{ item.promotionTypeName }}
            </van-tag>
            <text v-if="item.activityName" class="list__item__title__name">
              {{ item.activityName }}
            </text>
          </view>
          <view class="list__item__value">
            {{ decreaseText }} <text class="text"> {{ item.decrease }}</text>
          </view>
        </view>
      </van-cell>
      <van-cell :border="false">
        <view class="decrease-amount">
          合计：{{ decreaseText }}
          <view :style="{ color: themeColors['general'] }" class="decrease-amount__value">
            {{ formattedDecreaseAmount }}
          </view>
        </view>
      </van-cell>
    </van-cell-group>

    <view class="activities__bottom-wrapper">
      <van-button
        block
        round
        :color="themeColors['general']"
        class="activities__bottom-btn"
        @click="handleClose"
      >
        我知道了
      </van-button>
    </view>
  </van-popup>
</template>

<script>
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';

function formatPrice(v) {
  return '¥ ' + (v / 100).toFixed(2);
}

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-tag': Tag,
    'van-popup': Popup,
    'van-button': Button,
  },

  props: {
    show: Boolean,
    list: Array,
    decreaseAmount: Number,
    themeColors: Object,
    decreaseText: {
      type: String,
      default: '省',
    },
  },

  computed: {
    formattedDecreaseAmount() {
      return formatPrice(this.decreaseAmount);
    },

    formattedList() {
      return (this.list || []).map((item) => ({
        ...item,
        decrease: formatPrice(item.decrease),
      }));
    },
  },

  methods: {
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scope>
.list {
  min-height: 430px;
}

.list__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.list__item:not(:first-child) {
  margin-top: 10px;
}

.list__item__title {
  display: flex;
  align-items: center;
  line-height: 0;
}

.list__item__title__tag {
  color: inherit !important;
  margin-right: 6px;
}

.list__item__title__name {
  font-size: 12px;
  line-height: 1;
  color: #323233;
}

.list__item__value {
  font-size: 12px;
  line-height: 1;
  color: #323233;
}

.list__item__value .text {
  font-weight: bold;
  color: #000;
}

.decrease-amount {
  font-size: 12px;
  color: #323233;
  text-align: right;
}

.decrease-amount__value {
  display: inline-block;
  font-weight: bold;
}

.activities__bottom-wrapper {
  margin: 0 16px 16px;
  border-radius: 999px;
}

.activities__bottom-btn {
  color: inherit;
  background: transparent;
  font-size: 16px;
  line-height: 50px;
}

.activities__bottom-btn::after {
  border: none;
}
</style>
