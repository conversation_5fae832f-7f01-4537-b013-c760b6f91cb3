# @wsc-tee-trade/detail-order-tips

顶部提示

![UI呈现](https://img.yzcdn.cn/public_files/e82b1ef4347edf9b35d4ff850c617486.png) ![订阅物流弹窗](https://img.yzcdn.cn/public_files/6ddbed419f78744dbcc0790f986bacf8.png) ![流程图](https://img.yzcdn.cn/public_files/d8dafc0994a2f7432b814627c1a638cc.png)

## 调试方式

- mock 源码

## 存疑

- refundType === 11 表示什么退款类型？
- env 是谁提供的，env 的来源是什么？
- 所有的字符串展示是否可以被合并成一个？
- mpAccount 微信账号？

## Data.Consume

| 名称 | 类型 | 说明 |
| --- | --- | --- |
| order | ??? | 订单信息，`_global.orderInfo` |
| mpAccount | ??? | 微信账号 `_global.mp_account` |
| paymentInfo | ??? | 支付信息 `_global.paymentInfo` |
| addressInfo | ??? | 地址信息 `_global.orderAddressInfo` |
| env | ??? | 环境变量 `_global.env` |
| isSourceFromWx | _boolean_ | 是否来自微信，由 `detail-page-setup` 中的 format 提供 `uri?source=wx` |
| isFans | _boolean_ | 是否是粉丝 `_global.isFans` |
