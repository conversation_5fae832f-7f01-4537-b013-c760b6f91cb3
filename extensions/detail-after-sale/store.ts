import api from './api';
import { isFxZpp } from './utils';

import args from '@youzan/weapp-utils/lib/args';
import { buildUrl } from '@youzan/tee-biz-util';
import { makePhoneCall } from '@youzan/tee-api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { createStore as _createStore } from '@ranta/store';
import type { StoreModule } from '@youzan/wsc-tee-trade-common/lib/types';
/* #ifdef web */
import ZNB from '@youzan/znb';
import Bot from '@youzan/im-base-pure/lib/core/Bot';
import * as versions from '@youzan/utils/versions';
import { action as ZanJSBridgeAction } from '@youzan/zan-jsbridge';
import { requestV2 } from '@youzan/tee-biz-request';

ZNB.init({
  kdtId: window._global.kdtId,
}).catch(() => ({}));
/* #endif */

const rootStore: StoreModule = {
  state: {
    env: {},
    kdtId: 0,
    orderNo: '',
    contact: {},
    goodsList: [],
    orderExtra: {},
    isFxZpp,
    // 买家
    partyId: 0,
    fansId: 0,
    // 创始笔记当事人
    foundPartyId: 0,
    foundPartyRole: 0,
    // 成交笔记当事人
    tradePartyId: 0,
    tradePartyRole: 0,
    // 群团团订单-帮卖类型
    orderPromoteType: 0,
    showAfterSaleMobile: false,
  },
  getters: {
    showIM() {
      const { contact } = this;
      if (this.isFxZpp) {
        // 群团团自己的订单屏蔽联系客服按钮
        return contact.isImOrder && +this.partyId !== +this.tradePartyId;
      }
      return contact.isImOrder;
    },
    // 交易组件3.0屏蔽相关操作后面会下线!
    isTradeComponent3() {
      const { orderExtra } = this;

      let BIZ_ORDER_ATTRIBUTE: Record<string, any> = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}');
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION === 'TRADE_COMPONENT_3_0' ||
          BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN'
        );
      } catch (error) {
        return false;
      }
    },
  },
  actions(ctx) {
    return {
      initFxZppData() {
        /* #ifdef web */
        if (this.isFxZpp) {
          api.getAttributeOrderInfo(this.orderNo).then((res) => {
            const {
              foundPartyId,
              foundPartyRole,
              tradePartyId,
              tradePartyRole,
              fansId,
              buyerPartyId,
              orderPromoteType,
            } = (res || {}) as Record<string, any>;
            this.foundPartyId = foundPartyId;
            this.foundPartyRole = foundPartyRole;
            this.tradePartyId = tradePartyId;
            this.tradePartyRole = tradePartyRole; // 1、店铺  2、个人
            this.partyId = buyerPartyId;
            this.fansId = fansId;
            this.orderPromoteType = orderPromoteType;
            ctx.data.tradePartyId = tradePartyId;
          });
        }
        /* #endif */
      },
      async handleContactIM() {
        /* #ifdef web */
        // 埋点
        ctx.logger.log({
          et: 'click',
          ei: 'online_service',
          en: '在线客服',
        });
        if (this.isTradeComponent3) {
          Toast('视频号订单，请咨询腾讯客服 400-670-0700');
          return;
        }
        const { kdtId, env, contact } = this;
        const { imFromSource = {} } = contact || {};

        if (env.isYouzanmars || env.isYouzanke) {
          // 精选新版本
          // 唤起原生服务
          ZanJSBridgeAction.gotoNative({
            page: 'im',
            data: imFromSource,
          });
        } else {
          if (window._global?.miniprogram?.isTTApp) {
            const data: any = await requestV2({
              path: '/retail/h5/douyin/version.json',
              method: 'GET',
              data: {
                kdt_id: this.kdtId,
              },
              withCredentials: true,
            });

            const mpVersion: string = data.releaseVersion;

            if (mpVersion && versions.default.compareVersions(mpVersion, '1.1.36') > 0) {
              ZNB.navigate({
                ttUrl: args.add('/pages/native-api/im/index', { kdtId }),
              });
              return;
            }
          }
          let url =
            Bot &&
            Bot.generateUrl({
              // 常用参数
              kdtId, // kdt_id, 必传, 店铺id
              fromSource: imFromSource, // 会话来源对象, 参考 https://doc.qima-inc.com/pages/viewpage.action?pageId=69569323
              origin: window.location.href, // origin， 打开C端客服页面的来源页面地址，仅用于iframe模式时的postMessage

              // 不常用参数
              channel: 'wsc', // channel, 'wsc' (微商城、零售), 'ls' (批发)，默认是'wsc'
              version: 2, // version, 默认是2, 影响后端选择渲染模板，基本不需要传
            });

          // 群团团店铺
          if (this.isFxZpp) {
            let transferFlag = false;
            // 当为帮卖订单咨询时，查询开团团长、帮卖团长之间是否有消息承接关系
            if (this.foundPartyId !== this.tradePartyId) {
              if (this.orderPromoteType === 2) {
                // 分享帮卖订单，直接联系团长
                transferFlag = true;
              } else {
                const res: Record<string, any> = await api.querySwitch({
                  founderId: this.foundPartyId,
                  promoterId: this.tradePartyId,
                });
                transferFlag = res?.success;
              }
            }

            // 先查询是否已开通微信客服，如已开通走微信客服逻辑（因webview无法打开微信客服，所以先跳转团长主页，然后识别链接有orderNo参数，再弹窗跳转）
            const customerServiceData: Record<string, any> = await api.queryCustomerService({
              // 帮卖订单 && 开启了开团承接，需要查询开团的配置
              partyId: transferFlag ? this.foundPartyId : this.tradePartyId,
              weappAppId: window._global?.qttWeappAppId,
            });

            // 符合条件走企微客服方式
            if (!customerServiceData?.disabled && customerServiceData?.url) {
              let weappUrl = `/packages/home-page/index?id=${this.tradePartyId}&orderNo=${this.orderNo}`;

              // 帮卖订单 && 开启了开团承接，跳转时携带开团partyId
              if (transferFlag) {
                weappUrl += `&founderPartyId=${this.foundPartyId}`;
              }

              ZNB.navigate({
                weappUrl,
              });
              return;
            }

            const data = await api.getShopInfo();
            const { shop_type: shopType } = data as Record<string, any>;
            // 是否是微小店
            const isWxd = shopType === 1;
            // 群团团消息不支持微小店，所以微小店消息还是使用原微商城的
            if (!isWxd) {
              url = args.add(
                url,
                {
                  channel: 'qttGroupMember',
                  partyId: this.partyId,
                  orderNo: this.orderNo,
                  fansId: this.fansId,
                  role: 3, // 1、团长  2、帮卖  3、团员
                  ...(transferFlag
                    ? {
                        targetPartyId: this.foundPartyId,
                        targetRole: this.foundPartyRole,
                        promoterToFounder: 1,
                      }
                    : {
                        targetPartyId: this.tradePartyId,
                        targetRole: this.tradePartyRole,
                      }),
                },
                true
              );
              const query = url.split('?')?.[1] || '';
              const qttPath = buildUrl('/v3/im/qtt', 'h5', '');
              url = qttPath + '?' + query;

              // 先保存关系再跳转im
              const saveParams: Record<string, any> = {
                memberId: this.partyId,
              };
              if (this.foundPartyId === this.tradePartyId) {
                // 自营订单
                saveParams.founderId = this.foundPartyId;
              } else {
                // 帮卖订单
                saveParams.founderId = this.foundPartyId;
                saveParams.promoterId = this.tradePartyId;
              }
              await api.saveRelation(saveParams);
            }
          }

          location.href = url;
        }
        /* #else */
        console.warn('handleContactIM仅支持web平台');
        /* #endif */
      },
      handleMakePhoneCall() {
        // 埋点
        ctx.logger.log({
          et: 'click',
          ei: 'call',
          en: '拨打电话',
        });
        const { areaCode = '', phoneNumber = '', mobileNumber = '' } = this.contact;
        const _phoneNumber = `${areaCode}${phoneNumber}`;

        makePhoneCall({
          phoneNumber: _phoneNumber || mobileNumber,
        });
      },
    };
  },
};

export default function createStore(ctx) {
  return _createStore({
    state: () => ({
      ...rootStore?.state,
    }),
    getters: {
      ...rootStore?.getters,
    },
    actions: {
      ...rootStore?.actions(ctx),
    },
  });
}
