<template>
  <view class="goods-item goods-item_invalid" @click.stop="toGoodsDetail">
    <view class="goods-img">
      <image class="goods-img__image" :src="goods.imgUrl" mode="aspectFill" />
      <invalid-mask />
    </view>

    <!-- 如果有其他商品推荐，则不显示失效商品的标题及描述 -->
    <view class="goods-item--right">
      <block v-if="!recommendList.length">
        <view
          :class="['t-multi-ellipsis--l2', goods.title ? 'goods-title' : 'goods-title-not-exist']"
        >
          <image
            v-if="goods.settlementRule && GOODS_TAG_MAP[goodsSettlementMark]"
            lazy-load="true"
            :class="['goods-title-tag', goodsSettlementMark]"
            :src="GOODS_TAG_MAP[goodsSettlementMark]"
          />
          <view class="goods-title-text">{{ goods.title }}</view>
        </view>

        <view class="goods-into-bottom">
          <view v-if="goods.errorMsg" class="err-msg">
            {{ goods.errorMsg }}
          </view>
        </view>
      </block>

      <!-- 其他商品推荐 -->
      <recommend-goods
        v-else
        class="recommend-goods"
        :recommend-list="recommendList"
        @to-goods-detail="toGoodsRoute"
      />
    </view>
  </view>
</template>

<script>
/* #ifdef web */
import { action } from '@youzan/zan-jsbridge';
/* #endif */

import { GOODS_TAG_MAP } from './constants';
import RecommendGoods from './RecommendGoods';

export default {
  components: {
    'recommend-goods': RecommendGoods,
  },
  props: {
    goods: {
      type: Object,
      required: true,
    },
    themeGeneralAlpha10Color: String,
    themeGeneralColor: String,
  },

  data() {
    return {
      GOODS_TAG_MAP,
      recommendList: [],
    };
  },
  computed: {
    goodsSettlementMark() {
      return this.goods?.settlementRule?.settlementMark || '';
    },
  },
  watch: {
    goods: {
      handler(val) {
        if (val.recommendListObj) {
          const recommendListTemp = val.recommendListObj.recommendList || [];
          this.recommendList = recommendListTemp.slice(0, 3);
        }
      },
      immediate: true,
    },
  },

  methods: {
    toGoodsRoute(params) {
      this.$emit('to-goods-detail', params);
    },
    toGoodsDetail() {
      const { alias } = this.goods;

      let link = `/pages/goods/detail/index?alias=${alias}`;

      /* #ifdef web */
      const {
        platform,
        url: { wap },
      } = window._global;
      // 有赞精选
      link = `${wap}/showcase/goods?alias=${alias}`;
      if (platform === 'youzanmars') {
        action.gotoWebview({
          url: link,
          page: 'web',
        });
      } else {
        const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isSwanApp, isKsApp } =
          window._global?.miniprogram || {};
        if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
          link = `/packages/goods/detail/index?alias=${alias}`;
        }
      }
      /* #endif */

      this.toGoodsRoute({ link });
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-block {
  overflow: hidden;
}

.goods-item {
  min-height: 96px;
  display: flex;

  &--right {
    flex: 1;
    position: relative;
    height: 96px;
    margin-left: 8px;
    display: flex;
    flex-direction: column;
    word-break: break-all;
  }
}

.goods-title {
  margin-bottom: 8px;
}

.goods-title-not-exist {
  margin-bottom: 0;
}

.goods-title-text {
  font-size: var(--eo-font-size-14, 14px);
  color: #969799;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.goods-title-tag {
  margin-right: 2px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;

  &.HAITAO {
    width: 28px;
  }

  &.PERIOD_BUY {
    width: 38px;
  }
}

.goods-img {
  float: left;
  height: 96px;
  width: 96px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  background-size: cover;
  border-radius: var(--theme-radius-card, 8px);
  overflow: hidden;
  background-color: #fff;

  &__image {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    left: 0;
    height: auto;
    max-height: 100%;
    max-width: 100%;
    width: auto;
    background-color: #fff;
    margin: auto;
  }
}

.err-msg {
  font-size: var(--eo-font-size-14, 14px);
  color: #323233;
  line-height: 1.15;
}

.goods-revive {
  font-size: 12px;
  color: #323233;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
