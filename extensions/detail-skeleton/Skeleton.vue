<template>
  <view v-if="fetching">
    <view class="placeholder" />
    <image
      class="skeleton-image"
      mode="scaleToFill"
      src="https://img.yzcdn.cn/public_files/2020/09/21/3825c94b8615284bd0daae6372cec367.png"
    />
  </view>
</template>

<script>
import { mapEvent } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      fetching: true,
    };
  },

  created() {
    mapEvent(this, {
      onLoadOrderInfo: () => {
        this.fetching = false;
      },
    });
  },
};
</script>

<style lang="scss">
.placeholder {
  display: flex;
  position: absolute;
  width: 100%;
  height: 100vh;
  background: #fff;
  z-index: 9998;
}
.skeleton-image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
}
</style>
