import Time from './Time.vue';
import { cloud } from '@youzan/ranta-helper';
import type { OrderTime } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class TimeExtension {
  ctx: any;

  /**
   * orderTime
   * @desc 订单时间
   * @type {OrderTime}
   */
  @cloud('orderTime', 'data')
  orderTime: OrderTime;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['time'], {
      callback: () => {
        const { time } = this.ctx.data;
        const newOpenData = {
          orderTime: cloudData.getOrderTime({ time }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    Time,
  };
}
