const cloudData = {
  getEnjoyBuyOrderInfo: ({ orderExtra }) => {
    const { ENJOY_BUY_ORDER_EXTRA = '{}' } = orderExtra || {};
    let enjoyBuyOrderData: Record<string, any> = {};
    try {
      enjoyBuyOrderData = JSON.parse(ENJOY_BUY_ORDER_EXTRA);
    } catch (e) {
      console.warn(e);
    }
    const {
      deliveryMode,
      customDeliveryMode,
      deliveryCycle,
      deliveryCycleCustomStartDate,
      deliveryCycleCustomEndDate,
      deliveryIssueTotal,
      deliveryAmount,
      deliveryStartDate,
      deliveryTimeSection,
      deliveryLeadTime,
      deliveryCycleModifyMaxLimit,
    } = enjoyBuyOrderData;

    return {
      deliveryMode,
      customDeliveryMode,
      deliveryCycle,
      deliveryCycleCustomStartDate,
      deliveryCycleCustomEndDate,
      deliveryIssueTotal,
      deliveryAmount,
      deliveryStartDate,
      deliveryTimeSection,
      deliveryLeadTime,
      deliveryCycleModifyMaxLimit,
    };
  },
  getOrderInfo: ({ order }) => {
    const { orderNo, buyerMemo } = order;
    return { orderNo, buyerMsg: buyerMemo };
  },
  getPaymentInfo({ payment }) {
    const { deductedRealPay, postage } = payment || {};
    return {
      postage,
      realPay: deductedRealPay,
    };
  },
  getPreSalePhaseInfo({ payment }) {
    const preSalePhaseInfo = (payment?.phasePays || []).map((phaseInfo) => {
      const {
        buyerRealPay,
        payUmpDiscountMoney,
        phase,
        phaseArrived,
        phaseDesc,
        phasePayStatusDesc,
        realPrice,
      } = phaseInfo;
      return {
        buyerRealPay,
        payUmpDiscountMoney,
        phase,
        phaseArrived,
        phaseDesc,
        phasePayStatusDesc,
        realPrice,
      };
    });
    return preSalePhaseInfo;
  },
};
const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);

export { cloudData, isSameObject };
