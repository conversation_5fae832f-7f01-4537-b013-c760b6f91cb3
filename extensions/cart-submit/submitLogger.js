// 结算按钮点击时选中商品id曝光
export const submitNormalLogger = (logger, goodsList) => {
  const itemObjs = goodsList.map((vo) => {
    return {
      goods_id: vo.goodsId,
      show_estimated_price: Number(typeof vo.estimatedPrice === 'number'),
    };
  });
  logger?.log({
    et: 'click',
    ei: 'settlement_trade',
    en: '去结算',
    pt: 'cart',
    params: {
      item_objs: itemObjs,
    },
  });
};
// 结算点击时，曝光有预估到手价的商品id
export const submitEstimatedPriceLogger = (logger, goodsList) => {
  const estimatedPriceList = goodsList.filter((vo) => typeof vo.estimatedPrice === 'number');
  const goodsIdList = estimatedPriceList.map((vo) => vo.goodsId);
  const skuIdList = estimatedPriceList.map((vo) => vo.skuId);
  if (!goodsIdList.length) {
    return;
  }
  logger?.log({
    et: 'click',
    ei: 'estimated_price_goodsid_show',
    en: '购物车-预估单价goodsid',
    pt: 'cart',
    params: {
      goods_id: goodsIdList,
      sku_id: skuIdList,
    },
  });
};

export const submitLogger = (logger, goodsList) => {
  try {
    submitNormalLogger(logger, goodsList);
    submitEstimatedPriceLogger(logger, goodsList);
  } catch (err) {
    console.error('去结算埋点错误：', err);
  }
};
