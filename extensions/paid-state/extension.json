{"extensionId": "@wsc-tee-trade/paid-state", "name": "@wsc-tee-trade/paid-state", "version": "0.2.3", "bundle": "<builtin>", "widget": {"default": "PaidState"}, "data": {"consume": {"kdtId": ["r"], "orderNo": ["r"], "payResult": ["r"], "hasPaid": ["r"], "isRefreshing": ["r"], "showAwardV2Block": ["r"], "paidCodeInfoIsShow": ["r"], "emptyPaidExtHolderBlock": ["r"]}}, "event": {"emit": ["checkPay"]}, "lambda": {"consume": ["parseAggregatedRechargeData"]}, "platform": ["weapp", "web"]}