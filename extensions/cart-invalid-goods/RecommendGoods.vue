<template>
  <view class="recommend-goods">
    <view class="recommend-goods__tip">相关推荐</view>
    <view v-if="recommendList.length === 1" class="recommend-goods__wrap">
      <view class="recommend-goods__img-wrap" @click.stop="goToGoodsPage(recommendList[0])">
        <image class="recommend-goods__img-wrap-img" :src="recommendList[0].imageUrl"></image>
      </view>
      <view class="recommend-goods__desc-wrap">
        <view
          class="recommend-goods__title"
          :class="{
            'recommend-goods__title-not-exist': !recommendList[0].title,
          }"
        >
          <view class="recommend-goods__title-text">
            {{ recommendList[0].title }}
          </view>
        </view>
        <view class="recommend-goods__good-price">￥{{ recommendList[0].price }}</view>
      </view>
    </view>

    <view v-else>
      <view v-for="(it, idx) in recommendList" :key="idx" class="recommend-goods__imgs-for">
        <view class="recommend-goods__imgs-wrap" @click.stop="goToGoodsPage(it)">
          <image class="recommend-goods__imgs-wrap-img" lazy-load="true" :src="it.imageUrl" />
        </view>
        <view class="recommend-goods__for-price">￥{{ it.price }}</view>
      </view>
    </view>
  </view>
</template>
<script>
import args from '@youzan/weapp-utils/lib/args';
import { getLogger } from '@youzan/tee-logger';
/* #ifdef web */
import { action } from '@youzan/zan-jsbridge';
/* #endif */

export default {
  name: 'recommend-goods',

  props: {
    recommendList: {
      type: Array,
      default: () => [],
    },
  },

  created() {
    this.log();
  },

  methods: {
    goToGoodsPage(goods) {
      const { alias, algs, bannerId } = goods;

      let link = args.add('/pages/goods/detail/index', { alias, banner_id: bannerId, alg: algs });

      /* #ifdef web */
      const {
        platform,
        url: { wap },
      } = window._global;
      // 有赞精选
      link = `${wap}/showcase/goods?alias=${alias}`;
      if (platform === 'youzanmars') {
        action.gotoWebview({
          url: link,
          page: 'web',
        });
      } else {
        const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isSwanApp, isKsApp } =
          window._global?.miniprogram || {};
        if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
          link = args.add('/packages/goods/detail/index', {
            alias,
            banner_id: bannerId,
            alg: algs,
          });
        }
      }
      /* #endif */

      this.$emit('to-goods-detail', { link });
    },

    log() {
      this.recommendList.forEach(function (item) {
        const logger = getLogger();
        logger?.log({
          et: 'view',
          ei: 'view',
          en: '商品曝光',
          params: {
            alg: item.algs,
            banner_id: item.bannerId,
            item_id: item.id,
            item_type: 'goods',
            component: 'expired_goods_recommend',
            recommend_name: '失效商品推荐',
          },
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.recommend-goods {
  background: #f7f8fa;
  border-radius: var(--theme-radius-card, 4px);
  height: 90px;
  padding-left: 12px;
  padding-top: 6px;
  font-size: 12px;
  line-height: 16px;
  color: #323233;
  position: relative;

  &__tip {
    width: 100%;
    height: 16px;
    float: left;
    font-family: PingFangSC-Medium, sans-serif;
    line-height: 16px;
    margin-bottom: 4px;
  }

  &__wrap {
    width: 100%;
    height: 62px;
    float: left;
    display: flex;
  }

  &__img-wrap {
    width: 62px;
    height: 62px;
    position: relative;
    border-radius: var(--theme-radius-card, 6px);
    overflow: hidden;
    background: #fff;

    image {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    &-img {
      width: 62px;
      height: 62px;
      object-fit: cover;
    }
  }

  &__desc-wrap {
    flex: 1;
    padding-left: 5px;
    height: 100%;
    word-break: break-all;
  }

  &__title {
    margin-bottom: 8px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

    &-not-exist {
      margin-bottom: 0;
    }
  }

  &__title-text {
    line-height: 16px;
    vertical-align: middle;
  }

  &__good-price {
    bottom: 8px;
    position: absolute;
    font-weight: var(--theme-common-price-font-weight, 600);
    font-family: Avenir;
  }

  &__imgs-for {
    width: 48px;
    margin-right: 8px;
    float: left;
  }

  &__imgs-wrap {
    width: 48px;
    height: 48px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    background: #fff;
    background-size: cover;
    margin-bottom: 3px;
    border-radius: var(--theme-radius-card, 6px);

    image {
      position: absolute;
      margin: auto;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }

    &-img {
      width: 48px;
      height: 48px;
      object-fit: contain;
      border-radius: var(--theme-radius-card, 6px);
    }
  }

  &__for-price {
    margin-bottom: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    font-weight: var(--theme-common-price-font-weight, 600);
    font-family: Avenir;
  }
}
</style>
