/**
 * 后端返回key，对应前端模板value
 */
export const PAY_RESULT_TEMPLATE = {
  NORMAL: 'NORMAL',
  NEWHOPE: 'NEWHOPE',
  WAIT: 'WAIT',
  PAY_ERROR: 'PAY_ERROR',
  OVERSALE_ERROR: 'OVERSALE_ERROR',
  /**
   * 降价拍
   */
  AUCTION: 'AUCTION',
  AUCTION_WAIT: 'AUCTION_WAIT',
};

export const SHARE_CMPT_MAP = {
  menu: 'native_wechat', // 右上角
  button: 'native_custom', // 按钮
};

// 小程序上暂时支持的支付有礼活动类型
export const supportedActivity = [
  'tradeincard',
  'promocode',
  'couponpackage',
  'seller',
  'seniorseller',
  'feature',
  'present',
];
