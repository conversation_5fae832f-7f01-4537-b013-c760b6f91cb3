<template>
  <van-cell v-if="showAfterSaleMobile || showIM" custom-class="after-sale" :border="false">
    <view class="after-sale__slot">
      <!-- 拨打电话 -->
      <view v-if="showAfterSaleMobile && !isFxZpp" class="after-sale__item">
        <view class="after-sale__btn" @click="handleMakePhoneCall">
          <van-icon class="after-sale__btn__icon" name="phone-o" size="14px" />
          <text>拨打电话</text>
        </view>
      </view>

      <view v-if="showAfterSaleMobile && showIM" class="vertical-line"></view>

      <!-- 联系客服 -->
      <!-- #ifdef weapp -->
      <message-contact
        class="message-contact"
        :source-param="sourceParam"
        :message-img="messageImg"
        :message-path="messagePath"
        :message-card="true"
      />
      <!-- #endif -->

      <!-- #ifdef web -->
      <view v-if="showIM" class="im-contact-wrapper" @click="handleContactIM">
        <van-icon class="im-contact__icon" name="comment-circle-o" size="14px" />
        <text>{{ contactText }}</text>
      </view>
      <!-- #endif -->
    </view>
  </van-cell>
</template>

<script>
// Components
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

// Dependencies
import { mapState, mapActions } from '@ranta/store';

export default {
  components: {
    'van-cell': Cell,
    'van-icon': Icon,
  },

  data() {
    return {
      ...mapState(this, [
        'env',
        'kdtId',
        'fansId',
        'showIM',
        'partyId',
        'orderNo',
        'contact',
        'isFxZpp',
        'goodsList',
        'orderExtra',
        'foundPartyId',
        'tradePartyId',
        'foundPartyRole',
        'tradePartyRole',
        'orderPromoteType',
        'showAfterSaleMobile',
      ]),
    };
  },

  computed: {
    sourceParam() {
      const { goodsList, orderNo, kdtId } = this;
      const goodsImgs = [];
      let goodsNum = 0;
      goodsList.forEach((item) => {
        goodsNum += item.num;
        goodsImgs.push(item.thumb);
      });

      return {
        kdt_id: kdtId,
        source: 'order',
        detail: JSON.stringify({
          order_no: orderNo,
          piece: goodsNum,
          imgs: goodsImgs.splice(0, 3),
        }),
      };
    },

    messageImg() {
      return (this.goodsList[0] || {}).thumb || '';
    },

    messagePath() {
      const { goodsList, orderNo, kdtId } = this;
      return `/packages/trade/order/result/index?orderNo=${orderNo}&num=${goodsList.length}&kdtId=${kdtId}`;
    },

    contactText() {
      const { contact } = this;
      let imOrderTextParse = {};
      try {
        imOrderTextParse = JSON.parse(contact.imOrderText);
      } catch (error) {
        imOrderTextParse = {};
      }
      return imOrderTextParse.default === 1 ? imOrderTextParse.label : '在线客服';
    },
  },

  created() {
    mapActions(this, ['handleContactIM', 'handleMakePhoneCall']);
  },
};
</script>

<style lang="scss">
.after-sale {
  padding: 0 !important;
}

.after-sale__slot {
  display: flex;
}

.after-sale__item {
  position: relative;
  flex: 1;
}

.vertical-line {
  width: 0;
  height: 21px;
  border-left: 1px solid #ebedf0;
  transform: scaleX(0.5);
  margin-top: 11px;
}

.after-sale__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 43px;
  font-size: 14px;
  color: #323233;
  background-color: #fff;
}

.after-sale__btn::after {
  border: none;
}

.after-sale__btn__icon {
  height: 14px;
  margin-right: 4px;
  font-size: 0;
}

.message-contact {
  display: flex;
  flex: 1;
  justify-content: center;
}

.im-contact-wrapper {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 43px;
  font-size: 14px;
  color: #323233;
  background-color: #fff;
}

.im-contact__icon {
  height: 14px;
  margin-right: 4px;
  font-size: 0;
}

.message-contact__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 43px;
  width: 100%;
  font-size: 14px;
  color: #323233;
  background-color: #fff;
}

.message-contact__btn__icon {
  height: 14px;
  margin-right: 4px;
  font-size: 0;
}
</style>
