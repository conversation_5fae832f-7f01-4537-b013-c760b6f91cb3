<template>
  <view v-if="orderSuccessCounter.length" class="activity-info">
    <text class="activity-info__tag">{{ goodsItem.activityTag }}</text>
    还剩
    <text v-for="item in orderSuccessCounter" :key="item.type">
      <text class="order-countdown" :style="'width:' + item.width">{{ item.value }}</text>
      <text>{{ item.text }}</text>
    </text>
    结束
  </view>
</template>

<script>
import CountDown from '@youzan/weapp-utils/lib/countdown';

const TIME_MAP = {
  HOUR: 'hour',
  MIN: 'min',
  SEC: 'sec',
};

export default {
  name: 'goods-item-ump',

  props: {
    goodsItem: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      countdownData: [
        {
          type: TIME_MAP.HOUR,
          value: '',
          text: '时',
          width: '0',
        },
        {
          type: TIME_MAP.MIN,
          value: '',
          text: '分',
          width: '0',
        },
        {
          type: TIME_MAP.SEC,
          value: '',
          text: '秒',
          width: '0',
        },
      ],
      orderSuccessCounter: [],
      timelimitedDiscountLogged: false,
    };
  },

  computed: {
    showGoodsActivity() {
      // 目前只有限时折扣需要展示商品级优惠提示
      return (
        this.goodsItem.activityTypeStr === 'timelimitedDiscount' && this.orderSuccessCounter.length
      );
    },
  },

  watch: {
    showGoodsActivity(newVal) {
      if (newVal && !this.timelimitedDiscountLogged) {
        this.timelimitedDiscountLogged = true;
        this.ctx.logger?.log({
          et: 'view', // 事件类型
          ei: 'time_view', // 事件标识
          en: '活动时间提示曝光', // 事件名称
          params: {
            component: 'marketing_campaign_time',
            activity_id: this.goodsItem?.activityId,
            goods_id: this.goodsItem?.goodsId,
          }, // 事件参数
        });
      }
    },
  },

  mounted() {
    const nowTime = Date.now();
    const startTime = this.goodsItem.activityStartTime || 0;
    const endTime = this.goodsItem.activityEndTime || 0;
    const day = Math.floor((endTime - nowTime) / 86400 / 1000);

    if (startTime > nowTime || day !== 0) {
      return;
    }
    this.countDown = new CountDown(endTime > nowTime ? endTime - nowTime : 0, {
      onChange: (timeData, strData) => {
        const { hour, minute, second } = strData;
        this.countdownData = this.countdownData.map((item) => {
          let value = '';
          switch (item.type) {
            case TIME_MAP.HOUR:
              value = hour;
              break;
            case TIME_MAP.MIN:
              value = minute;
              break;
            case TIME_MAP.SEC:
              value = second;
              break;
          }
          return {
            ...item,
            width: `${value.length * 8}px`,
            value,
          };
        });
        if (hour !== '00') {
          // 小时场景，只展示时分
          this.orderSuccessCounter = this.countdownData.slice(0, 2);
        } else if (minute !== '00') {
          // 分钟场景只展示分钟
          this.orderSuccessCounter = this.countdownData.slice(1, 2);
        } else if (second !== '00') {
          // 秒场景只展示秒
          this.orderSuccessCounter = this.countdownData.slice(2);
        } else {
          this.orderSuccessCounter = [];
        }
      },
    });
  },

  destroyed() {
    this.countDown?.shop && this.countDown.stop();
  },
};
</script>

<style scoped lang="scss">
.activity-info {
  box-sizing: border-box;
  padding: 12px 12px 0 44px;
  font-size: 12px;
  line-height: 1;
  display: flex;
  overflow: hidden;
  position: relative;

  &__tag {
    font-family: PingFangSC-Medium, sans-serif;
    font-size: 12px;
    color: #323233;
    line-height: 1;
    margin-right: 8px;
  }
}
</style>
