import get from '@youzan/utils/object/get';
import each from '@youzan/weapp-utils/lib/each';
import { requestV2 } from '@youzan/tee-biz-request';
import { cdnImage } from '@youzan/tee-biz-util';
import { findGoodsBuyIds, separateGoodsForLogistics } from './utils';

/**
 * 配送方式优先级：同城(2) > 快递(0) > 自提(1)
 */
const EXPRESS_TYPE_PRIORITY = [2, 0, 1];

export function checkPayApi(data) {
  return requestV2({
    path: '/wsctrade/order/payresult/checkPay.json',
    data,
  });
}

export function fetchPayResultApi(query) {
  return requestV2({
    path: '/wsctrade/order/payresult.json',
    data: { ...query, source: 'weapp', newMemberGuideFlag: true },
    config: {
      skipShopInfo: true,
      skipKdtId: true,
    },
  });
}

export function getWaitBuyList({ waitBuyGoodsIds }) {
  return requestV2({
    path: '/wsctrade/cart/goodsList.json',
  }).then((data) => {
    // "待支付商品列表" 和 "购物车商品" 重叠的商品
    const items = findGoodsBuyIds(waitBuyGoodsIds, get(data, '0.items'));

    if (!items || !items.length) {
      return Promise.reject();
    }

    const separateGoodsList = get(separateGoodsForLogistics(items), 'data') || [];
    // 按配送方式进行优先级排序
    separateGoodsList.sort((o1, o2) => {
      const o1Priority = EXPRESS_TYPE_PRIORITY.indexOf(+get(o1, 'expressType'));
      const o2Priority = EXPRESS_TYPE_PRIORITY.indexOf(+get(o2, 'expressType'));
      if (o1Priority > o2Priority) {
        return 1;
      }
      if (o1Priority < o2Priority) {
        return -1;
      }
      return 0;
    });
    const firstSeparateGoods = separateGoodsList[0];
    const finalGoodsList = get(firstSeparateGoods, 'list');
    const expressType = get(firstSeparateGoods, 'expressType');
    if (!finalGoodsList || !finalGoodsList.length) {
      return Promise.reject();
    }

    each(finalGoodsList, (item) => {
      item.imgUrl = cdnImage(item.attachmentUrl, '!112x112.jpg');
    });

    return {
      goodsList: finalGoodsList,
      expressType,
    };
  });
}

export function getGiftId(orderNo) {
  return requestV2({
    path: '/wscump/gift/giftid.json',
    data: { orderNo },
  }).then((resp) => {
    if (resp && resp.alias) {
      return resp.alias;
    }
  });
}
