/** ****************************************************************************
 * 如何写好测试用例：
 * 1. **所有开放相关的测试用例请汇总在一个extension中（例如：page-setup或者page-core）**
 *    原因说明：聚合在一起编写便于理解和阅读，拆分会降低可维护性
 * 2. **不要在测试用例中出现选择器（例如：id选择器、类选择器）**
 *    原因说明：写死选择器会影响用例的可用性，未来如果稍微改一下类名这个用例就废了（直觉上感觉
 *    同步改测试用例的类名选择器是一件很容易的事情，这是错觉，实际上就是会导致没人去维护）。
 *    另外，这里测试的是开放能力，所以大部分场景都应该通过开放能力配合实现，而不是直接操作DOM。
 * 3. **尽量只检查类型和数据字段是否符合定义要求，而不是去检查一个字段的具体值**
 *    原因说明：业务上的测试用例，做值的检查很难维护，稍微一个商家配置项的变更都会影响值的最终
 *    结果。我举个简单例子，商品的下单价，假如正常是9.99，测试的值要等于9.99。突然有一天这个
 *    商品加入了一个满减优惠，满9元减1元变成了8.99，这个用例就废了。又有一天，突然这个商品只
 *    支持商家配送了，而且配送价是3元，变成9.99-1+3=11.99了，用例又废了。所以我们注意到，这
 *    个过程中的值会不断变化，唯一不变的是这个价格永远是number类型。你可能还会质疑：那我不允许
 *    配置修改不就好了么？答案是做不到的，一些需要测试的场景的值本来就是冲突的，比如上面的例子
 *    中要测试营销活动对价格的变化，总不能不允许设置满减这个营销活动吧。另外一点是没办法约束所
 *    有人不去动配置项，别人也不知道改了这个配置项会对几个地方产生影响。
 *
 * 其他：如果需要测试一个业务流程，请单独另开一个测试文件（因为单独的测试文件会单独跑一个页面）
 * 避免出现被其他用例影响的问题。
 * *************************************************************************** */
import type { OrderListTabs } from '@youzan-cloud/cloud-biz-types';
import { orderListTabsSchema } from '@youzan-cloud/cloud-biz-types/lib/index.zod';
import { validateOpenDataType } from './utils';

const jestConsole = console;

beforeEach(() => {
  global.console = console;
});

afterEach(() => {
  global.console = jestConsole;
});

/** ****************************************************************************
 * # 开放数据
 * 我们采用zod进行运行时校验，采用zod是因为它完全实现了Typescript中的能力
 * 第一步：获取到对应的完整数据类型定义
 * 第二步：生成zod格式的校验数据可以直接使用大模型，比如ChatGPT或者老贾（请务必不要手动创建，
 *        因为手动写的校验可能会遗漏数据类型，并且有些不好写的类型也不容易实现）
 * 第三步：复制下面的模板，然后调整对应的代码并将生成的zod代码放进去：
 *
 * ```ts
 * import { <你需要使用的的Schema> } from './typeSchema';
 * test('Data.<开放数据名>', async () => {
 * await validateOpenDataType<开放数据类型>('<开放数据名>', <你使用的Schema>);
 *                         // ^ from @youzan-cloud/cloud-biz-types
 * });
 * ```
 * *************************************************************************** */
describe('[订单列表页]检查开放数据', () => {
  test('Data.orderListTabs', async () => {
    await validateOpenDataType<OrderListTabs>('orderListTabs', orderListTabsSchema);
  });

  // test('Data.orderListInfo', async () => {
  //   await validateOpenDataType<OrderListOrderInfo>('orderListInfo', orderListInfoSchema);
  // });
});

/** ****************************************************************************
 * # 开放方法
 * 这里仅校验单个开放方法，请勿增加多个开放方法联动使用的用例
 * 1. 检查运行时该方法是否存在
 * 2. 校验正常情况下方法是否能正常调用
 * 3. 检查异常情况下方法是否能正常抛错
 * *************************************************************************** */
describe('[订单列表页]检查开放方法', () => {
  test('Method.handleOrderAction', async () => {
    expect(yz).toHaveProperty('handleOrderAction');

    await expect(yz.handleOrderAction()).rejects.toThrow();
  });

  test('Method.switchOrderListTab', async () => {
    expect(yz).toHaveProperty('switchOrderListTab');
    await expect(yz.switchOrderListTab({ type: 'shipped' })).resolves.toBeUndefined();
    await expect(yz.switchOrderListTab({ type: 'all' })).resolves.toBeUndefined();
  });

  test('Method.refreshOrderInfo', async () => {
    expect(yz).toHaveProperty('refreshOrderInfo');
    await expect(yz.refreshOrderInfo({ orderNo: 'orderNo' })).resolves.toBeUndefined();
  });

  test('Method.refreshOrderList', async () => {
    expect(yz).toHaveProperty('refreshOrderList');
    await expect(yz.refreshOrderList()).resolves.toBeUndefined();
    await expect(yz.refreshOrderList({ keyword: 'keyword' })).resolves.toBeUndefined();
  });

  test('Method.fetchOrderList', async () => {
    expect(yz).toHaveProperty('fetchOrderList');
    await expect(yz.fetchOrderList()).resolves.toBeUndefined();
  });
});

/** ****************************************************************************
 * # 开放钩子
 * 这里仅校验单个钩子，主要有：
 * 1. 检查运行时该钩子是否存在
 * 2. 根据【是否允许多插件】校验hook返回值是数组还是对象(支持 = 数组；不支持 = 对象)
 * 3. 校验hook中定义的callback是否被正确触发
 * *************************************************************************** */
describe('[订单列表页]检查开放钩子', () => {
  test('Hook.beforeOrderActionHandle', async () => {
    expect(yz).toHaveProperty('beforeOrderActionHandle');

    const mockFn = jest.fn();
    await yz.beforeOrderActionHandle(mockFn);
    // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
    const hook = ctx.cloud.invoke('beforeOrderActionHandle');
    await expect(hook).resolves.toEqual([undefined]);
    expect(mockFn).toHaveBeenCalled();
  });

  test('Hook.beforeCancelOrderPopupToggle', async () => {
    expect(yz).toHaveProperty('beforeCancelOrderPopupToggle');

    const mockFn = jest.fn();
    await yz.beforeCancelOrderPopupToggle(mockFn);
    // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
    const hook = ctx.cloud.invoke('beforeCancelOrderPopupToggle');
    await expect(hook).resolves.toEqual(undefined);
    expect(mockFn).toHaveBeenCalled();
  });

  test('Hook.beforeOrderBtnsRender', async () => {
    expect(yz).toHaveProperty('beforeOrderBtnsRender');

    const mockFn = jest.fn();
    await yz.beforeOrderBtnsRender(mockFn);
    // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
    const hook = ctx.cloud.invoke('beforeOrderBtnsRender');
    await expect(hook).resolves.toEqual(undefined);
    expect(mockFn).toHaveBeenCalled();
  });
});

/** ****************************************************************************
 * # 开放事件
 * 这里仅校验单个事件，主要有：
 * 1. 检查运行时该事件是否存在
 * 2. 校验event中定义的callback是否被正确触发
 * *************************************************************************** */
// describe('[订单列表页]检查开放事件', () => {
//   test('Event.onPayChannelChange', async () => {
//     expect(yz).toHaveProperty('onPayChannelChange');
//
//     const ext = getCtx('@wsc-tee-trade/trade-buy-pay-view');
//
//     const mockFn = jest.fn();
//     await yz.onPayChannelChange(mockFn);
//
//     await ext.cloud.emit('onPayChannelChange');
//     expect(mockFn).toHaveBeenCalled();
//   });
// });
