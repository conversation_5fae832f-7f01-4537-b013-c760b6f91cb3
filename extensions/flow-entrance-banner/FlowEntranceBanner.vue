<template>
  <view>
    <view v-if="show" class="flow-entrance-banner" :style="bannerStyle" @click="handelClick">
      <image mode="widthFix" :src="bannerSrc" class="flow-entrance-banner__image" />
    </view>
  </view>
</template>

<script>
import { url } from '@youzan/tee-util';
import Tee from '@youzan/tee';
import { mapData } from '@youzan/ranta-helper-tee';
import { fetchInfo } from './api';

export default {
  data() {
    return {
      info: null,
      bizName: '',
      bannerStyle: '',
      kdtId: '',
      isWholesaleOrder: false,
      componentName: 'flow_entrance_banner',
    };
  },

  computed: {
    bannerSrc() {
      console.log('bannerSrc', this.info);
      return this.info && this.info.banner;
    },
    show() {
      if (this.isWholesaleOrder) {
        return false;
      }
      return !!this.info;
    },
  },

  created() {
    mapData(this, ['kdtId', 'bizName', 'isWholesaleOrder'], {
      callback: () => {
        this.getInfo();
      },
    });
  },
  methods: {
    getInfo() {
      const { kdtId, bizName, isWholesaleOrder } = this;

      /**
       * 存在kdtId bizName和非批发商订单时才进行接口请求
       */
      if (!kdtId || !bizName || isWholesaleOrder) {
        return;
      }

      fetchInfo({
        bizName,
        kdtId: this.kdtId,
      }).then((res) => {
        const { open = false, info = null } = res;
        const currentInfo = open && info ? info : null;
        if (currentInfo) {
          const { bannerStyle = '' } = currentInfo;
          this.info = currentInfo;
          this.bannerStyle = bannerStyle;
          this.triggerLogger('view', 'flow_entrance_banner_view', '流量入口图片曝光');
        }
      });
    },

    triggerLogger(et, ei, en) {
      const { info, componentName = '', bizName = '' } = this;
      const { linkOpt: { source = '' } = {} } = info;

      const { kdtId } = this;

      this.ctx.logger.log({
        et,
        ei,
        en,
        params: {
          biz_name: bizName,
          from_source: `${source}_${kdtId}`,
          component: componentName,
        },
      });
    },

    handelClick() {
      const { linkOpt: { path = '', href = '', source = '' } = {} } = this.info;
      const { kdtId } = this;

      if (!path && !href) return;

      this.triggerClickLogger && this.triggerClickLogger();

      let targetLink = path;
      /* #ifdef web */
      targetLink = href;
      /* #endif */

      const curPath = url.args.add(targetLink, {
        from_source: `${source}_${kdtId}`,
        kdt_id: kdtId,
      });

      // 小程序打开新小程序，web端打开链接
      /* #ifdef web */
      Tee.navigate({ url: curPath });
      /* #endif */

      /* #ifdef weapp */
      Tee.$native.navigateToMiniProgram({
        appId: 'wxf1fdc416d4ced1b3',
        path: curPath,
      });
      /* #endif */
    },

    triggerClickLogger() {
      this.triggerLogger('click', 'flow_entrance_banner_click', '流量入口图片点击');
    },
  },
};
</script>

<style lang="scss">
.flow-entrance-banner {
  border-radius: 8px;
  overflow: hidden;
  height: 88px;
  background-color: #f8f8f8;
  &__image {
    width: 100%;
    height: 100%;
    display: flex;
    border-radius: inherit;
  }
}
</style>
