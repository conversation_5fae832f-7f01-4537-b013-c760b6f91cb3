import { checkRetailShop } from '@youzan/utils-shop';
import Main from './Main.vue';

export default class RetailOrderErrorInfo {
  constructor(options) {
    this.ctx = options.ctx;
    this.widget = {
      widget: Main,
    };
    this.ctx.env.getQueryAsync().then((query) => {
      const { retailOrderScene = '' } = query;
      // 24 小时货架，不走这个逻辑
      this.isRetailOrderScene = this.ctx.data.isRetailShelfPage || retailOrderScene === '24hshelf';

      if (checkRetailShop(this.ctx.data?.shop ?? {}) || this.isRetailOrderScene) {
        this.initPreHooks();
      } else {
        this.ctx.process.define('callHookAfterFetchState', (params = {}) => Promise.resolve(params));
        this.ctx.process.define('callHookGenConfirmParams', (params = {}) => Promise.resolve(params));
      }
    });
  }

  initPreHooks() {
    // 获取完 pre-book 和 confirm 之后会调用
    this.ctx.process.define('callHookAfterFetchState', (params = {}) => {
      const {
        display: {
          canSelfFetch,
          showExpressTab,
          showLocalDeliveryTab,
          localDeliveryLackFeeStr,
          localDeliveryStartFeeStr,
        },
        goods,
        deliveryCheck: { shopNotSupportReasonList = [] } = {},
        address: { id = [], list = [] } = {},
        shop: { kdtId: orderKdtId } = {},
        postage: { postageItems = [], currentExpressType } = {},
        extra = {},
      } = params;
      const showNotEnoughPricePop = shopNotSupportReasonList.some((item) => +item.type === 3003); // 未满启动价格
      const showOutOfDeliveryPop =
        shopNotSupportReasonList.some((item) => +item.type === 1001); // 超出配送范围
      const currentAddress = list.find((item) => item.id === id); // 当前地址
      const isSupportExpress = postageItems.some((item) => +item.expressType === 0);
      this.ctx.data.retailOrderErrorInfo = {
        canSelfFetch, // 是否支持自提
        showExpressTab, // 是否支持内容快递
        isSupportExpress, // 是否支持快递方式
        showLocalDeliveryTab, // 是否支持配送
        showOutOfDeliveryPop,
        showNotEnoughPricePop,
        showNeedRequiredPop: extra.HAVE_MUST_ITEMS === '0', // 是否需要必选品
        localDeliveryStartFeeStr, // 启动价
        localDeliveryLackFeeStr, // 差价
        goods, // 下单商品
        currentAddress, // 当前地址
        currentExpressType, // 当前商家配送方式
        orderKdtId, // 下单店铺ID
        isRetailOrderScene: this.isRetailOrderScene,
        shouldResetCoupons: showNotEnoughPricePop || showOutOfDeliveryPop,
      };
      return Promise.resolve(params);
    });

    this.ctx.process.define('callHookGenConfirmParams', (params = {}) => Promise.resolve(this.setRetailParams(params)));
  }

  setRetailParams(params) {
    if (!this.ctx.data.retailOrderErrorInfo) return params;
    const { shouldResetCoupons = false, currentExpressType } = this.ctx.data.retailOrderErrorInfo;
    const { delivery = {}, extensions = {} } = params;

    // 快递、同城配送模式下 切换地址列表 不会携带 expressTypeChoice 参数，需要补齐
    if (!Object.prototype.hasOwnProperty.call(delivery, 'expressTypeChoice') && +currentExpressType !== 1) {
      delivery.expressTypeChoice = currentExpressType;
    }

    // 切店、切换配送方式场景下，新增 extra CHANGE_EXPRESS_RESET_COUPONS 区分
    if (shouldResetCoupons) {
      extensions.CHANGE_EXPRESS_RESET_COUPONS = '1';
    }
    return {
      ...params,
      delivery,
      extensions,
    };
  }

  static widgets = {
    Main,
  };
}
