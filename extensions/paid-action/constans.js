export const GOODS_TYPE = {
  VIRTUAL: 60,
  E_CARD: 61,
  MEMBER_CARD: 20,
  KNOWLEDGE: 31,
  FENXIAO: 10,
};

export const SHOP_AD_MAP = {
  pop_ad: 'pop_ad',
  logistics_subscribe: 'subscription',
  fission_coupons: 'fission_coupons',
  auto_issue_coupon: 'auto_issue_coupon',
  prior_use: 'priorUse',
  card_grant: 'card_grant',
};

export const NEEDS_CALCULATE_SHOP_AD_NUMBERS = [
  SHOP_AD_MAP.pop_ad,
  SHOP_AD_MAP.logistics_subscribe,
  SHOP_AD_MAP.fission_coupons,
  SHOP_AD_MAP.auto_issue_coupon,
  SHOP_AD_MAP.prior_use,
  SHOP_AD_MAP.card_grant,
];

export const BTNS_MAP = {
  SUBSCRIPTION: 'SUBSCRIPTION', // 订阅物流
  SAVE_PICK_UP_CODE: 'SAVE_PICK_UP_CODE', // 查看提货码
  SAVE_CARD_VOUCHER: 'SAVE_CARD_VOUCHER', // 查看券码
  KEEP_SHOPPING: 'KEEP_SHOPPING', // 继续逛逛
  GIVE_AWAY: 'GIVE_AWAY', // 立即赠送
  DETAIL: 'DETAIL', // 查看订单
  GROUPBUY: 'GROUPBUY', // 社区团购再次购买
  SHARE: 'SHARE', // 分享一下,
  BUY_AGAIN: 'BUY_AGAIN', // 再来一单
  DIRECT_BUY_AGAIN: 'DIRECT_BUY_AGAIN', // 再来一单直接到下单
  TAKE_GOODS_CODE: 'TAKE_GOODS_CODE', // 取货口令
  PURCHASE_TAKE: 'PURCHASE_TAKE', // 订货商-去提货
  PURCHASE_CENTER: 'PURCHASE_CENTER', // 订货商-订货商中心
  EXHIBITION_RESERVE_SHARE: 'EXHIBITION_RESERVE_SHARE', // 活动预定详情
  CARD_VOUCHER_EXHIBITION_DETAIL: 'CARD_VOUCHER_EXHIBITION_DETAIL', // 查看活动详情
};

// 1. 支持的按钮能力；2. 控制按钮的显示顺序
export const STANDARD_BTN_GROUP = [
  BTNS_MAP.SUBSCRIPTION, // 订阅物流
  BTNS_MAP.TAKE_GOODS_CODE, // 取货口令
  BTNS_MAP.SAVE_PICK_UP_CODE, // 查看提货码
  BTNS_MAP.SAVE_CARD_VOUCHER, // 查看券码
  BTNS_MAP.PURCHASE_TAKE, // 订货商-去提货
  BTNS_MAP.KEEP_SHOPPING, // 继续逛逛
  /* #ifdef web */
  BTNS_MAP.GIVE_AWAY, // 立即赠送
  /* #endif */
  BTNS_MAP.CARD_VOUCHER_EXHIBITION_DETAIL, // 活动详情(高于查看订单)
  BTNS_MAP.DETAIL, // 查看订单
  BTNS_MAP.GROUPBUY, // 社区团购再次购买
  BTNS_MAP.BUY_AGAIN, // 再来一单
  BTNS_MAP.DIRECT_BUY_AGAIN, // 再来一单
  BTNS_MAP.SHARE, // 分享一下
  BTNS_MAP.PURCHASE_CENTER, // 订货商-订货商中心
  BTNS_MAP.EXHIBITION_RESERVE_SHARE, // 活动预定 邀请好友
];

// 社区团购订单的按钮列表目前在前端控制着
export const GROUP_BUY_DEFAULT_BTNS = {
  [BTNS_MAP.DETAIL]: {
    defaultText: '查看订单',
    needJump: true,
    type: BTNS_MAP.DETAIL,
  },
  [BTNS_MAP.GROUPBUY]: {
    defaultText: '继续团购',
    needJump: true,
    type: BTNS_MAP.GROUPBUY,
  },
  [BTNS_MAP.SHARE]: {
    defaultText: '分享一下',
    needJump: true,
    type: BTNS_MAP.SHARE,
  },
  [BTNS_MAP.PICK_UP_CODE]: {
    defaultText: '查看提货码',
    needJump: true,
    type: BTNS_MAP.PICK_UP_CODE,
  },
};

export const BUTTON_CLASS = {
  primary: 'action-block__btn-primary',
  default: 'action-block__btn-default',
};

// 按钮业务类型
export const BIZ_TYPE = {
  DEFAULT: 'NORMAL',
  GROUPBUY: 'GROUPBUY',
  SOLITAIRE_BUY: 'SOLITAIRE_BUY',
};
