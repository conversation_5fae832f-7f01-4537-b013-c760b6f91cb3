import { bridge, cloud, cloudHook, mapData } from '@youzan/ranta-helper';
import { mapCtxData } from '@ranta/store';
import {
  WechatReceiptBeforeActionHandle,
  WechatReceiptBeforeActionRender,
} from '@youzan-cloud/cloud-biz-types';

import Main from './Main.vue';
import PaidAction from './PaidAction.vue';
import createStore from './store';
import initProcess from './process';

export default class WxPaidActionExt {
  ctx: any;

  store: any;

  destoryProcess: any;

  @cloud('beforeActionRender', 'hook', { allowMultiple: true })
  beforeActionRender = cloudHook<WechatReceiptBeforeActionRender>();

  @cloud('beforeActionHandle', 'hook', { allowMultiple: true })
  beforeActionHandle = cloudHook<WechatReceiptBeforeActionHandle>();

  /**
   * openDetail
   * @deprecated 从 2.0 开始
   * @desc 打开详情
   */
  @bridge('openDetail', 'process')
  openDetailV1() {
    this.ctx.process.invoke('openDetail');
  }

  /**
   * openResult
   * @deprecated 从 2.0 开始
   * @desc 打开结果页面
   */
  @bridge('openResult', 'process')
  openResultV1() {
    this.ctx.process.invoke('openResult');
  }

  /**
   * openHome
   * @deprecated 从 2.0 开始
   * @desc 打开主页
   */
  @bridge('openHome', 'process')
  openHomeV1() {
    this.ctx.process.invoke('openHome');
  }

  /**
   * openShare
   * @deprecated 从 2.0 开始
   * @desc 打开分享页面
   */
  @bridge('openShare', 'process')
  openShareV1() {
    this.ctx.process.invoke('openShare');
  }

  /**
   * openOutLink
   * @deprecated 从 2.0 开始
   * @desc 打开外部链接
   */
  @bridge('openOutLink', 'process')
  openOutLinkV1(url) {
    this.ctx.process.invoke('openOutLink', url);
  }

  static widgets = { PaidAction, Main };

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    mapCtxData(this, ['orderNo', 'kdtId', 'returnUrl', 'themeColors', 'themeCss', 'payResult']);
    this.destoryProcess = initProcess(this.ctx, this.store);
    mapData(this, {
      payResult: (val) => {
        if (val.payResultVO) {
          this.store.initButtonList();
        }
      },
    });
  }

  pageDestroyed() {
    this.destoryProcess();
  }
}
