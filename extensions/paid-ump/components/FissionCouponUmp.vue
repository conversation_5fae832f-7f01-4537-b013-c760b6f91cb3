<template>
  <button
    v-if="coupon.quantity > 0"
    class="fission-coupon"
    :class="[coupon.styleVersion === 'v2' ? 'fission-coupon-v2' : 'fission-coupon-v1']"
    data-detail="fission"
    open-type="share"
    @click="clickFissionCoupon"
  >
    <view class="fission-coupon__bg"></view>
    <view v-if="coupon.styleVersion === 'v2'">
      <view class="fission-coupon__text">
        送你
        <text class="fission-coupon__text-primary">{{ coupon.quantity }}</text>
        张拼手气优惠券
      </view>
      <text class="fission-coupon__desc">快分享给好友，一起领取吧！</text>
      <view class="fission-coupon__btn"></view>
    </view>
    <view v-else class="fission-coupon__text">恭喜你！送你{{ coupon.quantity }}张优惠券</view>
  </button>
</template>

<script>
import Tee from '@youzan/tee';
import get from '@youzan/utils/object/get';

const app = getApp();

export default {
  props: {
    coupon: {
      type: Object,
      default: () => ({}),
    },
    orderNo: {
      type: String,
      default: '',
    },
  },

  methods: {
    clickFissionCoupon(event) {
      const detail = get(event, 'target.dataset.detail', '');
      this.$emit('share', detail);
      setTimeout(() => {
        const url = `/packages/ump/fission/index?order_no=${this.coupon?.orderNo || this.orderNo}`;
        Tee.navigate({ url });
      }, 1000);
      app.logger?.log?.({
        et: 'click',
        ei: 'click_liebianquan',
        en: '裂变券点击',
        si: app.getKdtId(),
      });
    },
  },
};
</script>

<style lang="scss">
@keyframes scale-btn {
  0% {
    transform: translateY(-27px) scale(1);
  }
  50% {
    transform: translateY(-27px) scale(0.8);
  }
  100% {
    transform: translateY(-27px) scale(1);
  }
}

.fission-coupon-v1 {
  width: 100%;
  padding: 0 12px;
  margin-top: 12px;
  position: relative;
  background: #f7f8fa;
  border: none;
  outline: none;
  text-align: left;
  &::after {
    content: '';
    border: none;
  }

  .fission-coupon {
    &__bg {
      width: 100%;
      height: 72px;
      background: url('https://img01.yzcdn.cn/pay-result/images/2913fc82e814345118edd91a11d89a8245b4fb.png')
        no-repeat;
      background-size: 100% 100%;
      border-radius: 4px;
    }

    &__text {
      color: #ffeaa1;
      font-size: 22px;
      font-weight: 500;
      position: absolute;
      width: 100%;
      top: 50%;
      left: 0;
      padding: 0 32px;
      transform: translateY(-50%);
    }
  }
}

.fission-coupon-v2 {
  width: 100%;
  padding: 0 12px;
  margin-top: 12px;
  position: relative;
  background: #f7f8fa;
  border: none;
  outline: none;
  text-align: left;
  &::after {
    content: '';
    border: none;
  }

  .fission-coupon {
    &__bg {
      width: 100%;
      height: 76px;
      background: url('https://img01.yzcdn.cn/public_files/761ae5b17129f0461cd9f5fa09cc0513.png')
        no-repeat;
      background-size: 100% 100%;
    }

    &__text {
      color: #fff;
      font-size: 20px;
      font-weight: 600;
      position: absolute;
      width: 100%;
      top: 40%;
      left: 0;
      padding-left: 80px;
      box-sizing: border-box;
      transform: translateY(-50%);
      text-shadow: 0 3px 6px #df5100;

      &-primary {
        color: #fff376;
      }
    }

    &__desc {
      color: #fff;
      font-size: 13px;
      position: absolute;
      width: 100%;
      top: 75%;
      left: 0;
      padding-left: 90px;
      box-sizing: border-box;
      transform: translateY(-50%);
    }

    &__btn {
      width: 54px;
      height: 54px;
      background: url('https://img01.yzcdn.cn/public_files/16e39fc648b1b50a86f5082f3ea1d2cb.png')
        no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      right: 28px;
      transform: translateY(-27px);
      animation: scale-btn 500ms infinite linear;
    }
  }
}

@media only screen and (max-width: 360px) {
  .fission-coupon-v2 {
    .fission-coupon__text {
      font-size: 20px;
    }

    .action-block__btn-default {
      width: 86px;
    }
  }
}

@media only screen and (max-width: 340px) {
  .fission-coupon-v2 {
    .fission-coupon__text {
      font-size: 16px;
    }

    .action-block__btn-default {
      width: 86px;
    }
  }
}

@media only screen and (max-width: 320px) {
  .fission-coupon-v2 {
    .fission-coupon__text {
      padding-left: 68px;
    }
    .fission-coupon__desc {
      font-size: 10px;
      padding-left: 80px;
    }
  }
}
</style>
