<template>
  <view class="goods-pro" v-if="process.length">
    <view class="goods-pro__item" v-for="(item, index) in process" :key="index">
      <view class="goods-pro__line" v-if="index !== 0"></view>
      <view :class="['goods-pro__text', index === current ? 'current' : '']">
        {{ item.name }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    processInfo: Object,
  },

  computed: {
    process() {
      const { processInfo = {} } = this;
      const { bomProcessList = [] } = processInfo;
      return bomProcessList;
    },

    current() {
      const { process = [] } = this;
      return process.findIndex((item) => item.current === 1);
    },
  },
};
</script>

<style lang="scss">
@keyframes identifier {
  from {
    opacity: 0.1;
  }

  to {
    opacity: 1;
  }
}

.goods-pro {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 10px;
  margin-top: 8px;

  &__item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #c8c9cc;
  }

  &__text {
    &.current {
      color: #323233;
      animation: identifier 1.5s infinite;
    }
  }

  &__line {
    height: 1px;
    width: 9px;
    background: #c8c9cc;
    margin: 0 2.7px;
  }
}
</style>
