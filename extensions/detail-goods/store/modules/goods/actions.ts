import Tee from '@youzan/tee';
import { url } from '@youzan/tee-util';
import { errorToast } from '@youzan/tee-biz-util';
import { requestV2 } from '@youzan/tee-biz-request';
import Toast from '@youzan/vant-tee/dist/toast/toast';

import api from '../../../api';
import { BTN, REFUND_ACTION } from '../../../contants';
/* #ifdef web */
import { navigateToTargetQttNote } from '../../../utils';
/* #endif */

enum ActionTypeEnum {
  ADD_TO_CART = 'addToCart',
  REFUND = 'refund',
  MESSAGE = 'message',
  POSTPONE = 'postpone',
  CANCEL_POSTPONE = 'cancelpostpone',
  REORDER = 'reorder',
}

interface HandleGoodsActionParams {
  type: ActionTypeEnum;
  itemId: string;
}

interface AddCartData {
  items: Array<{
    goodsId: unknown;
    skuId: unknown;
    kdtId: unknown;
    num: unknown;
    propertyIds?: unknown;
  }>;
}

export default function (ctx) {
  return {
    // [mutation]
    SET_SHOW_RECOMMEND(value) {
      this.showRecommend = value;
    },
    // [mutation]
    SET_SHOW_ADD_TO_CART_SUCCESS(value) {
      this.showAddToCartSuccess = value;
    },

    closeActionSheet() {
      this.SET_SHOW_ADD_TO_CART_SUCCESS(false);
    },

    // 退款
    refund(itemId: string) {
      const goodsItem = this.itemList.find((item) => item.itemId === itemId);
      ctx.cloud
        .invoke('beforeRefundClick', {
          id: goodsItem.id,
          orderNo: this.orderNo || '',
          type: 'refund',
        })
        .then(() => {
          const { kdtId, orderNo, sourceInfo = {} } = this;

          // 抖音卡券小程序订单不允许发起申请退款
          if (sourceInfo.orderMark === 'dy_coupon' && goodsItem.refundText === '申请退款') {
            Toast('此订单为抖音小程序卡券订单，请到抖音小程序中处理');
            return;
          }

          api.getRefundState({ kdtId, itemId, orderNo }).then((res = {}) => {
            const { isOldOrder, redirectTarget, redirectUrl, reason = '服务器开小差了' } = res;
            /* #ifdef web */
            // 网点订单详情跳转退款使用订单级的kdtId
            if (isOldOrder) {
              return Tee.navigate({
                url: goodsItem.refundUrl,
                type: 'redirectTo',
              });
            }
            /* #endif */
            if (redirectTarget === REFUND_ACTION.TOAST) {
              Toast(reason);
            } else if (redirectUrl) {
              const { mpVersion, isXhsLocalLife, bizEnv } = ctx.env.getQuery();
              const query = {
                src: redirectUrl,
                title: '申请退款',
                mpVersion,
              };

              /* #ifdef web */
              let toUrl = redirectUrl;

              if (mpVersion) {
                toUrl = url.args.add(toUrl, { mpVersion });
              }

              if (isXhsLocalLife) {
                toUrl = url.args.add(toUrl, { isXhsLocalLife });
              }

              // url 上带 bizEnv 则继续传下去
              if (bizEnv) {
                toUrl = url.args.add(toUrl, { bizEnv });
              }

              // 赞拼拼增加标识
              if (this.isFxZpp) {
                toUrl = url.args.add(toUrl, { bizEnv: 'fx-zpp' });
                const openSubscriptionSettings =
                  url.args.get('openSubscriptionSettings', window.location.href) || 0;
                toUrl = url.args.add(toUrl, { openSubscriptionSettings });
              }

              // 先囤后约未预约的订单
              const { hasReservedOrders = {} } = goodsItem;
              const reservedOrders = Object.keys(hasReservedOrders);
              if (reservedOrders.length > 0) {
                toUrl = url.args.add(toUrl, { reservedOrders: JSON.stringify(hasReservedOrders) });
              }

              // 抖音小程序不走重定向
              Tee.navigate({
                url: toUrl,
                type: this.miniprogram?.isTTApp ? 'navigateTo' : 'redirectTo',
              });
              /* #endif */

              /* #ifdef weapp */
              Tee.navigate({
                url: url.args.add('/packages/trade/order/safe/index', query, true),
              });
              /* #endif */
            }
          });
          ctx.logger.log({
            et: 'click',
            ei: 'goto_refund',
            en: '订单详情-发起退货退款',
            params: {
              order_no: orderNo,
            },
          });
        });
    },

    async addToCart(itemId: string) {
      const data = this.itemList.find((item) => item.itemId === itemId);
      ctx.logger.log({
        et: 'click', // 事件类型
        ei: 'add_cart_at_order_detail_click', // 事件标识
        en: '加入购物车点击', // 事件名称
        params: {
          component: 'goodsInfoCard',
          goodsId: data.goodsId,
        }, // 事件参数
      });
      try {
        const addCartData: AddCartData = {
          items: [
            {
              goodsId: data.goodsId,
              skuId: data.skuId,
              kdtId: this.kdtId,
              num: data.num, // item.num 加购数量沿用订单中的数量
            },
          ],
        };

        // 修复商品级"加入购物车"不支持商品属性的问题
        const propertyIds = (data.properties || []).map((item) => item.valId);
        propertyIds.length && (addCartData.items[0].propertyIds = propertyIds);

        await requestV2({
          path: '/wsctrade/cart/batchAddGoods.json',
          method: 'POST',
          contentType: 'application/json',
          errorMsg: '添加购物车失败，请稍后重试',
          withCredentials: true,
          options: {
            rawResponse: true,
          },
          data: addCartData,
        });
        // 当推荐商品存在时，展示加购弹窗，否则展示 toast
        if (!this.showRecommend) {
          Toast('已成功加入购物车');
        } else {
          this.addCartResultTitle = '加购成功';
          this.showAddToCartSuccess = true;
        }
      } catch (err) {
        this.addCartResultTitle = '加购失败';
        errorToast(err, { message: '添加购物车失败，请稍后重试' });
      }
    },

    // 查看留言
    toggleMessagePopup(itemId: string) {
      if (this.showMessagePopupItemId === itemId) {
        this.showMessagePopupItemId = -1;
        return;
      }
      this.showMessagePopupItemId = itemId;
    },

    /**
     * 订单商品收货顺延
     * ! 如果有定制问题反馈说定制了goods-info slot 之后发现使用this.yz.handleGoodsAction 操作顺延不生效
     * ! 不要慌，这是一个已知问题，原因是因为顺延是一个吸附在原有按钮上的popup，需要得到原按钮的具体坐标
     * ! 如果三方定制掉了整个goods-info，那么这个dom也就找不到了，所以不会生效，目前暂时还没有很好的处理方法
     * ! 可以找产品协调，调整成dialog形式，或者尝试处理开放API:handleGoodsAction，让三方传入指定的selector ClassName
     * ! [popup与按钮示例图](https://img.yzcdn.cn/public_files/123e2bc0400d7f1058bb8cd73bff3ad5.png)
     * @param itemId 商品itemId
     */
    handlePostpone(itemId: string) {
      const detailGoodsWidget = Tee.getGlobal('detail-goods-widget');
      if (!detailGoodsWidget) {
        console.warn('找不到Goods.vue组件');
        return;
      }
      let btnEle;
      /* #ifdef web */
      btnEle = document.querySelector(`#goods-btn-${BTN.postpone.value}`).getBoundingClientRect();
      const bodyEle = document.querySelector('body').getBoundingClientRect();
      ctx.event.emit('showPostponePopup', {
        position: {
          left: btnEle.left - 5 - bodyEle.left,
          top: btnEle.top + btnEle.height / 2 - bodyEle.top,
        },
        itemId,
      });
      /* #endif */

      /* #ifdef weapp */
      btnEle = document;
      Promise.all([
        new Promise((resolve) =>
          detailGoodsWidget
            .createSelectorQuery()
            .select(`#goods-btn-${BTN.postpone.value}`)
            .boundingClientRect(resolve)
            .exec()
        ),
        new Promise((resolve) =>
          detailGoodsWidget.createSelectorQuery().selectViewport().scrollOffset(resolve).exec()
        ),
      ]).then(([rect, offset]: [DOMRect, any /* 小程序的类型定义 */]) => {
        ctx.event.emit('showPostponePopup', {
          position: {
            left: rect.left - 5 + offset.scrollLeft,
            top: rect.top + rect.height / 2 + offset.scrollTop,
          },
          itemId,
        });
      });
      /* #endif */
    },

    /**
     * 处理商品操作
     * @param {HandleGoodsActionParams}
     */
    handleGoodsAction({ type, itemId }: HandleGoodsActionParams) {
      ctx.cloud
        .invoke('beforeHandleGoodsAction', { orderNo: this.orderNo, type, itemId })
        .then(() => {
          const { isHotel = false, orderExtra = {} } = this;
          if (this.isTradeComponent3) {
            Toast('视频号订单，请前往视频号订单中心操作');
            return;
          }
          switch (type) {
            case BTN.addToCart.value:
              this.addToCart(itemId);
              break;
            case BTN.refund.value:
              this.refund(itemId);
              break;
            case BTN.message.value:
              this.toggleMessagePopup(itemId);
              break;
            case BTN.postpone.value:
              if (!isHotel) {
                this.handlePostpone(itemId);
              } else {
                const targetItem = this.itemList.find((item) => item.id === itemId);
                Tee.navigate({
                  url: targetItem.postponeDeliveryUrl,
                  type: 'redirectTo',
                });
              }
              break;
            /* #ifdef web */
            case BTN.reorder.value:
              navigateToTargetQttNote(orderExtra.ATTR_FX_ZPP_NOTE_ID);
              break;
            /* #endif */
            case 'cancelpostpone':
              // ^ 目前看来这个case只有酒店商品用到，因此这里没有在BTN中增加常量，如果未来标品也用到了可以继续考虑兼容
              if (isHotel) {
                const targetItem = this.itemList.find((item) => item.id === itemId);
                Tee.navigate({
                  url: targetItem.cancelPostponeDeliveryUrl,
                  type: 'redirectTo',
                });
              }
              break;
            default:
              break;
          }
        });
    },
  };
}
