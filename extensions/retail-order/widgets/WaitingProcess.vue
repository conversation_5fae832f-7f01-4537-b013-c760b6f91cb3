<template>
  <view v-if="processData.needDisplay" class="process" :class="customClass">
    <view v-if="!processData.orderNum" class="time">
      <text>现在下单，立即制作</text>
    </view>
    <view v-else class="time">
      前面还有<text class="count">{{ processData.orderNum }}</text
      >单/<text class="count">{{ processData.goodsNum }}</text
      >杯
    </view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      processData: {},
    };
  },
  props: {
    customClass: String,
  },
  created() {
    mapData(this,['processData']);
  },
};
</script>

<style lang="scss" scoped>
.process {
  margin-top: 10tpx;
  &.sku-order{
    padding-left: 12tpx;
    margin-top: -4tpx;
  }
}
.time {
  display: inline;
  height: 22tpx;
  font-weight: 500;
  font-size: 13tpx;
  line-height: 23tpx;
  color: #323233;
}
.count {
  font-size: 20px;
  color: var(--general, #ee0a24);
}
</style>
