<template>
  <view>
    <view v-if="couponActivitiesList.length" class="activity-item" @click="toggleActivitiesPopup">
      <text class="activity-title">优惠卡券</text>
      <view class="activity-value-wrapper">
        <text class="activity-value">
          {{ formatedDecreaseAmount }}
        </text>
        <van-icon
          v-if="showPopupTip"
          class="activity-question-icon"
          color="#1989fa"
          name="question-o"
          size="14px"
          @click.stop="toggleActivitiesPopup"
        />
      </view>
    </view>

    <!-- 活动 popup -->
    <activities-popup
      :show="showPopup"
      :list="couponActivitiesList"
      :decrease-amount="decreaseAmount"
      :theme-colors="themeColors"
      decrease-text="-"
      @close="toggleActivitiesPopup"
    >
      <view slot="title" class="header">优惠卡券</view>
    </activities-popup>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import { formatPrice } from './utils';
import ActivitiesPopup from './components/ActivitiesPopup';

export default {
  components: {
    'van-icon': Icon,
    'activities-popup': ActivitiesPopup,
  },
  data() {
    return {
      activitiesList: [],
      showPopup: false,
      themeColors: {},
    };
  },

  computed: {
    decreaseAmount() {
      return this.couponActivitiesList.reduce((result, item) => {
        result += item.decrease;
        return result;
      }, 0);
    },
    formatedDecreaseAmount() {
      return formatPrice(this.decreaseAmount);
    },

    couponActivitiesList() {
      return (this.activitiesList || [])
        .filter((item) => item.type === 'coupon')
        .map((item) => {
          return {
            ...item,
            promotionTypeName: '优惠券',
            activityName: item.discountCouponStr,
          };
        });
    },

    showPopupTip() {
      return this.couponActivitiesList.length > 1;
    },
  },

  created() {
    mapData(this, ['activitiesList', 'themeColors']);
  },

  methods: {
    toggleActivitiesPopup() {
      // 当没有提示图标的时候，不响应弹起事件
      if (!this.showPopup && !this.showPopupTip) return;
      this.showPopup = !this.showPopup;
    },
  },
};
</script>

<style scoped>
.header {
  text-align: center;
  line-height: 48px;
  font-size: 16px;
  color: #323232;
}
.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}
.activity-title {
  font-size: 14px;
  color: #969799;
}
.activity-value {
  font-size: 14px;
  color: #323233;
}
</style>
