<template>
  <view class="detail-table-number" v-if="!!tableNumber">
    <view class="detail-table-number_title">桌号</view>
    <view class="detail-table-number_number">{{ tableNumber }}</view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { object } from '@youzan/tee-util';

export default {
  data() {
    return {
      orderBizExtra: {},
    };
  },
  computed: {
    tableNumber() {
      return object.get(this.orderBizExtra, 'tableName');
    },
  },
  created() {
    mapData(this, ['orderBizExtra']);
  },
};
</script>

<style lang="scss">
.detail-table-number {
  height: 91px;
  margin-top: 10px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &_title {
    font-size: 14px;
    font-weight: 500;
  }
  &_number {
    font-size: 34px;
    font-weight: 500;
  }
}
</style>
