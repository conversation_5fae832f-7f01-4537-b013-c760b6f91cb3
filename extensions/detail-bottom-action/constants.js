const PING = '\u8bc4';
const JIA = '\u4ef7';
const EVALUATION = PING + JIA;

// 底部按钮
export const BOTTOM = {
  more: { name: '更多', value: 'more', type: 'default' },
  confirm: { name: '确认收货', value: 'confirm', type: 'primary' },
  confirmHotel: { name: '确认入住', value: 'confirmHotel', type: 'primary' },
  groupon: { name: '查看团详情', value: 'groupon', type: 'default' },
  coupon: { name: '领优惠券', value: 'coupon', type: 'default', version: 'v1' },
  gift: { name: '查看礼包', value: 'gift', type: 'default' },
  evaluate: { name: '立即' + EVALUATION, value: 'evaluate', type: 'default' },
  viewEvaluate: {
    name: '查看' + EVALUATION,
    value: 'viewEvaluate',
    type: 'default',
  },
  invoice: { name: '补开发票', value: 'invoice', type: 'default' },
  share: { name: '我要晒单', value: 'share', type: 'default' },
  batchRefund: { name: '批量退款', value: 'batchRefund', type: 'default' },
  wholeAfterSales: {
    value: 'wholeAfterSales',
    name: '多商品售后',
    type: 'default',
  },
  extend: { name: '延长收货', value: 'extend', type: 'default' },
  cancel: { name: '取消订单', value: 'cancel', type: 'default' },
  peerpay: { name: '代付详情', value: 'peerpay', type: 'default' },
  buyAgain: { name: '再来一单', value: 'buyAgain', type: 'default' },
  courseDetail: { name: '查看课程', value: 'courseDetail', type: 'default' },
  deleteOrder: { name: '删除订单', value: 'deleteOrder', type: 'default' },
  directBuyAgain: { name: '再来一单', value: 'directBuyAgain', type: 'default' },
  modifyAddress: {
    name: '修改地址',
    value: 'modifyAddress',
    type: 'default',
  },
  /* #ifdef web */
  qttOrderComment: { name: '立即晒单', value: 'qttOrderComment', type: 'default' },
  qttOrderCommentted: {
    name: '已晒单',
    value: 'qttOrderCommentted',
    type: 'default',
  },
  // 群团团分享好友
  qttShare: {
    type: 'default',
    name: '分享好友',
    value: 'onQttShare',
  },
  // 群团团再来一单
  qttBuyAgain: {
    type: 'default',
    name: '再来一单',
    value: 'onQttBuyAgain',
  },
  /* #endif */
};

// 订单详情页 按钮控制
export const isIVRDisabled = [
  'peerpay',
  'groupon',
  'share',
  'cancel',
  'coupon',
  'buyAgain',
  'courseDetail',
  'invoice',
  'gift',
  'evaluate',
  'viewEvaluate',
  'directBuyAgain',
  'modifyAddress',
];

export const isKosTokenVerifiedDisabled = [
  'confirm',
  'extend',
  'cancel',
  'buyAgain',
  'evaluate',
  'viewEvaluate',
  'directBuyAgain',
  'modifyAddress',
];

export const AUTO_GO_EVALUATE_FLAG = 'confirm_goods';
