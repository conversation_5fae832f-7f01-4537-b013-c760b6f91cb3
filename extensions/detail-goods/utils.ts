import api from './api';
import { ZNB } from '@youzan/tee-biz-navigate';

export const cloudData = {
  getGoodsInfo: <T>({ itemList }, version: 'v1' | 'v2'): T => {
    let cloudData;
    switch (version) {
      case 'v1':
        cloudData = itemList?.map((item) => {
          const {
            goodsId,
            itemId,
            weight,
            goodsInfo,
            goodsType,
            goodsUrl,
            num,
            skuId,
            message: buyerMemo,
            showRefund: isShowRefund,
          } = item;
          return {
            goodsId,
            itemId,
            weight,
            goodsInfo,
            goodsType,
            goodsUrl,
            num,
            skuId,
            buyerMemo,
            isShowRefund,
          };
        });
        break;
      case 'v2':
        cloudData = {
          list: itemList?.map((item) => {
            const {
              goodsId,
              itemId,
              weight,
              goodsInfo,
              goodsType,
              goodsUrl,
              num,
              skuId,
              message: buyerMemo,
              showRefund: isShowRefund,
              payPrice,
              originPrice,
            } = item;
            return {
              goodsId,
              itemId,
              weight,
              goodsInfo: {
                alias: goodsInfo.alias,
                courseGoodsMark: goodsInfo.courseGoodsMark,
                imgUrl: goodsInfo.imgUrl,
                mark: goodsInfo.mark,
                pointsPrice: goodsInfo.pointsPrice,
                shortTitle: goodsInfo.shortTitle,
                title: goodsInfo.title,
              },
              goodsType,
              goodsUrl,
              num,
              skuId,
              buyerMemo,
              isShowRefund,
              payPrice,
              originPrice,
            };
          }),
        };
        break;
    }
    return cloudData;
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);

/* #ifdef web */
// 跳转至团购详情
export const navigateToTargetQttNote = (noteId) => {
  if (!noteId) return;

  const url = `/packages/detail-page/view/index?noteId=${noteId}`;
  const isWeapp = window._global?.miniprogram?.isWeapp;

  if (isWeapp) {
    // @ts-ignore
    ZNB.navigate({
      weappUrl: url,
    });
  } else {
    api
      .getUrlLinkRedirectUrl({
        url: url.substr(1),
        kdtId: 102382218,
      })
      .then((res) => {
        if (res && res.yzLongLink) {
          location.href = res.yzLongLink;
        }
      });
  }
};
/* #endif */
