<template>
  <!-- 储值卡 -->
  <award-base-card :info="formatInfo" @click-handle="clickHandle" :theme-color="themeColor" />
</template>

<script>
import BaseCard from './BaseCard.vue';
import Tee from '@youzan/tee';
import moneyFormat from '@youzan/utils/money/format';
import { CashbackType } from '../../constants';
import { cdnImage } from '@youzan/tee-biz-util';

const PLUGIN_TYPE = {
  TUAN: 2,
  ORDER: 203,
};

export default {
  name: 'award-store-value',
  components: {
    'award-base-card': BaseCard,
  },
  props: {
    info: {
      type: Array,
      default: () => [
        {
          cash: 13,
          cashbackType: 2,
          customerDesc: '金额将返回至原有账户',
          delayDays: 0,
          id: 10000500309,
          isUserCardActivated: false,
          type: 203,
        },
      ],
    },
    themeColor: String,
  },
  computed: {
    curInfo() {
      /** 后端虽然是array的返回，但是储值金和现金只会同时出现一个 */
      return this.info?.[0] ?? {};
    },
    formatInfo() {
      const { isUserCardActivated, customerDesc, cashbackType } = this.curInfo;
      const isMoney = cashbackType === CashbackType.Money;
      return {
        icon: cdnImage('upload_files/2021/07/06/FgpypQqIcxNsB3SSaxW7eBPCxMOX.png'),
        text: this.formatTitle(),
        desc: customerDesc,
        buttonText: isUserCardActivated || isMoney ? '立即查看' : '立即激活',
      };
    },
  },
  mounted() {
    const { type, cashbackType, isUserCardActivated } = this.curInfo;
    const isMoney = cashbackType === CashbackType.Money;
    this.$emit('log-attach', {
      et: 'view',
      ei: 'view_cashback',
      en: '返现曝光',

      params: {
        cashback_plugin: type === PLUGIN_TYPE.TUAN ? '团购返现' : '订单返现',
        cashback_type: cashbackType === CashbackType.Money ? '现金' : '储值金',
        // 现金不需要激活，默认为true
        is_active: isUserCardActivated || isMoney ? 1 : 0,
      },
    });
  },
  methods: {
    formatTitle() {
      const { cash, cashbackType } = this.curInfo;
      const suffix = cashbackType === CashbackType.Money ? '现金' : '储值金';
      return moneyFormat(cash, true, false) + '元' + suffix;
    },

    clickHandle() {
      const { isUserCardActivated, url, cashbackType, type } = this.curInfo;

      this.$emit('log-attach', {
        et: 'click',
        ei: isUserCardActivated ? 'click_cashback' : 'click_activate',
        en: isUserCardActivated ? '返现立即查看点击' : '返现立即激活点击',
        params: {
          cashback_plugin: type === PLUGIN_TYPE.TUAN ? '团购返现' : '订单返现',
          cashback_type: cashbackType === CashbackType.Money ? '现金' : '储值金',
        },
      });
      Tee.navigate({
        url,
      });
    },
  },
};
</script>
