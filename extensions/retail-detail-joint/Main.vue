<template>
  <view v-if="participants.length > 0" class="joint" @click="onJumpDetail">
    <view class="joint__title">拼单</view>
    <view class="joint__container">
      <view class="joint__container__participants">
        <view
          v-for="(item, i) in participants"
          :key="i"
          class="participant"
          :class="['participant' + i]"
        >
          <image class="participant__img" :src="item.imgUrl" />
        </view>
        <view v-if="hasMore" class="participant participant5">
          <view class="participant__icon">
            <view class="participant__icon--circle"></view>
          </view>
          <view class="participant__icon">
            <view class="participant__icon--circle"></view>
          </view>
          <view class="participant__icon">
            <view class="participant__icon--circle"></view>
          </view>
        </view>
      </view>
      <view class="joint__container__opts">
        <view v-if="status === 100">
          分享账单
          <van-icon
            custom-class="joint__container__opts--icon"
            name="arrow"
            size="12px"
            color="#646566"
          />
        </view>
        <view v-else-if="status === 99">拼单已关闭</view>
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import { getJointDetail } from './api/index';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      participants: [],
      status: null,
      hasMore: false,
    };
  },

  created() {
    this.hasRequestDetail = false; // 是否已经请求过详情数据
    mapData(
      this,
      ['orderNo', 'orderBizExtra'],
      {
        callback: () => this.getDetail(),
      },
      { isSetData: false }
    );
  },

  methods: {
    getDetail() {
      if (this.hasRequestDetail) return;
      const { kdtId, orderNo, orderBizExtra } = this.ctx.data;
      const jointId = orderBizExtra?.jointDTO?.jointId;
      if (kdtId && orderNo && jointId) {
        this.hasRequestDetail = true;
        getJointDetail({ kdtId, orderNo, jointId }).then((result) => {
          const { participantBriefInfoList, jointOrderStatus } = result;
          this.participants = participantBriefInfoList.slice(0, 5);
          this.hasMore = participantBriefInfoList.length > 5;
          this.status = jointOrderStatus;
          this.jointId = jointId;
        });
      }
    },
    onJumpDetail() {
      const { status, jointId } = this;
      if (+status === 100) {
        Tee.navigate({
          url: `/packages/retail/order-pool/detail/index?orderPoolId=${jointId}`,
          type: 'navigateTo',
        });
      }
    },
  },
};
</script>

<style lang="scss">
.joint {
  display: flex;
  margin-top: 10tpx;
  padding: 0 16tpx;
  flex-direction: column;
  background-color: #fff;

  &__title {
    display: flex;
    width: 100%;
    height: 40tpx;
    font-size: 14tpx;
    color: #323233;
    font-weight: 500;
    justify-content: flex-start;
    align-items: center;
  }

  &__container {
    display: flex;
    height: 68tpx;
    align-items: center;
    justify-content: space-between;

    &__participants {
      display: flex;
      position: relative;
      height: 100%;

      .participant {
        display: flex;
        position: absolute;
        top: 16tpx;
        width: 36tpx;
        height: 36tpx;
        justify-content: center;
        align-items: center;
        background-color: #fafafa;
        border: 2tpx solid #fff;
        border-radius: 50%;
        box-sizing: border-box;

        &__img {
          overflow: hidden;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }

        &__icon {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;

          &--circle {
            width: 4tpx;
            height: 4tpx;
            background-color: #dcdee0;
            border-radius: 50%;
          }
        }
      }
    }

    &__opts {
      display: flex;
      font-size: 14tpx;
      color: #646566;
      align-items: center;

      &--icon {
        margin-left: 8tpx;
      }
    }
  }
}
/* 设计给的前面的覆盖一部分后面的，只能用绝对定位的方式了 */
.participant0 {
  left: 0;
  z-index: 5;
}

.participant1 {
  left: 30tpx;
  z-index: 4;
}

.participant2 {
  left: 60tpx;
  z-index: 3;
}

.participant3 {
  left: 90tpx;
  z-index: 2;
}

.participant4 {
  left: 120tpx;
  z-index: 1;
}

.participant5 {
  left: 150tpx;
  padding: 0 4tpx;
  background-color: #fff;
  border-color: #dcdee0;
  z-index: 0;
}
</style>
