<template>
  <!-- 积分卡 -->
  <award-base-card :info="formatInfo" @click-handle="clickHandle" :theme-color="themeColor" />
</template>

<script>
import BaseCard from './BaseCard.vue';
import Tee from '@youzan/tee';
import { cdnImage } from '@youzan/tee-biz-util';

export default {
  name: 'award-point',
  components: {
    'award-base-card': BaseCard,
  },
  props: {
    info: {
      type: Object,
      default: () => ({
        credit: 100,
        creditName: '积分',
        num: 0,
      }),
    },
    themeColor: String,
  },
  computed: {
    formatInfo() {
      const { credit, creditName, desc } = this.info;
      return {
        icon: cdnImage('public_files/99ba1265c329fc7a0c7973d64c617e5d.png'),
        text: `${credit}${creditName ?? '积分'}`,
        desc,
        buttonText: '立即查看',
      };
    },
  },
  created() {
    this.$emit('log-attach', {
      et: 'view',
      ei: 'show_fanjifen',
      en: '返积分曝光',
    });
  },
  methods: {
    clickHandle() {
      this.$emit('log-attach', {
        et: 'click',
        ei: 'click_fanjifen_xiangqing',
        en: '返积分“查看详情”点击',
      });
      const { url } = this.info;

      Tee.navigate({
        url,
        type: 'navigateTo',
      });
    },
  },
};
</script>
