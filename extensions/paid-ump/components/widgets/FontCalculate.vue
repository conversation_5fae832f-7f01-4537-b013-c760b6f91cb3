<template>
  <view class="font-calculate" :style="containerStyle">
    <view class="loading" v-if="calculating">-</view>
    <view :class="['value', item.unitDesc === '' ? 'simple' : '']" :style="fontStyle">
      <view class="prefix" v-if="item.preDesc">{{ item.preDesc }}</view
      >{{ item.valuesDesc }}<view class="unit">{{ item.unitDesc }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'font-calculate',
  props: {
    maxWidth: {
      type: Number,
      default: 80,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
    curFontSize: {
      type: Number,
      default: 30,
    },
    minFontSize: {
      type: Number,
      default: 16,
    },
    color: String,
  },
  data() {
    return {
      valueFontSize: this.curFontSize,
      calculating: true,
    };
  },
  computed: {
    fontStyle() {
      return `font-size: ${this.valueFontSize}px;max-width: ${this.maxWidth}px;opacity:${
        this.calculating ? 0 : 1
      }`;
    },
    containerStyle() {
      return `max-width: ${this.maxWidth}px;color: ${this.color};`;
    },
  },
  mounted() {
    setTimeout(
      () =>
        this.calculate().then(() => {
          this.calculating = false;
        }),
      0
    );
  },
  methods: {
    async calculate() {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        // eslint-disable-next-line no-await-in-loop
        const curStyle = await this.getBoundingClientRectWidth('.font-calculate');
        // eslint-disable-next-line no-await-in-loop
        const valueStyle = await this.getBoundingClientRectWidth('.value');
        const { width: curWidth, height: curHeight } = curStyle;
        // 因为存在省略的情景，所以 value 的 width 需要尽可能的小于 maxWidth，才可以确保缩进的正确执行
        if (
          curWidth <= this.maxWidth &&
          curHeight < 2 * this.valueFontSize &&
          valueStyle.width < this.maxWidth
        )
          return;
        if (this.valueFontSize <= this.minFontSize) return;
        this.valueFontSize -= 2; // 出于效率考虑，2 比 1 快一倍
      }
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = this.createSelectorQuery();
        query
          .select(selector)
          .boundingClientRect()
          .exec((res) => {
            const result = res[0] ? res[0] : {};
            resolve(result);
          });
      });
    },
  },
};
</script>

<style lang="scss">
.font-calculate {
  display: inline-block;
  position: relative;
  line-height: 22px;
}
.loading {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 2;
  text-align: center;
}

.value {
  font-weight: bold;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  opacity: 0;
}

.value.simple {
  font-weight: normal;
}

.unit,
.prefix {
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
  margin: 0 2px;
}
</style>
