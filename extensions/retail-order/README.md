### 订单确认页零售专用 ext

===================================

### 治理

> 按照约定零售定制化内容在订单确认页仅使用 retail-order 这个 ext，这必然导致随着时间和需求的累积，index 内代码行数越来越多，难以维护，所以在和 **@瘦马** 大哥讨论后，2021.10.25 开始逐步的治理零售下单页业务。做的事情很简单，就是相办法把 index 内的代码挪到其他文件去。在理想情况下，index 就是一个执行零售各定制化业务方法初始化的地方，在这个理想前提下，能保证 index 的代码行数得到有效控制，且业务逻辑都在其他文件内，开发也就不用特别关心 index 做了什么事情。

为了达成这个目标，我们必须要做一些约定，并且按照约定，我们已经迁移出很多新文件，都在 controllers 文件夹下。

- process 等直接在业务文件内完整定义，然后放入 index 内的 processFns 或 processFns。
- widget 和 data 类似。
- watch 可能存在多个业务 watch 同一份数据，所以业务文件内定义 watchXXXData，index 内在对应的 ctx.watch 内调用 watchXXXData。

此约定最大的问题是业务文件内的 this 问题，或者说需要获取到 index 内的 this。中台化的开发特点其实就是数据都真实挂载在 ranta 定义的 ctx 上，所以所有业务方法内都传入一个 ctx，所有业务逻辑操作均只修改 ctx 上的 data。为了某些可能的特殊场景，目前 processFns 和 processFns 内的 fn 执行时均绑定了 index 内的 this，但 **非常不推荐** 你在业务文件中真的这么使用 this：

- 几乎所有场景下你都可以靠 ctx 来完成对数据的操作，如果有数据挂载在 index 内的 this 上（比如 getter 属性），那也可以把这些 getter 属性改造为 ctx 上的 data（class 的 getter 属性并不会有任何性能上的优势，仅仅是写法好看些）。
- 要想在业务文件中使用 this，你就必须对 this 了解的足够清楚，例如你将无法使用熟悉的箭头函数来定义导出的 function。
- 最大的问题是使用心智负担，如果你仅仅使用 ctx，你将完全没有使用 this 带来的额外影响，毕竟 ctx 是作为参数传入的。

### 业务 controller

### 关键属性

- isRetailWeappScene 零售小程序
- isRetailOrderScene 零售点单宝
