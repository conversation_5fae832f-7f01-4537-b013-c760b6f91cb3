<template>
  <view
    :class="['action-block', 'action-block--normal']"
    ref="paid-action"
    :style="themeCss"
    v-if="status == 'ALL_PAID'"
  >
    <view class="action-block__wrap">
      <!-- 按钮区域 -->
      <view>
        <van-button
          v-for="(item, index) in groupButton.primary"
          :key="index"
          :class="item.className"
          @click="clickHandler(item)"
        >
          {{ item.defaultText || '心愿单' }}
        </van-button>
      </view>
      <view>
        <van-button
          v-for="(item, index) in groupButton.default"
          :key="index"
          :class="item.className"
          @click="clickHandler(item)"
        >
          {{ item.defaultText }}
        </van-button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import get from '@youzan/utils/object/get';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import { mapActions, mapState } from '@ranta/store';
import { BUTTON_GROUP } from './constants';

export default {
  components: {
    'van-button': Button,
  },
  data() {
    return {
      payResult: {},
      themeCss: '',
      ...mapState(this, ['groupButton', 'buttonGroup']),
    };
  },

  computed: {
    status() {
      return get(this.payResult, 'payResultVO.payState');
    },
  },

  watch: {
    buttonGroup(val) {
      if (!val) return;
      const buttons = Object.keys(val);
      // 处理按钮曝光埋点
      if (buttons?.length && !this.log) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.log = true;
        buttons.forEach((key) => {
          BUTTON_GROUP.forEach((item) => {
            if (item.key === key) {
              this.ctx.process.invoke(
                'logger',
                'view',
                `show_button_${item.key}`,
                `${item.value}按钮曝光`
              );
            }
          });
        });
      }
    },
  },

  created() {
    mapData(this, ['themeCss']);
    mapData(this, {
      payResult: (val) => {
        this.payResult = val;
        this.$nextTick(() => {
          this.ctx.data.paidActionHeight =
            (this.$refs['paid-action'] && this.$refs['paid-action'].offsetHeight + 30) || 0;
        });
      },
    });
    mapActions(this, ['handleAction']);
  },

  methods: {
    async clickHandler(actionItem) {
      this.handleAction(actionItem);
    },
  },
};
</script>

<style lang="scss">
$red-color: #ee0d27;
$red-color-active: #eb7784;

.primary-block {
  display: flex;
  align-items: center;
}
.action-block {
  padding: 24px 20px 16px;
  background: #fff;

  &--normal {
    text-align: center;
  }

  &--mutli {
    display: block;

    > .t-button {
      width: 100%;

      & + .t-button {
        margin-top: 10px;
      }
    }
  }

  &__btn-primary.t-button,
  &__btn-default.t-button {
    display: inline-block;
    height: 36px;
    line-height: 36px;
    border-radius: var(--theme-radius-button, 2px) !important;
    white-space: nowrap;
    font-size: 14px;
    border: none;
    overflow: hidden;
  }

  &__btn-primary.t-button {
    position: relative;
    min-width: 118px;
    border-radius: var(--theme-radius-button, 18px) !important;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
    display: inline-block;
    margin: 0 auto 18px;
    background: #fff;
    border: 1px solid var(--general, '#f44');
    color: var(--general, '#f44');

    &:not(:last-child) {
      margin-right: 24px;
    }
  }

  &__btn-default.t-button {
    background: #fff;
    padding-right: 16px;
    width: 86px;
    color: #969799;

    &:last-child {
      padding-right: 15px;
    }

    &:not(:last-child)::after {
      content: ' ';
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #dcdee0;
      vertical-align: middle;
      position: absolute;
      right: 0;
      top: 50%;
      margin-top: -8px;
    }

    &:active::before {
      opacity: 0;
    }
  }
}
</style>
