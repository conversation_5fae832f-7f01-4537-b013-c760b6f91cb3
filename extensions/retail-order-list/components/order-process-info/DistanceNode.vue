<template>
  <text class="distance-text">{{ distanceStr }}</text>
</template>

<script>
import { gcjToBaidu } from '@youzan/shop-tee-shared';
import { calculateDistance, formatDistance } from '@youzan/utils/number/distance';

export default {
  name: 'distance-node',
  props: {
    opt: Object,
    location: Object,
  },
  computed: {
    distanceStr() {
      const { location, opt } = this;
      // 没有定位坐标，展示默认文案
      if (!location.latitude || !opt.lat) {
        return opt.defaultText;
      }
      // 定位坐标转换为百度再做计算
      const baiduLocation = gcjToBaidu(location.longitude, location.latitude);
      const distanceStr = formatDistance(
        calculateDistance(baiduLocation.latitude, baiduLocation.longitude, opt.lat, opt.lng)
      );
      return opt.preDesc + distanceStr + opt.afterDesc;
    },
  },
};
</script>

<style lang="scss" scoped>
.distance-text {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>