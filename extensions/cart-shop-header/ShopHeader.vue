<template>
  <view class="shop-header">
    <view
      class="shop-header__name"
      @click="switchShop"
      v-if="(isEmpty || !addressDetail) && !isLiveHalfPage"
    >
      <van-icon name="shop-o" size="16px" color="#111" class="shop-header__name__shop-icon" />
      <view class="shop-header__name__text" i18n-off>{{ shopTitleFormat }}</view>
      <van-icon
        name="arrow"
        size="14px"
        color="#111"
        class="shop-header__name__arrow-icon"
        v-if="isLink"
      />
    </view>
    <view v-else-if="addressDetail && !isLiveHalfPage" class="shop-header__name" @click="onClick">
      <van-icon name="location" size="18px" color="#111" class="shop-header__name__shop-icon" />
      <view class="shop-header__name__text" i18n-off>{{ addressDetail }}</view>
      <van-icon
        v-if="isLink"
        name="arrow"
        size="14px"
        color="#111"
        class="shop-header__name__arrow-icon"
      />
    </view>
    <view
      v-if="isWholesaler && !isLiveHalfPage"
      class="shop-header__go-wholesale"
      @click="goPurchaseOrder"
      >查看进货单</view
    >
    <view class="shop-header__edit-btn" @click="switchEditMode" v-if="!isEmpty">
      {{ btnText }}
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapState, mapActions } from '@ranta/store';
import { buildUrl } from '@youzan/tee-biz-util';

import Tee from '@youzan/tee';
import args from '@youzan/utils/url/args';
import { getDefaultLogisticType } from './api';
import authorize from '@youzan/wsc-tee-trade-common/lib/order-utils/authorize';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef weapp */
import { enterShopSelect } from '@youzan/tee-chain-store';
/* #endif */
/* #ifdef web */
import { action } from '@youzan/zan-jsbridge';
import * as SafeLink from '@youzan/safe-link';
/* #endif */

/* #ifdef weapp */
const app = getApp();
/* #endif */

// 该key对应交易地址编辑的事件的key，为了减少体积，不再引用npm包
const addressSelectEventKey = 'address-select';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      homePath: '',
      /* #ifdef web */
      isXhsApp: false,
      /* #endif */
      ...mapState(this, [
        'shopTitle',
        'editMode',
        'btnText',
        'isWholesaler',
        'isSwitchOpen',
        'isChainShop',
        'shopMetaInfo',
        'isCanSwitchShop',
      ]),
      currentAddress: {},
      currentExpressType: 0,
      shopList: [],
      isLiveHalfPage: false, // 半屏私域直播间来源购物车
    };
  },
  computed: {
    addressDetail() {
      return this.currentAddress.addressDetail;
    },
    isApproveSwitchShop() {
      return this.isChainShop && this.isCanSwitchShop;
    },
    isLink() {
      // 默认自提时，连锁可点击
      if (this.currentExpressType === 1) {
        return this.isApproveSwitchShop;
      }
      // 默认配送时有两种情况，1. 购物车为空，此时展示店铺名称，连锁可点击。2. 购物车不为空，存在addressDetail时会展示地址信息，也可以点击。
      return this.isEmpty
        ? this.isApproveSwitchShop
        : this.addressDetail || this.isApproveSwitchShop;
    },
    shopTitleFormat() {
      return this.shopTitle || (this.shopMetaInfo && this.shopMetaInfo.shopName) || '';
    },
    isEmpty() {
      return this.shopList.length === 0;
    },
  },
  created() {
    mapData(this, ['currentAddress', 'currentExpressType', 'shopList']);
    this.init();
  },
  mounted() {
    mapActions(this, ['initData', 'switchEditMode']);
    this.initData();
    /* #ifdef web */
    this.isXhsApp = window._global?.miniprogram?.isXhsApp;
    this.isLiveHalfPage = !!args.get('isLiveHalfPage', window.location.href);
    /* #endif */
  },

  destroyed() {
    /* #ifdef weapp */
    this.offEvent();
    /* #endif */
  },
  methods: {
    switchShop() {
      if (!this.isApproveSwitchShop) {
        return;
      }
      // 重定向到新的店铺选择页面
      /* #ifdef web */
      const { offlineData } = window._global;
      SafeLink.redirect({
        url: offlineData?.url || '',
      });
      return;
      /* #endif */
      // eslint-disable-next-line no-unreachable
      this.ctx.process.invoke('navigateFromCart', {
        link: `/packages/shop-select/chain-store/shopselect/index?dbKey=location&redirectUrl=%2Fpackages%2Ftrade%2Fcart%2Findex`,
      });
    },
    goHome() {
      let link = '/packages/home/<USER>/index';
      let znbType = '';
      let navigateType = '';
      /* #ifdef web */
      const {
        platform,
        url: { wap },
        kdtId,
      } = window._global;
      // 有赞精选
      if (platform === 'youzanmars') {
        action.doAction({ action: 'goHome' });
      } else {
        link = `${wap}/showcase/homepage?kdt_id=${kdtId}`;
        const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isSwanApp, isKsApp } =
          window._global?.miniprogram || {};
        if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
          link = '/pages/home/<USER>/index';
          if (isTTApp) {
            link = '/pages/home/<USER>/main';
          }
          znbType = 'switchTab';
        }
      }
      /* #endif */

      /* #ifdef weapp */
      navigateType = 'reLaunch';
      /* #endif */

      this.ctx.process.invoke('navigateFromCart', { link, navigateType, znbType });
    },
    /* #ifdef web */
    getWebCartUrl() {
      const { pathname } = window.location;
      const query = this.ctx.env.getQuery();
      let url = args.add(buildUrl(pathname, 'h5'), {
        kdt_id: window._global.kdtId,
      });
      // 购物车参数address_id需要外部带入，这里排除掉，其他都进行encode处理加入url中，为了避免数据带入的参数有特殊字符导致url异常。
      for (const key in query) {
        if (key === 'address_id') {
          continue;
        }
        const val = query[key];
        if (val) {
          url = args.add(url, { [key]: encodeURIComponent(val) });
        }
      }
      return url;
    },
    /* #endif */

    onClick() {
      this.goSelectAddress();
    },
    goSelectAddress() {
      /* #ifdef web */
      // h5中选择地址跳转链路：购物车 -> 地址列表(选择地址后) -> 选择门店页(选择门店后） -> 购物车
      let addressPageLink = buildUrl('/wsctrade/order/address/list', 'h5');
      addressPageLink = args.add(addressPageLink, { redirect_url: this.getWebCartUrl() });

      Tee.navigate({
        type: 'navigateTo',
        url: addressPageLink,
      });
      /* #endif */

      /* #ifdef weapp */
      this.listenEvent();
      Tee.navigate({
        type: 'navigateTo',
        url: `packages/order-native/address-list/index?switchable=1&id=${this.currentAddress.id}`,
      });
      /* #endif */
    },

    goPurchaseOrder() {
      let link = buildUrl('/wsctrade/wholesale/purchaseOrder', 'h5');
      /* #ifdef weapp */
      link = `/pages/common/webview-page/index?src=${encodeURIComponent(
        buildUrl('/wsctrade/wholesale/purchaseOrder', 'h5')
      )}`;
      /* #endif */
      this.ctx.process.invoke('navigateFromCart', { link });
    },

    init() {
      /**
       * h5需要通过地址id来获取默认的用户地址，因为h5是跳页选择地址然后redirect_url回到购物车，
       * 通过url中获取地址id来判断选择的地址信息。
       * 小程序通过event通讯来获取用户选择的地址
       */

      this.ctx.env
        .getQueryAsync()
        .then((query) => {
          const params = {};
          /* #ifdef web */
          if (query.address_id || query.addressId) {
            params.address_id = query.address_id || query.addressId;
          }
          /* #endif */
          /* #ifdef weapp */
          // 小程序切换地址后，最终在切店完成通过url的dbid带入页面地址信息
          const dbData = app.db.get(query.dbid) || {};
          if (dbData.id) {
            params.address_id = dbData.id;
          }
          /* #endif */
          return params;
        })
        .then((params) => {
          getDefaultLogisticType(params).then((res) => {
            const { expressType, defaultAddress } = res;
            // expressType: 0 快递 1 自提 2同城
            this.ctx.data.currentExpressType = expressType;
            // 配送优先取用户地址，没有再展示店铺地址，自提直接展示店铺地址。
            // 自提时需要当前定位信息，因此需要先获取当前定位信息。
            if (expressType !== 1 && defaultAddress) {
              this.setAddress(defaultAddress);
            } else if (expressType === 1) {
              this.getUserLocation().then((location) => {
                if (location) {
                  this.ctx.data.currentLocation = location;
                }
              });
            }
          });
        });
    },

    setAddress(currentAddress) {
      if (!currentAddress) {
        return;
      }
      this.ctx.data.currentAddress = currentAddress;
    },

    /**
      获取用户当前定位地址：
      1. 获取当前定位
      2. 根据当前定位解析出地址信息
  */
    getUserLocation() {
      return authorize('scope.userLocation')
        .then(
          () =>
            new Promise((resolve, reject) =>
              this.ctx.process.invokePipe('tryLocation', {
                success: (baiduLocation) => resolve(baiduLocation),
                fail: reject,
              })
            )
        )
        .then((baiduLocation) => {
          return {
            lon: baiduLocation.lng,
            lat: baiduLocation.lat,
          };
        })
        .catch((err) => {
          console.log('err', err);
          return null;
        });
    },
    /* #ifdef weapp */
    goShopSelectPage(options = {}) {
      // TODO: 写死购物车路由，由于存在pages/tab/three/index这种路由来渲染购物车，如果用当前页面路由，当切店后会非预期的跳转到店铺B的tab/three配置页，回不到购物车页面。
      const route = 'packages/trade-cart/cart/index';
      const redirectUrl = encodeURIComponent(
        args.add(route, {
          mode: this.currentExpressType === 1 ? 0 : 1, // 1: 外卖 0: 自提
          filter: 0,
        })
      );
      enterShopSelect(
        {
          redirectUrl,
        },
        {
          redirectType: 'navigateTo',
          ...options,
        }
      );
    },
    onAddressSelected(address) {
      // 地址更新后刷新数据
      this.setAddress(address);
      setTimeout(() => {
        this.ctx.event.emit('updateCartAsyncData');
      }, 16);
    },
    listenEvent() {
      app.once(addressSelectEventKey, this.onAddressSelected.bind(this));
    },
    offEvent() {
      app.off(addressSelectEventKey, this.onAddressSelected.bind(this));
    },
    /* #endif */
  },
};
</script>

<style lang="scss" scoped>
.shop-header {
  height: 48px;
  background: #fff;
  display: flex;
  margin-bottom: var(--theme-page-card-margin-bottom, 12px);
  justify-content: space-between;
  align-items: center;

  &__name {
    width: 0;
    padding: 0 12px;
    padding-left: var(--theme-trade-md-gutter, 12px);
    display: flex;
    align-items: center;
    flex: 1;

    &__text {
      margin-left: 4px;
      font-size: 16px;
      line-height: 48px;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #111;
      font-weight: 500;
    }

    &__arrow-icon {
      margin-left: 4px;
      position: relative;
      top: 1px;
    }

    &__shop-icon,
    &__arrow-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__edit-btn,
  &__go-wholesale {
    padding: 0 12px;
    line-height: 48px;
    font-size: 16px;
    color: #111;
  }

  &__edit-btn {
    padding-right: var(--theme-trade-md-gutter, 12px);
  }

  &__go-wholesale {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      width: 1px;
      background-color: #d8d8d8;
      top: 17px;
      bottom: 16px;
      right: 0;
    }
  }
}
</style>
