<template>
  <view class="detail-time-crm">
    <view class="detail-time-crm__item time-item__order-no">
      <view class="detail-time-crm__order-no">
        订单编号：<text class="text-color">{{ outBizNo }}</text>
      </view>
      <van-button size="mini" custom-class="copy-button" @click="handleCopyBtnClick"> 复制 </van-button>
    </view>
    <view v-if="!!time.createTime" class="detail-time-crm__item">
      创建时间：
      <text class="text-color">{{ createTime }}</text>
    </view>
  </view>
</template>

<script>
import Button from '@youzan/vant-tee/dist/button/index';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { setClipboardData } from '@youzan/tee-api';
import { date } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-button': Button,
  },

  data() {
    return {
      outBizNo: '',
      time: {},
    };
  },

  computed: {
    createTime() {
      const { time } = this;
      return time.createTime ? date.formatDate(time.createTime, 'YYYY-MM-DD HH:mm:ss') : '';
    },
  },

  created() {
    mapData(this, ['outBizNo', 'time']);
  },

  methods: {
    handleCopyBtnClick() {
      setClipboardData(this.outBizNo).then(() => {
        /* #ifdef web */
        Toast.success('已复制');
        /* #endif */
      });
    },
  },
};
</script>

<style lang="scss">
.detail-time-crm {
  margin-top: 10px;
  margin-bottom: 180px;
  font-size: 12px;
  padding: 10px 16px;
  line-height: 22px;
  background: #fff;

  &__item {
    display: flex;
    line-height: 22px;
    color: #969799;
  }

  &__order-no {
    margin-right: 10px;
  }

  .text-color {
    color: #323233;
  }

  .time-item__order-no {
    display: flex;
    align-items: center;
  }

  .copy-button {
    height: 18px !important;
    line-height: 16px !important;
    min-width: 30px !important;
    justify-content: center !important;
  }
}
</style>
