import { getPayResult } from './api';
import get from '@youzan/utils/object/get';
import Toast from '@youzan/vant-tee/dist/toast/toast';

let index = 0;
const delayTimes = [...new Array(6).fill(3000), ...new Array(4).fill(5000)];

export default class WxPaidPageSetupExt {
  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.data.isWxReceipt = true;
    this.initWatchs();
  }

  initWatchs() {
    this.orderNoUnWatch = this.ctx.watch('orderNo', (val) => {
      this.orderNo = val;
      this.init();
    });
    this.kdtIdUnWatch = this.ctx.watch('kdtId', (val) => {
      this.kdtId = val;
      this.init();
    });
  }

  init() {
    if (this.orderNo && this.kdtId) {
      this.ctx.data.payResult = {};
      this.getApi();
    }
  }

  getApi() {
    return getPayResult(this.orderNo, this.kdtId).then((data) => {
      const status = get(data, 'payResultVO.payState');
      data.url = get(window, '_global.url', {});
      if (!data.paidPromotion) {
        data.paidPromotion = get(data, 'shopPayResult.orderPayResultGroup[0].paidPromotion', {});
      }
      if (!data.shopCoupons) {
        data.shopCoupons = get(data, 'shopPayResult.shopCoupons', []);
        data.fissionCouponReceiveOrderNo = get(data, 'payResultVO.fissionCouponReceiveOrderNo');
      }
      if (data.isThirdApp) {
        delete data.payResultVO.buttonGroup.SUBSCRIPTION;
      }
      this.ctx.data.payResult = data;
      this.ctx.data.webImg = data.weappImg;

      if (status && status !== 'ALL_PAID') {
        this.delayContinue();
      } else {
        Toast.clear();
      }
      if (index > 10) {
        Toast.clear();
      }
    });
  }

  delayContinue() {
    // 轮询接口，响应后每秒一次，最多10次
    if (++index <= 10) {
      if (index === 1 || index === '1') {
        Toast.loading();
      }
      setTimeout(() => {
        this.getApi();
      }, delayTimes[index - 1]);
    }
  }

  pageDestroyed() {
    this.orderNoUnWatch && this.orderNoUnWatch();
    this.kdtIdUnWatch && this.kdtIdUnWatch();
  }
}
