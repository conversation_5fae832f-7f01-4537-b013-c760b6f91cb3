<template>
  <view class="fc-ump_coupon" :style="couponStyle" @click="handleClick">
    <view class="fc-ump_coupon-goods">
      <native-text class="alert-info"
        >[<native-text class="text">{{ originData.cooperateKdtName }}</native-text
        >]的商品，快来抢购！</native-text
      >
      <view v-if="availableGoods.length > 1">
        <view class="list">
          <view v-for="item in availableGoods" :key="item.alias" class="item">
            <image :src="item.logo" class="image" />
            <view class="price">¥{{ item.price }}</view>
          </view>
        </view>
      </view>
      <view v-else-if="availableGoods.length === 1">
        <view class="single-goods">
          <image :src="availableGoods[0].logo" class="image" />
          <view class="info">
            <native-text class="title">{{ availableGoods[0].title }}</native-text>
            <view class="price"
              >¥<text class="price-weight"
                ><text class="price-weight-big">{{ availableGoods[0].bigPrice }}</text
                >.{{ availableGoods[0].centPrice }}</text
              ></view
            >
          </view>
        </view>
      </view>
    </view>
    <view class="fc-ump_coupon-quota">
      <view class="container">
        <view class="quota" :style="originData.couponValueFontSize">
          <text class="unit" v-if="originData.isShowMax">最高</text>{{ originData.couponQuota
          }}<text class="unit" v-if="originData.isShowUnit">元</text
          ><text class="unit" v-if="originData.isShowDiscount">折</text>
        </view>
      </view>
      <native-text class="message">{{ originData.preferentialCopyWriting }}</native-text>
      <van-button custom-class="button" type="danger" round size="mini">立即领取</van-button>
    </view>
  </view>
</template>

<script>
import Button from '@youzan/vant-tee/dist/button/index';

import { adType } from '../../utils/flow-change';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'flow-change-ump-coupon',

  components: {
    'van-button': Button,
  },

  props: {
    originData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {};
  },

  computed: {
    couponStyle() {
      const couponHeight = this.availableGoods.length > 1 ? 140 : 160;
      return `height: ${couponHeight}px`;
    },
    availableGoods() {
      return (this.originData.availableGoods || []).map((item) => {
        return {
          ...item,
          logo: fullfillImage(item.logo, 'small'),
        };
      });
    },
  },

  created() {
    this.logger('view');
  },

  mounted() {},

  methods: {
    logger(eventType) {
      this.$emit('onLogger', {
        data: this.originData,
        type: adType.coupon,
        eventType,
      });
    },
    handleClick() {
      this.logger('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.fc-ump {
  &_coupon {
    width: 702rpx;
    background: url('https://b.yzcdn.cn/flow-change-ump/coupon-bg-3.x.png') no-repeat;
    background-size: 100% 100%;
    padding-top: 32px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    &-goods {
      width: calc((100% * 233) / 343);
      padding: 0 20px;
      box-sizing: border-box;
      .alert-info {
        /* #ifdef weapp */
        display: flex;
        /* #else */
        display: block;
        /* #endif */
        font-size: 12px;
        margin: 12px 0 0 6px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .text {
          display: inline-block;
          font-weight: bold;
        }
      }
      .list {
        display: flex;
        margin-top: 8px;
        .item {
          width: 60px;
          height: 60px;
          border-radius: 5px;
          margin-right: 8px;
          position: relative;
          .image {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
          .price {
            position: absolute;
            bottom: 0;
            height: 16px;
            width: 100%;
            color: #fff;
            background: rgba(0, 0, 0, 0.5);
            font-size: 10px;
            text-align: center;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
          }
        }
      }
      .single-goods {
        display: flex;
        margin-top: 12px;
        .image {
          width: 72px;
          height: 72px;
          border-radius: 5px;
          margin-right: 8px;
          .image {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
        }
        .info {
          .title {
            /* #ifdef weapp */
            display: block;
            /* #endif */
            font-size: 14px;
            width: 125px;
            height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            /* 微信小程序下需要同时存在两个，skyline下不支持webkit-box */
            /* stylelint-disable-next-line declaration-block-no-duplicate-properties */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .price {
            display: flex;
            align-items: baseline;
            font-size: 12px;
            margin-top: 12px;
            color: #f44;
            &-weight {
              font-weight: bold;
              display: flex;
              align-items: baseline;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 70px;
              &-big {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
    &-quota {
      width: calc((100% * 110) / 343);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 30px;
        .quota {
          font-size: 30px;
          font-weight: bold;
          color: #f44;
          display: flex;
          align-items: baseline;
          .unit {
            font-size: 14px;
            font-weight: normal;
          }
        }
      }
      .message {
        width: 95px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
        font-size: 12px;
        color: #646566;
        margin: 4px 0 10px;
      }
      .button {
        color: white;
        background: #f44;
        border-color: #f44;
        font-size: 14px;
        width: 82px;
      }
    }
  }
}
</style>
