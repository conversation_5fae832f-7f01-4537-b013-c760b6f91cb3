<template>
  <van-cell-group custom-class="refund-action" :border="false" v-if="showRefundInfo">
    <!-- #ifdef weapp -->
    <van-cell icon="point-gift" :title="msg" :border="false" />
    <!-- #endif -->
    <!-- #ifdef web -->
    <van-cell icon="point-gift" :title="msg" :border="false" is-link @click="toRefundDetailFn" />
    <!-- #endif -->
  </van-cell-group>
</template>

<script>
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Tee from '@youzan/tee';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },
  data() {
    return {
      giftInvite: {},
      showRefundInfo: false,
      kdtId: '',
      orderNo: '',
    };
  },

  computed: {
    msg() {
      return `${this.giftInvite.totalNum}份礼物，已领取${this.giftInvite.givenNum}份，未领取礼物已退款`;
    },
  },
  /* #ifdef web */
  methods: {
    toRefundDetailFn() {
      Tee.navigate({
        url: `/v2/trade/refund/fundprocessbyorder?order_no=${this.orderNo}&kdt_id=${this.kdtId}`,
      });
    },
  },
  /* #endif */

  created() {
    mapData(this, ['giftInvite', 'showRefundInfo', 'kdtId', 'orderNo']);
  },
};
</script>

<style lang="scss">
.refund-action {
  margin-top: 10px;
}
</style>
