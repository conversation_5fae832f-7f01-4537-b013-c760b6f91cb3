class LoggerClass {
  log: any = () => {
    console.error('请初始化Logger, 使用initLogger(ctx)');
  };

  pageSpm: String;

  init(ctx) {
    this.log = ctx.logger.log;
    this.pageSpm = ctx.logger.getPageSpm();
  }

  enterPage() {
    this.log({
      et: 'display', // 事件类型
      ei: 'enterpage', // 事件标识
      en: '浏览页面', // 事件名称
      pt: 'orderSearchPage', // 页面类型
      params: {
        // spm: 'str',
      },
    });
  }

  boughtGoodsClick({ goodsId, user_id: _userId }, bannerId) {
    this.log({
      et: 'click', // 事件类型
      ei: 'Already_bought_goods_click', // 事件标识
      en: '已购商品点击', // 事件名称
      params: {
        banner_id: `${this.pageSpm}~${bannerId}`,
        goods_id: goodsId,
        user_id: 'str',
        component: 'Already_bought_goods',
      }, // 事件参数
    });
  }

  boughtGoodsExposed({ goodsId, alg }, bannerId) {
    this.log({
      et: 'view', // 事件类型
      ei: 'view', // 事件标识
      en: '已购商品曝光', // 事件名称
      params: {
        alg,
        banner_id: `${this.pageSpm}~${bannerId}`,
        goods_id: goodsId,
        recommend_name: '常购商品',
        user_id: 'str',
        component: 'Already_bought_goods',
        item_type: 'goods',
        item_id: goodsId,
      }, // 事件参数
    });
  }

  boughtGoodsComponentExposed() {
    this.log({
      et: 'view', // 事件类型
      ei: 'Already_bought_goods_view', // 事件标识
      en: '已购商品组件曝光', // 事件名称
      params: {
        user_id: 'str',
        component: 'Already_bought_goods',
      }, // 事件参数
    });
  }

  turnPageClick() {
    this.log({
      et: 'click', // 事件类型
      ei: 'turn_page_click', // 事件标识
      en: '翻页点击', // 事件名称
      params: {
        component: 'Already_bought_goods',
      }, // 事件参数
    });
  }

  historyExposed() {
    this.log({
      et: 'view', // 事件类型
      ei: 'historical_record_view', // 事件标识
      en: '历史记录曝光', // 事件名称
      params: {
        component: 'orderSearchHistory',
      }, // 事件参数
    });
  }

  historyClicked() {
    this.log({
      et: 'click', // 事件类型
      ei: 'historical_record_click', // 事件标识
      en: '历史记录点击', // 事件名称
      params: {
        component: 'orderSearchHistory',
      }, // 事件参数
    });
  }

  historyCleared() {
    this.log({
      et: 'click', // 事件类型
      ei: 'empty_record_click', // 事件标识
      en: '清空记录点击', // 事件名称
      params: {
        component: 'orderSearchHistory',
      }, // 事件参数
    });
  }
}

const Logger = new LoggerClass();

export function initLogger(ctx) {
  Logger.init(ctx);
}

export default Logger;
