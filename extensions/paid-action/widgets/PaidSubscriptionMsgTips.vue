<template>
  <!-- 微信小程序环境下订阅消息提示组件 -->
  <view v-show="showWeappSubscribe && showSubscribeMsg" :style="themeCSS">
    <view v-if="showTips">
      <van-overlay mask :show="showTips" z-index="1" />
      <view class="overlay-wrapper" @click="closeTips">
        <image
          src="https://img01.yzcdn.cn/upload_files/2024/06/26/FtuV2PfvKClPxTH8cnN1pulJWi1N.gif"
          alt=""
          class="overlay-wrapper--img"
        />
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Overlay from '@youzan/vant-tee/dist/overlay/index.vue';
import { checkRetailShop } from '@youzan/utils-shop';
import { requestTemplateIds } from '../utils/subscription-msg';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { getIsCrmShop, queryOrderInfoByNo } from '../api';
import commonToast from '@youzan/wsc-tee-trade-common/lib/utils/toast';

const SCENE_ENUM = {
  0: 'deliveryDetail',
  1: 'order_pay_success_by_self_fetch',
  2: 'order_pay_success_by_local_delivery',
};
const YOUZAN_OFFLINE_ORDER_SCENE = 'crm_order_evaluation_msg';
/**  crm店铺升级状态 */
const CRM_STATUS_ENUM = {
  /* scrm项目迁移未开始 */
  Unavailable: 0,
  /** scrm项目迁移中 */
  Upgrading: 1,
  /** scrm项目迁移完成 */
  Available: 2,
};

export default {
  components: {
    'van-overlay': Overlay,
  },

  data() {
    return {
      showTips: false,
      hasSubscribe: false,
      templateIds: [],
      scene: 'afterPaySuccess',
      themeCSS: '',
      showSubscribeMsg: true,
    };
  },

  computed: {
    showWeappSubscribe() {
      return this.templateIds.length > 0 && Tee.$native.canIUse('requestSubscribeMessage');
    },
  },
  // showWxSubscribe更新到data
  watch: {
    showWeappSubscribe: {
      immediate: true,
      handler() {
        this.ctx.data.showWeappSubscribe = this.showWeappSubscribe;
      },
    },
  },

  created() {
    this.app = getApp();
    mapData(this, ['themeCSS']);
    mapData(this, {
      payResult: (payResult) => {
        const { shopMetaInfo = {} } = this.app.getShopInfoSync() || {};
        const isRetailShop = checkRetailShop(shopMetaInfo);
        this.isRetailShop = isRetailShop;
        if (isRetailShop) {
          const { expressType } = payResult;
          SCENE_ENUM[expressType] && (this.scene = SCENE_ENUM[expressType]);
        }

        Promise.all([getIsCrmShop(), queryOrderInfoByNo(payResult.orderNo)])
          .then(([{ status }, [orderInfo]]) => {
            const isSupportOfflineSubscription =
              status === CRM_STATUS_ENUM.Available &&
              ['retail_free_buy', 'retail_scan_buy'].includes(orderInfo.source_info?.order_mark);
            if (isSupportOfflineSubscription) {
              this.fetchTemplateIds([this.scene, YOUZAN_OFFLINE_ORDER_SCENE]);
            } else {
              this.fetchTemplateIds([this.scene]);
            }
          })
          .catch(() => {
            this.fetchTemplateIds([this.scene]);
          });
      },
    });
    mapEvent(this, {
      // 云定制 监听组件展示状态事件
      SubscribeNotice: (display) => {
        this.showSubscribeMsg = display;
      },
      handlerWeappSubscription: () => this.handlerWeappSubscription(),
    });
  },

  methods: {
    handlerWeappSubscription() {
      const { isRetailShop, scene } = this;
      const { templateIds = [] } = this;
      // 已经订阅不在重复执行
      if (this.hasSubscribe) {
        commonToast({ title: '消息已订阅' });
        return;
      }
      this.ctx.process.invokePipe('requestSubscribeMessagePush', {
        noToast: true,
        templates: templateIds,
        onFail: (err) => {
          let errorMsg = '订阅微信通知失败';
          if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
            errorMsg = '请在小程序设置中允许订阅消息';
          }
          this.app.logger.requestError({
            name: 'wxSubscribeError',
            message: errorMsg,
            alert: 'info',
            detail: err,
          });
        },
        onSuccess: (res) => {
          if (JSON.stringify(res).indexOf('accept') > -1) {
            this.hasSubscribe = true;
            this.ctx.logger?.log({
              et: 'click', // 事件类型
              ei: 'allow_click', // 事件标识
              en: '点击允许', // 事件名称
              params: {
                component: 'subscribe_order',
              }, // 事件参数
            });
            commonToast({ title: '订阅成功' });
          } else if (JSON.stringify(res).indexOf('reject') > -1) {
            commonToast({ title: '请在小程序设置中允许订阅消息' });
            this.app.logger.requestError({
              name: 'wxSubscribeError',
              message: '请在小程序设置中允许订阅消息',
              alert: 'info',
              detail: res,
            });

            this.ctx.logger?.log({
              et: 'click', // 事件类型
              ei: 'cancle_click', // 事件标识
              en: '点击取消', // 事件名称
              params: {
                component: 'subscribe_order',
              }, // 事件参数
            });
          }
          if (isRetailShop) {
            const accept = JSON.stringify(res).indexOf('accept') > -1; // 这是我照着其他地方抄的，如果不对请自行调整，因为原先这个accept压根就没有定义eslint会报错
            this.ctx.logger?.log({
              et: 'click',
              ei: accept ? 'accept_msg_subscribe' : 'reject_msg_subscribe',
              en: accept ? '接受授权订阅小程序消息' : '拒绝授权订阅小程序消息',
              params: {
                subscribe_pos: '支付完成页', // 订阅点 从哪个位置发起的订阅
                // subscribe_source: subscribeSource, // 来源 是微信小程序还是24h货架
                delivery_way: scene, // 配送方式  到店自提、同城配送、快递  若没有则不传
              },
            });
          }
        },
        onShowTips: () => {
          this.showTips = true;
        },
        onCloseTips: () => {
          this.showTips = false;
        },
      });

      this.ctx.logger?.log({
        et: 'click',
        ei: 'click_wx_subscription',
        en: '点击订阅消息',
        si: this.app.getKdtId(),
      });
    },

    closeTips() {
      this.showTips = false;
    },

    fetchTemplateIds(sceneList) {
      const promiseList = [];
      sceneList.forEach((scene) => {
        promiseList.push(requestTemplateIds({ scene }));
      });
      Promise.all(promiseList).then((res) => {
        res.forEach((resItem = {}) => {
          if (!this.templateIds.length) {
            this.templateIds = resItem.templateIdList || [];
          } else {
            this.templateIds = [].concat(this.templateIds, resItem.templateIdList || []);
          }
        });
      });
    },
  },
};
</script>

<style lang="scss">
// 微信订阅弹窗样式
.wx-subscription {
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  margin: 12px;
  border-radius: var(--theme-radius-card, 12px);

  &__image {
    width: 52px;
    height: 40px;
    margin: 8px;
  }

  &__text {
    flex: 1;
    margin-left: 8px;
    text-align: left;

    .main {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      line-height: 20px;
    }

    .des {
      font-size: 12px;
      color: #7d7e80;
      font-weight: 400;
      line-height: 16px;
      margin-top: 4px;
    }
  }

  &__btn {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin-right: 8px;
  }
}

.overlay-wrapper {
  position: fixed;
  width: 200px;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;

  &--img {
    width: 200px;
    height: 146px;
    border-radius: 4px;
  }
}
</style>
