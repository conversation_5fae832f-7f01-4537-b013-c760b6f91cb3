import type { OrderStatusData } from '@youzan-cloud/cloud-biz-types';

export const cloudData = {
  getOrderStatus: ({
    orderState,
    orderStateTitle,
    autoReceiveOrderTime = 0,
    progressBar = [],
    closeType,
    closeTypeStr,
  }): OrderStatusData => {
    return {
      orderState,
      orderStateTitle,
      autoReceiveOrderTime,
      // @ts-ignore
      progressBar: progressBar.map((item) => ({ isArrive: item.arrive, text: item.type })),
      closeType,
      // @ts-ignore ts定义问题，等标准同步后可以删除这个 ts-ignore 最迟 2024-03-30 即可安全删除
      closeTypeText: closeType > 0 ? closeTypeStr : '',
    };
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
