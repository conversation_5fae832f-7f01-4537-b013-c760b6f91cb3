import { PROMOTION_TYPES } from '../../constants';

const customAttachMapper = {
  [PROMOTION_TYPES.Tradeincard]: {
    mark: 'click_marketing_paidpro_coupon',
    name: '支付有礼送券/码点击',
  },
  [PROMOTION_TYPES.Promocode]: {
    mark: 'click_marketing_paidpro_coupon',
    name: '支付有礼送券/码点击',
  },
  [PROMOTION_TYPES.Couponpackage]: {
    mark: 'click_marketing_paidpro_coupon',
    name: '支付有礼送券/码点击',
  },
  [PROMOTION_TYPES.Present]: {
    mark: 'click_marketing_paidpro_present',
    name: '支付有礼送赠品点击',
  },
  [PROMOTION_TYPES.Wheel]: {
    mark: 'click_marketing_paidpro_games',
    name: '支付有礼玩游戏点击',
    params: {
      game_type: '幸运大抽奖',
    },
  },
  [PROMOTION_TYPES.Guaguale]: {
    mark: 'click_marketing_paidpro_games',
    name: '支付有礼玩游戏点击',
    params: {
      game_type: '刮刮乐',
    },
  },
  [PROMOTION_TYPES.Crazyguess]: {
    mark: 'click_marketing_paidpro_games',
    name: '支付有礼玩游戏点击',
    params: {
      game_type: '疯狂猜',
    },
  },
  [PROMOTION_TYPES.Zodiac]: {
    mark: 'click_marketing_paidpro_games',
    name: '支付有礼玩游戏点击',
    params: {
      game_type: '生肖翻翻看',
    },
  },
  [PROMOTION_TYPES.Survey]: {
    mark: 'click_marketing_paidpro_vote',
    name: '支付有礼投票调查点击',
  },
  [PROMOTION_TYPES.Seller]: {
    mark: 'click_marketing_paidpro_salesman',
    name: '支付有礼成为销售员点击',
  },
  [PROMOTION_TYPES.Seniorseller]: {
    mark: 'click_marketing_paidpro_salesman',
    name: '支付有礼成为销售员点击',
  },
  [PROMOTION_TYPES.Feature]: {
    mark: 'click_marketing_paidpro_deco',
    name: '支付有礼浏览微页面点击',
  },
  [PROMOTION_TYPES.ActivitiesQrCode]: {
    mark: 'click_marketing_paidpro_liveqrcode',
    name: '支付有礼自定义图片点击活码',
  },
};

const viewMapper = {
  [PROMOTION_TYPES.Present]: {
    mark: 'view_marketing_paidpro_present',
    name: '支付有礼送赠品曝光',
  },
  [PROMOTION_TYPES.Wheel]: {
    mark: 'view_marketing_paidpro_games',
    name: '支付有礼玩游戏曝光',
    params: {
      game_type: '幸运大抽奖',
    },
  },
  [PROMOTION_TYPES.Zodiac]: {
    mark: 'view_marketing_paidpro_games',
    name: '支付有礼玩游戏曝光',
    params: {
      game_type: '生肖翻翻看',
    },
  },
  [PROMOTION_TYPES.Guaguale]: {
    mark: 'view_marketing_paidpro_games',
    name: '支付有礼玩游戏曝光',
    params: {
      game_type: '刮刮乐',
    },
  },
  [PROMOTION_TYPES.Crazyguess]: {
    mark: 'view_marketing_paidpro_games',
    name: '支付有礼玩游戏曝光',
    params: {
      game_type: '疯狂猜',
    },
  },
  [PROMOTION_TYPES.Survey]: {
    mark: 'view_marketing_paidpro_vote',
    name: '支付有礼投票调查曝光',
  },
  [PROMOTION_TYPES.Seller]: {
    mark: 'view_marketing_paidpro_salesman',
    name: '支付有礼成为销售员曝光',
  },
  [PROMOTION_TYPES.Seniorseller]: {
    mark: 'view_marketing_paidpro_salesman',
    name: '支付有礼成为销售员曝光',
  },
  [PROMOTION_TYPES.Feature]: {
    mark: 'view_marketing_paidpro_deco',
    name: '支付有礼浏览微页面曝光',
  },
  [PROMOTION_TYPES.ActivitiesQrCode]: {
    mark: 'view_marketing_paidpro_liveqrcode',
    name: '支付有礼推广活码曝光',
  },
};

// 自定义点击仅需区分插件类型即可
export const promotionAttachLog = ({ type, isCustom, track = 'click', logFn }) => {
  const curItem = track === 'click' ? customAttachMapper[type] : viewMapper[type];
  if (!curItem) return console.warn('埋点异常', type, track);
  logFn({
    et: track, // 事件类型
    ei: curItem.mark, // 事件标识
    en: curItem.name, // 事件名称
    params: {
      is_custom: isCustom ? 1 : 0,
      ...(curItem.params ?? {}),
    },
  });
};
