<template>
  <view v-if="isShowRetailOrderList" class="page-retail-header">
    <view>
      <van-search
        :value="query.keyword || ''"
        :show-action="query.keyword"
        action-text="清空"
        :readonly="true"
        content-class="search-input"
        @click-input="goOrderSearch"
        @cancel="onClear"
        placeholder="搜索订单信息"
        use-left-icon-slot
      >
        <view slot="left-icon" class="icon"></view>
      </van-search>
    </view>
    <van-tabs
        color="#323233"
        line-width="67"
        :swipe-threshold="2"
        :active="activeTabIndex"
        :pid="pid"
        :border="false"
        type="line"
        style="border-bottom: 1px solid #EBEDF0"
        @click="onTabClick"
    >
      <van-tab
          v-for="item in tabs"
          :key="item.type"
          title-style="font-size: 16px"
          :title="item.text"
          :pid="pid"
      />
    </van-tabs>
  </view>

  <wsc-order-list-tabs v-else />
</template>
<script>
// Utils
import { mapState } from '@ranta/store';

// Components
import Search from '@youzan/vant-tee/dist/search/index.vue';
import Tab from '@youzan/vant-tee/dist/tab/index.vue';
import Tabs from '@youzan/vant-tee/dist/tabs/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

import { DEFAULT_TAB_LIST } from '../index';

export default {
  components: {
    'van-search': Search,
    'van-tab': Tab,
    'van-tabs': Tabs,
    'van-icon': Icon,
  },

  data() {
    return {
      tabs: [...DEFAULT_TAB_LIST],
      pid: `order-list-tab${Math.floor(Math.random() * 10000)}`,
      ...mapState(this, [
        'isDrugShop',
        'isRetailShop',
        'pageType',
        'activityTab',
        'yunDesignConfig',
        'query',
        'isReTmp',
      ]),
    };
  },

  mounted() {
    /* #ifdef weapp */
    // 下拉刷新背景色和页面背景色保持一致
    wx.setBackgroundColor({
      backgroundColor: '#f7f8fa',
    });
    /* #endif */
  },

  computed: {
    activeTabIndex() {
      const defaultTypeIndex = this.tabs.findIndex((tab) => tab.type === this.activityTab.type);
      return defaultTypeIndex !== -1 ? defaultTypeIndex : 0;
    },
    isShowRetailOrderList() {
      const { isRetailShop, isDrugShop, isReTmp } = this;
      const isShowRetailOrderList = isRetailShop && !isDrugShop && isReTmp;
      this.ctx.data.isShowRetailOrderList = isShowRetailOrderList;
      return isShowRetailOrderList;
    }
  },

  methods: {
    onClear() {
      // 刷新列表会默认将page加1 现在设置为0 默认加载第一页
      this.ctx.process.invoke('setActivityTab', { type: this.activityTab.type, page: 0, pageId: 1 });
      this.ctx.process.invoke('setQuery', { ...this.query, keyword: '' });
      this.ctx.process.invoke('refreshOrderList');
    },
    goOrderSearch() {
      this.store.gotoOrderSearch();
    },
    onTabClick({ index }) {
      this.store.activityTabChange(this.tabs[index]);
    },
  },
};
</script>
<style lang="scss">
.page-retail-header {
  //position: fixed;
  display: flex;
  left: 0;
  right: 0;
  top: var(--nav-height);
  background: #fff;
  z-index: 10;
  flex-direction: column;
}

.search-input {
  padding: 7px 0;
  border-radius: 8px !important;
}
.icon {
  width: 25px;
  height: 22px;
  margin-left: 8px;
  background: url(https://b.yzcdn.cn/vant/search-o1.png) left center no-repeat;
  background-size: 15px;
}

// TODO: 下面这些原先是h5的，后续要重新考虑兼容性
//.list-tabs {
//  box-shadow: 0 2px 12px 0 rgba(100, 101, 102, 0.12);
//}
//.retail-tab-wrapper {
//  box-shadow: 0 2px 12px 0 rgba(100, 101, 102, 0.12);
//  display: flex;
//  flex-direction: row;
//  align-items: center;
//  justify-content: flex-start;
//  width: 100%;
//  background-color: #fff;
//  .search-btn {
//    padding-left: 90px;
//    padding-right: 20px;
//  }
//}
</style>
