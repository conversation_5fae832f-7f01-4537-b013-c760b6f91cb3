import loadScript from '@youzan/utils/browser/loadScript';
import { getStorageSync } from '@youzan/tee-api';
import {LOCAL_DELIVERY_MAP, RIDER_DDIRECTION, SHOP_THUMB_URL, BUYER_THUMB_URL} from '../../constant';

function RIDER_MARKER_TPL({ statusText, distanceMode, distanceText, direct, estimateArriveTimeTip }) {
  const riderDirect =
    RIDER_DDIRECTION.TO_EAST === direct ? 'rider-marker__rider--east' : 'rider-marker__rider--west';
  return `<div class="rider-marker">
      <div class="rider-content">
        ${statusText}
        <div class="rider-distance">
          ${distanceMode}
          <span>${distanceText}</span>
        </div>
        <div>
          ${estimateArriveTimeTip}
        </div>
      </div>
      <div class="rider-marker__angle"></div>
      <div class="rider-marker__rider ${riderDirect}"></div>
    </div>`;
}

function BUYER_MARKER_TPL(thumbUrl, roleName = '商家', showHover) {
  return `<div class="buyer-marker">
      <div class="buyer-thumb"><img src="${thumbUrl}" /></div>
      ${showHover ? '<div class="buyer-marker__popup">' + roleName + '</div>' : ''}
    </div>`;
}
export default {
  data() {
    this.mapContext = null;
    this.riderMarker = null; // 骑手
    this.shopMarker = null; // 商家
    this.meMarker = null; // 买家
    this.mapComplete = false; // 地图已绘制好，从20200304开始发现高德地图在scriptonload后直接newMap会失败
    this.loadMainScriptPromise = null;
    this.AMap = null;
    return {};
  },

  methods: {
    loadMainScript() {
      const mapConfig = {
        security: '6c3bcdda746e99e0ca83f2e1bcdcfb6f',
        key: 'f439f3e455f43c05b7ec10b9f657d36e',
        ...getStorageSync('_ADDRESS_MAP_KEY_')?.amap,
      };
      // eslint-disable-next-line @youzan-open/tee/no-platform-field
      window._AMapSecurityConfig = {
        // eslint-disable-next-line @youzan-open/tee/no-platform-field
        ...(window._AMapSecurityConfig || {}),
        securityJsCode: mapConfig.security,
      };

      if (!this.loadMainScriptPromise) {
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        const SCRIPT_URL = `https://webapi.amap.com/maps?v=1.4.15&key=${mapConfig.key}`;
        this.loadMainScriptPromise = loadScript(SCRIPT_URL).then(() => {
          this.AMap = window.AMap;
          return this.AMap;
        });
      }

      return this.loadMainScriptPromise;
    },
    // 初始化高德地图
    initMap(renew = false) {
      if (this.mapContext && !renew) {
        Promise.resolve();
      }
      const position = this.position;
      if (position[0] <= 0 || position[1] <= 0) {
        return;
      }

      const MAP_COMPLETE_TIMEOUT = 3500; // 一般从new Map到onComplete需1200毫左右

      return new Promise((resolve) => {
        // 经尝试timeout 300左右开始出现一定概率失败
        setTimeout(() => {
          if (this.mapComplete) return resolve();
          this.loadMainScript().then(() => {
            this.mapContext = new this.AMap.Map(this.$refs.amap, {
              zoom: 11, //  级别
              resizeEnable: true,
              zoomEnable: true,
              doubleClickZoom: true,
              touchZoom: true,
              dragEnable: true,
              scrollWheel: true,
              center: position, //  中心点坐标
            });
            this.mapContext.on('complete', (e) => {
              this.mapComplete = true; // 标记绘制好了
              resolve();
            });

            setTimeout(() => {
              // 地图绘制超时，启动重试
              if (!this.mapComplete) {
                resolve(this.initMap(true));
              }
            }, MAP_COMPLETE_TIMEOUT);
          });
        }, 500);
      });
    },

    // 处理地图漂浮物
    initMapExtra() {
      this.addDeliveryPath(this.cityDetail);
      if (this.cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_TAKING) {
        // 待取货
        this.setShopMarker(this.cityDetail);
      } else if (this.cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_SENDING) {
        // 配送中
        this.setBuyerMarker(this.cityDetail);
      }
    },

    setRider(position, riderContent) {
      const { AMap } = this;
      if (AMap && this.mapContext) {
        if (!this.riderMarker) {
          this.riderMarker = new AMap.Marker({
            position: new AMap.LngLat(position[0], position[1]), // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
            offset: new AMap.Pixel(-40, -110),
            content: RIDER_MARKER_TPL(riderContent),
          });
          this.mapContext.add(this.riderMarker);
        } else {
          this.riderMarker.setContent(RIDER_MARKER_TPL(riderContent));
          this.riderMarker.setPosition(new AMap.LngLat(position[0], position[1]));
        }
        this.mapContext.setCenter(new AMap.LngLat(position[0], position[1]));
      }
    },

    addDeliveryPath(cityDetail) {
      const { transporterLat, transporterLng, buyerLat, buyerLng, shopLat, shopLng } =
        cityDetail || {};
      const { AMap } = this;
      if (transporterLat && transporterLng) {
        const lngLat = [transporterLng, transporterLat];
        const riderContent = {
          statusText: '请稍等',
          distanceMode: '',
          distanceText: '',
          direct: 'east',
        };
        if (cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_TAKING) {
          riderContent.statusText = '骑手已接单';
          riderContent.distanceMode = '距商家';
          if (shopLat && shopLng) {
            riderContent.direct = transporterLng > shopLng ? RIDER_DDIRECTION.TO_WEST : RIDER_DDIRECTION.TO_EAST;
            this.loadMainScript().then(() => {
              const distance =
                parseInt(AMap.GeometryUtil.distance(lngLat, [shopLng, shopLat]) || 0) / 1000;
              riderContent.distanceText = distance.toFixed(1) + 'km';
              riderContent.estimateArriveTimeTip = this.localDelivery.estimateArriveTimeTip || '';
              const zoom = this.getCalculateZoom(Number(distance.toFixed(1)));
              this.mapContext.setZoom(zoom);
              this.setRider(lngLat, riderContent);
            });
          }
        } else if (cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_SENDING) {
          riderContent.statusText = '配送中';
          riderContent.distanceMode = '距你';
          if (buyerLat && buyerLng) {
            riderContent.direct = transporterLng > buyerLng ? RIDER_DDIRECTION.TO_WEST : RIDER_DDIRECTION.TO_EAST;
            this.loadMainScript().then(() => {
              const distance =
                parseInt(AMap.GeometryUtil.distance(lngLat, [buyerLng, buyerLat]) || 0) / 1000;
              riderContent.distanceText = distance.toFixed(1) + 'km';
              riderContent.estimateArriveTimeTip = this.localDelivery.estimateArriveTimeTip || '';
              const zoom = this.getCalculateZoom(Number(distance.toFixed(1)));
              this.mapContext.setZoom(zoom);
              this.setRider(lngLat, riderContent);
            });
          }
        }
      }
    },

    getCalculateZoom(distance) {
      let zoom = 11;
      if (distance <= 3) {
        zoom = 13;
      } else if (distance <= 6) {
        zoom = 12;
      }
      return zoom;
    },

    setBuyerMarker(cityDetail) {
      const { buyerLat, buyerLng } = cityDetail || {};
      const { AMap } = this;
      if (AMap && this.mapContext) {
        if (buyerLat && buyerLng) {
          if (!this.meMarker) {
            this.meMarker = new AMap.Marker({
              position: new AMap.LngLat(buyerLng, buyerLat),
              offset: new AMap.Pixel(-12, -40),
              content: BUYER_MARKER_TPL(BUYER_THUMB_URL),
              map: this.mapContext,
            });
          } else {
            this.meMarker.setPosition(new AMap.LngLat(buyerLng, buyerLat));
          }
        }
      }
    },

    setShopMarker(cityDetail) {
      const { shopLat, shopLng } = cityDetail || {};
      const { AMap } = this;
      if (AMap && this.mapContext) {
        if (shopLat && shopLng) {
          if (!this.shopMarker) {
            this.shopMarker = new AMap.Marker({
              position: new AMap.LngLat(shopLng, shopLat),
              offset: new AMap.Pixel(-12, -40),
              content: BUYER_MARKER_TPL(SHOP_THUMB_URL),
              map: this.mapContext,
            });
            this.shopMarker.on('click', (e) => {
              this.shopMarker.setContent(BUYER_MARKER_TPL(SHOP_THUMB_URL, '我是买家', false));
            });
          } else {
            this.shopMarker.setPosition(new AMap.LngLat(shopLng, shopLat));
          }
        }
      }
    },
  },
};
