<template>
  <coupon-card
    v-if="isCoupon"
    @log-attach="couponLogAttach"
    @pure-log="pureLog"
    :order-no="orderNo"
    :kdt-id="kdtId"
    :info="info"
    :theme-color="themeColor"
  />
  <coupon-pack-card
    v-else
    :order-no="orderNo"
    @log-attach="couponLogAttach"
    :info="info"
    :theme-color="themeColor"
    :kdt-id="kdtId"
  />
</template>

<script>
import { PROMOTION_TYPES } from '../../../constants';
import Coupon from './Coupon';
import CouponPack from './CouponPack';

export default {
  name: 'award-coupon',
  components: {
    'coupon-pack-card': CouponPack,
    'coupon-card': Coupon,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    orderNo: String,
    themeColor: String,
  },
  computed: {
    isCoupon() {
      return this.info.type === PROMOTION_TYPES.Tradeincard;
    },
  },
  created() {
    // 后续如果有聚合展示就需要在埋点这里做出区分
    this.$emit('log-attach', {
      et: 'view',
      ei: 'view_marketing_paidpro_coupon',
      en: '支付有礼送券/码曝光',
      params: {
        is_custom: 0,
        activity_id: this.isCoupon ? this.info.coupon?.couponId : null,
        kdt_id: this.kdtId,
        activity_type: 'paidUmpAward',
      },
    });
  },
  methods: {
    pureLog(payload) {
      this.$emit('log-attach', payload);
    },
    couponLogAttach(isReceive) {
      this.$emit('log-attach', {
        et: 'click',
        ei: 'click_marketing_paidpro_coupon',
        en: '支付有礼送券/码点击',
        pt: 'receipt',
        params: {
          is_custom: 0,
          click_type: isReceive ? '立即领取' : '立即使用',
          activity_id: this.isCoupon ? this.info.coupon?.couponId : null,
          kdt_id: this.kdtId,
          activity_type: 'paidUmpAward',
        },
      });
    },
  },
};
</script>
