<template>
  <view v-if="isShowSearchBarUI" class="search-bar">
    <view class="search-bar__search" @click="onClick">
      <view class="search-bar__search-keyword" v-if="searchCondition.value">
        <van-icon name="search" />
        <text class="text">{{ searchCondition.value }}</text>
      </view>
      <view class="search-bar__search-placeholder" v-else>
        <van-icon name="search" />
        <text class="text">搜索订单</text>
      </view>
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      ...mapState(this, ['searchCondition', 'isDrugShop', 'searchBarOpt']),
    };
  },

  computed: {
    isShowSearchBarUI() {
      return this.searchBarOpt.isShow && !this.isDrugShop;
    },
  },

  methods: {
    onClick() {
      this.store.gotoOrderSearch();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-bar {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  padding: 8px 16px;

  &__search {
    background: #f7f8fa;
    border-radius: 999px;
    flex: 1;
    padding: 5px 12px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #c8c9cc;

    &-keyword {
      display: flex;
      align-items: center;
      height: 24px;
      line-height: 24px;

      .text {
        color: #323233;
        margin-left: 5px;
      }
    }
    &-placeholder {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      line-height: 24px;

      .text {
        margin-left: 5px;
      }
    }
  }
}
</style>
