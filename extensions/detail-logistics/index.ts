import Logistics from './Logistics.vue';
import { url } from '@youzan/tee-util';
import navigate from '@youzan/tee-biz-navigate';
import { buildUrl } from '@youzan/tee-biz-util';
import { getStorageSync } from '@youzan/tee-api';
import { bridge } from '@youzan/ranta-helper';
import { mapProcess } from '@youzan/ranta-helper-tee';

/* #ifdef web */
const bizEnv = url.args.get('bizEnv', window?.location?.href);
const isXhsLocalLife = url.args.get('isXhsLocalLife', window?.location?.href);
/* #endif */

export default class LogisticsExtension {
  ctx: any;

  static widgets = {
    Logistics,
  };

  /**
   * viewExpress
   * @deprecated 从 2.0 开始
   * @desc 查看物流
   */
  @bridge('viewExpress', 'process')
  viewExpress() {
    this.gotoExpressPage();
  }

  constructor(options) {
    this.ctx = options.ctx;

    mapProcess(this, {
      gotoExpressPage: this.gotoExpressPage.bind(this),
    });
  }

  gotoExpressPage() {
    const { orderNo, kdtId } = this.ctx.data;

    let path = '';
    /* #ifdef web */
    // 催付订单弹窗是否在其他页面已经展示过
    const orderPayPromptPopup = getStorageSync('order-pay-prompt-popup') || null;
    path = buildUrl(`/wsctrade/express/detail?order_no=${orderNo}&kdtId=${kdtId}`, 'h5', kdtId);
    if (orderPayPromptPopup) {
      path = `${path}&isOrderPayPromptPopup=1`;
    }
    if (bizEnv) {
      path = url.args.add(path, { bizEnv });
    }
    if (isXhsLocalLife) {
      path = url.args.add(path, { isXhsLocalLife });
    }
    /* #endif */

    /* #ifdef weapp */
    path = url.args.add('/packages/trade/order/express/index', { orderNo, kdtId });
    /* #endif */

    navigate({
      url: path,
      type: 'navigateTo',
    });
  }
}
