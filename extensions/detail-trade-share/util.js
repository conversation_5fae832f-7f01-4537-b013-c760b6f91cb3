import Tee from '@youzan/tee';
import { url, object } from '@youzan/tee-util';

/**
 * @param {String} scope
 * @returns {Promise} authorize
 */
export default (right) => {
  return new Promise((resolve, reject) => {
    Tee.$native.getSetting({
      success: (res) => {
        if (!res.authSetting[right]) {
          Tee.$native.authorize({
            scope: right,
            success: resolve,
            fail: reject,
          });
        } else {
          resolve();
        }
      },
      fail: reject,
    });
  });
};

// 在 URL 上添加统计参数
export function addAnalyticsParams(ctx, sourceUrl) {
  const loggerData = ctx.logger.getLogParams();
  const context = (loggerData && object.get(loggerData, 'context')) || {};
  return sourceUrl
    ? url.args.add(sourceUrl, {
        dc_ps: context.dc_ps || '',
        from_source: context.from_source || '',
        from_params: context.from_params || '',
      })
    : '';
}
