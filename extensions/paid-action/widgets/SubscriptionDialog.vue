<template>
  <van-popup
    :show="visible"
    :custom-style="customPopupStyle"
    position="bottom"
    close-on-click-overlay
    @click-overlay="onClose"
  >
    <view class="subscription-dialog__title">订阅物流</view>

    <!-- #ifdef web -->
    <view :class="[isSubscriptionShow ? 'subscription-dialog__take' : '']">
      <paid-subscription-msg
        text="订阅物流"
        :bg-color="'#F7F8FA'"
        :plain-btn="false"
        @psmshow="psmshow"
      />
    </view>
    <!-- #endif -->

    <view class="subscription-dialog__content">
      <!-- 一般是文字描述 -->
      <block v-if="subscriptionModel[0] && subscriptionModel[0].length">
        <view
          v-for="(item, index) in subscriptionModel[0]"
          :key="index"
          :class="[isSubscriptionShow ? 'subscription-dialog__im-des' : 'subscription-dialog__des']"
        >
          {{ item }}
        </view>
      </block>
      <!-- 一般是图片展示 -->
      <block v-if="subscriptionModel[1] && subscriptionModel[1].length">
        <image
          v-for="item in subscriptionModel[1]"
          :key="item"
          :src="item"
          @error="onImgError"
          class="subscription-dialog__img"
        />
      </block>
      <!-- 一般是文字描述 -->
      <block v-if="subscriptionModel[2] && subscriptionModel[2].length">
        <view
          v-for="(item, index) in subscriptionModel[2]"
          :key="index"
          class="subscription-dialog__des"
        >
          {{ item }}
        </view>
      </block>
    </view>
    <view class="subscription-dialog__buttons">
      <van-button
        custom-class="subscription-dialog__btn"
        :custom-style="btnCustomStyle"
        block
        @click="onManualClose"
      >
        我知道了
      </van-button>
      <!-- #ifdef web -->
      <!-- 自动打开弹窗时展示 -->
      <view v-if="isAutoOpen">
        <van-checkbox
          v-model="noMoreTips"
          custom-class="subscription-dialog__checkbox"
          icon-size="16px"
          checked-color="var(--main-bg, #323233)"
          icon-class="subscription-dialog__checkbox-icon"
          label-class="subscription-dialog__checkbox-label"
        >
          {{ isSubscriptionShow ? '不再提示' : '不再提示订阅物流消息' }}
        </van-checkbox>
      </view>
      <!-- #endif -->
    </view>
  </van-popup>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Button from '@youzan/vant-tee/dist/button/index';
import Checkbox from '@youzan/vant-tee/dist/checkbox/index';
import { setStorage } from '@youzan/tee-api';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { buildSubscriptionModel, YOUZAN_MARS_MODEL } from '../utils/subscription';

export default {
  components: {
    'van-popup': Popup,
    'van-button': Button,
    'van-checkbox': Checkbox,
  },

  data() {
    return {
      kdtId: 0,
      visible: false,
      themeCSS: '',
      themeColors: '',
      mpData: {},
      isAutoOpen: false,
      subscriptionInfo: {},
      subscriptionModel: [],
      noMoreTips: false, // 默认非选
      isSubscriptionShow: false,
      btnCustomStyle:
        'color: var(--main-text, #fff); background: var(--main-bg, #323233); border: 1px solid var(--main-bg, #323233);border-radius: var(--theme-radius-button, 999px);',
    };
  },

  computed: {
    customPopupStyle() {
      return `border-top-left-radius: var(--theme-radius-dialog, 20px); border-top-right-radius: var(--theme-radius-dialog, 20px); text-align: center;${this.themeCSS}`;
    },
  },
  watch: {
    visible(visible) {
      if (visible) {
        this.ctx.logger.log({
          et: 'view',
          ei: 'show_fuchuang_dingyuewuliu',
          en: '“订阅物流”浮窗曝光',
          si: this.kdtId,
        });
      }
    },
  },

  created() {
    mapData(this, ['kdtId', 'themeCSS', 'themeColors']);
    mapData(
      this,
      {
        payResult: (val) => {
          this.mpData = val.mpData || {};
        },
      },
      { isSetData: false }
    );

    mapEvent(this, {
      showSubscriptionDialog: (isAutoOpen = false) => {
        this.isAutoOpen = isAutoOpen;
        // 如果是第二次打开
        if (Object.keys(this.subscriptionInfo).length) {
          this.subscriptionModel = this.handleImSubscriptionPop(this.subscriptionInfo);

          this.visible = true;
        } else {
          buildSubscriptionModel({ mpData: this.mpData, kdtId: this.kdtId }).then((res) => {
            // 多次重试失败一般会返回空
            if (res) {
              this.subscriptionInfo = res;
              this.subscriptionModel = this.handleImSubscriptionPop(res);

              this.visible = true;
            } else {
              Toast('您可收藏订单详情,跟踪订单物流');
            }
          });
        }
      },
      hideSubscriptionDialog: () => {
        this.visible = false;
        // 当为自动打开时，关闭后执行下一个自定打开事件
        this.ctx.event.emit('closeAutoOpenPopup', {
          isRealClose: true,
        });
      },
    });
  },

  methods: {
    psmshow(val) {
      this.isSubscriptionShow = val || false;
      // 订阅物流规则改变后重新刷下内容展示
      if (this.visible) {
        this.subscriptionModel = this.handleImSubscriptionPop(this.subscriptionInfo);
      }
    },

    handleImSubscriptionPop(subscriptionInfo) {
      const content = subscriptionInfo.content || [];
      if (this.isSubscriptionShow && subscriptionInfo.type === 'focusMpAccount') {
        return [[], content[1], ['长按识别二维码关注店铺公众号，查看订单物流']];
      }
      return content;
    },

    onImgError() {
      if (this.subscriptionInfo?.type === 'focusMpAccount') {
        // 降级到精选APP引导
        this.subscriptionInfo.content = YOUZAN_MARS_MODEL.content;
        this.subscriptionModel = YOUZAN_MARS_MODEL.content;
      }
    },

    onClose() {
      this.ctx.event.emit('hideSubscriptionDialog');
    },

    // 手动关闭
    onManualClose() {
      /* #ifdef web */
      if (this.isAutoOpen) {
        if (this.noMoreTips) setStorage(`pay_success_express_dialog_click_${this.kdtId}`, 1);
      }
      /* #endif */

      /* #ifdef weapp */
      setStorage(`pay_success_express_dialog_click_${this.kdtId}`, 1);
      /* #endif */
      this.ctx.event.emit('hideSubscriptionDialog');
    },
  },
};
</script>

<style lang="scss">
.subscription-dialog {
  &__title {
    padding: 11px 0;
    font-weight: 500;
    color: #323233;
    font-size: 16px;
    margin-bottom: 16px;
  }

  &__img {
    display: block;
    margin: 0 auto;
    min-width: 178px;
    min-height: 178px;
    /* #ifdef web */
    height: auto; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    width: 45.3vw; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    /* #endif */
    /* #ifdef weapp */
    height: 300px; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    width: 159px; /* stylelint-disable-line declaration-block-no-duplicate-properties */
    /* #endif */
  }

  &__des {
    text-align: center;
    color: #333;
    font-size: 14px;
    margin: 16px 0;
    padding: 0 32px;
  }

  &__buttons {
    margin: 20px 24px 24px;
  }

  &__btn {
    margin-bottom: 20px;
    border-radius: var(--theme-radius-button, 999px);
  }
  /* #ifdef web */
  &__take {
    margin: 8px 0 24px;
  }
  /* #endif */
  &__im-des {
    color: #c8c9cc;
    font-size: 12px;
    width: 209px;
    margin: 12px auto 12px;
    line-height: 18px;
  }
  /* #ifdef web */
  &__checkbox {
    display: flex;
    justify-content: center;

    &-label {
      color: #969799;
      font-size: 14px;
    }

    &-icon {
      vertical-align: -2px;
    }
  }
  /* #endif */
}
</style>
