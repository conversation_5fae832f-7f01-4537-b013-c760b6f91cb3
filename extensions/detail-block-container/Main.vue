<template>
  <view v-if="isShow">
    <contents />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  props: {
    blockName: String,
  },

  data() {
    return {
      showOrderAddress: true,
      showOrderStatus: true,
    };
  },

  computed: {
    isShow() {
      const { blockName, showOrderAddress, showOrderStatus } = this;
      if (!['order-address', 'order-status'].includes(blockName)) return true;
      return (
        (blockName === 'order-address' && showOrderAddress) ||
        (blockName === 'order-status' && showOrderStatus)
      );
    },
  },

  created() {
    mapData(this, ['showOrderAddress', 'showOrderStatus']);
  },
};
</script>
