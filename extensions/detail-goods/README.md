# @wsc-tee-trade/detail-goods

商品列表

![UI呈现](https://img.yzcdn.cn/public_files/576ce99bf4104af1a5b6db678b7e9484.png) ![复购券](https://img.yzcdn.cn/public_files/aaafae3de23a7af87b854c1a9ea9384f.png)

## 调试方式

- 下单

## 存疑

- 普通商品的逻辑是否可以单独抽离成 Component
- orderResult.itemInfo.safeRefundButton.buttonType in [1,3,4] 是什么意思？
- 什么是老订单？isOldOrder
- REFUND_ACTION 的枚举值没有注释看不懂

## Component.Consume

| 名称         | 说明 |
| ------------ | ---- |
| TextCollapse | ???  |

## Widget.Provide

| 名称  | 说明 |
| ----- | ---- |
| Goods | ???  |

## Widget.Consume

| 名称       | 说明 |
| ---------- | ---- |
| CartWidget | ???  |

## Data.Consume

| 名称 | 类型 | 说明 |
| --- | --- | --- |
| themeColors |  | ??? |
| themeCSS |  | ??? |
| kdtId |  | ??? |
| orderNo |  | ??? |
| order |  | ??? |
| env |  | ??? |
| goods |  | ??? |
| activityType |  | ??? |
| isHotel | _boolean_ | 酒店订单标记 |
| addressInfo |  | ??? |
| itemList |  | ??? |
| isShowPostponeShip | _boolean_ | 是否显示周期购顺延 |
| pointsName |  | ??? |
| orderBizExtra |  | ??? |
| ump |  | ??? |
| marketingInfo |  | ??? |
| buyAgainCouponAvailable |  | ??? |
| canUseTradeUmpV1 | _boolean_ | 支付后营销优化一期切流 数据来源：/wscump/trade/use-trade-ump-v1.json |

## Data.Provide

| 名称          | 类型 | 说明 |
| ------------- | ---- | ---- |
| formatedGoods |      | ???  |

## Lambda.Consume

| 名称     | 说明 |
| -------- | ---- |
| hexToRgb | ???  |

## Event.Emit

| 名称              | 说明 |
| ----------------- | ---- |
| showPostponePopup | ???  |

## Event.Listen

| 名称        | 说明 |
| ----------- | ---- |
| loadSuccess | ???  |

## Process.Define

| 名称        | 说明 |
| ----------- | ---- |
| refundGoods | ???  |

## Process.invoke

| 名称                       | 说明 |
| -------------------------- | ---- |
| handleUrlWithShopAutoEnter | ???  |

## 关键字速查

部分代码可能由于历史问题，我们不能一眼看出意思，或者能看懂意思，但不明白历史背景，这里列出的是一些可能存在疑惑的关键字，可以速查

| 关键字 | 说明 |
| --- | --- |
| isOldOrder | 依赖后端返回，什么是老订单意思不明<br>api: /wsctrade/refund/getRefundState.json<br>zanapi: http://zanapi.qima-inc.com/site/service/view/241838 |
| canUseTradeUmpV1 | 支付后营销优化一期一个 apollo 切流<br>api: /wscump/trade/use-trade-ump-v1.json<br>wsc-h5-ump: trade.UmpController.useTradeUmpV1Json |
