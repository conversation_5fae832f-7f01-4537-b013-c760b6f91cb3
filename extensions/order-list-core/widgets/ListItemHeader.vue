<template>
  <view class="list-item-header__wrapper">
    <view v-if="listItem.isWholesaleOrder" class="list-item-wholesale-flag">批发订单</view>
    <view class="list-item-header">
      <view class="list-item-header__left" @click="goHome">
        <block v-if="listItem.shopName">
          <text class="list-item-header__icon">
            <image
              v-if="listItem.showBrandCert"
              class="list-item-header__icon-brand"
              :src="SHOP_ICON_MAP['brand']"
            />
          </text>

          <text class="list-item-header__shopname">
            {{ listItem.shopName }}
          </text>

          <!-- #ifdef web -->
          <van-icon v-if="listItem.homeUrl" name="arrow" class="list-item-header__link" />
          <!-- #endif -->
        </block>
      </view>

      <view class="list-item-header__state" style="color: #323233" @click="gotoDetail">
        {{ statueStr }}
      </view>
    </view>
    <text class="order-item__no"
      >订单编号：{{
        listItem.crmOfflineType === 'offline' ? listItem.outBizNo : listItem.orderNo
      }}</text
    >
  </view>
</template>

<script>
// Components
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

// Dependencies
import { mapState } from '@ranta/store';
import navigate from '@youzan/tee-biz-navigate';
import { cdnImage } from '@youzan/tee-biz-util';

const SHOP_ICON_MAP = {
  // 品牌
  brand: cdnImage('public_files/80c25e13ccef83035c6f16d31bc93c55.png'),
};

export default {
  name: 'list-item-header',

  components: {
    'van-icon': Icon,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {
      SHOP_ICON_MAP,
      ...mapState(this, ['isDrugShop', 'isXhsApp', 'isKsApp', 'isAlipayApp', 'isQQApp']),
    };
  },

  computed: {
    statueStr() {
      const { medicalRx, statueStr, orderStateStr } = this.listItem;

      return statueStr || (this.isDrugShop ? medicalRx.rxStatusDesc : orderStateStr);
    },
  },

  methods: {
    goHome() {
      /* #ifdef web */
      if (!this.listItem.shopName || !this.listItem.homeUrl) {
        return;
      }

      // 支付宝 QQ小程序 跳转到原生首页
      if (this.isAlipayApp || this.isQQApp || this.isXhsApp || this.isKsApp) {
        navigate({
          web: {
            type: 'znb',
            znb: {
              aliappUrl: `/pages/home/<USER>/index`,
              qqUrl: `/pages/home/<USER>/index`,
              xhsUrl: `/pages/home/<USER>/index`,
              ksUrl: `/pages/home/<USER>/index`,
              type: 'switchTab',
            },
          },
        });
      } else {
        const { kdtId } = this.listItem;
        navigate({
          web: {
            type: 'safeLink',
            safeLink: { url: this.listItem.homeUrl, kdtId },
          },
        });
      }
      /* #endif */
    },

    gotoDetail() {
      this.store.gotoOrderDetail({ listItem: this.listItem });
    },
  },
};
</script>

<style lang="scss" scoped>
.list-item-wholesale-flag {
  width: 56px;
  height: 16px;
  font-size: 12px;
  color: #323233;
  line-height: 16px;
  text-align: center;
  margin-bottom: -8px;
  border-radius: 8px 0 8px 0;
  background-color: #ebedf0;
}
.list-item-header__wrapper {
  display: flex;
  flex-direction: column;
}
.list-item-header {
  width: 100%;
  padding: 16px 12px 4px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__left {
    display: flex;
    align-items: center;
    width: 0;
    flex: 1;
  }

  &__icon {
    line-height: 0;
    margin-right: 4px;
    flex: none;

    img {
      vertical-align: middle;

      &:not(:last-child) {
        margin-right: 4px;
      }
    }
  }

  &__icon-company {
    height: 19px;
  }

  &__icon-brand {
    height: 16px;
  }

  &__shopname {
    color: #323233;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
  }

  &__link {
    color: #969799;
    margin-left: 4px;
    font-size: 14px;
  }

  &__state {
    font-family: PingFangSC-Medium, sans-serif;
    text-align: right;
    line-height: 18px;
    font-size: 14px;
    color: #969799;
  }
}
.order-item__no {
  padding: 0 15px 10px;
  font-size: 12px;
  color: #999;
}
</style>
