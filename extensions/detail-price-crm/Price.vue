<template>
  <van-cell-group custom-class="crm-order-price" :border="false">
    <!-- 支付信息 -->
    <van-cell v-if="showPaymentDetailInfo" custom-class="price-cell_margin">
      <view v-if="payment.payPrice" class="price-item">
        <text class="price-item__title">商品金额</text>
        <text class="price-item__value">{{ payPriceText }}</text>
      </view>
      <view v-if="payment.postage" class="price-item">
        <text class="price-item__title">{{ postageTitle }}</text>
        <text class="price-item__value">{{ postageText }}</text>
      </view>
      <view v-if="outerPromotion.voucherPromotionAmount" class="price-item">
        <text class="price-item__title">优惠卡券</text>
        <text class="price-item__value">- {{ voucherPromotionAmountText }}</text>
      </view>
      <view v-if="outerPromotion.otherPromotionAmount" class="price-item">
        <text class="price-item__title">其他优惠</text>
        <text class="price-item__value">- {{ otherPromotionAmountText }}</text>
      </view>
    </van-cell>
    <van-cell>
      <view class="crm-order-price__value">
        <text class="real-pay__title">实付款：</text>
        <text :style="{ color: themeColors }" class="real-pay__value">{{ realPayText }}</text>
      </view>
    </van-cell>
  </van-cell-group>
</template>

<script>
import {
  getPostageLabel,
  getPostageValue,
} from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import format from '@youzan/utils/money/format';

const formatPrice = (price = 0) => {
  return `¥${format(price, true, false)}`;
};

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },

  data() {
    return {
      payment: {},
      outerPromotion: {}, // 外部订单优惠信息
      themeColors: {},
    };
  },

  computed: {
    realPayText() {
      return formatPrice(this.payment.buyerRealPay);
    },
    payPriceText() {
      return formatPrice(this.payment.payPrice);
    },
    postageText() {
      const { payment } = this;
      return getPostageValue(payment.expressPayMode, payment.postage);
    },
    postageTitle() {
      return getPostageLabel(this.payment.expressPayMode);
    },
    voucherPromotionAmountText() {
      return formatPrice(this.outerPromotion.voucherPromotionAmount);
    },
    otherPromotionAmountText() {
      return formatPrice(this.outerPromotion.otherPromotionAmount);
    },
    showPaymentDetailInfo() {
      const { outerPromotion, payment } = this;
      return (
        // 优惠信息不准确则支付商品金额/运费/优惠信息都不展示，否则需要运费/优惠至少一个不为0
        outerPromotion.isPromotionCorrect &&
        (payment.postage ||
          outerPromotion.voucherPromotionAmount ||
          outerPromotion.otherPromotionAmount)
      );
    },
  },

  created() {
    mapData(this, ['payment', 'outerPromotion']);
    mapData(
      this,
      {
        themeColors: (value) => {
          this.themeColors = value.general || '#f44';
        },
      },
      { isSetData: false }
    );
  },
};
</script>

<style lang="scss">
.crm-order-price {
  margin-top: 10px;

  &__value {
    font-size: 0;
    height: 24px;
  }
}

.price-cell_margin {
  padding-top: 4px !important;
}

.price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}

.price-item__title {
  font-size: 14px;
  color: #969799;
}

.price-item__value {
  font-size: 14px;
  color: #323233;
}

.real-pay__title {
  color: #323233;
  font-size: 14px;
}

.real-pay__value {
  font-weight: bold;
  font-size: 14px;
  color: #f44;
}
</style>
