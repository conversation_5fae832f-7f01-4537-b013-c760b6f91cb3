<template>
  <view>
    <view class="res-header">
      <van-icon name="warning-o" class="res-header__icon" color="#f90" />
      <view class="res-header__title">确认资金到账情况中</view>
      <view class="res-header__desc">付款中，请稍后刷新</view>
      <van-button class="res-header__btn" size="small" :loading="loading" @click="refresh">
        刷新
      </van-button>
    </view>
    <van-cell-group>
      <van-cell title="你的支付信息" />
      <view class="body">
        <view class="row"> 付款金额：<text style="color: #3c0">确认中</text> </view>
        <view class="row"> 订单编号：{{ orderNo }} </view>
        <view v-if="payWayPrompt" class="row">支付方式：{{ payWayPrompt }} </view>
      </view>
    </van-cell-group>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';

export default {
  components: {
    'van-icon': Icon,
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-button': Button,
  },

  props: {
    orderNo: String,
    displayPrompt: Object,
    loading: Boolean,
  },

  computed: {
    payWayPrompt() {
      return this.displayPrompt?.payWayPrompt ?? '';
    },
  },

  methods: {
    refresh() {
      this.$emit('refresh');
    },
  },
};
</script>

<style lang="scss">
.body {
  font-size: 12px;
  padding: 10px 15px;
  background-color: #fff;
  color: #333;

  .row {
    line-height: 22px;
  }
}

.res-header {
  position: relative;
  padding: 15px 0 15px 60px;

  &__icon {
    position: absolute;
    top: 16px;
    left: 30px;
    font-size: 20px;
  }

  &__title {
    font-size: 14px;
    color: #333;
  }

  &__desc {
    font-size: 12px;
    color: #ccc;
  }

  &__btn {
    position: absolute;
    top: 14px;
    right: 10px;
    padding: 5px 4px;
  }
}

.action {
  &--normal {
    display: flex;
    margin: 20px 5px 10px;

    .action__button {
      flex: 1;
      padding: 0 5px;
    }
  }

  &--multi {
    display: block;
    margin: 20px 10px 10px;

    .action__button {
      width: 100%;
      margin-top: 10px;
      display: block;
    }
  }
}
</style>
