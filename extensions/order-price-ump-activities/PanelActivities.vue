<template>
  <!-- 活动 -->
  <view class="activity-panel">
    <view v-for="(item, index) in formatedPanelActivitiesList" :key="index" class="activity-item">
      <text class="activity-title">
        {{ item.promotionTypeName }}
      </text>
      <text class="activity-value">
        {{ item.formatedDecrease }}
      </text>
    </view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { formatPrice, replacePointsName } from './utils';

export default {
  data() {
    return {
      activitiesList: [],
      pointsName: '',
    };
  },

  computed: {
    formatedPanelActivitiesList() {
      return this.panelActivitiesList.map((item) => ({
        ...item,
        formatedDecrease: formatPrice(item.decrease),
      }));
    },

    panelActivitiesList() {
      return (this.activitiesList || [])
        .filter((item) => item.isShopActivity === 0 && item.type !== 'coupon')
        .map((item) => {
          if (item.type === 'pointDeduction') {
            item.promotionTypeName = replacePointsName(item.promotionTypeName, this.pointsName);
            item.activityName = replacePointsName(item.activityName, this.pointsName);
          }
          return item;
        });
    },
  },

  created() {
    mapData(this, ['activitiesList', 'pointsName']);
  },
};
</script>

<style lang="scss">
.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}

.activity-title {
  font-size: 14px;
  color: #969799;
}

.activity-detail {
  display: flex;
  align-items: center;
}

.activity-value {
  font-size: 14px;
  color: #323233;
}
</style>
