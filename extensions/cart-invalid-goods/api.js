import { requestV2 } from '@youzan/tee-biz-request';

const REQUEST_PATH = {
  deleteSingle: '/wsctrade/cart/deleteGoods.json',
  deleteBatch: '/wsctrade/cart/deleteBatchList.json',
  getMultiRecommendGoods: '/wsctrade/cart/getMultiRecommendGoods.json',
};

function fetchV2(path, data, method = 'POST') {
  const config = {};
  return requestV2({
    path,
    data,
    method,
    config,
  });
}

function pickIdFromGoods(goods) {
  const {
    kdtId,
    goodsId,
    skuId,
    storeId,
    channelId,
    activityId,
    cartId = null,
    activityAlias = '',
  } = goods;
  const data = {
    cartId,
    kdtId,
    goodsId,
    skuId,
    activityId,
    activityAlias,
  };

  if (storeId > 0) {
    data.storeId = storeId;
  }

  if (channelId > 0) {
    data.channelId = channelId;
  }

  return data;
}

function deleteCartItem(params) {
  return fetchV2(REQUEST_PATH.deleteSingle, pickIdFromGoods(params));
}

function deleteCartBatch(goodsList) {
  const ids = goodsList.map((goods) => pickIdFromGoods(goods));
  return fetchV2(REQUEST_PATH.deleteBatch, {
    ids,
  });
}

function getMultiRecommendGoods(params) {
  const data = {
    goodsIds: params,
  };
  return fetchV2(REQUEST_PATH.getMultiRecommendGoods, data);
}

export default {
  deleteCartItem,
  deleteCartBatch,
  getMultiRecommendGoods,
};
