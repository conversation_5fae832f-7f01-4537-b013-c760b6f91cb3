import { requestV2 } from '@youzan/tee-biz-request';

const REQUEST_PATH = {
  bookKey: '/wsctrade/order/book.json',
  voucher: '/wsctrade/cart/getVoucher.json',
  drugWhiteList: '/wsctrade/drug/isInWhiteList.json',
  reselectGoodsActivity: '/wsctrade/cart/reselectGoodsActivity.json',
};

function fetchV2(path, data, method = 'POST') {
  const config = {};
  return requestV2({
    path,
    data,
    method,
    config,
  });
}

function postBookKey(params) {
  return fetchV2(REQUEST_PATH.bookKey, params);
}

function getVoucher(params) {
  return fetchV2(REQUEST_PATH.voucher, params, 'POST');
}

function getDrugWhiteList() {
  return fetchV2(REQUEST_PATH.drugWhiteList, {}, 'GET');
}

function reselectGoodsActivity(params) {
  return fetchV2(REQUEST_PATH.reselectGoodsActivity, params, 'POST');
}

export default {
  postBookKey,
  getVoucher,
  getDrugWhiteList,
  reselectGoodsActivity,
};
