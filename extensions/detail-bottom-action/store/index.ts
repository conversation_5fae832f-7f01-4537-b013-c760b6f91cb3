import { IStoreObject, mergeStore } from './helper';

import bottomAction from './modules/bottom-action';

// 展示渲染视图层
import { createStore } from '@ranta/store';

const defaultStore: IStoreObject = {
  state: {},
  getters: {},
  actions: {},
};

const rootStore: IStoreObject = [defaultStore, ...[bottomAction]].reduce(
  (a, b) => mergeStore(a, b),
  {}
);

export default function createAddressStore(ctx) {
  return createStore({
    state: () => ({
      ...rootStore.state,
      ...bottomAction.getState(ctx),
    }),
    getters: {
      ...rootStore.getters,
    },
    actions: {
      ...rootStore.actions,
      ...bottomAction.getActions(ctx),
    },
  });
}
