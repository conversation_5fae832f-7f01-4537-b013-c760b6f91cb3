<template>
  <view class="pay-error">
    <view class="pay-error__img" />
    <view class="pay-error__desc">
      系统支付结果确认中，请稍等
      <view>点击刷新再试一次吧</view>
    </view>
    <view class="pay-error__action">
      <van-button
        type="danger"
        :block="true"
        :plain="true"
        :loading="loading"
        @click="clickHandler"
      >
        刷新
      </van-button>
    </view>
  </view>
</template>

<script>
import VanButton from '@youzan/vant-tee/dist/button/index.vue';

export default {
  components: {
    'van-button': VanButton,
  },
  props: {
    loading: <PERSON><PERSON><PERSON>,
  },
  methods: {
    clickHandler() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss">
.pay-error {
  padding: 10px 0;
  text-align: center;

  &__img {
    height: 245px;
    background: url('https://img.yzcdn.cn/v2/image/wap/ump/group_tuan/<EMAIL>');
    background-size: auto 200px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
  }

  &__desc {
    color: #999;
    font-size: 14px;
  }

  &__action {
    padding: 20px 10px;
  }
}
</style>
