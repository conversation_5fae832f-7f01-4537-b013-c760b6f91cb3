<template>
  <view v-if="orderListTabOpt.isShow && !isDrugShop">
    <van-tabs
      :z-index="99"
      :swipe-threshold="6"
      color="var(--main-bg, #ee0a24)"
      :pid="pid"
      :active="activeTabIndex"
      wrap-class="list-tabs"
      :border="false"
      @click="onTabClick"
    >
      <van-tab v-for="item in tabs" :key="item.type" :title="item.text" :pid="pid" />
    </van-tabs>
  </view>
</template>
<script>
// Utils
import { mapState } from '@ranta/store';
import get from '@youzan/utils/object/get';

// Components
import Tab from '@youzan/vant-tee/dist/tab/index.vue';
import Tabs from '@youzan/vant-tee/dist/tabs/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

export default {
  components: {
    'van-tab': Tab,
    'van-tabs': Tabs,
    'van-icon': Icon,
  },

  data() {
    return {
      pid: `order-list-tab${Math.floor(Math.random() * 10000)}`,
      ...mapState(this, [
        'isIvr',
        'isDrugShop',
        'isRetailShop',
        'pageType',
        'activityTab',
        'yunDesignConfig',
        'orderListTabOpt',
        'topNavHeight',
      ]),
    };
  },

  computed: {
    tabs() {
      return get(this, 'orderListTabOpt.tabs', []);
    },
    activeTabIndex() {
      let activeTabIndex = 0;
      const defaultTypeIndex = this.tabs.findIndex(
        (tab) => tab.type === this.activityTab.type || tab.type === this.pageType
      );
      if (defaultTypeIndex !== -1) {
        activeTabIndex = defaultTypeIndex;
      }

      return activeTabIndex;
    },
  },

  methods: {
    gotoOrderSearch() {
      this.store.gotoOrderSearch();
    },
    onTabClick({ index }) {
      this.store.activityTabChange(this.tabs[index]);
    },
  },
};
</script>
<style lang="scss">
.list-tabs {
  box-shadow: 0 2px 12px 0 rgba(100, 101, 102, 0.12);
}
.retail-tab-wrapper {
  box-shadow: 0 2px 12px 0 rgba(100, 101, 102, 0.12);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  background-color: #fff;
  .search-btn {
    padding-left: 90px;
    padding-right: 20px;
  }
}
</style>
