const EVENT_TYPE = {
  CLICK: 'click',
  VIEW: 'view',
};

const log = (ctx, params) => {
  ctx.logger && ctx.logger.log(params);
};

export const LOG_CANCEL_ORDER = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'cancel_order',
    en: '取消订单',
  });
};
export const LOG_DELAY_GOODS = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'delay_goods',
    en: '延长收货',
  });
};
export const LOG_CONFIRM_GOODS = (ctx, orderNo) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'confirm_goods',
    en: '确认收货',
    params: {
      order_no: orderNo,
    },
  });
};
export const LOG_EXPRESS_REMINDER = (ctx) => {
  // 埋点
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'express_reminder',
    en: '提醒发货',
  });
};
export const LOG_ORDER_DELETE = (ctx, params: { kdtId; orderNo; buyerId }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'order_delete_click',
    en: '删除订单按钮点击',
    params: {
      kdt_id: params.kdtId,
      order_no: params.orderNo,
      buyer_id: params.buyerId,
    },
  });
};
export const LOG_CONFIRM_HOTEL_GOODS = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'click_checkin',
    en: '点击入住',
  });
};
export const LOG_NEW_BUY_AGAIN_CLICK = (ctx, params) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'new_buy_again_click',
    en: '新再来一单点击',
    pt: 'ol',
    params,
  });
};
export const LOG_NEW_BUY_AGAIN_VIEW = (ctx, params) => {
  log(ctx, {
    et: EVENT_TYPE.VIEW,
    ei: 'new_buy_again_view',
    en: '新再来一单曝光',
    pt: 'ol',
    params,
  });
};
export const LOG_CLICK_WINDOW_BILL = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'click_window_bill',
    en: '点击海报',
  });
};
export const LOG_CLICK_WINDOW_FRIEND = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'click_window_friend',
    en: '点击分享好友',
  });
};
export const LOG_CLICK_WINDOW_COPY = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'click_window_copy',
    en: '点击复制链接',
  });
};
export const LOG_BULK_ADD_CART = (ctx, params: { orderNo: string; goodsIds: number[] }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'bulk_add_cart',
    en: '批量加入购物车',
    pt: 'ol',
    params: {
      goods_id_list: params.goodsIds,
      order_no: params.orderNo,
    },
  });
};
export const LOG_VIEW_CANCEL_ORDER_REASON = (ctx, params: { orderNo: string }) => {
  log(ctx, {
    et: 'view',
    ei: 'view_cancel_order_reason',
    en: '取消订单原因曝光',
    pt: 'ol',
    params: {
      order_no: params.orderNo,
    },
  });
};
export const LOG_GET_PAID_CLICK = (ctx, params: { orderNo: string }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'get_paid_click',
    en: '找人代付点击',
    params: {
      order_no: params.orderNo,
      component: 'cancle_order_component',
    },
  });
};
export const LOG_CANCLE_ORDER_COMPONENT_VIEW = (ctx, params: { orderNo: string; type: string }) => {
  log(ctx, {
    et: EVENT_TYPE.VIEW,
    ei: 'cancle_order_component_view',
    en: '取消订单引导弹窗组件曝光',
    params: {
      component_type: params.type,
      order_no: params.orderNo,
      component: 'cancle_order_component',
    },
  });
};
export const LOG_CANCLE_ORDER_CLICK = (ctx, params: { orderNo: string; type: string }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'cancle_order_click',
    en: '取消订单点击',
    params: {
      component_type: params.type,
      order_no: params.orderNo,
      component: 'cancle_order_component',
    },
  });
};
export const LOG_MODIFY_GOODS_CLICK = (
  ctx,
  // eslint-disable-next-line camelcase
  params: { orderNo: string; goodsList: Array<{ goodsId: number; title: string }> }
) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'modify_goods_click',
    en: '前往修改商品点击',
    params: {
      goods_id: params.goodsList[0].goodsId,
      goods_name: params.goodsList[0].title,
      order_no: params.orderNo,
      component: 'cancle_order_component',
    },
  });
};
export const LOG_SELECT_REASON = (ctx, params: { orderNo: string; reason: string }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'select_reason',
    en: '选择原因',
    pt: 'ol',
    params: {
      order_no: params.orderNo,
      reason: params.reason,
    },
  });
};
export const LOG_CANCLE_ORDER = (ctx, params: { orderNo: string; isBuyAgain: boolean }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'cancle_order',
    en: '确认取消订单',
    pt: 'ol',
    params: {
      order_no: params.orderNo,
      state: params.isBuyAgain ? 'open' : 'close',
    },
  });
};
export const LOG_VIEW_MARKETING_PAIDPRO = (ctx, params: { type: string; orderNo: string }) => {
  log(ctx, {
    et: EVENT_TYPE.VIEW,
    ei: 'view_marketing_paidpro',
    en: '支付有礼按钮曝光',
    params,
  });
};
export const LOG_CLICK_MARKETING_PAIDPRO = (ctx, params: { type: string; orderNo: string }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK, // 事件类型
    ei: 'click_marketing_paidpro', // 事件标识
    en: '支付有礼按钮点击', // 事件名称
    pt: 'ol', // 页面类型
    params,
  });
};
export const LOG_CLICK_DIVIDE_COUPON = (ctx, params = {}) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'click_divide_coupon',
    en: '点击领裂变券按钮',
    pt: 'ol',
    params,
  });
};
export const LOG_CLICK_CRM_OFFLINE_ORDER = (ctx) => {
  log(ctx, {
    et: 'click',
    ei: 'click_crm_offline_order',
    en: '点击CRM线下门店订单',
    pt: 'orderlist',
  });
};
export const LOG_SEARCH_ORDER_CLICK = (ctx) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'search_order_click',
    en: '订单搜索入口点击',
    pt: 'ol',
    params: {},
  });
};
export const LOG_CLOSE_BUYAGAIN_COUPON = (ctx, params: { orderNo: string }) => {
  log(ctx, {
    et: EVENT_TYPE.CLICK,
    ei: 'close_buyagain_coupon',
    en: '复购券浮层关闭',
    params,
  });
};
export const LOG_VIEW_BUYAGAIN_COUPON = (ctx, params: { type: string; orderNo: string }) => {
  log(ctx, {
    et: EVENT_TYPE.VIEW,
    ei: 'view_buyagain_coupon',
    en: '复购券浮层曝光',
    params,
  });
};
export const LOG_CLICK_TRANSPORT = (ctx) => {
  log(ctx, {
    et: 'click',
    ei: 'click_transport',
    en: '点击查看物流',
    params: {
      component: 'list_index',
    },
  });
};
