<template>
  <van-popup
    :show="show"
    position="bottom"
    safe-area-inset-bottom
    custom-class="coupon-popup"
    :custom-style="customStyle"
    @close="handleClose"
    closeable
    close-icon-class="close-icon"
  >
    <h3 class="coupon-popup__title" :catchtouchmove="true">优惠券</h3>
    <view class="coupon-popup__content">
      <coupon
        v-for="coupon in showCouponList"
        :kdt-id="kdtId"
        :key="coupon.couponId"
        :coupon="coupon"
        :theme-colors="themeColors"
        :is-pd-live="isPdLive"
        @sendCoupon="handleSendCoupon(coupon)"
        @clickItem="clickItem"
        @goToAddOnPage="goToAddOnPage"
        @to-navigate="toNavigate"
        @launchFastJoinSDK="launchFastJoinSDK"
      ></coupon>
    </view>
  </van-popup>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Checkbox from '@youzan/vant-tee/dist/checkbox/index.vue';
import Coupon from './CartCoupon.vue';
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */
import { createIntersectionObserver } from '@youzan/tee-api';

const GUIDE_TYPE = {
  FREE: 1,
  PAID: 2,
  CARD: 3,
};

export default {
  name: 'coupon-popup',

  components: {
    'van-popup': Popup,
    coupon: Coupon,
    'check-box': Checkbox,
    'van-icon': Icon,
  },

  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */
  props: {
    couponList: Array,
    showPopup: Boolean,
    themeColors: Object,
    themeCSS: Object,
    loggers: Object,
    kdtId: [Number, String],
    isPdLive: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.firstPopup = true;

    return {
      show: this.showPopup,
      displayType: 'flat',
    };
  },

  computed: {
    customStyle() {
      let bottomHeight = 0;
      /* #ifdef weapp */
      bottomHeight = this.popupBottom;
      if (this.safeBottom) {
        bottomHeight = this.noSafeBottom;
      }
      /* #endif */
      return `bottom: ${bottomHeight}px`;
    },
    showCouponList() {
      return this.couponList.map((coupon) => {
        const {
          valueCopywriting,
          unitCopywriting,
          thresholdCopywriting,
          title,
          validTimeCopywriting,
          couponId,
          optimalApplicableLimitIdentityType,
          needGuide,
        } = coupon;
        const couponNeedGuide =
          needGuide &&
          [GUIDE_TYPE.FREE, GUIDE_TYPE.PAID].includes(optimalApplicableLimitIdentityType);
        return {
          ...coupon,
          guideType: optimalApplicableLimitIdentityType,
          headValue: valueCopywriting,
          unit: unitCopywriting,
          condition: thresholdCopywriting,
          title,
          valid: validTimeCopywriting,
          needGuide: couponNeedGuide,
          got: couponId,
          actionText: couponNeedGuide ? '入会领取' : '立即领取',
        };
      });
    },
  },

  watch: {
    showPopup: {
      immediate: true,
      handler(val) {
        this.show = val;
        // 每次展示都上报
        if (val) {
          this.loggers?.logGatherPopupExpo?.();
          setTimeout(() => {
            this.observeCoupons();
          }, 0);
        }
      },
    },
    couponList: {
      immediate: true,
      handler(val) {
        if (val && val.length && this.show) {
          setTimeout(() => {
            this.observeCoupons();
          }, 0);
        }
      },
    },
  },

  destroyed() {
    this.observer?.disconnect();
  },

  methods: {
    observeCoupons() {
      if (this.observer) this.observer.disconnect();

      /* #ifdef web */
      this.observer = createIntersectionObserver(this, {
        thresholds: [0.5],
        observeAll: true,
        cache: true,
      }).relativeToViewport();
      /* #else */
      this.observer = this.createIntersectionObserver({
        thresholds: [0.5],
        observeAll: true,
        cache: true,
      }).relativeToViewport();
      /* #endif */

      this.observer.observe('.coupon-popup__content >>> .coupon-card ', (entry) => {
        if (entry.intersectionRatio >= 0.5) {
          const { coupon } = entry.dataset;
          if (!coupon.couponId) {
            this.loggers?.logCouponGetExpo?.(coupon.activityId);
          }
          if (!coupon.satisfy && coupon.couponId && coupon.ableUse) {
            this.loggers?.logGoAddonBtnExpo?.(coupon.activityId);
          }
        }
      });
    },
    handleClose() {
      this.$emit('close');
    },
    handleSendCoupon(coupon) {
      this.$emit('sendCoupon', coupon);
    },
    clickItem({ item, coupon }) {
      this.$emit('clickItem', { item, coupon });
    },
    launchFastJoinSDK(data) {
      this.$emit('launchFastJoinSDK', data);
    },
    goToAddOnPage(coupon) {
      /**
       * 网店商品使用范围 applicableOnlineGoodsRangeType
       * 0: 该渠道商品不可用 1: 不限制 2: 部分可用 3: 部分不可用
       *
       */
      this.loggers?.logGoAddonBtnClick?.(coupon.activityId);
    },
    toNavigate(params) {
      this.$emit('to-navigate', params);
    },
  },
};
</script>

<style lang="scss">
.close-icon {
  top: 12px !important;
}

.popup-safe-bottom {
  padding-bottom: 34px;
}
.coupon-popup {
  width: 100%;
  min-height: 50vh;
  max-height: 80vh !important;
  border-top-left-radius: var(--theme-radius-dialog, 20px);
  border-top-right-radius: var(--theme-radius-dialog, 20px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &__title {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #323233;
    padding-top: 11px;
    padding-bottom: 11px;
    position: relative;
  }

  &__close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__content {
    padding: 16px 16px 32px;
    overflow: scroll;
    font-size: 13px;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
    position: relative;
    flex: 1;

    > p {
      margin-bottom: 15px;
    }
  }
  .cap-couponbox__block-des {
    margin: 0 8px !important;
  }
  .coupon-addon-extra {
    width: 100%;
    .t-checkbox__icon .t-icon {
      width: 16px;
      height: 16px;
      background: white;
    }
    .extra-action {
      border-radius: 0 0 4px 0;
      position: absolute;
      right: -8px;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular, sans-serif;
      font-size: 12px;
      width: 68px;
      justify-content: center;
      padding-right: 5px;
    }
    .cart-item-list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .cart-item {
        width: 56px;
        height: 84px;
        padding-right: 8px;
        position: relative;
      }
      .item-thumb {
        width: 56px;
        height: 56px;
        border-radius: var(--theme-radius-card, 4px);
      }
      .item-price {
        font-family: PingFangSC-Medium, sans-serif;
        font-size: 10px;
        color: #323233;
        height: 14px;
        text-align: center;
      }
      .item-num {
        font-family: PingFangSC-Regular, sans-serif;
        font-size: 10px;
        color: #969799;
        height: 14px;
        text-align: center;
      }
      .item-check {
        position: absolute;
        left: 2px;
        top: 2px;
      }
    }
  }
}
</style>
