<template>
  <view :class="['gift__bottom-links', isFix && 'fix-bottom']">
    <text class="gift__bottom-links-item" @click="handleUsingTipsClick">使用说明</text>
    <text class="gift__bottom-links-item" @click="handleUsingRulesClick">用户规则</text>
    <text class="gift__bottom-links-item" @click="handleGiftListClick">礼物记录</text>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import buildUrl from '@youzan/utils/url/buildUrl';

export default {
  props: {
    isFix: Boolean,
    kdtId: Number,
  },
  methods: {
    handleGiftListClick() {
      this.ctx.dmc.navigate('GiftList', {
        kdt_id: this.kdtId,
      });
    },

    handleUsingTipsClick() {
      /* #ifdef weapp */
      this.toWebview('using-tips');
      /* #else */
      this.ctx.dmc.navigate('GiftUsingTips');
      /* #endif */
    },

    handleUsingRulesClick() {
      /* #ifdef weapp */
      this.toWebview('rules');
      /* #else */
      this.ctx.dmc.navigate('GiftRules');
      /* #endif */
    },

    toWebview(key) {
      Tee.navigate({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          buildUrl(`wsctrade/gift/${key}`, 'h5')
        )}`,
      });
    },
  },
};
</script>

<style lang="scss">
.gift__bottom-links {
  color: #5f729a;
  text-align: center;
  margin-top: 20px;
  padding-bottom: calc(87px + env(safe-area-inset-bottom));

  &.fix-bottom {
    margin-top: 0;
    padding-bottom: calc(124px + env(safe-area-inset-bottom));
  }
  &-item {
    display: inline-block;
    height: 14px;
    line-height: 14px;
    padding: 0 12px;
    font-size: 14px;

    &:last-child {
      padding-left: 12px;
    }
  }
}
</style>
