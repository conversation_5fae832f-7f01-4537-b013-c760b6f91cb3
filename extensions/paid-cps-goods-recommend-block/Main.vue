<template>
  <view v-if="showExtHolder" class="cps-goods-recommend-block">
    <!-- 躺赚  -->
    <cps-recommend-goods v-if="widgetCpsRecommendGoodsInit" @is-show="onView" />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      showExtHolder: false,
      widgetCpsRecommendGoodsInit: false,
    };
  },

  created() {
    mapData(this, ['showExtHolder']);

    this.widgetCpsRecommendGoodsInit = !!this.ctx.widgets.CpsRecommendGoods;
  },
  methods: {
    onView(flag) {
      this.$emit('is-show', flag);
    },
  },
};
</script>

<style>
.cps-goods-recommend-block {
  /* #ifdef web */
  /* 为了消除cps-recommend-goods组件内部的margin */
  margin: 0 -12px -8px -12px;
  /* #endif */
  /* #ifdef weapp */
  margin-top: 8px;
  border-radius: 8px;
  overflow: hidden;
  /* #endif */
}
</style>
