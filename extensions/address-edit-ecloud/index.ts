import type { ModuleContext } from '@youzan/ranta-tee';
import ExtensionMetadata from './extension';
import createProcess from './saas/createProcess';

export default class Extension {
  ctx: ModuleContext.Ctx<ExtensionMetadata>;

  constructor(options: ModuleContext.Options<ExtensionMetadata>) {
    const { ctx } = options;

    this.ctx = options.ctx;
    this.ctx.data.cloudData = {
      displayConfig: {
        isShowWechatAddress: true,
      },
    };

    // 扩展点
    const ecloudProcesses = createProcess(ctx);
    Object.keys(ecloudProcesses).forEach((key: keyof typeof ecloudProcesses) => {
      const process = ecloudProcesses[key];
      // @ts-ignore
      // eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
      this.ctx.process.define(`ecloud:${key}`, process);
    });
  }
}
