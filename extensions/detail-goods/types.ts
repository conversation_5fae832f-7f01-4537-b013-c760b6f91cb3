/**
 * 商品级操作类型
 * 'refund' = 申请退款
 * 'message' = 查看留言
 * 'postpone' = 我要顺延
 */
export enum GoodsActionEnum {
  REFUND = 'refund',
  MESSAGE = 'message',
  POSTPONE = 'postpone',
}
export interface GoodsAction {
  /** 商品id */
  itemId: string;
  /** 操作类型 */
  type: GoodsActionEnum;
}

/**
 * 商品类型
 * 0 = 普通类型商品
 * 10 = 分销商品
 * 24 = 周期购
 * 31 = 知识付费商品
 * 35 = 酒店商品
 * 182 = 普通虚拟商品
 * 183 = 电子卡券商品
 */
enum IGoodsTypeEnum {
  COMMON = 0,
  FENXIAO = 10,
  PERIOD_BUY = 24,
  KNOWLEDGE = 31,
  HOTEL = 35,
  NORMAL_VIRTUAL = 182,
  VIRTUAL_TICKET = 183,
}

interface IGoodsItem {
  /** 商品 Id */
  goodsId: number;
  itemId: string;
  /** 商品重量 */
  weight?: number;
  /** 商品重量 */
  goodsInfo: IGoodsInfo;
  /** 商品重量 */
  goodsType: IGoodsTypeEnum;
  /** 商品重量 */
  goodsUrl: string;
  /** 商品重量 */
  num: number;
  /** 商品重量 */
  skuId: number;
  /** 商品留言 */
  buyerMemo: Record<string, any>;
  /** 商品是否“申请退款”按钮 */
  isShowRefund?: boolean;
}

// ----- begin goodsList -----
interface IGoodsInfo {
  /** 商品别名 */
  alias: string;
  /** 课程商品标识 */
  courseGoodsMark: string;
  /** 商品图片链接 */
  imgUrl: string;
  /** 普通商品标识 */
  mark: number;
  /** 商品积分优惠 */
  pointsPrice: string;
  /** 商品副标题 */
  shortTitle: string;
  /** 商品标题 */
  title: string;
}

export interface IGoodsList extends Array<IGoodsItem> {}
// ----- end goodsList ------
