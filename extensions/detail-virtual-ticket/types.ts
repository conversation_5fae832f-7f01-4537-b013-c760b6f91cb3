/**
 * 电子卡券核销状态
 * 1 = 待使用
 * 2 = 已使用
 * 3 = 已退款
 * 4 = 已过期
 * 5 = 退款中
 */
export enum EcardStateEnum {
  NOT_VERIFIED = 1, // 待使用
  VERIFIED = 2, // 已使用
  DISABLED = 3, // 已退款
  EXPIRED = 4, // 已过期
  REFUND = 5, // 退款中
}
export const ECARD_CONF = {
  [EcardStateEnum.NOT_VERIFIED]: {
    value: '',
    title: '待使用',
    suffix: '',
    weight: 1,
  },
  [EcardStateEnum.VERIFIED]: {
    value: '已使用',
    title: '已使用',
    suffix: '--del',
    weight: 3,
  },
  [EcardStateEnum.DISABLED]: {
    value: '已退款',
    title: '已退款',
    suffix: '--del',
    weight: 2,
  },
  [EcardStateEnum.EXPIRED]: {
    value: '已过期',
    title: '已过期',
    suffix: '--del',
    weight: 4,
  },
  [EcardStateEnum.REFUND]: {
    value: '退款中',
    title: '退款中',
    suffix: '',
    weight: 0,
  },
};
