<template>
  <view class="self-fetch" v-if="showSelfFetch">
    <view v-if="!selfFetchInfo.selfFetchState">
      <!-- 未核销正常展示 -->
      <view class="self-fetch__title">核销码</view>
      <view class="self-fetch__content">
        <image
          v-if="selfFetchInfo.qrcode"
          class="self-fetch__qrcode"
          :src="selfFetchInfo.qrcode"
          alt="二维码"
        />
        <van-loading v-else class="self-fetch__content__loading" />
      </view>
      <view class="self-fetch__code">{{ parsedFetchNo || '正在生成中...' }}</view>
      <view v-if="selfFetchInfo.qrcode" class="self-fetch__desc">请将核销码出示给收银员</view>
    </view>
    <view v-else>
      <!-- 已核销 -->
      <van-cell title="提货码" label-class="cell-label-self-fetch" :border="false">
        {{ parsedFetchNo + '（已核销）' }}
      </van-cell>
    </view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Loading from '@youzan/vant-tee/dist/loading/index.vue';

export default {
  components: {
    'van-cell': Cell,
    'van-loading': Loading,
  },

  data() {
    return {
      showSelfFetch: false,
      selfFetchInfo: {
        qrcode: '',
        fetchNo: '',
        selfFetchState: '',
      },
    };
  },

  computed: {
    parsedFetchNo() {
      if (this.selfFetchInfo && this.selfFetchInfo.fetchNo) {
        return this.selfFetchInfo.fetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3');
      }
      return '';
    },
  },

  created() {
    mapData(this, ['showSelfFetch', 'selfFetchInfo']);
  },
};
</script>

<style lang="scss" scoped>
.self-fetch {
  background: #fff;
  margin-top: 10px;
}

.self-fetch__title {
  color: #323233;
  font-size: 16px;
  font-weight: 500;
  line-height: 44px;
  text-align: center;
}

.self-fetch__content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 178px;
  width: 178px;
  margin: 15px auto 0;
}

.self-fetch__qrcode {
  width: 178px;
  height: 178px;
  border: 1px solid #dcdee0;
}

.self-fetch__content__loading {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 120%;
  background-position: center;
  text-align: center;
  line-height: 80px;
}

.self-fetch__code {
  font-family: Avenir, sans-serif;
  font-size: 16px;
  line-height: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 16px 0 12px;
}

.self-fetch__desc {
  color: #969799;
  font-size: 14px;
  padding: 10px 0 15px;
  text-align: center;
}

.cell-label-self-fetch {
  flex-grow: 0;
  flex-basis: 90px;
}
</style>
