import type { Instance } from '@youzan/ranta-helper';
import Tee from '@youzan/tee';
import {
  startPullDownRefresh,
  stopPullDownRefresh,
  setClipboardData,
  setNavigationBarTitle,
} from '@youzan/tee-api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';

import { object, string } from '@youzan/tee-util';
import {
  PAGE_TYPE,
  NAVIGATE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import { mapEvent } from '@youzan/ranta-helper-tee';

import api from './weapp/api';
import { formatOrder } from './weapp/format';
import CloudExtension from './cloud/CloudExtension';
import { getRechargeInfo } from './common/prepaid';

const app = getApp();
const logger = (message, detail) => {
  app.logger &&
    app.logger.appError({
      name: 'get_order_info_error',
      message,
      detail: detail || {},
    });
};

export default class PageSetupExtension extends CloudExtension<PageSetupExtension> {
  constructor(options) {
    super(options);
    const currentRoutes = getCurrentPages() || [];

    // 是否为前往crm线下门店订单页
    const isCrmOrderDetail =
      currentRoutes[currentRoutes.length - 1]?.route === 'packages/trade/crm-order-detail/index';

    if (!isCrmOrderDetail) {
      /**
       * 做一层兜底，防止在切流过程中有其他一方直接跳转到新版
       */
      navigateToRantaPage({
        type: NAVIGATE_TYPE.REDIRECT,
        pageType: PAGE_TYPE.DETAIL,
        fromRantaPage: true,
      });
    }

    this.initData();
    this.ctx.process.invoke('getOrderEnterShopPolicy');

    this.initEvents();
    this.initProcesses();
  }

  onPullDownRefresh() {
    this.fetchOrderInfo();
    this.waitingOrderProgress();
  }

  async onPageShow({ query = {} }: { query: Record<string, any> }) {
    query = string.mapKeysToCamelCase(query);
    const { isRetailApp = false } = app.globalData || {};

    let currentOrderNo = query.orderNo || '';
    // 如果orderNo不是正常的E单，尝试调用接口，将支付明细单号转换成E单号
    // 相关jira: https://jira.qima-inc.com/browse/ONLINE-868032
    if (currentOrderNo && !currentOrderNo.startsWith('E')) {
      // 使用 async/await 等待转换完成后再继续
      try {
        const res = await api.convertPaymentOrderNo({ orderNo: currentOrderNo });
        const { order_no: orderNo } = (res || [])[0] || {};
        if (orderNo) {
          // 如果是合并支付，查询出来就是V单，那么跳转到订单列表
          if (orderNo.startsWith('V')) {
            Tee.redirectTo('/packages/trade/order/list/index');
            return;
          }
          currentOrderNo = orderNo;
        }
      } catch (err) {
        logger('转换支付明细单号失败', err);
      }
    }
    if (isRetailApp) {
      this.initRetailShopData({ ...query, orderNo: currentOrderNo });
      return;
    }
    const kdtId = query.kdtId || app.getKdtId();
    this.ctx.data.orderNo = currentOrderNo;
    this.ctx.data.kdtId = kdtId;
    this.ctx.logger.setEvent({ si: kdtId });
    this.initTradeUmpV1();
    this.fetchOrderInfo({ showToast: true });
  }

  onShareAppMessage(event) {
    const [shareData] = this.ctx.process.invoke('getShareAppMessage', event.from);
    return shareData;
  }

  initRetailShopData(query) {
    app.waitForEnterShop().then((res) => {
      const kdtId = query.kdtId || res.kdtId;
      const orderNo = query.orderNo || '';
      this.ctx.data.orderNo = orderNo;
      this.ctx.data.kdtId = kdtId;
      this.ctx.logger.setEvent({ si: kdtId });
      this.initTradeUmpV1();
      this.fetchOrderInfo({ showToast: true });
    });
  }

  // 营销支付优化白名单判断
  initTradeUmpV1() {
    api
      .getTradeUmpV1()
      .catch(() => false)
      .then((canUse) => {
        console.log('canUse??', canUse);
        this.ctx.data.canUseTradeUmpV1 = canUse;
      });
  }

  // 获取订单数据
  fetchOrderInfo({ showToast = false } = {}) {
    showToast && Toast.loading({ message: '数据加载中' });

    const supportBlindBox = true;
    const { orderNo, kdtId } = this.ctx.data;

    // TODO：针对个别用户小程序订单详情打不开情况，增加日志追踪辅助排查，定位到问题后删除logger和logKey相关代码
    // https://jira.qima-inc.com/browse/ONLINE-367479
    const logKey = Date.now().toString(16);

    api
      .fetchOrderInfo({ orderNo, kdtId, supportBlindBox })
      .then((res) => {
        Toast.clear();
        stopPullDownRefresh();

        const goodsItem = object.get(res, 'itemInfo[0]', {});
        const rxNo = object.get(res, 'mainOrderInfo.extra.rxNo', '');
        // 是否是批发订单
        const isWholesaleOrder = object.get(res, 'mainOrderInfo.isWholesaleOrder', false);

        this.ctx.data.rxNo = rxNo;
        const cardGoodsId = goodsItem.goodsType === 20 ? goodsItem.goodsId : '';
        logger('请求返回取值goodsItem', { goodsItem, logKey });

        if (isWholesaleOrder) {
          setNavigationBarTitle('批发订单详情');
        }

        // 不支持查看
        if (res.unsupportedInfo && Object.keys(res.unsupportedInfo).length) {
          // @ts-ignore
          this.ctx.event.emit('onLoadOrderInfo');
          return this.showUnsupportedModal(res.unsupportedInfo);
        }

        // 待支付重定向至支付页
        if (res.mainOrderInfo.state === 10) {
          return navigateToRantaPage({
            pageType: PAGE_TYPE.PAY,
            type: NAVIGATE_TYPE.REDIRECT,
            query: {
              orderNo,
            },
          });
        }

        // 会员卡订单
        if (cardGoodsId) {
          return Tee.redirectTo(`/packages/card/detail/index?goods_id=${cardGoodsId}`);
        }

        // 重定向到社区团购 详情
        const originSource = object.get(res.sourceInfo, 'originSource', {});
        const source = object.get(originSource, 'source', '');
        logger('请求返回取值originSource', { originSource, logKey });

        if (source === 'mall_group_buy') {
          return Tee.redirectTo(
            `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}`
          );
        }

        const { ump, buyerInfo } = res;
        logger('请求返回取值ump,buyerInfo', { ump, buyerInfo, logKey });

        if (ump?.goodsAvlActivity?.id) {
          logger('营销情况', { logKey });

          api
            .checkCouponAvailable({
              activityId: ump?.goodsAvlActivity?.id,
              source: 'order_detail',
              kdtId,
              userId: buyerInfo.buyerId,
            })
            .then(() => {
              logger('优惠券有效', { logKey });
              this.formatData(res, true, logKey);
            })
            .catch((err) => {
              logger('检查优惠券报错', { err, logKey });
              this.formatData(res, false, logKey);
            });
        } else {
          logger('默认情况', { logKey });

          this.formatData(res, false, logKey);
        }
      })
      .catch((err) => {
        Toast.clear();
        stopPullDownRefresh();

        app.logger &&
          app.logger.appError({
            name: 'get_order_info_error',
            message: '获取订单详情报错',
            detail: {
              orderNo,
              kdtId,
              supportBlindBox,
              error: err,
            },
          });

        // 前往错误页
        const errorId = app.db.set({ text: err.msg, code: err.code });
        Tee.redirectTo('/packages/common/error/index?dbid=' + errorId);
      });
  }

  formatData(res, buyAgainCouponAvailable, logKey) {
    const formatOrderLogger = (name, detail) => {
      logger(name, {
        ...detail,
        logKey,
      });
    };
    // 塞入格式化后的订单数据
    logger('格式化订单信息start', { logKey });
    Object.assign(this.ctx.data, formatOrder(res, formatOrderLogger), {
      buyAgainCouponAvailable,
    });

    logger('格式化订单信息end', { logKey });

    const { mainOrderInfo, shopInfo, orderBizExtra, paymentInfo, orderExtra } = res;
    logger('数据绑定orderExtra', { orderExtra, logKey });

    const { pickUpCode } = res.mainOrderInfo || {};
    orderExtra.retailPickUpCode = pickUpCode;

    this.ctx.data.orderExtra = orderExtra;
    this.ctx.data.rechargeInfo = getRechargeInfo(orderExtra);
    Object.assign(this.ctx.data.bigOrder, {
      mainOrderInfo,
      shopInfo,
      orderBizExtra,
      paymentInfo,
    });
    // @ts-ignore
    this.ctx.event.emit('onLoadOrderInfo');
  }

  // 显示不支持查看modal
  showUnsupportedModal(unsupportedInfo) {
    Dialog.confirm({
      title: '',
      message: unsupportedInfo.content,
      confirmButtonText: '复制链接',
      showCancelButton: false,
    })
      .then(() => {
        const duration = 1500;
        setClipboardData(unsupportedInfo.url).then(() => {
          setTimeout(() => {
            if (getCurrentPages().length === 1) {
              Tee.redirectTo('/packages/trade/order/list/index');
            } else {
              Tee.navigateBack();
            }
          }, duration);
        });
        Dialog.close();
      })
      .catch(() => {
        Dialog.close();
      });
  }

  initEvents() {
    mapEvent(this as unknown as Instance, {
      refreshOrderInfo: startPullDownRefresh,
    });
    // @ts-ignore
    this.ctx.event.once('onLoadOrderInfo', () => {
      // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
      this.ctx.cloud.emit('onOrderLoadedEvent'); // for cloud 1.0
    });
  }

  // 初始化process
  initProcesses() {
    this.ctx.process.define('afterReceiverInfoUpdated', () => {
      this.fetchOrderInfo({ showToast: true });
    });

    this.ctx.process.define('setReceivingContent', (content) => {
      this.ctx.data.receivingContent = content;
    });

    this.ctx.process.define('setShowMessage', (isShow) => {
      this.ctx.data.showMessage = isShow;
    });

    this.ctx.process.define('setItemList', (newItemList) => {
      this.ctx.data.itemList = newItemList;
    });

    this.ctx.process.define('setBottomBtnsDisplay', (payload) => {
      this.ctx.data.bottomBtnsDisplay = payload;
    });

    this.ctx.process.define('toggleBlockDisplay', ({ blockName, display }) => {
      switch (blockName) {
        case 'OrderAddress':
          this.ctx.data.showOrderAddress = display;
          break;
        case 'OrderStatus':
          this.ctx.data.showOrderStatus = display;
          break;
      }
    });
  }

  initData() {
    this.ctx.data.order = {};
    this.ctx.data.bigOrder = {
      shopInfo: {},
      paymentInfo: {},
      mainOrderInfo: {},
      orderBizExtra: {},
    };

    this.ctx.data.bizName = 'order_detail';
    this.ctx.data.requestExtraParams = 'note,coupon';
    this.ctx.data.buyerId = app.getBuyerId();
    this.ctx.data.cpsConfigKey = 'cps_goods_recommend_order_detail';
    this.ctx.data.receivingContent = '';
    this.ctx.data.showMessage = true;
    this.ctx.data.orderBizExtra = {};
    this.ctx.data.order = {};
    this.ctx.data.bottomBtnsDisplay = {
      confirmHotel: true,
    }; // for 有赞云，如果有值且为false则强制隐藏
    this.ctx.data.showOrderAddress = true;
    this.ctx.data.showOrderStatus = true;
  }

  waitingOrderProgress() {
    this.ctx.process.invoke('waitingOrderProgress');
  }
}
