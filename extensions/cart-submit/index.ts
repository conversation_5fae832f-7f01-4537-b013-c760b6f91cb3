import { cloud, bridge, useAsHook, cloudHook } from '@youzan/ranta-helper';
import { mapData, mapDataAll, mapProcess } from '@youzan/ranta-helper-tee';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';
import type {
  CartBeforeBottomBarNoticeRender,
  CartInfo,
  TradeCartPayment,
} from '@youzan-cloud/cloud-biz-types';
import type { CartStateV1, PaymentV1 } from './types';
import Main from './Main.vue';
import Submit from './Submit.vue';
import BestPriceBar from './widgets/BestPriceBar.vue';
import createStore from './store';
import { cloudData, isSameObject } from './utils';

export default class SubmitExtension {
  ctx: any;

  store: any;

  @cloud('beforeBottomBarNoticeRender', 'hook', { allowMultiple: true })
  beforeBottomBarNoticeRender = cloudHook<CartBeforeBottomBarNoticeRender>();

  /**
   * beforeOrderCreate
   * @desc [可中断]提交订单前触发
   */
  @cloud('beforeOrderCreate', 'hook', { allowMultiple: true })
  beforeOrderCreate = useAsHook<() => Promise<void>>();

  /**
   * cartInfo
   * @desc 结算信息
   */
  @cloud('cartInfo', 'data')
  cartInfo: CartInfo;

  /**
   * payment
   * @desc 结算信息
   */
  @cloud('payment', 'data')
  payment: TradeCartPayment;

  /**
   * payment
   * @desc 商品合计金额
   */
  @bridge('payment', 'data')
  paymentV1: PaymentV1;

  /**
   * cartState
   * @desc 商品合计金额
   */
  @bridge('cartState', 'data')
  cartStateV1: CartStateV1;

  /**
   * createAndGoOrder
   * @deprecated 从 2.0 开始
   * @desc 购物车下单流程
   */
  @bridge('createAndGoOrder', 'process')
  createAndGoOrder() {
    this.ctx.process.invokePipe('createAndGoOrder');
  }

  /**
   * createOrder
   * @desc 创建订单
   */
  @cloud('createOrder', 'method', { allowMultiple: false })
  @bridge('createOrder', 'process')
  createOrder() {
    this.ctx.process.invokePipe('createAndGoOrder');
  }

  /**
   * handleChangeSubmitText
   * @deprecated 从 2.0 开始
   * @desc 更改结算文案
   */
  @bridge('handleChangeSubmitText', 'process')
  handleChangeSubmitText(text) {
    this.ctx.process.invoke('changeSubmitText', text);
  }

  /**
   * handleCartSubmitBeforeAsync
   * @deprecated 从 2.0 开始
   * @desc 点击结算时
   */
  @bridge('handleCartSubmitBeforeAsync', 'asyncEvent')
  handleCartSubmitBeforeAsync = useAsHook<() => Promise<void>>();

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    this.initMapCtxData();
    this.initCloudData();
    this.initProcess();

    mapStoreToCtx(this, ['cartBottomData']);
  }

  initMapCtxData() {
    mapCtxData(this, [
      'showEmptyCart',
      'submitData',
      'editMode',
      'checkedGoodsList',
      'isCheckedAll',
      'presentData',
      'themeCSS',
      'themeStyle',
      'isDrugShop',
      'isTabPage',
      'dataLoaded',
      'shopCart',
      'isHasSelfFetchGoodsSelected',
      'selectedPromotions',
      'numChangeInvalid',
    ]);
  }

  static widgets = {
    Main,
    Submit,
    BestPriceBar,
  };

  initProcess() {
    mapProcess(this, {
      createAndGoOrder: () => this.store.handleBtnTap(),
      changeSubmitText: (text) => this.store.changeSubmitText(text),
      changeDiscountDetailVisible: (visible) => this.store.changeDiscountDetailVisible(visible),
    });
  }

  initCloudData() {
    mapData(this, ['cartBottomData'], {
      isSetData: false,
      callback: () => {
        const { cartBottomData } = this.ctx.data;
        const newOpenData = {
          paymentV1: cloudData.getPaymentV1({ cartBottomData }),
          payment: cloudData.getPayment({ cartBottomData }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
    mapDataAll(this, ['cartBottomData', 'editMode', 'isCheckedAll'], {
      isSetData: false,
      callback: () => {
        const { cartBottomData, editMode, isCheckedAll } = this.ctx.data;
        const newOpenData = {
          cartStateV1: cloudData.getCartStateV1({ cartBottomData, editMode, isCheckedAll }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
    mapDataAll(this, ['editMode', 'isCheckedAll'], {
      isSetData: false,
      callback: () => {
        const { editMode, isCheckedAll } = this.ctx.data;
        const newOpenData = {
          cartInfo: cloudData.getCartInfo({ editMode, isCheckedAll }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
