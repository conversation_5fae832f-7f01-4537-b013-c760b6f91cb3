<template>
  <van-list
    class="order-list"
    :loading="false"
    :finished="!activityTab.hasNext"
    :offset="300"
    @load="loadOrder"
    :finished-text="''"
  >
    <view :style="rootStyle">
      <cps-goods-order />
      <view v-for="(item, index) in orderList" :key="index">
        <list-item
          :list-index="index"
          :list-item="item"
          :cloud-index="index"
          :cloud-order-no="item.orderNo"
        />
        <welike-entry-wrapper v-if="index === 0" />
      </view>
    </view>

    <view class="order-list__footer">
      <van-loading v-if="loading" type="img">加载中...</van-loading>
      <view class="order-list__no-more" v-if="!activityTab.hasNext && !isEmptyList"
        >没有更多了</view
      >
    </view>
  </van-list>
</template>

<script>
import List from '@youzan/vant-tee/dist/list/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Loading from '@youzan/vant-tee/dist/loading/index.vue';
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-list': List,
    'van-popup': Popup,
    'van-loading': Loading,
  },

  data() {
    return {
      ...mapState(this, [
        'isEmptyList',
        'activityTab',
        'orderList',
        'loading',
        'stickyHeaderHeight',
        'isShowRetailOrderList',
      ]),
    };
  },

  computed: {
    rootStyle() {
      // 零售订单中心距离固定UI的上间距为75px，其他为15px
      const marginTopGap = this.isShowRetailOrderList ? 75 : 15; // 订单列表距离固定UI的上间距
      return `margin-top: ${this.stickyHeaderHeight + marginTopGap}px`;
    },
  },

  methods: {
    loadOrder() {
      if (this.activityTab.page === 0) return;
      this.store.loadOrder();
    },
  },
};
</script>

<style lang="scss" scoped>
.order-list {
  overflow: hidden;

  &__footer {
    margin: 30px auto;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  &__no-more {
    color: #969799;
    font-size: 14px;
    text-align: center;
  }
}
</style>
