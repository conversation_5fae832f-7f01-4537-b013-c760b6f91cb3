import Toast from '@youzan/vant-tee/dist/toast/toast';
import { getTicketDetail, getVoucherCardPoster } from '../../../utils/voucher-card';
import { cdnImage } from '@youzan/tee-biz-util';
import { getPosterAndDownload } from '../../../utils/poster-helper';
import { ITicketDetailResponse } from '../../../types';

export default function (ctx: any) {
  return {
    initVoucherDetail() {
      this.voucherCardIsLoading = true;
      if (this.voucherCard && this.voucherCard.qrcode) {
        return;
      }
      this.tryFetchVoucherDetail()
        .then(() => {
          this.voucherCardIsLoading = false;
        })
        .catch(() => {
          this.voucherCardIsError = true;
        });
    },

    tryFetchVoucherDetail(tryCount = 0) {
      return new Promise((resolve, reject) => {
        if (tryCount < 3) {
          this.fetchVoucherDetail()
            .then(resolve)
            .catch(() => {
              setTimeout(() => {
                this.tryFetchVoucherDetail(++tryCount).then(resolve).catch(reject);
              }, 1500);
            });
        } else {
          reject('');
        }
      });
    },

    fetchVoucherDetail() {
      return new Promise((resolve, reject) => {
        let source = 'h5';
        /* #ifdef weapp */
        source = 'weapp';
        /* #endif */
        getTicketDetail({
          orderNo: this.orderNo,
          source,
        })
          .then((res: ITicketDetailResponse) => {
            if (res && res.qrCode) {
              const defaultImage = cdnImage(
                'public_files/2019/08/06/3f099aa967abd3821d610344d7ba3729.jpg'
              );
              const items = ctx.data.orderItems.map((item) => {
                return {
                  title: item.title, // 商品标题
                  num: item.num, // 商品数量
                  sku: item.skuItems.map((sku) => `${sku.k}：${sku.v}`).join('；') || '默认规格', // sku信息, 如黄色：250;
                  imgUrl: item.imgUrl || defaultImage, // 商品图片地址
                  validity:
                    res.validity.indexOf(' - ') > -1
                      ? `有效期至${res.validity.split(' - ')[1]}`
                      : res.validity,
                };
              });

              this.voucherCard = {
                items,
                cardnums:
                  res.tickets instanceof Array ? res.tickets.map((item) => item.ticketCode) : [],
                barcode: res.barCode,
                qrcode: res.qrCode,
                code: res.code,
              };
              resolve(null);
            } else {
              reject('');
            }
          })
          .catch((response) => {
            const { msg } = response?.data || {};
            reject(msg);
          });
      });
    },

    // 保存券码
    saveVoucherCardPoster() {
      if (this.voucherCardIsError) {
        return Toast({ message: '券码生成失败，请稍后再试或前往订单详情查看', icon: 'none' });
      }
      getPosterAndDownload({
        needRetry: true,
        getPoster: () => {
          return getVoucherCardPoster({
            ...this.voucherCard,
          });
        },
        onSuccess(imgUrl) {
          /* #ifdef web */
          ctx.event.emit('showSaveImageDialog', imgUrl);
          /* #endif */
        },
      });
    },
  };
}
