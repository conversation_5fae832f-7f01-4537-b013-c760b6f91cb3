import PaidAction from './widgets/PaidAction.vue';
import WishMsgPopup from './widgets/WishMsgPopup.vue';
import SubscriptionDialog from './widgets/SubscriptionDialog.vue';
import PaidSubscriptionMsg from './widgets/PaidSubscriptionMsg.vue';
import ActivateBenefitcardDialog from './widgets/ActivateBenefitcardDialog.vue';
import LongClickSaveImageDialog from './widgets/LongClickSaveImageDialog.vue';
import PaidSubscriptionMsgTips from './widgets/PaidSubscriptionMsgTips.vue';

import toCamelCase from '@youzan/utils/string/toCamelCase';
import Tee from '@youzan/tee';
import { mapEvent } from '@youzan/ranta-helper-tee';
import navigate from '@youzan/tee-biz-navigate';
import { cloud, bridge, useAsHook } from '@youzan/ranta-helper';
import type {
  BeforeAutoPopupDisplayPayload,
  OrderPaidActionParams,
  OrderPaidBeforeDisableOrderPaidBtns,
} from '@youzan-cloud/cloud-biz-types';
import { mapCtxData } from '@ranta/store';
import { GOODS_TYPE, BTNS_MAP } from './constans';
import { isBlindBox } from './util';
import createStore from './store';

/* #ifdef web */
import { buyAgain } from './utils/buy-again';
/* #endif */

export default class PaidActionExtension {
  ctx: any;

  store: any;

  @cloud('beforeAutoPopupDisplay', 'hook', { allowMultiple: true })
  beforeAutoPopupDisplay = useAsHook<(payload: BeforeAutoPopupDisplayPayload) => Promise<void>>();

  @cloud('beforeDisableOrderPaidBtns', 'hook', { allowMultiple: false })
  beforeDisableOrderPaidBtns = useAsHook<OrderPaidBeforeDisableOrderPaidBtns>();

  @cloud('handleOrderPaidAction', 'method', { allowMultiple: false })
  handleOrderPaidAction(params: OrderPaidActionParams) {
    const { type } = params;
    const action = this.store.btnList.find((_) => {
      const _type = toCamelCase(_.type.toLocaleLowerCase());
      if (type === 'buyAgain') {
        return [type, 'directBuyAgain'].includes(_type);
      }
      return _type === type;
    });
    if (action) {
      this.store.processPaidAction(action);
    }
  }

  @bridge('handleOrderDetail', 'process')
  handleOrderDetailV1() {
    this.ctx.event.emit('gotoOrderDetail');
  }

  @bridge('disableAutoPopup', 'process')
  disableAutoPopupV1() {
    return this.ctx.event.emit('disableAutoPopup');
  }

  /* #ifdef web */
  @bridge('orderOneMore', 'process')
  orderOneMoreV1() {
    this.ctx.event.emit('buyAgain');
  }
  /* #endif */

  /* #ifdef weapp */
  @bridge('handleSelfFetchCode', 'process')
  handleSelfFetchCodeV1() {
    this.ctx.event.emit('showSelfFetchDialog');
  }

  @bridge('handleVirtualTicket', 'process')
  handleVirtualTicketV1() {
    this.ctx.event.emit('showVoucherCardDialog');
  }

  @bridge('handleSubscription', 'process')
  handleSubscriptionV1() {
    this.ctx.event.emit('showSubscriptionDialog');
  }

  @bridge('hidePickUpCodeBtn', 'process')
  hidePickUpCodeBtnV1() {
    this.ctx.event.emit('hidePickUpCodeBtn');
  }
  /* #endif */

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    this.ctx.data.disableAutoPopup = false;
    mapCtxData(this, [
      'kdtId',
      'orderNo',
      'orderItems',
      'payResult',
      'hasPaid',
      'mpData',
      'hidePickUpCodeBtn',
    ]);

    this.initEvent();
  }

  beforePageMount() {
    this.store.watch(
      'payResult',
      () => {
        /* #ifdef web */
        this.store.fetchYouzanFollowWhiteList();
        /* #endif */
        this.store.updateBtnList();
      },
      { immediate: true }
    );
  }

  initEvent() {
    mapEvent(this, {
      gotoOrderDetail: () => this.goOrderDetail(),
      gotoIndentorCenter: () => this.gotoIndentorCenter(),
      gotoIndentorShipment: () => this.gotoIndentorShipment(),
      disableAutoPopup: () => this.disableAutoPopup(),
      /* #ifdef web */
      buyAgain: () => this.buyAgain(),
      /* #endif */
    });
  }

  goOrderDetail() {
    const { payResult = {} } = this.ctx.data;
    const { buttonGroup = {} } = payResult;
    const detailUrl = buttonGroup.DETAIL.url;

    /* #ifdef web */
    navigate({
      web: {
        type: 'safeLink',
        safeLink: {
          url: detailUrl,
        },
      },
    });
    /* #endif */

    /* #ifdef weapp */
    const { orderNo } = this.ctx.data;
    const { isVOrder, orderItems, isGroupBuy, activityType } = payResult;
    const firstGoods = (orderItems && orderItems[0]) || {};
    const { goodsType, goodsId } = firstGoods;
    let url = `/packages/trade/order/result/index?orderNo=${orderNo}`;

    if (+goodsType === GOODS_TYPE.MEMBER_CARD) {
      url = `/packages/card/detail/index?goods_id=${goodsId}&orderNo=${orderNo}&guideDialogType=success`;
    } else if (isVOrder) {
      // 目前v单需要跳转订单列表页, 后面需要处理掉这段逻辑,不兼容多店铺场景
      url = '/packages/trade/order/list/index';
    } else if (+goodsType === 206) {
      // 206为买单订单
      url = `/packages/trade/order/unicashier-result/index?order_no=${orderNo}`;
    } else if (isGroupBuy) {
      url = `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}&isPay=true`;
    } else if (isBlindBox(activityType)) {
      url = `/pages/common/webview-page/index?src=${encodeURIComponent(detailUrl || '')}`;
    }

    Tee.$native.redirectTo({ url });
    /* #endif */
  }

  /* #ifdef web */
  buyAgain() {
    const { kdtId, orderNo } = this.ctx.data;

    buyAgain({
      ctx: this.ctx,
      // 迁移h5-trade逻辑，老逻辑没支持直接下单
      type: BTNS_MAP.BUY_AGAIN,
      kdtId,
      orderNo,
      // TODO: 先和PayAction逻辑保持一致，orderItems传个空，只影响埋点字段
      // orderItems: this.orderItems,
      logger: this.ctx.logger,
    });
  }
  /* #endif */

  disableAutoPopup() {
    this.ctx.data.disableAutoPopup = true;
    this.ctx.event.emit('hideSubscriptionDialog');
    this.ctx.event.emit('hideSelfFetchDialog');
    this.ctx.event.emit('hideVoucherCardDialog');
  }

  gotoIndentorCenter() {
    const { payResult = {} } = this.ctx.data;
    const { buttonGroup = {} } = payResult;
    const { url } = buttonGroup.PURCHASE_CENTER;
    navigate({ url });
  }

  gotoIndentorShipment() {
    const { payResult = {} } = this.ctx.data;
    const { buttonGroup = {} } = payResult;
    const { url } = buttonGroup.PURCHASE_TAKE;
    navigate({ url });
  }

  static widgets = {
    PaidAction,
    WishMsgPopup,
    SubscriptionDialog,
    PaidSubscriptionMsg,
    ActivateBenefitcardDialog,
    LongClickSaveImageDialog,
    PaidSubscriptionMsgTips,
  };
}
