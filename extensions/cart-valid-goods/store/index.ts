import { createStore } from '@ranta/store';
import { IStoreObject, mergeStore } from './helper';
import main from './modules/main';
import present from './modules/present';
import edu from './modules/edu';
import goods from './modules/goods';

const rootStore: IStoreObject = [main, present, edu].reduce((a, b) => mergeStore(a, b), {});

const initWatcher = (store) => {
  main.registerWatchs(store);
};

export default function createSkuStore(ctx) {
  const store = createStore({
    state: () => ({
      ...rootStore.state,
      ...goods.state,
    }),
    getters: {
      ...rootStore.getters,
    },
    actions: {
      ...rootStore.actions,
      ...main.getActions(ctx),
      ...present.getActions(ctx),
      ...edu.getActions(),
      ...goods.getActions(ctx),
    },
  });
  initWatcher(store);
  return store;
}
