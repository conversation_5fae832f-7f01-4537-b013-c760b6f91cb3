// Widgets
import Coupon from './Coupon.vue';

// Utils
import createStore from './store';
import { cloudData, isSameObject } from './utils';

// Dependencies
import { mapCtxData, mapStoreToCtx } from '@ranta/store';
import { cloud } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import type { OrderDetailCouponInfo } from '@youzan-cloud/cloud-biz-types';

export default class CouponExtension {
  static widgets = {
    Coupon,
  };

  ctx: any;

  store: any;

  /**
   * couponInfo
   * @desc 优惠券信息
   * @type {CouponInfo}
   */
  @cloud('couponInfo', 'data')
  couponInfo: OrderDetailCouponInfo;

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);

    mapCtxData(this, ['coupon', 'coupons', 'couponCode', 'display', 'rootStyle']);
    mapStoreToCtx(this, ['couponInfo']);
    mapData(this, ['couponInfo'], {
      isSetData: false,
      callback: () => {
        const { couponInfo = {} } = this.ctx.data;
        const newOpenData = {
          couponInfo: cloudData.getCouponInfo({ couponInfo }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
