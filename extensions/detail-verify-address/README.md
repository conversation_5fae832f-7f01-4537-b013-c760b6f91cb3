# @wsc-tee-trade/detail-verify-address

卡券核销地址

![UI呈现](https://img.yzcdn.cn/public_files/e6707ef6da3b83aefe80d46ed2dcbc3c.png)

## 调试方式

需开启 **多网点**

#### 创建商品

- 前往 [商品-商品管理-创建商品](https://dian16911610.shangjia.youzan.com/v4/goods/edit#/)
  - 商品类型：电子卡券
  - 商品名称：xxx 电子卡券商品
  - 商品图片：任选一张
  - 价格：0.01
  - 库存：9999
- 点击 下一步 - 保存并查看

#### 下单

- 找到一个 电子卡券商品
- 商详页 - 点击 立即支付
- 下单页 - 提交订单 - 确认支付
- 支付成功页 - 点击 查看详情
- 订单详情页 - 查看效果

## 存疑

## Widget.Provide

| 名称                    | 说明 |
| ----------------------- | ---- |
| VerifyAddress `default` | ???  |

## Data.Consume

| 名称                  | 类型      | 说明                                      |
| --------------------- | --------- | ----------------------------------------- |
| showVirtualTicket     | _boolean_ | `_global.orderBizExtra.showVirtualTicket` |
| showShopVerifyAddress | _boolean_ | `_global.orderInfo.showShopVerifyAddress` |
| verifyAddressInfo     | ???       | ???                                       |
| env                   | ???       | 环境变量                                  |

## 关键字速查

| 名称 | 说明 |
| ---- | ---- |
|      | ???  |
