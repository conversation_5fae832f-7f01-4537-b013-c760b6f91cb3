/* 订阅消息 */
import { PAGE_ID, BIZ_TYPE, BIZ_TYPE_TO_SCENE } from '../contants';
import { getTemplate } from '../api';

const MemberRepurchaseScene = 'retial_coupon_notice_scene';
// 下单成功通知
const OrderSuccessBySelfFetch = 'order_success_by_self_fetch';
export function initHooks(ctx) {
  ctx.process.define('callHookBeforeEvokeSubscribeDialog', () => {
    const { currentExpressType, isRetailOrderScene } = ctx.data;
    // 如果是24小时货架并且履约方式是快递，则不弹窗直接跳走
    if (isRetailOrderScene && currentExpressType === BIZ_TYPE['express-0']) {
      return Promise.resolve();
    }
    const scene = BIZ_TYPE_TO_SCENE[currentExpressType];
    const promises = [getTemplate({ scene }), getTemplate({ scene: MemberRepurchaseScene })];

    const isSelfFetch = currentExpressType === BIZ_TYPE['self-fetch'];
    // 自提场景替换原来的订阅消息
    promises.unshift(
      isSelfFetch
        ? getTemplate({ scene: OrderSuccessBySelfFetch })
        : ctx.process.invokePipe('queryWechatSubscribeResult', PAGE_ID.ORDER_BUY)
    );
    return Promise.all(promises).then(([templateRes, templateIdRes, templateIdResByCouponScene]) => {
      const templateList = (isSelfFetch ? templateRes?.templateIdList : templateRes?.templateList) || [];
      const { templateIdList = [] } = templateIdRes || {};
      const { templateIdList: templateIdListByCouponScene = [] } = templateIdResByCouponScene || {};

      const templateId = templateIdList.map((id) => ({ templateId: id }));
      return new Promise((resolve) => {
        ctx.process.invokePipe('requestSubscribeMessagePush', {
          templates: templateIdListByCouponScene.concat([...templateId, ...templateList]),
          onFail: resolve,
          onSuccess: resolve,
          onShowTips: () => {
            ctx.data.showSubscribeGuide = true;
          },
          onCloseTips: () => {
            ctx.data.showSubscribeGuide = false;
          },
          onSelfLog: {
            subscribePos: '支付完成后回调',
            subscribeSource: isRetailOrderScene ? '24h_shelf' : 'wx_shop',
            deliveryWay: scene,
          },
        });
      });
    });
  });
}
