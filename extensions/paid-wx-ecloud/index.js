import initProcess from './scss/process';
import openState from './scss/state';

export default class Extension {
  // Tips: 如果未使用 options.ctx，可以移除 constructor 函数
  constructor(options) {
    this.ctx = options.ctx;

    this.destoryProcess = initProcess(this.ctx);
    this.destoryWatch = this.initWatch();
  }

  initWatch() {
    this.unwatchPayResult = this.ctx.watch('payResult', (payResult) => {
      this.payResult = payResult;
      this.assignData();
    });
    this.unwatchOrderNo = this.ctx.watch('orderNo', (orderNo) => {
      this.orderNo = orderNo;
      this.assignData();
    });
    return () => {
      this.unwatchPayResult && this.unwatchPayResult();
      this.unwatchOrderNo && this.unwatchOrderNo();
    };
  }

  assignData() {
    const state = {
      ...this.payResult.payResultVO,
      orderNo: this.orderNo,
    };
    Object.keys(openState).forEach((key) => {
      const ecloudKey = `ecloud:${key}`;
      const oldValue = this.ctx.data[ecloudKey];
      const newValue = openState[key](state);
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        this.ctx.data[ecloudKey] = newValue;
      }
    });
  }

  pageDestroyed() {
    this.destoryWatch();
    this.destoryProcess();
  }
}
