<template>
  <view class="empty-view__container">
    <image
      class="empty-view__container-image"
      :src="image || 'https://img.yzcdn.cn/weapp/wsc/uR7rur.png'"
    ></image>
    <text>{{ text }}</text>
  </view>
</template>

<script>
export default {
  props: {
    text: String,
    image: String,
  },
};
</script>

<style lang="scss" scoped>
.empty-view__container {
  display: flex;
  flex-direction: column;
  align-items: center;

  &-image {
    width: 80px;
    height: 84px;
    margin: 80px 0 20px 0;
  }
}
</style>
