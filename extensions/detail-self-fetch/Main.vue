<template>
  <view v-if="isShowRetailPickUpCode || showSelfFetch">
    <take-goods-code v-if="isShowRetailPickUpCode || selfAutoVerify" />
    <!-- scan-buy ext提供, self-fetch-scan-buy不需要提供数据，因为他是一个widget -->
    <self-fetch-scan-buy v-if="isScanBuyOrder" />
    <self-fetch
      v-else-if="!isShowRetailPickUpCode && selfAutoVerify === false"
      :item-info="itemInfo"
      :rx-status="rxStatus"
      :kdt-id="kdtId"
      :order-no="orderNo"
      :is-period-buy="isPeriodBuy"
      :self-fetch-info="selfFetchInfo"
      :e-show-self-fetch="eShowSelfFetch || cloudConfig.isShowSelfFetch"
    />
  </view>
</template>

<script>
import SelfFetch from './components/SelfFetch';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';
import { object } from '@youzan/tee-util';

export default {
  components: {
    'self-fetch': SelfFetch,
  },

  props: {
    cloudConfig: {
      type: Object,
      default: () => ({
        isShowSelfFetch: false,
      }),
    },
  },

  data() {
    return {
      showSelfFetch: false,
      isScanBuyOrder: false,
      isPeriodBuy: false,
      kdtId: 0,
      orderNo: '',
      selfFetchInfo: {
        address: '',
        lng: '',
        lat: '',
        qrcode: '',
        fetchNo: '',
        time: '',
        fetcher: '',
        name: '',
      },
      // 医药相关使用
      itemInfo: [],
      rxStatus: {},
      orderExtra: {},
      eShowSelfFetch: false,
    };
  },

  computed: {
    selfAutoVerify() {
      return object.get(this.orderExtra, 'SELF_AUTO_VERIFY') === '1';
    },
    isShowRetailPickUpCode() {
      return object.get(this.orderExtra, 'retailPickUpCode');
    },
  },

  created() {
    mapData(this, [
      'showSelfFetch',
      'selfFetchInfo',
      'isScanBuyOrder',
      'orderNo',
      'kdtId',
      'isPeriodBuy',
      'orderExtra',
      'rxStatus',
    ]);

    mapData(this, {
      goods: (val) => {
        this.itemInfo = val;
      },
    });

    mapProcess(this, {
      setShowSelfFetch: (payload) => {
        this.eShowSelfFetch = payload;
      },
    });
  },
};
</script>
