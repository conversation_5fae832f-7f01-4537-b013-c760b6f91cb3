import Toast from '@youzan/vant-tee/dist/toast/toast';

// 按照老数组的顺序，把新优惠券顺序排一下
export function reorderCouponByOrigin(
  newCoupons = [],
  oldCoupons = [],
  logGoodsDecreasedToastExpo = () => {
    // do nothing
  }
) {
  let couponReordered = [];
  // 不修改入参内容
  const newCouponCopy = [...newCoupons];
  let goodsCountDecreased = false;
  oldCoupons.forEach((oldCoup, oldCoupIndex) => {
    const { activityId: oldCoupActivityId } = oldCoup;

    // 看看优惠券顺序有没有变化
    if (
      newCoupons[oldCoupIndex]?.activityId &&
      oldCoupActivityId !== newCoupons[oldCoupIndex]?.activityId
    ) {
      console.log('优惠券顺序发生变化!');
    }

    const newCoupIndex = newCouponCopy.findIndex(
      (item) => item.activityId === oldCoupActivityId && item.ableUse
    );

    if (newCoupIndex > -1) {
      const [newCoup] = newCouponCopy.splice(newCoupIndex, 1);
      if (newCoup.allAbleCartGoods?.length < oldCoup.allAbleCartGoods?.length) {
        goodsCountDecreased = true;
      }
      couponReordered.push(newCoup);
    }
  });
  // 如果新数组长度更长
  if (newCouponCopy.length) {
    couponReordered = [...couponReordered, ...newCouponCopy];
  }

  if (goodsCountDecreased) {
    Toast('因活动规则配置，部分已选中商品无法使用该优惠券', { duration: 1000 });
    logGoodsDecreasedToastExpo();
  }

  return couponReordered;
}
