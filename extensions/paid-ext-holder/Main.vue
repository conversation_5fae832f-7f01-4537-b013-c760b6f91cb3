<template>
  <view>
    <view v-if="showExtHolder" class="ext-holder">
      <!-- 【流量交换活动】在目标店铺的支付成功页展示图文链接 -->
      <flow-change-ump v-if="!isWholesaleOrder" @is-show="onFlowChangeUmpView" />

      <!-- h5才有导购券 -->
      <!-- #ifdef web -->
      <guide-coupon-block v-if="widgetGuideCouponBlockInit" @is-show="onGuideCouponView" />
      <!-- #endif -->

      <!-- 裂变优惠券 -->
      <fission-coupon-ump v-if="isShowFissionCoupon" />

      <!-- h5:没有支付有礼，再展示大数据优惠券 -->
      <!-- #ifdef web -->
      <bigdata-coupon v-else-if="showBigDataCoupon" />
      <!-- #endif -->

      <!-- 支付有礼 -->
      <paid-promotion-banner
        v-if="
          (hasPaid || isWxReceipt) && !canUseTradeUmpV1 && paidPromotion && paidPromotion.detailUrl
        "
      />

      <!-- 周边好物 -->
      <paid-cps-goods-recommend @is-show="onPaidCpsGoodsView" />
    </view>
    <!-- #ifdef weapp -->
    <protocol-popup-weapp />
    <!-- #endif -->
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import { mapData } from '@youzan/ranta-helper-tee';
import MaskBlock from './widgets/components/MaskBlock';
import { mapState, mapActions } from '@ranta/store';

/* #ifdef weapp */
const app = getApp();
/* #endif */

export default {
  components: {
    'mask-block': MaskBlock,
  },
  data() {
    return {
      showExtHolder: false,
      showOfficialAccount: false,
      canUseTradeUmpV1: false,
      widgetGuideCouponBlockInit: false,
      // 只有商家小票场景才展示蒙层
      showMask: false,
      realHeight: 0,
      maskStyle: '',
      kdtId: 0,
      returnUrl: '',
      payResult: {},
      ...mapState(this, [
        'hasPaid',
        'isWxReceipt',
        'inSourcingFission',
        'paidPromotion',
        'showBigDataCoupon',
        'isWholesaleOrder',
        'fissionCoupon',
        'weappImg',
        'webImg',
        'isShowFissionCoupon',
      ]),
    };
  },
  watch: {
    // 如果是批发，则默认隐藏流量互换/周边好物
    isWholesaleOrder: {
      handler(val) {
        if (val) {
          this.store.onFlowChangeUmpView(false);
          this.store.onPaidCpsGoodsView(false);
        }
      },
      immediate: true,
    },
  },
  created() {
    mapData(this, ['canUseTradeUmpV1', 'kdtId', 'returnUrl', 'showExtHolder']);
    mapActions(this, [
      'onFlowChangeUmpView',
      'onPaidCpsGoodsView',
      /* #ifdef web */
      'onGuideCouponView',
      /* #endif */
    ]);

    this.widgetGuideCouponBlockInit = !!this.ctx.widgets.GuideCouponBlock;
    /* #ifdef weapp */
    app.getShopConfigData().then((data = {}) => {
      const showOfficialAccount =
        Tee.$native.canIUse('official-account') && +data.show_follow_mp_after_pay === 1;
      this.showOfficialAccount = showOfficialAccount;
    });
    /* #endif */
  },
};
</script>
