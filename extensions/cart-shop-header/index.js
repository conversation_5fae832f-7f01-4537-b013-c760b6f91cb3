import ShopHeader from './ShopHeader.vue';
import { EDIT_MODE_MAP } from './constants';
import { mapCtxData } from '@ranta/store';
import createStore from './store';

export default class ShopHeaderExtension {
  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);

    // 初始化为结算模式
    this.ctx.data.editMode = EDIT_MODE_MAP.SUBMIT;

    mapCtxData(this, ['editMode', 'shopTitle', 'shopMetaInfo']);
  }

  static widgets = {
    ShopHeader,
  };
}
