<template>
  <van-cell-group custom-class="virtual-ticket" v-if="showVirtualTicket">
    <van-cell>
      <view class="virtual-ticket__header">
        <view class="virtual-ticket__header__title">
          <view class="virtual-ticket__header__title__value">
            {{ virtualTicket.title }}
          </view>
          <view class="virtual-ticket__header__title__label">有效期：{{ virtualTicket.time }}</view>
        </view>
        <view @click="clickQrCode" class="virtual-ticket__header__qrcode" />
      </view>
    </van-cell>

    <van-cell>
      <view class="virtual-ticket__list">
        <view
          v-for="(item, index) in virtualTicket.list"
          :key="item.ticketNo"
          class="virtual-ticket__list__item"
        >
          <view>
            <text>券码{{ index + 1 }}：</text>
            <text
              :class="
                'virtual-ticket__list__item__code virtual-ticket__list__item__code' +
                item.verifyStateDetail.suffix
              "
            >
              {{ item.ticketNo }}
            </text>
          </view>
          <view
            v-if="item.verifyState === 1"
            class="virtual-ticket__list__item__copy"
            @click="onCopy(item.ticketNo)"
          >
            复制
          </view>
          <view v-else class="virtual-ticket__list__item__state">
            {{ item.verifyStateDetail.value }}
          </view>
        </view>
      </view>
    </van-cell>
  </van-cell-group>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import { setClipboardData } from '@youzan/tee-api';
import { buildUrl } from '@youzan/tee-biz-util';
import navigate from '@youzan/tee-biz-navigate';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import args from '@youzan/utils/url/args';
import { mapData } from '@youzan/ranta-helper-tee';
import { formateVirtualTicket } from './utils';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },

  data() {
    return {
      showVirtualTicket: false,
      orderNo: '',
      virtualTicket: {},
    };
  },

  created() {
    mapData(this, ['showVirtualTicket']);
    mapData(
      this,
      {
        virtualTicket: (val) => {
          this.virtualTicket = formateVirtualTicket(val);
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    clickQrCode() {
      const { orderNo } = this.ctx.data;
      const { order } = this.ctx.data;
      let baseWebUrl = `/wsctrade/order/virtualTicket/detail?kdt_id=${order.kdtId}&order_no=${order.orderNo}`;

      /* #ifdef web */
      const kosToken = args.get('kos_token');

      if (kosToken) {
        baseWebUrl += `&kos_token=${encodeURIComponent(kosToken)}`;
      }
      /* #endif */

      navigate({
        url: `/packages/trade/cert/verify-ticket/index?order_no=${orderNo}`,
        type: 'navigateTo',
        web: {
          type: 'safeLink',
          safeLink: {
            url: buildUrl(baseWebUrl, 'h5', order.kdtId),
            kdtId: order.kdtId,
          },
        },
      });
    },

    onCopy(ticket) {
      setClipboardData(ticket).then(() => {
        Toast('复制成功');
      });
    },
  },
};
</script>

<style lang="scss">
.virtual-ticket {
  margin-top: 10px;
  background: #fff;
}

.virtual-ticket__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  text-align: left;
}

.virtual-ticket__header__title__value {
  font-weight: bold;
  color: #111;
}

.virtual-ticket__header__title__label {
  font-size: 10px;
  color: #666;
}

.virtual-ticket__header__qrcode {
  height: 22px;
  width: 22px;
  background-image: url('https://b.yzcdn.cn/fix-base64/e76e5a8715a0f19d4fb38b313828dabf57df7d7c7eee05b222fdd840d4794c99.png');
  background-repeat: no-repeat;
  background-size: 22px 22px;
  background-position: center center;
}

.virtual-ticket__list {
  text-align: left;
}

.virtual-ticket__list__item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.virtual-ticket__list__item:not(:first-child) {
  margin-top: 10px;
}

.virtual-ticket__list__item__code {
  margin-left: 10px;
  font-size: 14px;
  color: #111;
  font-weight: bold;
}

.virtual-ticket__list__item__code--del {
  color: #999;
  text-decoration: line-through;
}

.virtual-ticket__list__item__copy {
  background: #f7f8fa;
  border-radius: 12px;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 12px;
  color: #111;
  text-align: right;
  line-height: 16px;
  padding: 4px 12px;
}

.virtual-ticket__list__item__copy:active {
  background: #ccc;
}

.virtual-ticket__list__item__state {
  font-size: 14px;
}
</style>
