<template></template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
export default {
  data() {
    return {
      goods: [],
      orderNo: '',
      kdtId: '',
      activityType: 0,
    };
  },

  created() {
    mapData(this, ['goods', 'orderNo', 'kdtId', 'activityType']);
  },

  computed: {
    defineEduGrouponLink() {
      const res = {
        goods: this.goods || [],
        orderNo: this.orderNo,
        kdtId: this.kdtId,
        activityType: this.activityType,
      };

      // 查看团详情
      let link = '';
      if (res.goods.length > 0) {
        const goodsItem = res.goods[0] || {};

        if (goodsItem.goodsType == 31) {
          // 知识付费拼团
          const alias = (goodsItem.goodsInfo || {}).alias;
          link = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            `https://h5.youzan.com/wscvis/ump/groupon/groupon-detail?order_no=${res.orderNo}&alias=${alias}&kdt_id=${res.kdtId}&activity_type=${res.activityType}`
          )}`;
        }
      }
      return link;
    },
  },

  watch: {
    defineEduGrouponLink(newValue) {
      console.log('watch getEduGrouponLink', newValue);
      if (newValue) {
        this.ctx.data.grouponLink = newValue;
      }
    },
  },
};
</script>

<style lang="scss"></style>
