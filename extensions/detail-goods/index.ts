import Goods from './Goods.vue';
import SellerRefundRecordPopup from './widgets/SellerRefundRecordPopup.vue';
import type { IGoodsList } from './types';
import type {
  HandleGoodsActionParams,
  OrderDetailBeforeDisableGoodsBtns,
  OrderDetailBeforeHandleGoodsAction,
  OrderDetailGoodsInfo,
} from '@youzan-cloud/cloud-biz-types';
import { cloud, bridge, useAsHook } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import { mapCtxData } from '@ranta/store';
import { cloudData, isSameObject } from './utils';
import createStore from './store';

export default class GoodsExtension {
  ctx: any;

  store: any;

  /**
   * showRefund
   * @deprecated 从 2.0 开始
   * @desc 处理“申请退款”
   */
  @bridge('showRefund', 'process')
  showRefund(itemId: string) {
    this.ctx.process.invoke('refundGoods', itemId);
  }

  /**
   * goodsList
   * @desc 订单商品信息
   * @type {IGoodsList}
   */
  @bridge('goodsList', 'data')
  goodsListV1: IGoodsList;

  /**
   * goodsInfo
   * @desc 商品信息
   * @type {GoodsInfo}
   */
  @cloud('goodsInfo', 'data')
  goodsInfo: OrderDetailGoodsInfo;

  /**
   * handleGoodsAction
   * @desc 触发商品级操作
   * @param {HandleGoodsActionParams} params
   */
  @cloud('handleGoodsAction', 'method', { allowMultiple: true })
  handleGoodsAction(params: HandleGoodsActionParams) {
    this.ctx.process.invoke('handleGoodsAction', params);
  }

  @cloud('beforeHandleGoodsAction', 'hook', { allowMultiple: true })
  @bridge('beforeGoodsBtnClickAsync', 'asyncEvent')
  beforeHandleGoodsAction = useAsHook<OrderDetailBeforeHandleGoodsAction>();

  /**
   * beforeRefundClickAsync
   * @deprecated 从 2.0 开始
   * @desc 申请退款触发前
   */
  @bridge('beforeRefundClickAsync', 'asyncEvent')
  beforeRefundClick =
    useAsHook<(payload: { id: string; orderNo: string; type: 'refund' }) => Promise<void>>();

  /**
   * beforeDisableGoodsBtns
   * @desc 禁用商品级按钮前触发
   */
  @cloud('beforeDisableGoodsBtns', 'hook', { allowMultiple: false })
  beforeDisableGoodsBtns = useAsHook<OrderDetailBeforeDisableGoodsBtns>();

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);

    mapData(this, ['itemList'], {
      callback: () => {
        const { itemList } = this.ctx.data;
        const newOpenData = {
          goodsListV1: cloudData.getGoodsInfo<IGoodsList>({ itemList }, 'v1'),
          goodsInfo: cloudData.getGoodsInfo<OrderDetailGoodsInfo>({ itemList }, 'v2'),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });

    mapCtxData(this, [
      'itemList',
      'orderNo',
      'kdtId',
      'sourceInfo',
      'contact',
      'miniprogram',
      'paymentInfo',
      'env',
      'isHotel',
      'orderExtra',
    ]);

    this.ctx.process.define('refundGoods', (itemId) => {
      this.store.refund(itemId);
    });
    this.ctx.process.define('handleGoodsAction', (params) => this.store.handleGoodsAction(params));
  }

  static widgets = {
    Goods,
    SellerRefundRecordPopup,
  };
}
