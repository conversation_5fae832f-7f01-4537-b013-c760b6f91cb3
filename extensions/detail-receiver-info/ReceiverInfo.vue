<template>
  <view v-if="showReceiverInfo" class="receiver-info">
    <view class="receiver-info-item receiver-hairline--top">
      <view class="custom-receiver-cell">
        <view class="custom-receiver-cell-left">
          <van-icon :name="iconName" color="#969799" />
        </view>
        <view class="custom-receiver-cell-content">
          <view class="custom-receiver-cell-title">
            <text class="text-strong">{{ receiverDetail }}</text>
          </view>
          <view class="custom-receiver-cell-label" v-if="addressDetail">
            {{ addressDetail }}
          </view>
        </view>
        <view
          class="receiver-cell-right"
          v-if="deliveryAllowShow.allowShowModifyAddress"
          @click="handleModifyAddress"
        >
          修改
          <van-icon name="arrow" color="#969799" />
        </view>
      </view>
    </view>
    <view v-if="showDeliveryTime" class="receiver-info-item receiver-hairline--top">
      <view class="custom-receiver-cell">
        <view class="custom-receiver-cell-left">
          <van-icon name="clock-o" color="#969799" />
        </view>
        <view class="custom-receiver-cell-content">
          <view class="custom-receiver-cell-title">
            预计 <text class="text-strong">{{ service.deliveryTime }}</text> 送达
          </view>
        </view>
        <view
          class="receiver-cell-right"
          v-if="deliveryAllowShow.allowShowModifyTimeBuckets"
          @click="handleModifyTime"
        >
          修改
          <van-icon name="arrow" color="#969799" />
        </view>
      </view>
    </view>
    <view class="receiver-info--stripe-border"></view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-icon': Icon,
  },

  data() {
    return {
      receiverInfo: {},
      showReceiverInfo: false,
      showDeliveryTime: false,
      service: {},
      order: {},
    };
  },

  computed: {
    hasDeliveryAddress() {
      const { receiverInfo } = this;
      return receiverInfo && receiverInfo.deliveryAddress;
    },
    iconName() {
      return this.hasDeliveryAddress ? 'location-o' : 'contact';
    },
    addressDetail() {
      return this.hasDeliveryAddress ? `收货地址：${this.receiverInfo.deliveryAddress}` : '';
    },
    receiverDetail() {
      const { receiverLabel, receiverName, receiverTel } = this.receiverInfo;
      return `${receiverLabel}：${receiverName} ${receiverTel}`;
    },
    deliveryAllowShow() {
      return this.order.deliveryAllowShow || {};
    },
  },

  created() {
    mapData(this, ['receiverInfo', 'showReceiverInfo', 'showDeliveryTime', 'service', 'order']);
  },
  methods: {
    handleModifyAddress() {
      const { allowShowModifyTimeBuckets } = this.deliveryAllowShow;
      if (allowShowModifyTimeBuckets) {
        this.ctx.data.showReceiverModifyPopup = true;
      } else {
        this.ctx.data.showAddressModifyPopup = true;
      }
    },
    handleModifyTime() {
      const { allowShowModifyAddress } = this.deliveryAllowShow;
      if (allowShowModifyAddress) {
        this.ctx.data.showReceiverModifyPopup = true;
      } else {
        this.ctx.data.showTimeModifyPopup = true;
      }
    },
  },
};
</script>

<style lang="scss">
$text-color: #323233;
$gray-darker: #646566;

.receiver-info {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 2px 16px;
  background: #fff;

  &-item {
    position: relative;
    padding: 12px 0;
    display: flex;
  }

  .receiver--cell-label {
    color: $gray-darker;
  }

  .receiver-cell-right {
    font-size: 14px;
    padding-left: 12px;
  }

  .text-strong {
    font-weight: 500;
  }

  .custom-receiver-cell {
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding: 0;

    &-disabled {
      opacity: 0.6;
    }

    &-left {
      margin-right: 8px;
      flex-shrink: 0;
    }

    &-content {
      flex: 1;
      min-width: 0;
    }

    &-title {
      font-size: 14px;
      color: $text-color;
      line-height: 20px;
    }

    &-label {
      font-size: 12px;
      color: $gray-darker;
      line-height: 16px;
      margin-top: 2px;
    }
  }
}

.receiver-info--stripe-border::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: url('https://b.yzcdn.cn/v2/image/wap/address/<EMAIL>');
  background-size: 36px 2px;
}

.receiver-hairline--top {
  &::before {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-bottom: 1px solid #ebedf0;
  }
}
</style>
