import MainWidget from './Main.vue';
import ActionBtn from './widgets/ActionBtn.vue';
import AddressBar from './widgets/AddressBar.vue';
import GiftGoods from './widgets/GiftGoods.vue';
import MessageInput from './widgets/MessageInput.vue';
import UserList from './widgets/UserList.vue';
import { ZNB } from '@youzan/tee-biz-navigate';
import getUniquePageKey from '@youzan/decorate-tee/src/common/utils/unique';
import TeeEvent from '@youzan/tee-event';

export default class Extension {
  ctx: any;

  static widgets = {
    Main: MainWidget,
    ActionBtn,
    AddressBar,
    GiftGoods,
    MessageInput,
    UserList,
  };

  constructor(options) {
    this.ctx = options.ctx;
  }

  async beforePageMount() {
    /* #ifdef web */
    // @ts-ignore
    ZNB.init({
      // @ts-ignore
      kdtId: window._global?.kdtId,
      // @ts-ignore
    }).catch((e) => {
      console.log(e);
    });
    /* #endif */
  }

  onShareAppMessage() {
    const config = this.ctx.data.shareConfig;
    return config;
  }

  onPageScroll(ev) {
    const eventKey = 'onPageScroll' + getUniquePageKey();
    TeeEvent.trigger(eventKey, ev);
  }
}
