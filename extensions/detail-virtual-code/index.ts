import VirtualCode from './VirtualCode.vue';
import { cloud } from '@youzan/ranta-helper';
import type { VirtualCodeData } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class VirtualCodeExtension {
  ctx: any;

  /**
   * virtualCode
   * @desc 虚拟商品
   * @type {VirtualCode}
   */
  @cloud('virtualCode', 'data')
  virtualCode: VirtualCodeData;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['qrCode'], {
      callback: () => {
        const { qrCode } = this.ctx.data;
        const newOpenData = {
          virtualCode: cloudData.getVirtualCode({ qrCode }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    VirtualCode,
  };
}
