<template>
  <view class="bottom-bar" :style="wrapperStyle">
    <submit />
  </view>
</template>

<script>
import { mapState } from '@ranta/store';

export default {
  data() {
    return {
      ...mapState(this, ['isTabPage']),
    };
  },
  computed: {
    wrapperStyle() {
      const { navHeight } = this.ctx.data;
      const bottom = this.isTabPage ? (navHeight > 0 ? navHeight : 49) : 0;
      return `bottom: ${bottom}px;`;
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-bar {
  position: fixed;
  bottom: 0;
  z-index: 10;
  width: 100%;
}
</style>
