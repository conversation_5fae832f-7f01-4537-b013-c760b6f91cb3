import MainWidget from './Main.vue';
import Tee from '@youzan/tee';

export default class Extension {
  // Tips: 如果未使用 options.ctx，可以移除 constructor 函数
  constructor(options) {
    this.ctx = options.ctx;
  }

  static widgets = {
    Main: MainWidget,
  };

  onPageShow() {
    // 微信7.0.7版本起可使用
    if (Tee.$native.canIUse('hideHomeButton')) {
      Tee.$native.hideHomeButton();
    }
    this.ctx.event.emit('onPageShow');
  }
}
