import Toast from '@youzan/vant-tee/dist/toast/toast';
import { cdnImage } from '@youzan/tee-biz-util';
import { getSelfFetchMessage, getSelfFetchPoster } from '../../../utils/self-fetch';
import { getPosterAndDownload } from '../../../utils/poster-helper';
import { ISelfFetchCodeResponse } from '../../../types';

export default function (ctx: any) {
  return {
    initSelfFetchData(selfFetch) {
      this.selfFetch = {
        ...selfFetch,
        selfFetchNo: this.filterSelfFetchNo(selfFetch.selfFetchNo ?? ''),
        loading: !selfFetch.selfFetchQRCode,
      };
    },
    requestErrorHandler() {
      // 轮询接口，响应后每秒一次，最多3次
      this.retryTimes += 1;
      if (this.retryTimes < 3) {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            this.selfFetchMessage().then(resolve).catch(reject);
          }, 1000);
        });
      }
      this.selfFetch = { hasError: true };
      return Promise.reject();
    },

    selfFetchMessage() {
      return getSelfFetchMessage({
        requestNo: this.orderNo,
        phase: ctx.data.phasePaymentStage,
      })
        .then((res: ISelfFetchCodeResponse) => {
          if (res && res?.selfFetchQRCode) {
            const defaultImage = cdnImage(
              'public_files/2019/08/06/3f099aa967abd3821d610344d7ba3729.jpg'
            );
            const items = ctx.data.orderItems.map((item) => {
              return {
                title: item.title, // 商品标题
                num: item.num, // 商品数量
                sku: item.skuItems.map((sku) => `${sku.k}：${sku.v}`).join('；') || '默认规格', // sku信息, 如黄色：250;
                imgUrl: item.imgUrl || defaultImage, // 商品图片地址
              };
            });
            // eslint-disable-next-line no-return-assign
            return (this.selfFetch = {
              ...res,
              selfFetchNo: this.filterSelfFetchNo(res.selfFetchNo),
              items,
              loading: false,
            });
          }
          return this.requestErrorHandler();
        })
        .catch(this.requestErrorHandler);
    },

    filterSelfFetchNo(selfFetchNo) {
      return selfFetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3');
    },

    // 保存提货码
    saveSelfFetchPoster() {
      if (this.selfFetch.hasError) {
        return Toast({
          message: `${this.selfFetchCodeName}生成失败，请稍后再试或前往订单详情查看`,
          icon: 'none',
        });
      }
      const {
        province,
        city,
        county,
        addressDetail,
        userTime,
        selfFetchQRCode,
        selfFetchNo,
        // items
      } = this.selfFetch;

      const { orderItems = [] } = ctx.data;
      const posterItems = orderItems.map((order) => {
        return {
          title: order.title, // 商品标题
          num: order.num, // 商品数量
          sku: order.skuItems?.map((sku) => `${sku.k}：${sku.v}`).join('；') || '默认规格', // sku信息, 如黄色：250;
          imgUrl: order.imgUrl, // 商品图片地址
        };
      });
      const options = {
        type: this.isScanBuyOrder ? 'self-fetch-scan-buy' : '',
        fetchAddress: province + city + county + addressDetail, // 自提地址
        fetchTime: userTime, // 自提时间
        items: posterItems.slice(0, 3), // 商品列表
        itemsLength: posterItems.length,
        qrcode: selfFetchQRCode, // 提货二维码
        number: selfFetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3'), // 提货码
      };
      getPosterAndDownload({
        needRetry: true,
        getPoster() {
          return getSelfFetchPoster(options);
        },
        onSuccess(imgUrl) {
          /* #ifdef web */
          ctx.event.emit('showSaveImageDialog', imgUrl);
          /* #endif */
        },
      });
    },
  };
}
