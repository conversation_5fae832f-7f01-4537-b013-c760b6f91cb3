export function getSkuStr(sku) {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    console.warn(e);
  }
  return parsedSku
    .map((item) => {
      return item.v;
    })
    .join('，');
}

export const formatSku = (sku) => {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    parsedSku = [];
    console.warn(e);
  }

  return parsedSku;
};

/*
  normalize sku tree

  [
    {
      count: 2,
      k: "品种", // 规格名称 skuKeyName
      k_id: "1200", // skuKeyId
      k_s: "s1" // skuKeyStr
      v: [ // skuValues
        { // skuValue
          id: "1201", // skuValueId
          name: "萌" // 具体的规格值 skuValueName
        }, {
          id: "973",
          name: "帅"
        }
      ]
    },
    ...
  ]
                |
                v
  {
    s1: [{
      id: "1201",
      name: "萌"
    }, {
      id: "973",
      name: "帅"
    }],
    ...
  }
 */
