/* 等待制作时长 */
import { getPendingOrderWaitingProgress } from '../api';
import { BIZ_TYPE } from '../contants';
import WaitingProcess from '../widgets/WaitingProcess.vue';

export const widgets = {
  WaitingProcess,
};

export function initData(ctx) {
  // 订单等待进度数据对象
  ctx.data.processData = {};
}

let cacheExpress, cacheKdtId;
export function initWaitingProgress(ctx, { currentExpressType, selfFetchShopId } = {}) {
  const {
    warehouseId,
    goodsInfoList,
    isSupplyMode,
  } = ctx.data;
  if (currentExpressType) cacheExpress = currentExpressType;
  if (selfFetchShopId) cacheKdtId = selfFetchShopId;
  if (!goodsInfoList.length) {
    ctx.data.processData = {};
    return;
  }
  if (cacheExpress === 1 && cacheKdtId) {
    getPendingOrderWaitingProgress({
      warehouseId: cacheKdtId,
      bizType: BIZ_TYPE['self-fetch'],
      goodsInfoList,
    }).then((data) => {
      ctx.data.processData = data;
    });
  } else if ([0, 2].includes(cacheExpress)) {
    if ((isSupplyMode && !warehouseId)) {
      ctx.data.processData = {};
      return;
    }
    getPendingOrderWaitingProgress({
      warehouseId: +warehouseId,
      bizType: BIZ_TYPE[`express-${cacheExpress}`],
      goodsInfoList,
    }).then((data) => {
      ctx.data.processData = data;
    });
  }
}
