<template>
  <van-popup
    :show="visible"
    custom-class="dd-popup"
    overlay-style="z-index: 97 !important;"
    safe-area-inset-bottom
    closeable
    position="bottom"
    @close="onClose"
  >
    <view class="dd-popup__title">优惠明细</view>
    <view class="dd-popup__body">
      <view class="dd-popup__price">
        <view class="dd-popup__left">商品总额</view>
        <view class="dd-popup__right">{{ goodsTotalPriceFormat }}</view>
      </view>
      <view class="dd-popup__price margin-bottom">
        <view class="dd-popup__left">共优惠</view>
        <view class="dd-popup__right discount-amount">
          <text v-if="isBest" class="tag">最佳优惠</text>
          -￥{{ totalDiscountFormat }}
        </view>
      </view>

      <view
        class="dd-popup__item grey-dark"
        v-for="item in promotionDetailsFormat"
        :key="item.appType"
      >
        <view class="dd-popup__left">{{
          item.appName === '优惠卡券'
            ? '优惠券'
            : item.appType === 115
            ? item.promotionTag || item.appName
            : item.appName
        }}</view>
        <view class="dd-popup__right">-￥{{ item.preferentialValue }}</view>
      </view>

      <view v-if="isBest" class="dd-change" @click="select">
        <view class="dd-change_title">更换活动</view>
        <view class="dd-change_icon">
          <view class="dd-change_content">{{ recommendPromotion }}</view>
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="dd-popup__total">
        <view class="dd-popup__left font-medium">合计</view>
        <view class="dd-popup__right font-semibold">
          <text>{{ totalPriceFormat }}</text>
        </view>
      </view>
      <view class="dd-popup__desc">以上优惠不包含运费，实际优惠金额请以确认订单页为准</view>
    </view>
  </van-popup>
</template>
<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import { formatTotalPrice } from '@youzan/wsc-tee-trade-common/lib/order-utils/format';

export default {
  name: 'discount-detail',

  components: {
    'van-icon': Icon,
    'van-popup': Popup,
  },

  props: {
    totalPrice: {
      type: Number,
      default: 0,
    },
    totalDiscount: {
      type: Number,
      default: 0,
    },
    promotionDetails: {
      type: Array,
      default: () => [],
    },
    goodsTotalPrice: {
      type: Number,
      default: 0,
    },
    visible: Boolean,
    promotionInfo: {
      type: Object,
      default: () => {},
    },
    pointsPrice: {
      type: Number,
      default: 0,
    },
    pointsName: {
      type: String,
      default: '积分',
    },
  },

  computed: {
    goodsTotalPriceFormat() {
      return formatTotalPrice(this.goodsTotalPrice, this.pointsPrice, this.pointsName);
    },
    totalDiscountFormat() {
      return this.toFixedPrice(this.totalDiscount);
    },
    promotionDetailsFormat() {
      return this.promotionDetails.map((item) => ({
        ...item,
        preferentialValue: this.toFixedPrice(item.preferentialValue),
      }));
    },
    totalPriceFormat() {
      return formatTotalPrice(this.totalPrice, this.pointsPrice, this.pointsName);
    },
    isBest() {
      const { promotionInfo } = this;
      // activityId存在并且不为0时 表示存在可指定活动 现在已经是最佳优惠 后端约定
      return !!promotionInfo.activityId;
    },
    recommendPromotion() {
      const { promotionInfo } = this;
      return promotionInfo.promotionTag;
    },
  },

  methods: {
    onClose() {
      this.$emit('click-discount-detail', false);
    },
    toFixedPrice(price) {
      return (price / 100).toFixed(2);
    },
    select() {
      this.$emit('select');
    },
  },
};
</script>
<style lang="scss">
$gray-dark: #969799;
.dd-popup {
  padding: 16px;
  border-top-left-radius: var(--theme-radius-dialog, 16px);
  border-top-right-radius: var(--theme-radius-dialog, 16px);
  box-sizing: border-box;
  margin-bottom: calc(56px + env(safe-area-inset-bottom));
  z-index: 98 !important;

  &__title {
    font-family: PingFangSC-Medium, sans-serif;
    text-align: center;
    font-size: 16px;
    color: #323233;
    padding-bottom: 27px;
  }

  &__body {
    overflow: auto;
    box-sizing: border-box;
    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去122px（标题栏和行动按钮栏高度和）,
    min-height: calc(50vh - 122px);
    min-height: calc(50vh - 122px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 122px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 122px);
    max-height: calc(80vh - 122px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 122px - env(safe-area-inset-bottom));
    padding-bottom: calc(56px + env(safe-area-inset-bottom));
  }

  &__price {
    font-size: 16px;
    color: #323233;
    display: flex;
    line-height: 22px;
    margin-bottom: 24px;
  }
  .margin-bottom {
    margin-bottom: 8px;
  }
  &__left {
    flex: 1;
    justify-content: flex-start;
    font-size: 14px;
    span {
      font-family: PingFangSC-Medium, sans-serif;
      color: #ee0a24;
      line-height: 20px;
    }
  }

  &__right {
    flex: 1;
    justify-content: flex-end;
    text-align: right;
    font-weight: var(--theme-common-price-font-weight, 600);
    font-family: Avenir;
  }
  .discount-amount {
    display: flex;
    align-items: center;
    color: var(--ump-icon, #323233);

    .tag {
      background-color: var(--ump-tag-bg, #fff);
      border-radius: 2px;
      padding: 0 4px;
      line-height: 18px;
      margin-right: 8px;
      font-size: 12px;
    }
  }
  &__item {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 14px;
    color: #323233;
    display: flex;
    margin-bottom: 12px;
  }
  .grey-dark {
    color: $gray-dark;
  }
  &__total {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 14px;
    color: #323233;
    display: flex;
    margin-top: 24px;
    line-height: 22px;
    margin-bottom: 8px;
  }

  &__desc {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 12px;
    color: $gray-dark;
    letter-spacing: 0;
    line-height: 16px;
  }
  .divider {
    position: relative;
    margin-top: 12px;
    margin-bottom: 8px;
    &::before {
      margin-right: 0;
    }
    &::after {
      margin-left: 0;
    }
  }
  .font-medium {
    font-family: PingFangSC-Medium, sans-serif;
  }
  .font-semibold {
    font-family: Avenir;
    font-size: 16px;
    font-weight: 600;
  }

  .arrow-up-divider::before,
  .arrow-up-divider::after {
    content: '';
    position: absolute;
    left: 6%;
    bottom: 0;
    width: 0;
    height: 0;
    margin-left: -5px;
    border: 5px transparent solid;
  }

  .arrow-up-divider::before {
    border-bottom-color: #ebedf0;
    opacity: 0.5;
  }
  .arrow-up-divider::after {
    border-width: 3px;
    margin-left: -3px;
    border-bottom-color: #fff;
  }

  .t-popup__close-icon {
    font-size: 20px;
    position: absolute;
  }
}
.dd-change {
  position: relative;
  display: flex;
  height: 36px;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  background-color: #f5f7f9;
  color: #333;
  font-size: 12px;
  font-weight: 400;
  border-radius: 8px;

  &_title {
    width: 80px;
  }

  &_icon {
    flex: 1;
    display: flex;
    color: #969799;
    justify-content: flex-end;
    align-items: baseline;
    overflow: hidden;
  }

  &_content {
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 4px;
  }
}
</style>
