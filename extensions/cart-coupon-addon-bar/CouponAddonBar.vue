<template>
  <view :style="themeStyle">
    <view v-if="addOnCopywriting && hasCheckedGoods" class="coupon-addon" @click="onPopupOpen">
      <view class="coupon-addon__title" v-if="isVip">{{ vipText }}</view>
      <view class="coupon-addon__title" v-else>{{ addOnPromotionStr }}</view>
      <view class="coupon-addon__text">
        {{ addOnCopywriting }}
      </view>
      <view v-if="couponAddOnData.actionTagStr" class="coupon-addon__action">
        {{ couponAddOnData.actionTagStr }}
        <van-icon class="t-icon" name="arrow" />
      </view>
    </view>
    <coupon-addon-popup
      v-if="isDirty || showPopup"
      :kdt-id="kdtId"
      :show-popup="showPopup"
      :coupon-list="coupons"
      :theme-colors="themeColors"
      :loggers="loggers"
      :is-pd-live="isPdLive"
      @close="onPopupClose"
      @sendCoupon="handleSendCoupon"
      @clickItem="onClickItem"
      @to-navigate="toNavigate"
      @launchFastJoinSDK="launchFastJoinSDK"
    />
  </view>
</template>
<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { sendCoupon } from './api';
import CouponAddonPopup from './components/CouponAddonPopup';
import getLoggers from './log';
import { mapState, mapActions } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper-tee';
import Tee from '@youzan/tee';
import { errorToast } from '@youzan/tee-biz-util';

export const GUIDE_TYPE = {
  FREE: 1,
  PAID: 2,
  CARD: 3,
};

export default {
  components: {
    'van-icon': Icon,
    'van-toast': VanToast,
    'coupon-addon-popup': CouponAddonPopup,
  },
  data() {
    return {
      loggers: {},
      isDirty: false,
      showPopup: false,
      memberConfig: {},
      ...mapState(this, [
        'kdtId',
        'couponAddOnData',
        'originData',
        'canUseCouponAddOn',
        'themeColors',
        'shopCart',
        'addOnCopywriting',
        'addOnPromotionStr',
        'hexToRgb',
        'couponNotFullyFetched',
        'themeStyle',
        'shopCart',
        'coupons',
        'hasCheckedGoods',
      ]),
      isPdLive: false,
    };
  },
  computed: {
    isVip() {
      const barItem = this.couponAddOnData?.addOnVoucherDetailDTOList?.[0];
      return [GUIDE_TYPE.FREE, GUIDE_TYPE.PAID].includes(
        barItem?.optimalApplicableLimitIdentityType
      );
    },
    vipText() {
      return `${this.memberConfig.name}专享`;
    },
  },
  async created() {
    const userInfo = (await this.ctx.lambdas?.getUserInfo?.()) || {};
    const { buyerId } = userInfo;
    this.loggers = getLoggers({
      buyerId,
      Logger: this.ctx.logger,
    });
    this.initActions();
    this.initLogger(this.loggers);
    mapData(this, ['memberConfig']);

    /* #ifdef web */
    // 私域直播间来源的购物车，不透出跳转营销页
    this.isPdLive = !!window._global?.pdlive;
    /* #endif */
  },
  destroyed() {
    // 清除本地缓存
    this.clearCouponOriginOrder();
    this.loggers.destroy();
  },
  methods: {
    initActions() {
      mapActions(this, ['clearCouponOriginOrder', 'getCouponAddon', 'initLogger']);
    },
    updateSelectGoods(...args) {
      return this.ctx.process?.invokePipe('updateSelectGoods', ...args);
    },
    launchFastJoinSDK(data) {
      this.ctx.process.invoke('launchFastJoinSDK', {
        hiddenToast: true,
        successCallback: () => {
          this.handleSendCoupon({ ...data.coupon, fastJoin: true });
        },
      });
    },
    onClickItem({ item, coupon }) {
      const { selectState } = item;

      this.loggers.logGoodsClick({
        goodsId: item.goodsId,
        activityId: coupon.activityId,
        type: selectState ? 'unselect' : 'select',
      });

      this.updateSelectGoods({
        type: selectState ? 'remove' : 'add',
        goods: item,
      }).then(() => {
        this.ctx.event.emit('updateCartGoodsList');
      });
    },
    onPopupClose() {
      this.showPopup = false;
      // 清除本地缓存
      this.clearCouponOriginOrder();
      if (this.couponNotFullyFetched) {
        Toast('你有可用的优惠券还未领取');
      }
    },
    onPopupOpen() {
      if (!this.couponAddOnData.actionTagStr) return;
      this.loggers.logGatherBarClick(this.couponAddOnData.actionTagStr);
      this.loggers.batchLogGoodsExpo(this.couponAddOnData.addOnVoucherDetailDTOList);
      this.showPopup = true;
      this.isDirty = true;
    },
    handleSendCoupon(coupon) {
      sendCoupon({ alias: coupon.alias })
        .then((res) => {
          const couponId = res?.couponId;
          this.loggers.logCouponGetClick({
            activityId: coupon.activityId,
            couponId,
          });
          if (couponId) {
            Toast(coupon.fastJoin ? '已成功入会并领券' : '领取成功');
            this.getCouponAddon(this.kdtId);
          } else {
            Toast(
              coupon.fastJoin
                ? `入会成功，但领券失败，${res.msg || '请稍后重试'}`
                : res.msg || '领取失败'
            );
          }
        })
        .catch((err) => {
          errorToast(err, { message: '领取失败' });
        })
        .finally(() => {
          if (coupon.fastJoin) {
            setTimeout(() => {
              /* #ifdef web */
              window.location.reload();
              /* #else */
              Tee.$native.startPullDownRefresh();
              /* #endif */
            }, 500);
          }
        });
    },
    toNavigate(params) {
      this.ctx.process.invoke('navigateFromCart', params);
    },
  },
};
</script>
<style lang="scss">
.coupon-addon {
  background: #fff;
  margin-top: var(--theme-page-card-margin-top, 0);
  margin-bottom: var(--theme-page-card-margin-bottom, 12px);
  margin-right: var(--theme-page-card-margin-right, 12px);
  margin-left: var(--theme-page-card-margin-left, 12px);
  border-radius: var(--theme-page-card-border-radius, 8px);
  padding: var(--theme-page-card-padding-top, 12px) var(--theme-page-card-padding-right, 12px)
    var(--theme-page-card-padding-bottom, 12px) var(--theme-page-card-padding-left, 12px);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &__title {
    border-radius: var(--theme-radius-tag, 2px);
    color: var(--theme-tag-ump-color, #323233);
    border: 1px solid var(--ump-border, transparent);
    background-color: var(--theme-tag-ump-bg-color, #f2f2ff);
    font-family: PingFangSC-Regular, sans-serif;
    font-size: var(--eo-font-size-12, 12px);
    line-height: 20px;
    padding: 0 6px;
    flex-shrink: 0;
  }

  & > &__action {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: var(--eo-font-size-12, 12px);
    line-height: 18px;
    display: flex;
    align-items: center;
    color: var(--ump-icon, #323233);
    flex-shrink: 0;

    .t-icon {
      margin-left: 4px;
    }
  }

  & > &__text {
    flex-grow: 1;
    font-family: PingFangSC-Regular, sans-serif;
    font-size: var(--eo-font-size-12);
    color: #323233;
    line-height: 20px;
    padding: 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
