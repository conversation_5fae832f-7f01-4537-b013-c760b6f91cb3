<template>
  <view v-if="retailPickUpCode || (isRetailMinappShelfOrder && takeGoodsCode)" class="take-goods-code">
    <van-cell>
      <view class="take-goods-code__title">
        {{ retailPickUpCode ? '取餐号' : '取货口令' }}
      </view>
      <view class="take-goods-code__content">{{ retailPickUpCode || takeGoodsCode }}</view>
      <view v-if="!processData.needDisplay" class="take-goods-code__tip">
        请凭取货口令前往柜台取货
      </view>
      <waiting-process />
    </van-cell>
    <self-fetch-info
      v-if="!retailPickUpCode"
      :self-fetch-info="selfFetchInfo"
      :miniprogram="miniprogram"
      :order-no="orderNo"
      :kdt-id="kdtId"
      @onModifyTime="handleModifyTime"
    />
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { object } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';
import SelFetchInfo from './SelFetchInfo';

export default {
  components: {
    'van-cell': Cell,
    'self-fetch-info': SelFetchInfo,
  },

  data() {
    return {
      orderMark: '',
      orderExtra: {},
      selfFetchInfo: {
        address: '',
        lng: '',
        lat: '',
        qrcode: '',
        fetchNo: '',
        time: '',
        fetcher: '',
      },
      processData: {},
      isRetailOrder: false,
      sourceInfo: {},
      miniprogram: {},
      order: {},
    };
  },

  computed: {
    isRetailMinappShelfOrder() {
      // return 'retail_minapp_shelf' === this.orderMark;
      return this.isRetailOrder && this.sourceInfo?.originSource?.isOnlineOrder;
    },
    retailPickUpCode() {
      return object.get(this.orderExtra, 'retailPickUpCode')
    },
    takeGoodsCode() {
      return object.get(this.orderExtra, 'PICK_UP_CODE');
    },
  },

  created() {
    mapData(this, [
      'orderMark',
      'selfFetchInfo',
      'orderExtra',
      'processData',
      'isRetailOrder',
      'sourceInfo',
      'miniprogram',
      'orderNo',
      'kdtId',
      'order',
    ]);
  },
  methods: {
    handleModifyTime() {
      this.ctx.data.showTimeModifyPopup = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.take-goods-code {
  &__title {
    font-weight: bold;
    color: #323233;
    line-height: 20px;
    padding-bottom: 4px;
    text-align: center;
  }

  &__content {
    font-weight: bolder;
    font-size: 34px;
    color: #323233;
    line-height: 44px;
    padding-bottom: 4px;
    text-align: center;
  }

  &__tip {
    font-size: 12px;
    color: #969799;
    line-height: 18px;
    text-align: center;
  }
}
</style>
