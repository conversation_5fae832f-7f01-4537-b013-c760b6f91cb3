<template>
  <van-dialog
    class="wait-buy"
    use-slot
    title="你还有以下商品待结算"
    :show="show"
    :show-cancel-button="true"
    confirm-button-text="继续结算"
    @confirm="onConfirm"
  >
    <view class="content">
      <view v-for="(item, index) in sliceArray" :key="index" class="img">
        <image :src="item.imgUrl" mode="aspectFit" class="img__image" />
      </view>
      <view v-if="goodsList.length > 3" class="ellipsis">...</view>
      <view class="count">等{{ goodsList.length }}件</view>
    </view>
  </van-dialog>
</template>

<script>
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import Tee from '@youzan/tee';

import { generateBuyGoodsList } from './util';

const app = getApp();
export default {
  components: {
    'van-dialog': VanDialog,
  },

  data() {
    return {
      waitBuyDialog: {},
      show: false,
      goodsList: [],
      sliceArray: [],
    };
  },

  created() {
    mapData(this, {
      waitBuyDialog: (value = {}) => {
        this.waitBuyDialog = value;
        this.show = value.show || false;
        this.goodsList = value.goodsList || [];
        this.sliceArray = (value.goodsList || []).slice(0, 3);
      },
    });
  },
  methods: {
    onConfirm() {
      const { goodsList = [], expressType = '' } = this.waitBuyDialog;

      const dbid = app.db.set({
        type: 'goods',
        goods_list: generateBuyGoodsList(goodsList),
        expressTypeChoice: expressType,
      });

      Tee.navigate({
        url: '/packages/order/index?orderFrom=cart&dbid=' + dbid,
      });
      this.ctx.data.waitBuyDialog = {
        ...this.ctx.data.waitBuyDialog,
        show: false,
      };
    },
  },
};
</script>

<style lang="scss">
.wait-buy {
  width: 315px;
}

.img {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  outline: 1px solid rgba(0, 0, 0, 0.05);
  margin-right: 8px;
  vertical-align: middle;

  &__image {
    width: 56px;
    height: 56px;
  }
}

.content {
  overflow: hidden;
  padding: 20px 30px;
}

.ellipsis {
  font-size: 16px;
  color: #969799;
  display: inline-block;
  margin-right: 8px;
}

.count {
  display: inline-block;
  font-size: 14px;
  color: #969799;
  line-height: 56px;
}
</style>
