// Widgets
import Price from './Price.vue';
import ExtraFees from './widgets/extra-fees/index.vue';

// Utils
import createStore from './store';
import type { OrderDetailPayment } from '@youzan-cloud/cloud-biz-types';
import { cloudData, isSameObject } from './utils';

// Dependencies
import { cloud } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';

export default class PriceExtension {
  static widgets = {
    Price,
    ExtraFees,
  };

  ctx: any;

  store: any;

  /**
   * price
   * @desc 计费
   * @type {Price}
   */
  @cloud('payment', 'data')
  payment: OrderDetailPayment;

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.data.extraPrices = [];
    this.store = createStore(this.ctx);

    mapCtxData(this, ['order', 'payment', 'goods', 'buyWayDetailUrl', 'showPostage']);
    mapStoreToCtx(this, ['price', 'extraPrices']);
    mapData(this, ['price', 'payment', 'extraPrices'], {
      isSetData: false,
      callback: () => {
        const { order = {}, price = {}, payment = {}, extraPrices = [] } = this.ctx.data;
        const newPrice = cloudData.getPayment({ order, price, payment, extraPrices });
        if (!isSameObject(newPrice, this.payment)) {
          this.payment = newPrice;
        }
      },
    });
  }
}
