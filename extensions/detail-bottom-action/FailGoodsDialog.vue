<template>
  <van-dialog
    :show="show"
    use-slot
    title="以下商品不支持“再来一单”"
    confirm-button-text="知道了"
    @close="onClose"
    @confirm="onConfirm"
  >
    <view class="cap-fail-good">
      <view
        v-for="(item, index) in goods"
        :key="index"
        class="cap-fail-good__item t-hairline--bottom"
      >
        <p class="cap-fail-good__item__title t-ellipsis">
          {{ item.title }}
        </p>
        <text class="cap-fail-good__item__num"> X{{ item.num }} </text>
        <p v-if="item.sku" class="cap-fail-good__item__sku">
          {{ item.sku }}
        </p>
      </view>
    </view>
  </van-dialog>
</template>

<script>
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';

export default {
  name: 'fail-goods-dialog',

  components: {
    'van-dialog': VanDialog,
  },
  props: {
    show: <PERSON><PERSON><PERSON>,
    goods: Array,
  },
  methods: {
    onClose() {
      this.$emit('close');
    },
    onConfirm() {
      this.$emit('confirm');
    },
  },
};
</script>

<style lang="scss">
.cap-fail-good {
  padding: 10px 20px;

  &__item {
    position: relative;
    padding: 10px 0;
    font-size: 12px;
    color: #666;

    &:last-child::after {
      display: none;
    }

    &__title {
      padding-right: 60px;
      font-size: 14px;
      color: #333;
    }

    &__num {
      position: absolute;
      top: 13px;
      right: 10px;
    }

    &__sku {
      margin-top: 6px;
    }
  }
}
</style>
