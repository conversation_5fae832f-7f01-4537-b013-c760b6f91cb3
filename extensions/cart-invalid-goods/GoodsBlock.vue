<template>
  <view class="goods-item-block custom-class">
    <van-swipe-cell :right-width="64" async-close @close="handleItemDelete">
      <goods :goods="goods" @to-goods-detail="toGoodsDetail" />
      <view slot="right" class="delete-btn">删除</view>
    </van-swipe-cell>
  </view>
</template>

<script>
import SwipeCell from '@youzan/vant-tee/dist/swipe-cell/index.vue';

export default {
  components: {
    'van-swipe-cell': SwipeCell,
  },
  props: {
    goods: Object,
    themeGeneralColor: String,
  },

  externalClasses: ['custom-class'],

  methods: {
    handleItemDelete({ instance, position }) {
      if (['cell', 'left', 'outside'].includes(position)) return instance.close();
      this.$emit('item-delete', {
        cartId: this.goods.cartId,
      });
      instance.close();
    },
    toGoodsDetail(params) {
      this.$emit('to-goods-detail', params);
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-item-block {
  background: #fff;
  overflow: hidden;
  padding: var(--theme-page-card-padding-top, 12px) var(--theme-page-card-padding-right, 12px)
    var(--theme-page-card-padding-bottom, 12px) var(--theme-page-card-padding-left, 12px);
}

.delete-btn {
  background: #f44;
  color: #fff;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  width: 64px;
  height: 100%;
}
</style>
