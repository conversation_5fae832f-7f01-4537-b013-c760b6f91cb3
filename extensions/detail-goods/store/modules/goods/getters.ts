export default {
  /**
   * 判断订单是否由抖音券支付
   * @returns {boolean}
   */
  isPaidByDouyinCoupon(): boolean {
    const { assetPayInfos = [] } = this.paymentInfo;

    return assetPayInfos.map((info) => info.payWay).includes('DY_COUPON_PAY');
  },
  isFxZpp(): boolean {
    return this.env?.isFxZpp;
  },
  // 交易组件3.0屏蔽相关操作后面会下线!
  isTradeComponent3() {
    const { orderExtra } = this;

    let BIZ_ORDER_ATTRIBUTE = {} as Record<string, any>;
    try {
      BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}');
      return (
        BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION === 'TRADE_COMPONENT_3_0' ||
        BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN'
      );
    } catch (error) {
      return false;
    }
  },
};
