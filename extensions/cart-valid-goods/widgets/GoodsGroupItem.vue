<template>
  <view>
    <custom-list-header v-if="isShowCustomGoodsListHeader" />

    <goods-group-ump
      v-if="goodsGroup.groupActivityInfo && !isShowCustomGoodsListHeader"
      :kdt-id="kdtId"
      :offline-id="offlineId"
      :is-editing="isEditing"
      :goods-list="goodsGroup.goodsList"
      :checked-goods-list="checkedGoodsList"
      :activity-info="goodsGroup.groupActivityInfo"
      :theme-general-color="themeGeneralColor"
      :theme-general-alpha-10-color="themeGeneralAlpha10Color"
      :theme-css="themeCSS"
      :theme-colors="themeColors"
      :logger="logger"
      @change-item-checked="
        handleItemChecked(
          goodsIndex,
          goodsGroupIndex,
          /* #ifdef web */ { detail: $event } /* #endif */
        )
      "
      @refresh-cart-goods-list="$emit('refresh-cart-goods-list')"
      :cloud-activity-id="
        (goodsGroup.groupActivityInfo && goodsGroup.groupActivityInfo.activityId) || 0
      "
      :cloud-index="goodsGroupIndex"
    />

    <custom-goods-item v-if="isShowCustomGoodsItem" :goods-group="goodsGroup" />

    <view v-else>
      <view v-for="(goods, goodsIndex) in goodsGroup.goodsList" :key="goods.goodsId">
        <view>
          <goods-item-ump :key="goodsIndex" :goods-item="goods" />
          <goods-item
            custom-class="goods-group__goods-item"
            :is-choose="!!(isEditing ? editCheckedGoods[goods.cartId + ''] : goods.checked)"
            :goods="goods"
            :edit-mode="isEditing"
            :is-activity="!!goodsGroup.groupActivityInfo"
            :is-can-choose="
              !(
                !isEditing &&
                isIOS &&
                goods.goodsType === 31 &&
                goods.bizExtension &&
                goods.bizExtension.cartBizMark &&
                goods.bizExtension.cartBizMark.isOnlineCourse === '1'
              )
            "
            :index="goodsGroupIndex"
            :total="goodsGroup.goodsList && goodsGroup.goodsList.length"
            :theme-general-color="themeGeneralColor"
            :theme-general-alpha-10-color="themeGeneralAlpha10Color"
            :theme-colors="themeColors"
            :theme-style="themeStyle"
            :theme-tag="themeTag"
            :show-estimated-price="showEstimatedPrice"
            :logger="logger"
            @item-checked="
              handleItemChecked(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @item-num-change="
              handleItemNumChange(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @item-num-change-logger="handleItemNumChangeLogger"
            @item-delete="
              handleItemDelete(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @change-goods-sku="handleChangeGoodsSku"
            @open-combo-popup="openComboPopup(goods)"
            @to-goods-detail="toGoodsDetail"
            @hide-count-down="hideCountDown"
            @goods-img-load="onGoodsImgLoad(goodsGroupIndex, goodsIndex)"
            :cloud-cart-id="goods.cartId"
            :cloud-goods-id="goods.goodsId"
            :cloud-index="goodsIndex"
          />
        </view>
      </view>
    </view>
    <!-- 叠加活动赠品 -->
    <view v-if="goodsGroup.groupActivityInfoList && goodsGroup.groupActivityInfoList.length > 0">
      <present
        v-for="(groupActivityInfo, index) in goodsGroup.groupActivityInfoList"
        :key="index"
        :activity-id="groupActivityInfo.activityId"
        :group-activity-info="groupActivityInfo"
        :present-list="presentData[groupActivityInfo.activityId]"
        :theme-general-color="themeGeneralColor"
        :can-select-present="canSelectPresent"
        @on-show="handleChangePresentPopup"
        @change-sku="handleChangePresentSku"
      />
    </view>
    <!-- 非叠加活动赠品 -->
    <present
      v-else-if="goodsGroup.groupActivityInfo"
      :activity-id="goodsGroup.groupActivityInfo.activityId"
      :can-select-present="canSelectPresent"
      :group-activity-info="goodsGroup.groupActivityInfo"
      :present-list="
        presentData[goodsGroup.groupActivityInfo && goodsGroup.groupActivityInfo.activityId]
      "
      :theme-general-color="themeGeneralColor"
      @on-show="handleChangePresentPopup"
      @change-sku="handleChangePresentSku"
    />
    <combo-detail-popup ref="combo-detail-popup" />
  </view>
</template>

<script>
import { mapState, mapActions } from '@ranta/store';
import ComboDetailPopup from './ComboDetailPopup.vue';

export default {
  name: 'goods-group',
  components: {
    'combo-detail-popup': ComboDetailPopup,
  },
  props: {
    goodsGroup: {
      type: Object,
      default: () => ({}),
    },
    goodsGroupIndex: Number,
  },
  data() {
    const { logger } = this.ctx;
    return {
      logger,
      isShowCustomGoodsListHeader: this.ctx?.widgets?.CustomGoodsListHeader,
      isShowCustomGoodsItem: this.ctx?.widgets?.CustomGoodsItem,
      ...mapState(this, [
        'kdtId',
        'offlineId',
        'editCheckedGoods',
        'checkedGoodsList',
        'themeCSS',
        'themeColors',
        'themeTag',
        'themeGeneralColor',
        'themeGeneralAlpha10Color',
        'canSelectPresent',
        'isIOS',
        'loadGoodsList',
        'isEditing',
        'presentData',
        'showEstimatedPrice',
        'themeStyle',
      ]),
    };
  },
  mounted() {
    this.initActions();
  },
  methods: {
    handleItemNumChangeLogger(param) {
      this.ctx.logger && this.ctx.logger.log(param);
    },
    onGoodsImgLoad(groupIndex, goodsIndex) {
      // 第一个商品 第一张图
      if (groupIndex === 0 && goodsIndex === 0) {
        this.ctx.hummer.mark?.log?.({ tag: 'trade-cart', scene: ['route'] });
      }
    },
    openComboPopup(goods) {
      const { comboDetail } = goods;
      this.$refs['combo-detail-popup'].openPopup(comboDetail);
    },
    toGoodsDetail(link) {
      this.ctx.process.invoke('navigateFromCart', { link });
    },
    hideCountDown() {
      this.ctx.event.emit('updateCartGoodsList');
    },
    handleChangePresentPopup(value) {
      this.$emit('on-present-popup-show', value);
    },
    handleChangePresentSku(value) {
      this.$emit('change-present-sku', value);
    },
    handleChangeGoodsSku(value) {
      this.$emit('change-goods-sku', value);
    },
    initActions() {
      mapActions(this, ['handleItemChecked', 'handleItemDelete', 'handleItemNumChange']);
    },
  },
};
</script>
