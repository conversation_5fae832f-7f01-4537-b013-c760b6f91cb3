<template>
  <van-dialog
    use-slot
    :show="show"
    :show-confirm-button="false"
    custom-style="background: transparent"
  >
    <view class="main-content">
      <view class="levelup-img" />
      <view class="levelup-title">
        <view class="levelup-title__before">{{ titleBefore }}</view>
        <view class="levelup-title__level">{{ titleLevel }}</view>
        <view class="levelup-title__after">{{ titleAfter }}</view>
      </view>
      <view v-if="hasBenefits" class="benefit-count-tip">
        <text class="all-benefit-count">{{ allBenefits.length }}项</text>
        会员专项权益已激活
      </view>
      <view v-if="formatedBenefits && formatedBenefits.length" class="benefit-items">
        <view class="benefit-item" v-for="(item, index) in formatedBenefits" :key="index">
          <view v-if="item.count" class="benefit-count"> {{ item.count }}张 </view>
          <view class="benefit-icon" :style="{ backgroundImage: 'url(' + item.icon + ')' }" />
          <view class="benefit-name">{{ item.showName }}</view>
        </view>
      </view>
      <view v-if="hasBenefits" class="benefit-tip">礼包仅首次升级时发放</view>
    </view>

    <button class="confirm-btn" @click="handleConfirm">
      {{ confirmText }}
    </button>

    <view class="close-icon-wrap">
      <van-icon name="close" color="#c8c9cc" @click="handleClose" />
    </view>
  </van-dialog>
</template>

<script>
import Tee from '@youzan/tee';
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { cdnImage } from '@youzan/tee-biz-util';
import { mapEvent, mapData } from '@youzan/ranta-helper-tee';
import * as API from './api';

export default {
  components: {
    'van-dialog': VanDialog,
    'van-icon': Icon,
  },

  data() {
    return {
      show: false, // Dialog visible
      level: null, // current level

      allBenefits: [], // 当前 level 所有权益
      hasBenefits: false, // 当前 level 是否有权益
      formatedBenefits: [], // 用于展示的权益列表

      // 标题相关字段
      titleBefore: '',
      titleLevel: '',
      titleAfter: '',

      // eslint-disable-next-line no-template-curly-in-string
      title: '任务完成，会员升级至${level}',
      confirmText: '立即查看',
      autoFetch: false,
    };
  },

  created() {
    mapData(this, ['confirmText']);
    mapData(this, {
      title: this._setTitle,
      autoFetch: (autoFetch) => autoFetch && this.fetchLevelAlterInfo(),
    });
    mapEvent(this, {
      checkLevelupTip: this.fetchLevelAlterInfo,
    });
    this.ctx.data.createLevelupTip = true;
  },

  methods: {
    /**
     * 获取等级变更信息
     */
    fetchLevelAlterInfo() {
      // 当前已经展示弹窗，不继续请求
      if (this.show) {
        return;
      }

      API.fetchMark(2)
        .then(({ status, detail }) => {
          const show = status === 1;
          this.ctx.event.emit('onFetchLevelupTip', {
            show,
            level: detail,
          });
          this._formatBenefits(detail);
          this.show = show;
          this.level = detail;
          if (show) {
            API.removeMark();
          }

          this._setTitle(this.title);
        })
        .catch(() => {
          this.ctx.event.emit('onFetchLevelupTip', {
            show: false,
            level: null,
          });
          this._resetData();
        });
    },

    /**
     * 格式化等级权益展示相关信息
     * @param {Object} levelDetail 等级详情
     */
    _formatBenefits(levelDetail) {
      let allBenefits = [];
      let formatedBenefits = [];

      // 处理 权益顺序 + 自定义权益
      if (!levelDetail || !levelDetail.levelBenefit) {
        allBenefits = [];
      } else {
        const benefits = levelDetail.levelBenefit || {};
        allBenefits = [
          'coupon',
          'points',
          'present',
          'discount',
          'postage',
          'pointsFeedBack',
          'diyTemplateList',
        ].reduce((res, benefitKey) => {
          const curBenefit = benefits[benefitKey] || [];
          res = res.concat(curBenefit);
          return res;
        }, []);
      }

      // 格式化展示权益
      let benefits = [...allBenefits];
      if (benefits.length) {
        if (benefits.length > 4) {
          benefits = benefits.slice(0, 3);
          benefits.push({
            benefitPluginInfo: {
              showName: '更多权益',
              icon: cdnImage('public_files/a1fb1079aa056c48631a190bc5247dd9.png'),
            },
          });
        }
        benefits = benefits.slice(0, 4);
      }
      formatedBenefits = benefits.map((benefit) => {
        const pluginInfo = benefit.benefitPluginInfo || {};
        return {
          key: benefit.benefitTplId,
          showName: pluginInfo.showName,
          icon: cdnImage(pluginInfo.icon),
          count: (benefit.couponList || []).reduce((res, coupon) => res + coupon.number || 0, 0),
        };
      });
      this.allBenefits = allBenefits;
      this.formatedBenefits = formatedBenefits;
      this.hasBenefits = !!allBenefits.length;
    },

    /**
     * 解析 title 属性用于展示
     * @param { String } title 标题
     */
    _setTitle(title) {
      this.title = title;
      const reg = /^(.*?)(\$\{level\})(.*?)$/;
      const matchRes = (title || '').match(reg);
      const { levelValue } = this.level || {};
      if (matchRes) {
        this.titleBefore = matchRes[1] || '';
        this.titleLevel = levelValue ? `Lv${levelValue}` : '';
        this.titleAfter = matchRes[3] || '';
      } else {
        this.titleBefore = title;
        this.titleLevel = '';
        this.titleAfter = '';
      }
    },

    // #region 事件
    /**
     * 确认事件
     */
    handleConfirm() {
      API.removeMark();
      this.ctx.event.emit('confirm', { level: this.level });
      this.ctx.event.emit('close', { level: this.level });
      this._resetData();
      const { redirect = true, kdtId } = this.ctx.data;
      if (redirect) {
        Tee.redirectTo(`/packages/levelcenter/free/index?kdt_id=${kdtId}`);
      }
    },

    /**
     * cancel 事件
     */
    handleClose() {
      API.removeMark();
      this.ctx.event.emit('close', { level: this.level });
      this._resetData();
    },

    /**
     * 重置 Data
     */
    _resetData() {
      this.show = false;
      this.level = null;
      this.allBenefits = [];
      this.hasBenefits = false;
      this.formatedBenefits = [];
    },
    // #endregion
  },
};
</script>

<style lang="scss">
.main-content {
  margin-top: 56px;
  padding: 22px 22px 32px;
  border-radius: 20px;
  background: #fff;
}

.levelup-img {
  width: 222px;
  height: 150px;
  background: url(//b.yzcdn.cn/public_files/89e6a53f784f293b23d379a9b915703d.png) no-repeat
    center/cover;
  margin: 0 auto 16px;
}

.levelup-title {
  text-align: center;
  font-size: 18px;
  color: #323233;
  font-weight: bold;

  .levelup-title__before,
  .levelup-title__level,
  .levelup-title__after {
    display: inline-block;
    vertical-align: middle;
  }

  .levelup-title__level {
    color: #ed6a0c;
    margin-left: 0.5em;
  }
}

.benefit-count-tip {
  margin: 8px 0 28px;
  color: #7d7e80;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
}

.all-benefit-count {
  color: #ed6a0c;
  margin-right: 0.2em;
  font-weight: bold;
}

.benefit-items {
  display: flex;
  align-items: center;
  justify-content: center;
}

.benefit-item {
  flex: 1;
  font-size: 12px;
  width: 66px;
  max-width: 25%;
  text-align: center;
  position: relative;
  color: #7d7e80;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0 auto 6px;
}

.benefit-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: inline-block;
}

.benefit-count {
  position: absolute;
  right: 0;
  top: -6px;
  background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
  border-radius: 999px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  padding: 2px 0.5em;
  transform: scale(0.8);
}

.benefit-tip {
  margin: 16px auto 0;
  text-align: center;
  line-height: 1.5;
  color: #88898b;
  font-size: 12px;
}

.confirm-btn {
  margin: 24px auto;
  color: #724804;
  background-image: linear-gradient(90deg, #e2bb7c 0%, #e8c388 100%);
  border-radius: 22px;
  opacity: 0.94;
  line-height: 40px;
  outline: none;
  border: none;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
}

.close-icon-wrap {
  text-align: center;
  font-size: 32px;
}
</style>
