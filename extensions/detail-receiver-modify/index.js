import { mapCtxData } from '@ranta/store';

import MainWidget from './Main.vue';
import ReceiverModify from './widgets/receiver-modify/index.vue';
import SelectAddressPopup from './widgets/select-address-popup/index.vue';
import SelectTimePopup from './widgets/select-time-popup/index.vue';

import createModifyStore from './store/index.ts';
import { mapData } from '@youzan/ranta-helper';
import { getStorageSync, removeStorageSync } from '@youzan-open/tee-api';
import { AUTO_SHOW_MODIFY_ADDRESS_POPUP } from './constants';
import get from '@youzan/utils/object/get';

export default class Extension {
  // Tips: 如果未使用 options.ctx，可以移除 constructor 函数
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.data.showReceiverModifyPopup = false;
    this.ctx.data.showAddressModifyPopup = false;
    this.ctx.data.showTimeModifyPopup = false;

    this.store = createModifyStore(this.ctx);

    mapCtxData(this, [
      'showReceiverModifyPopup',
      'showAddressModifyPopup',
      'showTimeModifyPopup',
      'orderNo',
      'service',
      'showDeliveryTime',
      'receiverInfo',
      'themeCSS',
      'order',
    ]);
    // 只需要web监听，小程序端会在pageShow判断
    /* #ifdef web */
    mapData(this, ['order'], {
      isSetData: false,
      callback() {
        const { order } = this.ctx.data;
        if (order) {
          // 延迟是为了确保order数据已经完成赋值
          setTimeout(() => {
            this.autoShowPopup();
          }, 100);
        }
      },
    });
    /* #endif */
    // 监听地址修改弹窗的显示，如果需要展示则获取用户地址列表
    mapData(this, ['showAddressModifyPopup'], {
      isSetData: false,
      callback() {
        if (this.ctx.data.showAddressModifyPopup) {
          this.store.getUserAddressList();
        }
      },
    });
  }

  /* #ifdef web */
  autoShowPopup() {
    const showModifyAddress = getStorageSync(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
    // 如果需要展示修改地址
    if (!showModifyAddress) {
      return;
    }

    // 存在storage，则表示一定可以修改地址，如果同时可修改时间，则展示修改信息弹窗和地址修改弹窗
    // 否则，只展示地址修改弹窗
    if (get(this.ctx.data.order, 'deliveryAllowShow.allowShowModifyTimeBuckets')) {
      this.ctx.data.showReceiverModifyPopup = true;
      this.ctx.data.showAddressModifyPopup = true;
    } else {
      this.ctx.data.showAddressModifyPopup = true;
    }

    removeStorageSync(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
  }
  /* #endif */

  // 小程序中每次页面显示时判断如果有缓存，则更新地址列表
  /* #ifdef weapp */
  onPageShow() {
    const { order } = this.ctx.data;
    const showModifyAddress = getStorageSync(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
    if (order && get(order, 'deliveryAllowShow.allowShowModifyAddress') && showModifyAddress) {
      removeStorageSync(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
      this.store.getUserAddressList();
    }
  }
  /* #endif */

  static widgets = {
    Main: MainWidget,
    ReceiverModify,
    SelectAddressPopup,
    SelectTimePopup,
  };
}
