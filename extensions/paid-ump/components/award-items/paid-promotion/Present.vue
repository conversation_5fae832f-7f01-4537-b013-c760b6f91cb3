<template>
  <award-container custom-class="container" :custom-style="themeBackground5Style">
    <image :src="curInfo.imgUrl" alt="present-img" class="present-img" />
    <view style="display: flex; justify-content: space-between; flex-direction: column; flex: 1">
      <view class="t-multi-ellipsis--l2 goods-name">
        <van-tag custom-class="present-tag" :color="theme15Color" :text-color="themeColor"
          >赠品</van-tag
        >{{ curInfo.title }}
      </view>
      <view class="content">
        <goods-price prefix="价值" :price="curGoodsPrice" :custom-style="themeColorStyle" />
        <van-button custom-class="button" :color="themeColor" round size="small" @click="clickBtn"
          >0元领取</van-button
        >
      </view>
    </view>
  </award-container>
</template>

<script>
import { Tag, Button } from '@vant/tee';
import Price from '../../widgets/Price';
import pick from '@youzan/utils/object/pick';
import moneyFormat from '@youzan/utils/money/format';
import AwardContainer from '../Container';
import { RGBA2RGB } from '../../../utils';

export default {
  name: 'award-present',
  components: {
    'goods-price': Price,
    'van-tag': Tag,
    'van-button': Button,
    'award-container': AwardContainer,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
  },
  computed: {
    themeBackground5Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.05)};`;
    },
    themeBackgroundStyle() {
      return `background: ${this.themeColor};`;
    },
    themeBackground30Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.3)};`;
    },
    themeBorderStyle() {
      return `border-color: ${this.themeColor};`;
    },
    themeColorStyle() {
      return `color: ${RGBA2RGB(this.themeColor)};`;
    },
    themeBorder30Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.3)};`;
    },
    theme15Color() {
      return RGBA2RGB(this.themeColor, 0.15);
    },
    curInfo() {
      return {
        ...pick(this.info, ['detailUrl', 'imgUrl']),
        ...(this.info?.presents ?? {}),
      };
    },
    curGoodsPrice() {
      return moneyFormat(this.curInfo.originPrice, true, false);
    },
  },
  methods: {
    clickBtn() {
      this.$emit('click-handle');
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  padding: 12px;
}

.t-multi-ellipsis--l2 {
  -webkit-line-clamp: 2;
}
.t-multi-ellipsis--l2,
.t-multi-ellipsis--l3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.present-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 8px;
}

.button {
  font-size: 14px !important;
  height: 24px !important;
  padding: 0 12px !important;
  width: 82px;
}

.present-tag {
  border-radius: 2px;
  margin-right: 6px;
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-name {
  font-size: 14px;
  line-height: 20px;
  vertical-align: baseline;
}
</style>
