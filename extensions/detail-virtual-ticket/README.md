# @wsc-tee-trade/detail-virtual-ticket

电子卡券 `纯展示型`

![UI呈现](https://img.yzcdn.cn/public_files/c393d58b4f596354279878203ed1c449.png)

## 调试方式

下单 - 电子卡券商品

## 存疑

- `watch` 做数据监听 是否可以替换成 `mapData` 做响应式？
- `ctx.data.order` 只是为了用 `kdtId` 和 `orderNo` 是否可以直接用 `shop-core` 提供的 kdtId？

## Widget.Provide

| 名称                    | 说明 |
| ----------------------- | ---- |
| VirtualTicket `default` | ???  |

## Data.Consume

| 名称              | 类型            | 说明         |
| ----------------- | --------------- | ------------ |
| showVirtualTicket | _boolean_       | 是否展示     |
| orderNo           | _string_        | 订单号       |
| order             | _Order_         | 订单信息     |
| virtualTicket     | _VirtualTicket_ | 虚拟卡券信息 |
