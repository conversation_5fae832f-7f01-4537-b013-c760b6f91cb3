<template>
  <view class="goods-item__inner" @click.stop="handleGoodsImgTap" :style="themeStyle">
    <view class="goods-img">
      <image
        class="goods-img__image"
        :mode="goods.imgMode"
        :src="goods.imgUrl"
        @load="$emit('goods-img-load')"
      />
    </view>

    <view class="goods-item--right">
      <view
        v-if="!editMode"
        :class="[
          goods.title ? 'goods-title' : 'goods-title-not-exist',
          isSub ? 't-multi-ellipsis--l1' : 't-multi-ellipsis--l2',
        ]"
      >
        <image
          v-if="goods.settlementRule && goodsTitleTag"
          :class="['goods-title-tag', goodsSettlementMark]"
          lazy-load="true"
          :src="goodsTitleTag"
        />
        <text class="goods-title-text">{{ goods.title }}</text>
      </view>

      <view
        v-else
        :class="[
          'goods-title',
          isSub ? 'handleStepOverLimitmulti-ellipsis--l1' : 't-multi-ellipsis--l2',
        ]"
      >
        <text class="goods-title-text">{{ goods.title }}</text>
      </view>

      <!-- 非待复活商品 -->
      <block v-if="!goods.revive">
        <view v-if="goods.sku" :class="['goods-sku', !!goods.tariffPriceText ? mb16 : '']">
          <view
            :class="['goods-sku-container', !isCanChangeSku ? 'goods-sku-container-normal' : '']"
            @click.stop="handleChangeGoodsSku"
          >
            <text class="tee-text">{{ goods.sku }}</text>
            <van-icon v-if="isCanChangeSku" name="arrow-down" custom-class="goods-sku-arrow" />
          </view>
        </view>

        <view class="goods-into-bottom">
          <!-- 商品图片标 -->
          <view class="cart-goods__secured-container">
            <image
              v-if="goods.yzGuarantee && goods.hideGuarantee === false"
              :src="GOODS_IMG_TAG_MAP.SECURED"
              alt="有赞放心购"
              class="cart-goods__secured-container-protect"
            />
            <view
              v-if="
                goods.yzGuarantee &&
                goods.hideGuarantee === false &&
                goods.isSevenDayUnconditionalReturn
              "
              class="seven-return-dot"
              >·</view
            >
            <view v-if="goods.isSevenDayUnconditionalReturn" class="seven-return"
              >7天无理由退货</view
            >
          </view>

          <view v-if="goodsTagList.length && !isEduIosOnlineGoods" class="goods-tags">
            <!-- FIXME: for item props -->
            <van-tag
              v-for="(item, index) in goodsTagList"
              :key="index"
              :plain="themeTag.plain"
              :hairline="themeTag.plain"
              custom-class="goods-tags__tag"
              >{{ item }}</van-tag
            >
          </view>

          <view
            v-if="
              !isEduIosOnlineGoods && (stockLimitText || startSaleNumAndLimitDesc || cutPriceDesc)
            "
            class="cart-goods__message-tips"
          >
            <view v-if="!!stockLimitText" class="cart-goods__message-tips-box">{{
              stockLimitText
            }}</view>
            <view v-if="startSaleNumAndLimitDesc" class="cart-goods__message-tips-box">
              <view v-if="!!stockLimitText" class="cart-goods__message-tips-divider"></view>
              <text>{{ startSaleNumAndLimitDesc }}</text>
            </view>
            <view v-if="!!cutPriceDesc" class="cart-goods__message-tips-box">
              <view
                v-if="(stockLimitText || startSaleNumAndLimitDesc) && isShowCutPriceDescDivider"
                class="cart-goods__message-tips-divider"
              ></view>
              <text>{{ cutPriceDesc }}</text>
            </view>
          </view>

          <view class="cart-goods__goods-message">
            <sale-count-down
              v-if="isNotStartSold"
              :start-sold-time="goods.startSoldTime"
              @hide-countdown="$emit('hide-count-down')"
            />

            <!-- 预售商品发货时间 -->
            <view v-if="presaleDate" class="presale-date">
              {{ presaleDate }}
              <text v-if="goods.deliveryTimeStr"> | {{ goods.deliveryTimeStr }} </text>
            </view>
            <view v-else-if="goods.deliveryTimeStr" class="delivery-time">
              {{ goods.deliveryTimeStr }}
            </view>

            <view class="goods-tax" v-if="!!goods.tariffPriceText && !isEduIosOnlineGoods">
              <text>进口税：{{ goods.tariffPriceText }}</text>
            </view>
          </view>

          <view
            v-if="goods.price !== null && goods.price >= 0 && !isEduIosOnlineGoods"
            :class="['goods-bottom', isCanStepper ? 'align-top' : '']"
          >
            <view :class="['goods-price', isCanStepperFormater ? 'pt6' : '']">
              <price
                :price="goods.price"
                :origin-price="goods.originPrice"
                :points-price="goods.pointsPrice"
                :points-name="shopCart.pointsName"
                :estimated-price="showEstimatedPrice ? goods.estimatedPrice : ''"
                :origin-style="isCanStepper ? 'display: block;margin-left: 0;margin-top: 4px;' : ''"
                :need-flex="false"
              />
            </view>

            <view class="goods-num">
              <stepper
                v-if="isCanStepperFormater"
                custom-class="stepper"
                :value="goodsNum"
                integer
                min="1"
                step="1"
                :max="goodsLimitNum"
                @click.stop="noopFn"
                :long-press="false"
                :disable-minus="stepperDisableMinus"
                :disable-plus="stepperDisablePlus"
                @plus="handleStepPlus"
                @minus="handleStepMinus"
                @change="handleNumStepChange"
                @overlimit="handleStepOverLimit"
                @click.native.stop=""
              />

              <view v-else>x{{ goods.num }}</view>
            </view>
          </view>

          <view v-if="isEduIosOnlineGoods" class="edu-err-msg">iOS端小程序不支持在线课</view>

          <view v-else-if="goods.errorMsg" class="err-msg">{{ goods.errorMsg }}</view>
        </view>
      </block>
      <!-- 待复活商品 -->
      <block v-else>
        <view class="goods-revive" :style="priceStyle">
          <text>请重新选择商品规格</text>

          <view class="goods-revive__tag" @click.stop="handleChangeGoodsSku"> 重选 </view>
        </view>
      </block>
    </view>
    <!-- 组合商品展示 -->
    <combo-detail
      v-if="comboDetail && comboDetail.length"
      :combo-detail="comboDetail"
      @open-combo-popup="$emit('open-combo-popup')"
      @change-swipe-status="changeSwipeStatus"
    />
    <van-toast ref="van-toast" />
  </view>
</template>

<script>
/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import SaleCountDown from '../components/SaleCountDown.vue';
import ComboDetail from '../components/ComboDetail.vue';
import money from '@youzan/weapp-utils/lib/money';
import { isEduIosOnlineGoods } from '../utils';
import Stepper from '@youzan/wsc-tee-trade-common/components/Stepper.vue';
import get from '@youzan/utils/object/get';
import { errorToast, cdnImage } from '@youzan/tee-biz-util';
import { mapState } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper';
import debounce from '@youzan/weapp-utils/lib/debounce';

const GOODS_IMG_TAG_MAP = {
  // 有赞放心购
  SECURED: cdnImage('icon/guarantee-icon.svg'),
};

const DRUG_QUALITY_LIMIT = 10000;

const publicPath = 'public_files/2019/08/19/';
const GOODS_TAG_MAP = {
  HAITAO: cdnImage(publicPath + 'fbd4c38994578e951ef1cdfd9104606d.png'), // 海淘
  PERIOD_BUY: cdnImage(publicPath + 'aea27fff45f6edb02bfd31c0b7ff3f04.png'), // 周期购
  MEMBER_DISCOUNT: cdnImage('cdn/FkhVnpHh7ZwFAvBaUwO8B0F2Gf4V-1.png'), // 会员折扣
  IS_DRUG_GOOD: cdnImage('path/to/cdn/dir/isDrugTag_3x.png'), // 处方药
};

// const ACTIVITY_TAGS = ['换购', '赠品'];

// 为什么这样写？计算属性带来的渲染副作用极大，为了提升渲染速度做此修改
// 移除computed，改为data计算与watch重计算
function formatComputedData(keys) {
  if (keys && typeof keys === 'string') {
    keys = [keys];
  }
  // 有依赖的computed需要提前计算
  const { activityTag, limitNum, stock } = this.goods;
  const isMemberDiscount = activityTag === '会员折扣';
  const goodsMaxNum = limitNum > 0 ? limitNum : stock;
  const computedMaps = {
    goodsSettlementMark() {
      return this.goods?.settlementRule?.settlementMark || '';
    },
    comboDetail() {
      const comboData = [];
      if (this.goods?.comboDetail?.groupList?.length) {
        this.goods.comboDetail.groupList.forEach(({ subComboList }) => {
          subComboList.forEach(({ num, thumbUrl }) => {
            comboData.push({
              src: thumbUrl,
              num,
            });
          });
        });
      }
      return comboData;
    },
    goodsTitleTag() {
      // 如果是处方药就增加处方药的标
      if (this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1') {
        return GOODS_TAG_MAP.IS_DRUG_GOOD;
      }
      return GOODS_TAG_MAP[this.goods.settlementRule?.settlementMark];
    },

    isMemberDiscount() {
      return isMemberDiscount;
    },

    goodsTagList() {
      const { activityTag, isInstallment, bizExtension = {} } = this.goods;

      const goodsTagList = [];

      if (bizExtension?.cartBizMark?.PRE_SALE_TYPE === '0') {
        goodsTagList.push('预售');
      }

      isMemberDiscount && goodsTagList.push(`${this.memberConfig?.name || '会员'}价`);

      /* #ifdef web */
      if (isInstallment && !this.isAlipayApp && !this.isQQApp) {
        goodsTagList.push('分期支付');
      }
      /* #endif */

      if (activityTag && activityTag !== '会员折扣') {
        goodsTagList.push(activityTag);
      }

      return goodsTagList;
    },

    stockLimitText() {
      return this.goods.isShowStockShort
        ? '库存紧张'
        : this.goods.isShowStockNum
        ? `仅剩${this.goods.stock}件`
        : '';
    },

    // 商品的最大购买量，如果有限购就为限购，没有就为库存
    goodsMaxNum() {
      return goodsMaxNum;
    },

    goodsLimitNum() {
      return this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1'
        ? Math.min(DRUG_QUALITY_LIMIT, goodsMaxNum)
        : goodsMaxNum;
    },

    goodsNum() {
      return this.goods?.num || 1;
    },

    stepperDisablePlus() {
      const { maxNum, num } = this.goods;
      return !!maxNum && num >= maxNum;
    },

    stepperDisableMinus() {
      return !!this.startSaleNum && this.goods.num <= this.startSaleNum && !this.goods.limitNum;
    },

    startSaleNumAndLimitDesc() {
      const { startSaleNum } = this;
      const { limitNum = 0 } = this.goods;

      return startSaleNum > 1
        ? `${startSaleNum}件起售${limitNum ? `，限购${limitNum}件` : ''}`
        : '';
    },

    cutPriceDesc() {
      const { cutPrice } = this.goods;

      if (!+cutPrice) return '';

      const toYuan = money(cutPrice).toYuan();

      return `比加入时便宜${toYuan}元`;
    },

    isCanChangeSku() {
      const isNotMeetStartSaleNum = !!this.startSaleNum && this.goods.num < this.startSaleNum;
      const isPlusBuyGoods = +this.goods.activityType === 24;
      // 判断是否套餐
      const isCombo = !!this.goods.comboDetail;

      return (
        !isCombo && !isPlusBuyGoods && !isNotMeetStartSaleNum && !this.isSub && !this.goods.canyinId
      );
    },

    isCanStepperFormater() {
      const isPlusBuyGoods = +this.goods.activityType === 24;

      return this.isCanStepper && !isPlusBuyGoods;
    },

    isNotStartSold() {
      const nowTime = new Date().getTime() / 1000;
      const { startSoldTime } = this.goods;

      return !!(startSoldTime && startSoldTime > nowTime);
    },

    isEduIosOnlineGoods() {
      return isEduIosOnlineGoods(this.goods);
    },

    presaleDate() {
      return this?.goods?.bizExtension?.cartBizMark?.PRE_SALE_DATE || '';
    },

    presaleDateStyle() {
      return `color: ${this.themeColors.notice}`;
    },
  };
  const computedKeys = keys && keys.length ? keys : Object.keys(computedMaps);

  return computedKeys.reduce((obj, key) => {
    obj[key] = computedMaps[key].call(this);
    return obj;
  }, {});
}

export default {
  components: {
    stepper: Stepper,
    'van-icon': Icon,
    'van-tag': Tag,
    'van-toast': VanToast,
    'sale-count-down': SaleCountDown,
    'combo-detail': ComboDetail,
    // TODO: view
  },

  props: {
    goods: Object,
    editMode: Boolean,
    // 是否隐藏活动商品
    hideTag: Boolean,
    // 是否可以调整数量
    isCanStepper: {
      type: Boolean,
      default: true,
    },
    // 是否是附属商品
    isSub: {
      type: Boolean,
      value: false,
    },
    // 起售件数
    startSaleNum: {
      type: Number,
      value: 0,
    },
    // TODO: Theme color
    // themeGeneralColor: {
    //   type: String,
    // },
    // themeGeneralAlpha10Color: {
    //   type: String,
    // },
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    themeTag: {
      type: Object,
      default: () => ({}),
    },
    showEstimatedPrice: {
      type: Boolean,
      default: false,
    },
    themeStyle: {
      type: String,
      default: '',
    },
  },

  data() {
    const initedData = {};
    /* #ifdef web */
    initedData.isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
    initedData.isQQApp = get(window, '_global.miniprogram.isQQApp', false);
    /* #endif */
    const computedData = formatComputedData.call(this);
    return {
      GOODS_TAG_MAP,
      GOODS_IMG_TAG_MAP,
      priceStyle: '',
      isShowCutPriceDescDivider: true,
      memberConfig: {},
      ...computedData,
      ...initedData,
      ...mapState(this, ['numChangeInvalid', 'shopCart']),
    };
  },
  watch: {
    goods() {
      this.updateComputedData();
    },
    memberConfig() {
      this.updateComputedData(['goodsTagList']);
    },
    themeColors() {
      this.updateComputedData(['presaleDateStyle']);
    },
  },
  created() {
    mapData(this, ['memberConfig']);
    this.handleStepChangeDebounce = debounce(this.handleStepChange.bind(this), 500);
  },
  mounted() {
    this.viewLog();
    this.computedPriceStyle();
    this.computedMessageDivider();
  },
  methods: {
    updateComputedData(keys) {
      const computedData = formatComputedData.call(this, keys);
      Object.keys(computedData).forEach((key) => {
        this[key] = computedData[key];
      });
    },
    changeSwipeStatus(status) {
      this.$emit('change-swipe-status', status);
    },
    handleStepPlus() {
      this.handleStepPlusorMinusLogger(true);
    },
    handleStepMinus() {
      this.handleStepPlusorMinusLogger(false);
    },

    handleStepPlusorMinusLogger(isPlus) {
      const { sku, skuData, skuId, goodsId, title } = this.goods;

      const param = {
        et: 'click',
        ei: `cart_${isPlus ? 'increase' : 'decrease'}_goods_num`,
        en: `购物车页面-${isPlus ? '增加' : '减少'}商品数量`,
        params: {
          no_sku: sku ? 0 : 1,
          sku_id: skuId,
          sku_name: skuData,
          goods_id: goodsId,
          goods_name: title,
        },
      };

      this.$emit('item-num-change-logger', param);
    },
    handleNumStepChange(e) {
      this.handleStepChangeDebounce(e);
    },
    handleStepChange(e) {
      // if (e > this.goodsMaxNum) {
      //   Toast("就这么几件了");
      // }

      const eventData = {
        cartId: this.goods.cartId,
        num: +e,
      };

      this.$emit('item-num-change', eventData);
    },

    handleGoodsImgTap() {
      if (this.editMode) return;
      const { alias } = this.goods;
      if (alias) {
        this.$emit('goods-img-click', { alias });
      } else {
        errorToast({ goods: this.goods }, { message: '商品数据异常，请刷新重试', log: true });
      }
    },

    handleStepOverLimit(detail) {
      if (this.numChangeInvalid) {
        return;
      }
      const isCourse = this.goods.goodsType === 31;
      if (this.stepperDisableMinus) {
        Toast(`该商品${this.startSaleNum}件起售哦`);
      } else if (detail === 'minus') {
        Toast(isCourse ? '最少购买1门课' : '最少购买1件哦');
      } else if (
        // 当前商品为处方药时，超过处方药限制购买数，toast提示
        this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1' &&
        this.goods.num >= DRUG_QUALITY_LIMIT &&
        detail === 'plus'
      ) {
        Toast(`为保障用药安全，不能添加更多`);
      } else if (!this.goods.limitNum || this.goods.limitNum > this.goods.stockNum) {
        Toast(isCourse ? '超出该课程可购买数量' : '该商品不能购买更多哦');
      } else if (this.goods.quotaCycle === 4) {
        // 限购类型为按单限购时，提示信息与其他限购类型不同
        Toast(`该商品每单限购${this.goods.limitNum}件`);
      } else {
        Toast(`商品限购${this.goods.limitNum}件`);
      }
    },

    handleChangeGoodsSku() {
      if (this.isCanChangeSku || this.goods.revive) {
        this.$emit('change-goods-sku');
      }
    },

    computedPriceStyle() {
      this.priceStyle = '';
      const query = this.createSelectorQuery();
      const goodsRight = query.select('.goods-item--right');
      goodsRight
        .boundingClientRect((res) => {
          if (!res) return;
          const { height } = res;
          if (height < 96) {
            this.priceStyle = `padding-top: ${96 - height}px`;
          }
        })
        .exec();
    },

    computedMessageDivider() {
      const query = this.createSelectorQuery();
      const goodsRight = query.select('.cart-goods__message-tips');
      goodsRight
        .boundingClientRect((res) => {
          if (!res) return;
          const { height } = res;
          if (height > 24) {
            this.isShowCutPriceDescDivider = false;
          }
        })
        .exec();
    },

    viewLog() {
      if (+this.goods.activityType === 1) {
        this.logger?.log({
          et: 'view', // 事件类型
          ei: 'cart_limited_discount_view', // 事件标识
          en: ' 购物车限时折扣曝光', // 事件名称
          params: {
            item_type: 'limitdiscount',
            activity_type: 'limitdiscount',
            kdt_id: this.goods.kdtId,
          }, // 事件参数
        });
      }
    },
    noopFn() {},
  },
};
</script>

<style lang="scss">
.goods-item__inner {
  min-height: 96px;
  margin-left: 37px;

  &_invalid {
    margin-left: 15px;
  }
}

.goods-img {
  float: left;
  height: 96px;
  width: 96px;
  background-size: cover;
  border-radius: var(--theme-radius-card, 8px);
  overflow: hidden;

  &__image {
    max-height: 100%;
    max-width: 100%;
    width: 96px;
    height: 96px;
    background-color: #fff;
    border-radius: var(--theme-radius-card, 8px);
  }
}

.goods-tags {
  margin-bottom: 8px;
  margin-top: 0;
  font-size: 0;

  &__tag {
    --tag-font-size: var(--eo-font-size-12, 12px);
    --tag-line-height: 1.3;

    padding: 0 4px;
    margin-right: 8px;
    vertical-align: middle;
    color: var(--theme-tag-ump-color, --ump-tag-text) !important;
    background: var(--theme-tag-ump-bg-color, --ump-tag-bg) !important;

    &::after {
      border-radius: var(--theme-radius-tag, 0) !important;
      border-color: var(--theme-tag-ump-border-color, --main-bg) !important;
    }
  }
}

.goods-title {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.goods-title-not-exist {
  margin-bottom: 0;
}

.goods-title-text {
  font-size: var(--eo-font-size-16, 16px);
  color: #111;
  font-weight: 500;
  line-height: 1.4;
  vertical-align: middle;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-title-tag {
  margin-right: 2px;
  width: calc(43px * var(--font-size-scale, 1));
  height: calc(16px * var(--font-size-scale, 1));
  display: inline-block;
  vertical-align: middle;

  &.HAITAO {
    width: calc(28px * var(--font-size-scale, 1));
  }

  &.PERIOD_BUY,
  &.IS_DRUG_GOODS {
    width: calc(38px * var(--font-size-scale, 1));
  }
}

.goods-sku {
  margin-bottom: 8px;
  line-height: 12px;

  &.mb16 {
    margin-bottom: 16px;
  }
}

.goods-sku-container {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 4px 8px;
  background: #f7f8fa;
  border-radius: var(--theme-radius-tag, 4px);
  font-size: 0;
  color: #969799;

  .tee-text {
    font-size: var(--eo-font-size-14, 14px);
    margin-right: 10px;
    line-height: 1.3;
  }

  .goods-sku-arrow {
    font-size: var(--eo-font-size-12, 12px);
    height: 12px;
    line-height: 12px;
    vertical-align: middle;
  }
}

.goods-sku-container-normal {
  padding: 0;
  background: transparent;

  text {
    margin: 0;
  }
}

.goods-item--right {
  position: relative;
  margin-left: 104px;
  min-height: 96px;
}

.goods-bottom {
  display: inline-block;
  width: 100%;
  margin-top: var(--theme-trade-sm-gutter, 0);
}

.align-top {
  align-items: flex-start;
}

.origin-block {
  display: block;
  margin-left: 0;
  margin-top: 4px;
}

.goods-price {
  display: inline-block;
  font-size: 14px;
  font-weight: var(--theme-common-price-font-weight, 600);
  font-family: Avenir;
  color: var(--price, #323233);
}

.pt6 {
  padding-top: 4px;
}

.goods-num {
  color: #666;
  font-size: var(--eo-font-size-12, 12px);
  float: right;
}

.goods-tax {
  font-size: var(--eo-font-size-12, 12px);
  line-height: 1.3;
  margin-bottom: 8px;
  color: #323233;
}

.activity-tag {
  background: #f44;
  border-bottom-right-radius: var(--theme-radius-card, 8px);
  border-top-right-radius: 8px;
  bottom: 10%;
  color: #fff;
  display: inline-block;
  font-size: 10px;
  padding: 0 4px;
  position: absolute;
  z-index: 1;
}

.presale-date {
  font-size: var(--eo-font-size-12, 12px);
  line-height: 1.3;
  margin-bottom: 8px;
}
.delivery-time {
  font-size: var(--eo-font-size-12, 12px);
  line-height: 1.3;
  margin-bottom: 8px;
  color: #ed6a0c;
}

.stock-less,
.start-sale-num {
  font-size: 12px;
  line-height: 16px;
  color: var(--ump-icon, #323233);
}

.err-msg {
  font-size: 12px;
  color: #323233;
  line-height: 16px;
}

.edu-err-msg {
  font-size: 12px;
  color: #323233;
  line-height: 16px;
  margin-bottom: 12px;
}

.cart-goods {
  &__img-tags {
    margin-bottom: 8px;
    padding-top: 8px;
  }

  &__secured {
    &-container {
      display: flex;
      align-items: center;

      &-protect {
        height: calc(14px * var(--font-size-scale, 1));
        width: calc(52px * var(--font-size-scale, 1));
        margin-bottom: 8px;
      }

      .seven-return {
        font-size: var(--eo-font-size-12, 12px);
        letter-spacing: 0;
        border-radius: 2px;
        margin-bottom: 8px;
        color: var(--brand-youzandanbao, #07c160);
      }

      .seven-return-dot {
        margin: 0 2px;
        margin-bottom: 8px;
        color: var(--brand-youzandanbao, #07c160);
      }
    }
  }

  &__message-tips {
    display: flex;
    flex-wrap: wrap;
    font-size: var(--eo-font-size-14, 14px);
    line-height: 1.3;
    color: var(--ump-icon, #323233);

    &-divider {
      width: 0;
      height: 12px;
      border-right: 1px solid #ebedf0;
      margin: 0 4px;
    }

    &-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
  }

  &__goods-message {
    display: flex;
    flex-direction: column;
    color: #323233;
  }
}

.custom-stepper {
  &__input,
  &__plus,
  &__minus {
    margin-left: var(--theme-page-stepper-input-margin-left, 1px) !important;
    margin-right: var(--theme-page-stepper-input-margin-right, 1px) !important;
    background-color: var(--theme-page-stepper-background-color, #f2f3f5) !important;
  }

  &__input {
    border: 1px solid var(--theme-page-stepper-border-color, rgba(0, 0, 0, 0)) !important;
  }

  &__plus,
  &__minus {
    border-color: var(--theme-page-stepper-border-color, rgba(0, 0, 0, 0)) !important;
    color: #323233;
    border-top: 1px solid;
    border-bottom: 1px solid;

    &--disabled {
      color: var(--theme-page-stepper-color-disabled, #c8c9cc) !important;
    }
  }

  &__plus {
    border-top-right-radius: var(--theme-page-stepper-border-top-right-radius, 4px) !important;
    border-bottom-right-radius: var(
      --theme-page-stepper-border-bottom-right-radius,
      4px
    ) !important;
    border-right: 1px solid;
    border-left: 0;
  }

  &__minus {
    border-top-left-radius: var(--theme-page-stepper-border-top-left-radius, 4px) !important;
    border-bottom-left-radius: var(--theme-page-stepper-border-bottom-left-radius, 4px) !important;
    border-left: 1px solid;
    border-right: 0;
  }
}

.goods-cut-price {
  font-size: 12px;
  line-height: 16px;
  color: var(--ump-icon, #323233);
  letter-spacing: 0;
}

.goods-revive {
  font-size: var(--eo-font-size-12, 12px);
  color: #323233;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__tag {
    width: calc(40px * var(--font-size-scale, 1));
    height: var(--eo-font-size-20, 20px);
    line-height: var(--eo-font-size-20, 20px);
    padding: 0;
    text-align: center;
    display: block;
    background: #fff;
    color: var(--main-bg);
    border: 1px solid var(--main-bg);
    border-radius: calc(var(--theme-radius-button, 10px) * var(--font-size-scale, 1)) !important;
  }
}
</style>
