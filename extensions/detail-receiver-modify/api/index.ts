import { requestV2 } from '@youzan/tee-biz-request';

export const fetchAddressList = (params) => {
  return requestV2({
    path: '/wsctrade/order/address/address-list-v2.json',
    data: {
      ...params,
      timeStamp: new Date().getTime(), // 加一个时间戳防止浏览器存在后退缓存
    },
  });
};

export const fetchDeliveryTimeBucket = ({ orderNo, addressId }) => {
  return requestV2({
    path: '/wsctrade/order/address/delivery-time-bucket.json',
    data: {
      orderNo,
      addressId,
      timeStamp: new Date().getTime(), // 加一个时间戳防止浏览器存在后退缓存
    },
  });
};

interface IUpdateReceiverInfo {
  address?: object;
  orderNo: string;
  /** 格式: startTime,endTime (开始结束时间时间戳，单位s，逗号分隔) */
  appointmentTime?: string;
}

export const updateReceiverInfo = (params: IUpdateReceiverInfo) => {
  return requestV2({
    method: 'POST',
    path: '/wsctrade/order/address/order-address-info.json',
    data: {
      ...params,
    },
  });
};
