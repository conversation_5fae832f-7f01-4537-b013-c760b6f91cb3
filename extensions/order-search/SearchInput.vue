<template>
  <van-search
    :value="keyword"
    :focus="focus"
    placeholder="搜索订单"
    @search="onSearch"
    @change="onChange"
    @blur="onBlur"
    content-class="borderRadius"
    use-left-icon-slot
  >
    <view slot="left-icon" class="icon"></view>
  </van-search>
</template>

<script>
import Search from '@youzan/vant-tee/dist/search/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import navigate from '@youzan/tee-biz-navigate';
import storage from './storage-util';
import { mapData } from '@youzan/ranta-helper-tee';
import Tee from '@youzan/tee';
import Event from '@youzan/tee-event';
import { url } from '@youzan/tee-util';

export default {
  components: {
    'van-search': Search,
    'van-icon': Icon,
  },
  data() {
    return {
      keyword: '',
      focus: true,
      kdtId: '',
    };
  },

  computed: {
    HISTORY_KEY() {
      return `${this.kdtId}-orderSearchHistory`;
    },
  },
  created() {
    mapData(this, ['kdtId']);

    this.ctx.lambdas.onEvent('change-history', (res) => {
      console.log('res', res);
      this.keyword = res;
      this.goToSearch();
    });
  },
  destroyed() {
    this.ctx.lambdas.offEvent?.('change-history');
  },
  methods: {
    onBlur() {
      this.focus = false;
    },
    onSearch() {
      this.goToSearch();
    },

    goToSearch() {
      if (this.keyword) {
        let list = JSON.parse(storage.getItem(this.HISTORY_KEY) || '[]');
        list = list.filter((item) => item.keyword !== this.keyword);
        list.unshift({
          keyword: this.keyword,
          time: new Date().getTime(),
        });
        storage.setItem(this.HISTORY_KEY, JSON.stringify(list));
        this.$emit('history-change');
      }

      let param = '';
      if (this.keyword) {
        param = `&keyword=${this.keyword}`;
      }
      /* #ifdef weapp */
      const pages = getCurrentPages();

      if (
        pages.length >= 2 &&
        (pages[pages.length - 2].route === 'packages/trade/order/list/index' ||
          /pages\/tab\/(one|two|three)\/index/.test(pages[pages.length - 2].route))
      ) {
        Tee.navigateBack();
        Event.trigger('search-order', {
          kdtId: this.kdtId,
          type: 'all',
          keyword: this.keyword || '',
        });
        return;
      }
      const orderListPath = url.args.add(
        '/packages/trade/order/list/index',
        {
          kdt_id: this.kdtId,
          refresh: true,
          navigateType: 'redirectTo',
          type: 'all',
          ...(this.keyword ? { keyword: this.keyword } : {}),
        },
        false
      );
      Tee.navigate({
        type: 'redirectTo',
        url: orderListPath,
      });
      /* #else */
      navigate({
        web: {
          type: 'safeLink',
          safeLink: {
            url: `/wsctrade/order/list?kdt_id=${this.kdtId}&type=all${param}`,
          },
        },
      });
      /* #endif */
    },

    onChange(event) {
      this.keyword = event.value;
    },
  },
};
</script>
<style lang="scss" scope>
.t-search {
  padding: 8px 16px;
}
.t-search__content {
  border-radius: 20px;
}
.t-icon,
.t-icon::before {
  display: flex !important;
}

.borderRadius {
  padding: 7px 0;
  border-radius: 8px !important;
}
.icon {
  width: 25px;
  height: 22px;
  margin-left: 8px;
  background: url(https://b.yzcdn.cn/vant/search-o1.png) left center no-repeat;
  background-size: 15px;
}
</style>
