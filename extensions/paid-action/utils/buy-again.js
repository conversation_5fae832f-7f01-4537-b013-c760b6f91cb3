import { requestV2 } from '@youzan/tee-biz-request';
import navigate from '@youzan/tee-biz-navigate';
import { args } from '@youzan/tee-util/lib/common/url';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import env from '@youzan/wsc-tee-trade-common/lib/env';
import { BTNS_MAP } from '../constans';
import { PAGE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import get from '@youzan/utils/object/get';
import { errorToast } from '@youzan/tee-biz-util';

const COUPON_CODE_MAP = {
  161201035: '优惠券活动已失效，无法再领取',
  161201033: '优惠券库存不足，无法再领取',
  161201406: '领取规则调整，该券不可再领',
  161201050: '当前领取人数太多，请稍后再试',
};

function directBuyAgain({ ctx, data = {}, extraData = {}, bannerId }) {
  // 订单列表使用此逻辑
  const { repurchaseCoupon } = extraData;
  let preToastDesc = '';
  // @天琊
  if (repurchaseCoupon) {
    const { code, valueCopywriting = '', unitCopywriting = '' } = repurchaseCoupon || {};
    if (+code === 0 && valueCopywriting && unitCopywriting) {
      preToastDesc = `已为你领取${valueCopywriting}${unitCopywriting}优惠券，下单享优惠`;
    } else if (COUPON_CODE_MAP[code]) {
      preToastDesc = COUPON_CODE_MAP[code];
    }
  }

  const { kdtId } = data;

  return requestV2({
    path: '/wsctrade/order/directBuyAgain.json',
    method: 'POST',
    data: {
      order_no: data.orderNo,
      kdt_id: kdtId,
      preToastDesc,
    },
  })
    .then((respData) => {
      // 跳转商详页
      if (+respData?.destination === 2) {
        ctx.dmc.navigate('GoodsDetail', {
          alias: respData.alias,
          forceNormalGoodsDetail: true,
        });
        return;
      }

      // 跳转下单页
      let payUrl = respData.buy_url || window._global.url.trade + respData.url;
      const { isYouzanmars, isAlipayApp, isQQApp, isXhsApp, isKsApp } = env;

      const urlParams = {
        showwxpaytitle: 1,
        kdt_id: kdtId,
        book_key: respData.book_key,
        bookKey: respData.book_key,
        banner_id: bannerId,
      };
      /* #ifdef web */
      payUrl = args.add(payUrl, urlParams);
      if (isYouzanmars) {
        navigate({
          web: {
            type: 'native',
            native: {
              url: payUrl,
              page: 'web',
            },
          },
        });
      } else if (isAlipayApp || isQQApp || isXhsApp || isKsApp) {
        navigate({
          web: {
            type: 'znb',
            znb: {
              aliappUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
              qqUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
              xhsUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
              ksUrl: `/pages/web-view/index?src=${encodeURIComponent(payUrl)}`,
            },
          },
        });
      } else {
        navigate({
          web: {
            type: 'jumpLink',
            jumpLink: {
              url: payUrl,
            },
          },
        });
      }
      /* #endif */
      /* #ifdef weapp */
      navigateToRantaPage({
        type: 'redirect',
        pageType: PAGE_TYPE.ORDER,
        query: urlParams,
      });
      /* #endif */
    })
    .catch((err) => {
      errorToast(err, { message: '再来一单请求失败。' });
    });
}

function cartBuyAgain({ data = {}, extraData = {} }) {
  return requestV2({
    path: '/wsctrade/order/buyAgain.json',
    method: 'POST',
    data: {
      order_no: data.orderNo,
      kdt_id: data.kdtId,
    },
  })
    .then((_resp) => {
      // 订单列表再来一单有此逻辑
      const { repurchaseCoupon } = extraData;
      /* #ifdef web */
      let urlParams = '';
      if (repurchaseCoupon) {
        const { code, valueCopywriting = '', unitCopywriting = '' } = repurchaseCoupon || {};
        urlParams = `&repurchaseCouponStatus=${code}&couponValue=${valueCopywriting}&couponUnit=${unitCopywriting}`;
      }

      const cartUrl = '/wsctrade/cart?kdt_id=' + data.kdtId + urlParams;
      navigate({
        web: {
          type: 'safeLink',
          safeLink: {
            url: cartUrl,
          },
        },
      });
      /* #endif */
      /* #ifdef weapp */
      let query = {
        kdt_id: data.kdtId,
      };
      if (repurchaseCoupon) {
        const { code, valueCopywriting = '', unitCopywriting = '' } = repurchaseCoupon || {};
        query = {
          ...query,
          repurchaseCouponStatus: code,
          couponValue: valueCopywriting,
          couponUnit: unitCopywriting,
        };
      }

      getApp().trigger('trade:add:cart');

      navigateToRantaPage({
        type: 'redirect',
        pageType: PAGE_TYPE.CART,
        query,
      });
      /* #endif */
    })
    .catch((err) => {
      errorToast(err, { message: '再来一单请求失败。' });
    });
}

export function buyAgain({ ctx, type, kdtId, orderNo, orderItems = [], logger }) {
  const bigFormData = {
    orderNo,
    kdtId,
    goodsList: orderItems.map((goodsItem) => ({
      title: goodsItem.title,
      sku_id: goodsItem.skuId,
      sku: goodsItem.skuItems,
      num: goodsItem.num,
      goods_id: goodsItem.goodsId,
    })),
  };

  // 埋点参数
  const goodsIds = [];
  orderItems.forEach((item) => {
    goodsIds.push(item.goodsId);
  });
  const params = {
    goods_id: goodsIds,
    order_no: orderNo,
  };

  if (type === BTNS_MAP.DIRECT_BUY_AGAIN) {
    // 下单页再来一单
    let buyerId = 0;
    /* #ifdef web */
    buyerId = get(window, '_global.buyer_id', '');
    /* #endif */
    /* #ifdef weapp */
    buyerId = getApp().getBuyerId();
    /* #endif */

    const pageRandomNumber = makeRandomString(8);
    const loggerSpm = logger?.getSpm() || '';
    const bannerId = `${loggerSpm}~new_buy_again_click~0~${pageRandomNumber}`;

    // 埋点
    logger?.log({
      et: 'click',
      ei: 'new_buy_again_click',
      en: '新再来一单点击',
      pt: 'ol',
      params: {
        ...params,
        banner_id: bannerId,
        buyer_id: buyerId,
        is_new: true,
      },
    });

    return directBuyAgain({
      ctx,
      bannerId,
      data: bigFormData,
    });
  }
  if (type === BTNS_MAP.BUY_AGAIN) {
    // 普通再来一单
    // 埋点
    logger?.log({
      et: 'click',
      ei: 'new_buy_again_click',
      en: '新再来一单点击',
      pt: 'ol',
      params: {
        ...params,
        is_new: false,
      },
    });

    return cartBuyAgain({ ctx, data: bigFormData });
  }
}
