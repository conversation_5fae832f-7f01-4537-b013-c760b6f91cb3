import { requestV2 } from '@youzan/tee-biz-request';

export const checkBlackRelation = (data) => {
  return requestV2({
    path: '/wscfenxiao/member-relation/checkBlackRelation.json',
    method: 'GET',
    data,
  });
};

// 查询公众号关注状态
export const queryMpFocus = (data) => {
  return requestV2({
    path: '/wscfenxiao/subscribe/queryMpFocus.json',
    method: 'GET',
    data,
  });
};

// 团长主页查询当前用户与团长订阅关系
export const querySubscribeRelation = (data) => {
  return requestV2({
    path: '/wscfenxiao/party/querySubscribeRelation.json',
    method: 'GET',
    data,
  });
};

// 创建订阅关系同步领取订阅有礼优惠券，需要关注发券结果，主要用于首页、团购详情页、团长主页
export const createSubscribeAndSyncTakeVoucher = (data) => {
  return requestV2({
    path: '/wscfenxiao/subscribe/createSubscribeAndSyncTakeVoucher.json',
    method: 'POST',
    data,
  });
};

// 保存引导关注页面路径，方便10分钟内后端推送页面地址
export const saveFocusMpPagePath = (data) => {
  return requestV2({
    path: '/wscfenxiao/subscribe/saveFocusMpPagePath.json',
    method: 'POST',
    data,
  });
};
