<template>
  <view class="tip" v-if="show" :style="style">
    <image
      class="img"
      src="https://img01.yzcdn.cn/upload_files/2024/10/16/FvYT7_ocoRa_IjGaY_q8aFwss9Wm.png"
    />{{ text }}
  </view>
</template>

<script>
import { BOTTOM } from './constants';

export default {
  props: {
    btns: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    show() {
      const { btns } = this;
      const { forQttOrderExtra } = window._global;
      if (!btns?.length || !forQttOrderExtra) return false;
      const { orderCommentShow } = forQttOrderExtra;
      if (!orderCommentShow) return false;

      const { hasReward, isReward } = orderCommentShow;
      const hasComment = btns.some((item) => item.value === BOTTOM.qttOrderComment.value);
      const hasCommentted = btns.some((item) => item.value === BOTTOM.qttOrderCommentted.value);
      const rewarded = isReward === 2; // 奖励已发放
      // 可以晒单 & 有奖励  || 已晒单 & 奖励已发放
      return (hasComment && hasReward) || (hasCommentted && rewarded);
    },
    text() {
      const { forQttOrderExtra } = _global;
      if (!forQttOrderExtra) return '';
      const { orderCommentShow } = forQttOrderExtra;
      if (!orderCommentShow) return '';
      const { btns } = this;
      const hasComment = btns.some((item) => item.value === BOTTOM.qttOrderComment.value);
      const hasCommentted = btns.some((item) => item.value === BOTTOM.qttOrderCommentted.value);
      const { discount, isReward } = orderCommentShow;
      if (hasComment) {
        return `晒单领${discount / 100}元券`;
      }
      if (hasCommentted && isReward === 2) {
        return `${discount / 100}元券已发放`;
      }
      return '';
    },
    style() {
      const { btns } = this;
      if (!btns) return '';
      const index = btns.findIndex(
        (item) =>
          item.value === BOTTOM.qttOrderComment.value ||
          item.value === BOTTOM.qttOrderCommentted.value
      );
      if (index < 0) return '';
      const rightBtnNum = index + 1;
      const right = Math.max(rightBtnNum * 86 - 100, 8);
      return `right: ${right}px;`;
    },
  },
};
</script>

<style lang="scss" scoped>
.tip {
  position: absolute;
  display: flex;
  top: -28px;
  right: 0;
  z-index: 8;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.05);
  color: #fff;
  font-size: 12px;
  line-height: 16px;
  &::after {
    position: absolute;
    content: '';
    display: block;
    width: 0;
    height: 0;
    top: auto;
    bottom: 0;
    right: 45px;
    border: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.7);
    transform: translate(-50%, 100%);
  }
}
.img {
  margin-right: 2px;
  width: 14px;
  height: 14px;
}
</style>
