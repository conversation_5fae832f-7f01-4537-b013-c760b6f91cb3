<template>
  <view v-if="list && list.length > 0" class="history">
    <view class="history-title">
      历史记录
      <van-icon name="delete-o" size="18" color="#999" @click="handleButtonClick" />
    </view>
    <view :class="['history-list', showBottomBorder ? '' : 'notBottomBorder']">
      <view
        v-for="(item, index) in list"
        :key="`${item}-${index}`"
        class="history-list-item"
        @click="handleItemClick(item)"
      >
        {{ item.showData }}
      </view>
    </view>
    <van-dialog ref="van-dialog-order-search-history" />
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import storage from '../storage-util';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import VanDialog from '@youzan/vant-tee/dist/dialog/index';
import Logger from '../logs';
import Toast from '@youzan/vant-tee/dist/toast/toast';

export default {
  components: {
    'van-icon': Icon,
    'van-dialog': VanDialog,
  },

  props: {
    kdtId: {
      type: String,
      default: '',
    },
    showBottomBorder: Boolean,
  },

  data() {
    return {
      historyList: [],
    };
  },

  computed: {
    list() {
      let filterList = [];
      filterList = this.historyList.filter((item) => item.time - new Date().getTime() < 604800);
      filterList.forEach((item) => {
        item.showData = item.keyword.length > 24 ? item.keyword.slice(0, 24) + '...' : item.keyword;
      });
      return filterList;
    },
    HISTORY_KEY() {
      return `${this.kdtId}-orderSearchHistory`;
    },
  },

  mounted() {
    Logger.historyExposed();
    this.getHistoryList();
  },

  methods: {
    handleItemClick(item) {
      this.$emit('handleItemClick', item.keyword);
      Logger.historyClicked();
    },

    handleButtonClick() {
      return Dialog.confirm({
        ref: this.$refs['van-dialog-order-search-history'],
        message: '确定清空搜索记录？',
        confirmButtonText: '清空',
      })
        .then(() => {
          this.historyList = [];
          storage.removeItem(this.HISTORY_KEY);
          Logger.historyCleared();
          Toast('记录已清空');
          Dialog.close();
        })
        .catch(() => {
          Dialog.close();
        });
    },
    getHistoryList() {
      try {
        this.historyList = JSON.parse(storage.getItem(this.HISTORY_KEY) || '[]');
      } catch (e) {
        console.error('history parse err', e);
      }
    },
  },
};
</script>

<style lang="scss" scope>
.history {
  background-color: #fff;

  &-title {
    font-family: PingFangSC-Medium, sans-serif;
    padding: 12px 16px 12px;
    font-size: 16px;
    line-height: 20px;
    color: #111;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-list {
    margin: 0 16px;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #ebedf0;
    max-height: 120px;

    &-item {
      font-size: 14px;
      color: #111;
      line-height: 18px;
      display: inline-block;
      padding: 6px 12px;
      border-radius: 6px;
      margin: 0 12px 12px 0;
      min-height: 18px;
      background: #f7f7f7;
    }
  }

  .notBottomBorder {
    border-bottom: none;
  }
}
</style>
