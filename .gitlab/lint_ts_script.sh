#!/usr/bin/env bash
cd ~
<NAME_EMAIL>:weapp/wsc-tee-h5.git
cd wsc-tee-h5
current_branch="${CI_COMMIT_BRANCH:-$CI_MERGE_REQUEST_TARGET_BRANCH_NAME}"
git fetch origin
if [[ -n "$(git ls-remote --exit-code --heads origin $current_branch)" ]]; then
  git checkout $current_branch
fi
echo "当前wsc-tee-h5分支: $(git rev-parse --abbrev-ref HEAD)"
echo "$KOKO_REPO_JSON" > ./koko.repo.json
yarn install --frozen-lockfile
cd src/ext-tee-wsc-trade
git checkout $CI_COMMIT_SHA
yarn install --frozen-lockfile
yarn lint:ts
