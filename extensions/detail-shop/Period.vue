<template>
  <view v-if="showPeriodBuyDelivery">
    <van-cell title="配送周期" title-width="80px" :value="periodTime" :style="customStyle" />
    <van-cell center title="送达日期" title-width="80px" :style="customStyle">
      <view>
        {{ periodDeliveryTime }}
      </view>
      <view>
        {{ periodBuyTimeText }}
      </view>
    </van-cell>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import Cell from '@youzan/vant-tee/dist/cell/index';

export default {
  components: {
    'van-cell': Cell,
  },

  props: {
    titleColor: {
      type: String,
      default: () => '',
    },
    valueColor: {
      type: String,
      default: () => '#666',
    },
  },

  data() {
    return {
      isPeriodBuy: false,
      isSelfFetch: false,
      isMultiPeriodBuy: false,
      order: {},
    };
  },

  computed: {
    showPeriodBuyDelivery() {
      const { isPeriodBuy, isSelfFetch, isMultiPeriodBuy } = this;
      return isPeriodBuy && !isSelfFetch && !isMultiPeriodBuy;
    },
    periodTime() {
      const { order, isPeriodBuy } = this;
      if (isPeriodBuy) {
        return order.periodDetail[0].periodAlias || '';
      }
      return '';
    },
    periodDeliveryTime() {
      const { order, isPeriodBuy } = this;
      if (isPeriodBuy) {
        return order.periodDetail[0].deliverTimeAlias || '';
      }
      return '';
    },
    periodBuyTimeText() {
      const { order, isPeriodBuy } = this;
      if (isPeriodBuy) {
        return order.periodDetail[0].periodBuyTimeText || '';
      }
      return '';
    },
    customStyle() {
      return `--cell-text-color: ${this.titleColor}; --cell-value-color: ${this.valueColor};`;
    },
  },

  created() {
    mapData(this, ['isPeriodBuy', 'isSelfFetch', 'isMultiPeriodBuy', 'order']);
  },
};
</script>
