<template>
  <view v-if="showVirtualTicket && showShopVerifyAddress" class="address">
    <view class="address__content">
      <view class="address__content__name">{{ verifyAddressInfo.name }}</view>
      <view class="address__content__address">
        {{ verifyAddressInfo.address }}
      </view>
      <view v-if="distance" class="address__content__distance">距离{{ distance }}m</view>
    </view>

    <!-- #ifdef weapp -->
    <view
      v-if="verifyAddressInfo.lat && verifyAddressInfo.lng"
      class="address__map__icon t-hairline--left"
      @click="onLocationWeappClick"
    />
    <!-- #endif -->

    <!-- #ifdef web -->
    <view
      v-if="isWeixin && verifyAddressInfo.lat && verifyAddressInfo.lng"
      class="address__map__icon t-hairline--left"
      @click="onLocationWebClick"
    />
    <!-- #endif -->
  </view>
</template>

<script>
import Tee from '@youzan/tee';
/* #ifdef web */
// eslint-disable-next-line @youzan-open/tee/no-define-in-branch
import ZNB from '@youzan/znb';
/* #endif */
/* #ifdef weapp */
import { getLocation } from '@youzan/tee-api';
/* #endif */
import { mapData } from '@youzan/ranta-helper-tee';

const validateLat = (lat) => lat >= -90 && lat <= 90;

const validateLng = (lng) => lng >= -180 && lng <= 180;

const validate = (lat, lng) => validateLat(lat) && validateLng(lng);

const calculateDistance = (from, to) => {
  const { latitude: latA, longitude: lngA } = from;
  const { latitude: latB, longitude: lngB } = to;
  if (!validate(latA, lngA) || !validate(latB, lngB)) {
    return 0;
  }

  const EARTH_RADIUS = 6378137.0;
  const radLat1 = (latA * Math.PI) / 180.0;
  const radLat2 = (latB * Math.PI) / 180.0;
  const a = ((latA - latB) * Math.PI) / 180.0;
  const b = ((lngA - lngB) * Math.PI) / 180.0;
  let s =
    2 *
    Math.asin(
      Math.sqrt(Math.sin(a / 2) ** 2 + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(b / 2) ** 2)
    );
  s *= EARTH_RADIUS;
  s = Math.round(s * 1000) / 1000;
  return s;
};

export default {
  data() {
    return {
      showVirtualTicket: false,
      showShopVerifyAddress: false,
      verifyAddressInfo: {},
      distance: 0,
      isWeixin: false,
    };
  },

  created() {
    mapData(this, ['showVirtualTicket', 'showShopVerifyAddress']);
    mapData(this, {
      verifyAddressInfo: () => {
        /* #ifdef web */
        this.setDistance();
        /* #endif */
      },
    });
    mapData(
      this,
      {
        env: (env) => {
          this.isWeixin = env.isWeixin;
        },
      },
      { isSetData: false }
    );
  },

  destroyed() {
    this.unwatchShowVirtualTicket && this.unwatchShowVirtualTicket();
    this.unwatchShowShopVerifyAddress && this.unwatchShowShopVerifyAddress();
    this.unwatchVerifyAddressInfo && this.unwatchVerifyAddressInfo();
    this.unwatchIsWeixin && this.unwatchIsWeixin();
  },

  methods: {
    setDistance() {
      const { lat, lng } = this.verifyAddressInfo;
      if (lat && lng) {
        /* #ifdef weapp */
        getLocation().then((res) => {
          const { latitude, longitude } = res;
          if (latitude && longitude) {
            const from = { latitude, longitude };
            const to = { latitude: lat, longitude: lng };
            this.distance = calculateDistance(from, to);
          }
        });
        /* #endif */
        /* #ifdef web */
        this.ctx.process.invokePipe('tee-api:getLocation').then((res) => {
          const { latitude, longitude } = res;
          if (latitude && longitude) {
            const from = { latitude, longitude };
            const to = { latitude: lat, longitude: lng };
            this.distance = calculateDistance(from, to);
          }
        });
        /* #endif */
      }
    },

    onLocationWeappClick() {
      const { lng, lat, name, address } = this.verifyAddressInfo;

      if (lng && lat) {
        Tee.$native.openLocation({
          name,
          address,
          latitude: lat,
          longitude: lng,
        });
      }
    },

    onLocationWebClick() {
      const { lng, lat, name } = this.verifyAddressInfo;

      try {
        ZNB.openLocation({
          name,
          latitude: lat,
          longitude: lng,
          address: this.address,
          scale: 14,
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>

<style lang="scss">
.address {
  padding: 10px 15px;
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #fff;
}

.address__content {
  flex: 1;
}

.address__content__name {
  color: #111;
  font-weight: bold;
  font-size: 14px;
  line-height: 20px;
}

.address__content__address {
  margin-top: 10px;
  color: #909090;
  font-size: 12px;
  line-height: 16px;
}

.address__content__distance {
  margin-top: 5px;
  color: #999;
  font-size: 10px;
  line-height: 14px;
}

.address__map__icon {
  margin: 0 5px 0 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 24px;
  width: 38px;
  background: url('https://img.yzcdn.cn/public_files/2018/12/17/b3caca272aa807ce5b3056ea043df7be.png')
    no-repeat right center;
  background-size: 14px 18px;
}
.t-hairline--left {
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    transform: scale(0.5);
    border-left-width: 1px;
  }
}
</style>
