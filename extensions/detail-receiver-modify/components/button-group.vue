<template>
  <view class="buttons">
    <view
      v-for="(btn, index) in btns"
      :key="index"
      :class="['button', btn.primary ? 'primary-btn' : '']"
      @click="handleClick(btn)"
    >
      {{ btn.text }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'button-group',
  props: {
    /**
     * @type {Array<{ text: string, type: string, primary: boolean }>}
     */
    btns: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  methods: {
    handleClick(btn) {
      this.$emit('click', btn);
    },
  },
};
</script>

<style lang="scss" scoped>
.buttons {
  position: absolute;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  left: 0;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  gap: 8px;
  justify-content: space-between;
  padding: 12px;
  margin-top: 20px;
  background-color: #fff;

  &::before {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #f7f8fa;
    transform: scaleY(0.5);
  }

  .button {
    flex: 1;
    height: 48px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 16px;
    background-color: #f2f3f5;
    color: #333;
  }

  .primary-btn {
    background-color: var(--main-bg, #7d6a57);
    color: #f8f8f8;
  }
}
</style>
