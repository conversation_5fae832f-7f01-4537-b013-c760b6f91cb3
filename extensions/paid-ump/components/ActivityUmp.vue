<template>
  <view class="poster" @click="activityClickHandler">
    <image :src="paidPromotion.imgUrl" mode="aspectFit" class="poster__image" />
  </view>
</template>

<script>
import args from '@youzan/weapp-utils/lib/args';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { PROMO_TYPE_ACTION } from '../constants';

export default {
  props: {
    orderNo: String,
    paidPromotion: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    activityClickHandler() {
      const { orderNo } = this;
      const { detailUrl, promotionType } = this.paidPromotion;
      const action = PROMO_TYPE_ACTION[promotionType];
      action &&
        action({
          orderNo,
          detailUrl,
          ...mapKeysCase.toCamelCase(args.getAll(detailUrl)),
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.poster {
  display: flex;
  height: 88px;
  box-sizing: content-box;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
  font-size: 0;
  width: calc(100vw - 24px);

  &__image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
}
</style>
