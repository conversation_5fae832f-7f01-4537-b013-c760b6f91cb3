import { BUTTON_CLASS, BTNS_MAP } from './constans';

export function getButtonClass(buttons, type) {
  const firstBtn = buttons[0];
  const mainButtonArr = [
    'SAVE_CARD_VOUCHER',
    'GIVE_AWAY',
    'SUBSCRIPTION',
    'SAVE_PICK_UP_CODE',
    BTNS_MAP.TAKE_GOODS_CODE,
    BTNS_MAP.PURCHASE_TAKE,
    BTNS_MAP.CARD_VOUCHER_EXHIBITION_DETAIL,
    BTNS_MAP.KEEP_SHOPPING, // 继续逛逛
  ];
  const isMainBtn = mainButtonArr.indexOf(type) !== -1;
  const isFirstBtn = firstBtn && firstBtn.type === type;
  const onlyOneBtn = buttons.length === 1;
  const hasMainBtn = mainButtonArr.some((btn) => buttons.indexOf(btn) > -1);
  const isPrimary = isMainBtn || isFirstBtn || onlyOneBtn || (type === 'DETAIL' && !hasMainBtn);
  return isPrimary ? BUTTON_CLASS.primary : BUTTON_CLASS.default;
}

// 盲盒订单类型
const ACTIVITY_TYPE = {
  blindBoxBuy: 401, // 盲盒购买订单
  blindBoxVerification: 402, // 盲盒核销订单
};

export function isBlindBox(activityType) {
  if (
    +activityType === ACTIVITY_TYPE.blindBoxBuy ||
    +activityType === ACTIVITY_TYPE.blindBoxVerification
  ) {
    return true;
  }
  return false;
}
