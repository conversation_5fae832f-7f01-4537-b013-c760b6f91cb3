import { requestV2 } from '@youzan/tee-biz-request';

export default function fetchData() {
  /* #ifdef web */
  const frequentlyPurchaseGoods = window?._global?.frequentlyPurchaseGoods || {};
  return frequentlyPurchaseGoods;
  /* #endif */
  /* #ifdef weapp */
  // eslint-disable-next-line no-unreachable
  return requestV2({
    path: '/wsctrade/order/get-frequently-purchase-goods.json',
    method: 'GET',
  });
  /* #endif */
}
