import { openBtns } from './contants';
import { BOTTOM, isIVRDisabled, isKosTokenVerifiedDisabled } from './constants';
import { buildUrl } from '@youzan/tee-biz-util';
import { object, url } from '@youzan/tee-util';

export const cloudData = {
  getBottomAction({ bottomBtns = [] }) {
    return {
      btns: bottomBtns.map(({ name, value }) => ({ name, type: value })),
    };
  },
  getOrderInfoV1: ({
    order = {} as Record<string, any>,
    orderBizExtra = {} as Record<string, any>,
    bottomBtns = [],
  }) => {
    const {
      orderNo,
      createTime,
      payTime,
      buyerMemo,
      stateStr,
      expressType,
      goodsPackages = [],
      afterSaleContact = {},
      isAllowConfirmReceive = false,
    } = order;
    const { isImOrder = false } = afterSaleContact;
    const { isShowEvaluate, isShowViewEvaluate } = orderBizExtra;

    const orderInfo = {
      orderNo,
      createTime,
      payTime,
      buyerMemo,
      stateStr,
      expressType,
      isShowIM: isImOrder,
      isShowExpress: !!(goodsPackages && goodsPackages.length),
      isShowConfirmOrder: isAllowConfirmReceive,
      isShowEvaluateOrder: isShowEvaluate,
      isShowViewEvaluate,
      isShowShareOrder: bottomBtns?.some((item) => item.value === 'share'),
      isShowExtendOrder: false,
      isShowCancelOrder: false,
      isShowBuyAgain: false,
      isAllowConfirmReceive: false,
    };

    openBtns.forEach((openBtn) => {
      const res = bottomBtns?.some((item) => openBtn.type.includes(item.value));
      orderInfo[openBtn.field] = res;
    });

    return orderInfo;
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);

// 获取底部按钮
const getAllBtns = ({
  order,
  gift,
  orderBizExtra,
  orderBizUrl,
  grouponLink,
  invoice = {} as Record<string, any>,
  miniprogram = {} as Record<string, any>,
  env,
  isIVR,
  bottomBtnsDisplay,
}) => {
  let btns = [];

  // 代付详情
  if (order.isAllowPeerpay) {
    const btn = BOTTOM.peerpay;
    btn.link = buildUrl(orderBizUrl.peerpayUrl || '', 'trade_youzan', order.kdtId);
    btns.push(btn);
  }

  // 确认收货
  if (order.isAllowConfirmReceive) {
    if (orderBizExtra.isHotel) {
      bottomBtnsDisplay.confirmHotel && btns.push(BOTTOM.confirmHotel);
    } else {
      btns.push(BOTTOM.confirm);
    }
  }

  // 延长收货
  if (order.isAllowLaterReceive) {
    btns.push(BOTTOM.extend);
  }

  // 查看团详情
  if (order.isAllowGroupon && !miniprogram.isAlipayApp) {
    const btn = BOTTOM.groupon;

    let link = `/packages/collage/groupon/detail/index?orderNo=${order.orderNo}&groupAlias=${order.groupBuy.alias}&type=${order.activityType}`;
    /* #ifdef web */
    link = object.get(order, 'groupBuy.groupDetailUrl', '');
    /* #endif */
    // 普通拼团
    btn.link = grouponLink || link;
    btns.push(btn);
  }

  // 我要晒订单
  if (order.isAllowShareOrder) {
    const btn = BOTTOM.share;
    btn.shareUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      `/packages/order/share-page/index?order_no=${order.orderNo}&kdt_id=${order.kdtId}`
    )}`;
    /* #ifdef weapp */
    btns.push(btn);
    /* #endif */

    /* #ifdef web */
    const fromSource = url.args.get('source', window.location.href);
    if (fromSource !== 'wx') {
      btns.push(btn);
    }
    /* #endif */
  }

  // 取消订单
  if (order.isAllowCancelOrder) {
    btns.push(BOTTOM.cancel);
  }

  // 领优惠券
  if (order.isAllowShareCoupon && !env.isYouzanmars) {
    btns.push({
      ...BOTTOM.coupon,
      version: order.shareCouponStyleVersion,
    });
  }

  // 小红书小程序暂时不支持再来一单
  if (!miniprogram.isXhsApp && !miniprogram.isKsApp) {
    // 再来一单
    if (order.isAllowDirectBuyAgain && order.directBuyAgainBtnConfig?.show && !env.isYouzanmars) {
      // 再来一单，直接进下单页
      btns.push(BOTTOM.directBuyAgain);
    } else if (order.isAllowBuyAgain) {
      btns.push(BOTTOM.buyAgain);
    }
  }

  // 查看课程
  if (orderBizExtra.showCourseDetail) {
    const btn = BOTTOM.courseDetail;
    /* #ifdef web */
    btn.link = buildUrl(`/wscvis/knowledge/index?p=mypay&kdt_id=${order.kdtId}`, 'h5', order.kdtId);
    /* #endif */

    /* #ifdef weapp */
    const url = buildUrl(
      `/wscvis/knowledge/index?p=mypay&kdt_id=${order.kdtId}`,
      'h5',
      order.kdtId
    );
    btn.link = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(url)}`;
    /* #endif */

    btns.push(btn);
  }

  // 补开发票
  if (invoice.isAllowInvoice) {
    const btn = BOTTOM.invoice;
    let link = `/packages/trade/order/invoice/index?type=detail&order_no=${order.orderNo}&kdt_id=${order.kdtId}`;
    /* #ifdef web */
    link = buildUrl(
      `/wsctrade/order/invoice?order_no=${order.orderNo}&kdt_id=${order.kdtId}`,
      'h5',
      order.kdtId
    );
    /* #endif */
    btn.link = link;
    btns.push(btn);
  }

  // 查看礼包
  if (gift && Object.keys(gift).length) {
    const { giftInvite, giftNo, giftSign } = gift;
    let link = '';
    const { alias = '', orderAlias = '', url = '' } = giftInvite || {};
    /* #ifdef weapp */
    if (alias && orderAlias) {
      link = `/packages/paidcontent/gift/index?alias=${alias}&share_alias=&channel_type=3&order_alias=${orderAlias}&gift_type=1`;
    }
    /* #endif */

    /* #ifdef web */
    if (giftInvite && giftInvite.url) {
      link = url;
    } else if (giftNo && giftSign) {
      // 实物礼包
      link = buildUrl(`/trade/gift?id=${giftNo}&sign=${giftSign}`, 'trade_youzan', order.kdtId);
    }
    /* #endif */
    if (link) {
      const btn = BOTTOM.gift;
      btn.link = link;
      btns.push(btn);
    }
  }

  // 评价
  if (orderBizExtra.isShowEvaluate) {
    let link = `/packages/evaluation/order/create/index?order_no=${order.orderNo}`;
    /* #ifdef web */
    link = orderBizUrl.evaluateUrl;
    /* #endif */
    if (link) {
      const btn = BOTTOM.evaluate;
      btn.link = link;
      btns.push(btn);
    }
  }

  // 查看评价
  if (orderBizExtra.isShowViewEvaluate) {
    let link = `/packages/evaluation/order/detail/index?order_no=${order.orderNo}`;
    /* #ifdef web */
    link = orderBizUrl.viewEvaluateUrl;
    /* #endif */
    if (link) {
      const btn = BOTTOM.viewEvaluate;
      btn.link = link;
      btns.push(btn);
    }
  }

  // 批量退款
  if (orderBizExtra.showBatchRefund || orderBizExtra.showWholeRefund) {
    let btn = BOTTOM.batchRefund;
    let link = `/packages/trade/order/batch-refund/index?order_no=${order.orderNo}&kdt_id=${order.kdtId}`;

    const { showWholeRefund = false } = orderBizExtra;
    if (showWholeRefund) {
      btn = BOTTOM.wholeAfterSales;
    }
    /* #ifdef web */
    link = buildUrl(
      `/wscafs/batch-refund/index?order_no=${order.orderNo}&kdt_id=${order.kdtId}`,
      'h5',
      order.kdtId
    );
    /* #endif */

    btn.link = link;
    btns.push(btn);
  }
  // 删除订单
  if (order.allowShowDeleteOrder) {
    const btn = BOTTOM.deleteOrder;
    btns.push(btn);
  }
  /* #ifdef web */
  // 过滤逻辑
  if (isIVR) {
    // 就要过滤掉 isIVR disabled 的按钮
    btns = btns.filter((item) => isIVRDisabled.indexOf(item.value) < 0);
  }

  if (orderBizExtra.isKosTokenVerified) {
    // 就要过滤掉 isKosTokenVerified disabled 的按钮
    btns = btns.filter((item) => isKosTokenVerifiedDisabled.indexOf(item.value) < 0);
  }

  // 针对美业订单屏蔽所有按钮
  if (order.bizType === 3) {
    btns = [];
  }

  // 赞拼拼订单只保留以下操作
  if (env?.isFxZpp) {
    btns = btns.filter((item) => [BOTTOM.confirm, BOTTOM.extend, BOTTOM.cancel].includes(item));
    // 群团团晒单. 未关闭 && 发货之后 && 可以晒单
    const { forQttOrderExtra } = window._global;
    if (!order.closeState && order.state >= 60 && forQttOrderExtra?.orderCommentShow) {
      if (forQttOrderExtra.orderCommentShow.createdAt) {
        const btn = BOTTOM.qttOrderCommentted;
        btns.push(btn);
      } else if (forQttOrderExtra.orderCommentShow.supportCommentShow) {
        const btn = BOTTOM.qttOrderComment;
        btns.push(btn);
      }
    }

    btns.unshift(BOTTOM.qttBuyAgain);

    // 已支付
    if (!order.closeState && order.state >= 20) {
      btns.push(BOTTOM.qttShare);
    }
  }
  /* #endif */

  return btns;
};

function buildGoodsList(goodsList, failGoodsList) {
  const failList = failGoodsList.map((failGoods) => {
    const goodsArr = goodsList.filter(
      (goods) => goods.sku_id === failGoods.sku_id && goods.goods_id === failGoods.goods_id
    );
    const foundGoods = goodsArr[0] || {};
    const sku = foundGoods.sku.map((item) => item.v).join(' ');
    return {
      title: foundGoods.title || foundGoods.goods_info.title || '',
      num: foundGoods.num,
      sku,
    };
  });

  const successList = goodsList.filter(
    (goods) =>
      !failGoodsList.some(
        (failGoods) => goods.sku_id === failGoods.sku_id && goods.goods_id === failGoods.goods_id
      )
  );

  return {
    failList,
    successList,
  };
}

export { getAllBtns, buildGoodsList };
