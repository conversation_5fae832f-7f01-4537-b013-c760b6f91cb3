<template>
  <view class="top-tips" v-if="showBindAccount && inited">
    <text
      >想要查看微信等其他渠道的订单？
      <!-- #ifdef web -->
      <navigator style="display: inline" :href="clientUrl.loginWithCodeUrl">点这里</navigator>
      <!-- #endif -->
    </text>
  </view>
</template>

<script>
import get from '@youzan/utils/object/get';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    let clientUrl = {};
    /* #ifdef web */
    clientUrl = get(window, '_global.clientUrl', {});
    /* #endif */
    return {
      clientUrl,
      openAppConfig: {},
      userInfo: { buyerId: 0 },
      inited: false,
    };
  },

  computed: {
    showBindAccount() {
      return !this.userInfo.buyerId && get(this.openAppConfig, 'bindYzAccount', false);
    },
  },

  created() {
    mapData(this, ['userInfo', 'openAppConfig'], {
      callback: () => {
        this.inited = true;
      },
    });
  },
};
</script>

<style lang="scss" scoped>
.top-tips {
  font-size: 12px;
  color: #f60;
  background-color: #fff7cc;
  padding: 10px;
  line-height: 1.8em;

  a {
    color: #4990e2;
  }

  .t-icon,
  span {
    vertical-align: middle;
  }

  .t-icon {
    margin-right: 10px;
    font-size: 13px;
  }
}
</style>
