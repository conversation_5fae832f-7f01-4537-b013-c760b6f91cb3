import { errorToast } from '@youzan/tee-biz-util';
import { batchUpdateSelectGoods } from '../../../api';

export default function () {
  return {
    // ios小程序批量把教育线上课商品取消勾选
    batchUnSelectEduOnlineGoods() {
      if (!this.isIOS || this.onlineCourseUnselectFlag) {
        return;
      }
      const needUnselectGoods = [];
      this.computedGoodsGroupList.forEach((goods) => {
        goods.goodsList.forEach((item) => {
          if (item.goodsType === 31 && item.bizExtension?.cartBizMark?.isOnlineCourse === '1') {
            needUnselectGoods.push(item);
          }
        });
      });
      // 批量操作
      const params = {
        kdtId: this.kdtId,
        goodsList: needUnselectGoods,
      };

      if (needUnselectGoods?.length) {
        batchUpdateSelectGoods(params)
          .then(() => {
            this.onlineCourseUnselectFlag = true;
            this.refreshCartGoodsList();
          })
          .catch((err) => {
            errorToast(err);
          });
      }
    },
  };
}
