import type { Instance } from '@youzan/ranta-helper';
import { mapEvent } from '@youzan/ranta-helper-tee';
import { object } from '@youzan/tee-util';

import { formatOrder } from './web/format';
import api from './weapp/api';
import CloudExtension from './cloud/CloudExtension';
import { getRechargeInfo } from './common/prepaid';

export default class PageSetupExtension extends CloudExtension<PageSetupExtension> {
  constructor(options) {
    super(options);
    this.initData();
    this.ctx.process.invoke('getOrderEnterShopPolicy');

    mapEvent(this as unknown as Instance, {
      refreshOrderInfo: () => {
        window.location.reload();
      },
    });

    this.initEvents();
    this.initProcesses();
  }

  onPageShow() {
    Object.assign(this.ctx.data, formatOrder());
    // @ts-ignore emit无参数
    setTimeout(() => this.ctx.event.emit('onLoadOrderInfo'), 1000);
    this.initTradeUmpV1();
  }

  initEvents() {
    // @ts-ignore
    this.ctx.event.once('onLoadOrderInfo', () => {
      // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
      this.ctx.cloud.emit('onOrderLoadedEvent'); // for cloud 1.0
    });
  }

  // 营销支付优化白名单判断
  initTradeUmpV1() {
    api
      .getTradeUmpV1()
      .then((canUse) => {
        this.ctx.data.canUseTradeUmpV1 = canUse;
      })
      .catch(() => false);
  }

  // 初始化process
  initProcesses() {
    // 在地址列表中选择地址
    this.ctx.process.define('afterReceiverInfoUpdated', () => {
      window.location.reload();
    });

    this.ctx.process.define('setReceivingContent', (content) => {
      this.ctx.data.receivingContent = content;
    });

    this.ctx.process.define('setShowMessage', (isShow) => {
      this.ctx.data.showMessage = isShow;
    });

    this.ctx.process.define('setItemList', (newItemList) => {
      this.ctx.data.itemList = newItemList;
    });

    this.ctx.process.define('setBottomBtnsDisplay', (payload) => {
      this.ctx.data.bottomBtnsDisplay = payload;
    });

    this.ctx.process.define('toggleBlockDisplay', (blockName, isShow) => {
      switch (blockName) {
        case 'OrderAddress':
          this.ctx.data.showOrderAddress = isShow;
          break;
        case 'OrderStatus':
          this.ctx.data.showOrderStatus = isShow;
          break;
      }
    });
  }

  initData() {
    const orderExtra = object.get(window, '_global.forOrderExtra', {});
    this.ctx.data.kdtId = object.get(window, '_global.kdtId');
    this.ctx.data.flowEntranceBannerBizName = 'order_detail';
    this.ctx.data.requestExtraParams = 'note,coupon';
    this.ctx.data.buyerId = object.get(window, '_global.buyerId');
    this.ctx.data.rxNo = object.get(window, '_global.rxNo');
    this.ctx.data.orderExtra = orderExtra;
    this.ctx.data.outerPromotion = object.get(window, '_global.outerPromotion');
    this.ctx.data.receivingContent = '';
    this.ctx.data.showMessage = true;
    this.ctx.data.orderBizExtra = {};
    this.ctx.data.order = {};
    this.ctx.data.bottomBtnsDisplay = {
      confirmHotel: true,
    }; // for 有赞云，如果有值且为false则强制隐藏
    this.ctx.data.showOrderAddress = true; // for 有赞云
    this.ctx.data.showOrderStatus = true; // for 有赞云

    // 批发订单详情、赞拼拼订单 不展示CPS周边好物入口 & 商品推荐模块, 包括小程序&H5
    const isWholesaleOrder = object.get(window, '_global.orderInfo.isWholesaleOrder', false);
    const isFxZpp = object.get(window, '_global.env.isFxZpp', false);

    if (!isWholesaleOrder && !isFxZpp) {
      this.ctx.data.bizName = 'order_detail';
      this.ctx.data.cpsConfigKey = 'cps_goods_recommend_order_detail';
    }
    this.ctx.data.rechargeInfo = getRechargeInfo(orderExtra);
  }
}
