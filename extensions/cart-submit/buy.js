import get from '@youzan/utils/object/get';
import each from '@youzan/weapp-utils/lib/each';
import money from '@youzan/weapp-utils/lib/money';
import { getStorageSync } from '@youzan/tee-api';
import args from '@youzan/utils/url/args';

const presentCookieKey = 'pick_present_activity_id_map';

/**
 * 获取商品属性ID列表
 * @param {Array} properties
 */
function getPropertyIds(properties = []) {
  const propertyIds = [];
  properties.forEach((property) => {
    const propValueList = get(property, 'propValueList', []);
    propValueList.forEach((value) => {
      propertyIds.push(value.propValueId);
    });
  });

  return propertyIds;
}

export function generateBuyGoodsList(goodsList = []) {
  const order = goodsList.map((goods) => {
    let bizTracePointExt = '';
    let message = '';
    try {
      const extraAttribute = JSON.parse(goods.extraAttribute);
      bizTracePointExt = extraAttribute.bizData || '';
      message = JSON.parse(goods.messages || null);
    } catch (e) {
      console.warn(e);
    }
    const result = {
      activityAlias: '',
      activityId: 0,
      activityType: 0,
      message,
      num: goods.num,
      price: goods.payPrice,
      skuId: goods.skuId,
      goodsId: goods.goodsId,
      kdtId: goods.kdtId,
      bizTracePointExt,
      propertyIds: getPropertyIds(goods.properties),
    };
    if (+goods.activityType === 24) {
      Object.assign(result, {
        activityId: goods.activityId,
        activityType: 24,
      });
    }
    return result;
  });
  return order;
}

/**
 * 营销后端要求在购物车生成bookKey时只传给后端用户明确选择过的赠品数据，如果用户没选择过则过滤掉
 * JIRA: https://jira.qima-inc.com/browse/ONLINE-648108
 * 营销后端：虚谷、嘉志
 * @param presentData
 */
const filterPickedPresentData = (presentData) => {
  if (!presentData) return {};
  const pickPresentCookieData = getStorageSync(presentCookieKey) || {};
  const pickedActivityIds = Object.keys(pickPresentCookieData).filter((activityId) =>
    /\d+/.test(activityId)
  );
  presentData.SELECTED_PRESENTS = presentData.SELECTED_PRESENTS.filter((present) =>
    pickedActivityIds.includes(String(present.activityId))
  );
  return presentData;
};

const filterSelectedPromotions = (selectedPromotions) => {
  if (!selectedPromotions) return {};
  const { activityType, activityId } = selectedPromotions;
  if (activityType) {
    return {
      APPOINT_PROMOTION_INSTANCES: JSON.stringify([
        {
          activityId,
          umpType: activityType,
          appointUse: true,
        },
      ]),
    };
  }
  return {};
};

// 生成下单数据
export const generateBuyOrderData = (goodsList = [], presentData = {}, selectedPromotions) => {
  const config = {
    canyinChannel: 1,
    canyinIds: [],
  };
  const items = [];
  const itemSources = [];
  const activities = [];

  const sellers = [
    {
      kdtId: get(goodsList, '[0].kdtId'),
      storeId: get(goodsList, '[0].storeId', 0),
    },
  ];

  goodsList.forEach((goods) => {
    let bizData;
    try {
      /* eslint-disable-next-line */
      bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
    } catch (e) {
      /* eslint-disable-next-line */
      console.log(e);
    }
    const baseId = {
      kdtId: goods.kdtId,
      goodsId: goods.goodsId,
      skuId: goods.skuId,
      propertyIds: goods.propertyIds || [],
      activityId: goods.activityId,
      activityType: +goods.activityType || 0,
    };

    // 外部订单来源
    const tpps = get(goods, 'bizExtension.cartBizMark.tpps');
    const { birthdayRelation = {} } = goods;
    const item = {
      ...baseId,
      storeId: goods.storeId || 0,
      price: goods.payPrice || 0,
      pointsPrice: goods.pointsPrice || 0,
      num: goods.limitNum ? Math.min(goods.num, goods.limitNum) : goods.num,
      itemMessage: goods.messages || '',
      extensions: {
        tpps,
        CART_ID: goods.cartId,
        BIRTHDAY_RELATION_NET_ORDER_MARK: birthdayRelation.birthdayRelationNetOrderMark,
        RELATION_BLESSING: birthdayRelation.relationBlessing,
        RELATION_TYPE_ID: birthdayRelation.relationTypeId,
        RELATION_TYPE_NAME: birthdayRelation.relationTypeName,
      },
      isSevenDayUnconditionalReturn: goods.isSevenDayUnconditionalReturn || false,
    };
    /* #ifdef web */
    // 私域直播购物车下单
    const pdlive = args.get('pdlive', window.location.href);
    if (pdlive) {
      const pdliveInfo = args.get('pdlive_info', window.location.href);
      item.extensions.ATTR_PL_PLAN_ID = pdlive;
      item.extensions.ATTR_PL_INFO = decodeURIComponent(decodeURIComponent(pdliveInfo));
    }
    /* #endif */
    // 支持商品套餐
    if (goods.comboDetail?.groupList?.length) {
      const subComboList = [];
      goods.comboDetail.groupList.forEach((group) => {
        group.subComboList.forEach((subCombo) => {
          const { goodsId, groupId, num, skuId, properties = [] } = subCombo;
          const propertyIds = properties.map((item) => item.valId);
          subComboList.push({ goodsId, groupId, num, skuId, propertyIds });
        });
      });
      item.combo = {
        comboType: goods.comboDetail.comboType,
        subComboList,
      };
    }

    if (goods.deliverTime) item.deliverTime = goods.deliverTime;

    // 0元抽奖活动标识
    const promotionMark = get(goods, 'bizExtension.cartBizMark.promotionMark', '');
    if (promotionMark) item.promotionMark = promotionMark;

    const itemSource = {
      ...baseId,
      bizTracePointExt: bizData,
      cartCreateTime: goods.createdTime,
      cartUpdateTime: goods.updatedTime,
    };

    const activity = {
      ...baseId,
      activityAlias: goods.activityAlias,
    };

    items.push(item);
    itemSources.push(itemSource);
    activities.push(activity);

    if (goods.canyinId) {
      config.canyinIds.push(goods.canyinId);
    }

    // 餐饮加料按单个商品计算
    (goods.ingredientInfoList || []).forEach((feedGoods) => {
      items.push({
        kdtId: feedGoods.kdtId,
        goodsId: feedGoods.goodsId,
        skuId: feedGoods.skuId,
        num: goods.num,
        price: feedGoods.price,
        extensions: { tpps },
      });

      activities.push({
        kdtId: feedGoods.kdtId,
        goodsId: feedGoods.goodsId,
        skuId: feedGoods.skuId,
      });
    });
  });

  return {
    config,
    items,
    sellers,
    source: {
      itemSources,
      orderFrom: 'cart',
    },
    ump: {
      activities,
    },
    extensions: {
      ...filterPickedPresentData(presentData),
      ...filterSelectedPromotions(selectedPromotions),
    },
  };
};

// 根据kdtId goodsId skuId 在商品列表中找出对应勾选的商品列表
export function findGoodsBuyIds(goodsIdsList = [], goodsList = []) {
  return goodsList.filter((goods) => {
    const { kdtId, goodsId, skuId, activityId } = goods;
    const id = [kdtId, goodsId, skuId, activityId].join('-');
    return goodsIdsList.indexOf(id) !== -1;
  });
}

export function separateGoods(kvGoods, titleMap) {
  const result = [];
  each(kvGoods, (list, key) => {
    if (!list || !list.length) {
      return;
    }
    const item = {
      title: titleMap[key],
      list,
    };
    let totalPrice = 0;
    each(list, (goods) => {
      totalPrice += goods.payPrice;
    });
    item.totalPrice = money(totalPrice).toYuan();
    result.push(item);
  });
  return result;
}

export function separateGoodsForMixType(goodsList, shopCart) {
  // 分类普通，周期购，海淘商品
  const goodsMap = {
    HAITAO: {
      title: '海淘商品',
      list: [],
    },
    PERIOD_BUY: {
      title: '周期购商品',
      list: [],
    },
    COMMON: {
      title: '普通商品',
      list: [],
    },
    COURSE: {
      title: '课程',
      list: [],
    },
  };
  const data = [];

  const PLUS_BUY_TYPE = 24;

  const plusBuyGoods = (goodsList || []).filter((item) => +item.activityType === PLUS_BUY_TYPE);
  const notPlusBuyGoodsTypeList = (goodsList || [])
    .filter((item) => +item.activityType !== PLUS_BUY_TYPE)
    .map((goods) => get(goods, 'settlementRule.settlementMark'));
  // 除加价购商品之外，选中的商品是否都是海淘
  const isAllHaiTaoGoods = notPlusBuyGoodsTypeList.every((item) => item === 'HAITAO');
  // 除加价购商品之外，选中的商品是否存在海淘
  const isHasHaiTaoGoods = notPlusBuyGoodsTypeList.some((item) => item === 'HAITAO');

  // 都是海淘商品且有加价购商品，那就不用分开结算
  const isHaitaoPlusBuy = plusBuyGoods.length > 0 && isAllHaiTaoGoods;
  // 存在海淘商品且不全是海淘商品且有加价购商品，需要分开结算
  // 分开结算这种情况下，需要判断加价购商品是否需要归类到海淘商品下
  const isLimitHaitaoPlusBuy = plusBuyGoods.length > 0 && !isAllHaiTaoGoods && isHasHaiTaoGoods;

  const plusBuyGroup = {};
  if (isLimitHaitaoPlusBuy) {
    const { goodsGroupList = [] } = shopCart || {};
    // 根据加价购活动类型，获取对应的商品分组
    goodsGroupList
      .filter((item) => +(item.groupActivityInfo || {}).activityType === PLUS_BUY_TYPE)
      .forEach((item) => {
        plusBuyGroup[item.groupActivityInfo.activityId] = item.goodsList;
      });
  }

  // 分类
  each(goodsList, (goods) => {
    let mark = get(goods, 'settlementRule.settlementMark');
    if (!goodsMap[mark]) {
      mark = 'COMMON';
    }

    // 存在海淘商品且不全是海淘商品且有加价购活动这种情况下，需要判断加价购商品是否需要归类到海淘商品下
    if (Object.keys(plusBuyGroup).length > 0) {
      const { activityId, activityType } = goods;
      // 判断当前活动类型是否是加价购
      if (+activityType === PLUS_BUY_TYPE) {
        // 获取当前加价购活动分组下的商品列表
        const group = plusBuyGroup[activityId] || [];
        // 看下商品分组里面的是不是有海淘商品且是被选中的
        const haitaoGoods = group.filter(
          (g) =>
            get(g, 'settlementRule.settlementMark') === 'HAITAO' &&
            goodsList.find((item) => item.goodsId === g.goodsId)
        );
        // 如果存在的话，说明当前选中的海淘商品有选中的加价购商品，那就把当前的加价购商品mark改成haitao，跟海淘商品一起结算
        if (haitaoGoods.length > 0) {
          mark = 'HAITAO';
        }
      }
    }

    goodsMap[mark].list.push(goods);
  });

  // 过滤
  each(goodsMap, (item) => {
    if (item.list.length) {
      data.push(item);
    }
  });

  if (data.length > 1) {
    const params = {
      needSeparate: true,
      hasCourse: data.some((item) => item.title === '课程'),
      data,
    };
    // 都是海淘商品且有加价购商品，那就不用分开结算
    if (isHaitaoPlusBuy) {
      params.needSeparate = false;
    }
    return params;
  }
  return {
    needSeparate: false,
  };
}

export function separateGoodsForLogistics(goods) {
  const map = {
    NORMAL_EXPRESS: {
      title: '快递发货',
      expressType: 0,
      list: [],
    },
    LOCAL_DELIVERY: {
      title: '同城配送',
      expressType: 2,
      list: [],
    },
    SELF_TAKE: {
      title: '到店自提',
      expressType: 1,
      list: [],
    },
  };
  const data = [];
  const itemsLength = goods.length;
  // 是否存在共同的物流方式
  let needSeparate = true;

  // 无物流商品 -- 非实物商品
  let noLogisticsGoodsNum = 0;

  each(goods, (item) => {
    const { logisticsTypeList } = item;

    // 虚拟商品没有物流方式，需要推到所有物流方式中
    if (Array.isArray(logisticsTypeList) && logisticsTypeList.length === 0) {
      noLogisticsGoodsNum++;
      each(map, (logistics) => {
        logistics.list.push(item);
      });
    }

    each(logisticsTypeList, (type) => {
      const logistics = map[type];
      if (!logistics) {
        return;
      }

      logistics.list.push(item);
    });
  });

  each(map, (val) => {
    const listLength = val.list.length;
    if (listLength > noLogisticsGoodsNum || listLength === itemsLength) {
      data.push(val);
    }

    if (listLength === itemsLength) {
      needSeparate = false;
    }
  });

  return {
    needSeparate,
    data,
  };
}
