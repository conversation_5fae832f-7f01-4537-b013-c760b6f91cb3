<template>
  <view>
    <!-- 交易组件3.0屏蔽相关操作后面会下线! -->
    <van-notice-bar
      mode="link"
      text="视频号订单，请前往视频号订单中心操作"
      @click="handleShowWxVideoGuide"
      v-if="isTradeComponent3"
    />
    <!-- #ifdef web -->
    <view v-else-if="shouldShowMsg && !isSourceFromWx" class="order-tips">
      <p v-if="isShowBuyWayTips">你已选择{{ buyWayStr }}，签收无误后按卖家约定支付货款。</p>
      <p v-if="isShowChangePriceTips">该订单已改价至 {{ realPay }}元，请与商家核实后重新下单。</p>
      <block v-else-if="showWeixin">
        <p>
          关注微信公众号[
          <text @click="handleOpenMerchantFollow" class="order-tips__mp-weixin js-open-follow">{{
            mpWeixin
          }}</text
          >]，发送“我的订单”查看订单信息
        </p>
      </block>
      <block v-else-if="showYouzanQrcode">
        <text>
          关注
          <text @click="handleOpenYouzanFollow" class="order-tips__mp-weixin">有赞公众号</text
          >，查看订单物流
        </text>
      </block>
      <p v-if="showBookmarkText">请收藏该页面地址，方便查询订单状态。</p>
      <subscription-dialogs @close="onShowSubscription(false)" :show="showSubscription" />
    </view>
    <!-- #endif -->

    <!-- 交易组件3.0订单引导弹窗,后面会下掉 -->
    <van-popup :show="showWxVideoGuide" round position="bottom" bind:close="onWxVideoGuideClose">
      <view class="wxvideo-guide-pop">
        <view class="pop-header">查看/处理视频号订单</view>
        <image
          src="https://b.yzcdn.cn/public_files/b4bd57493510a13fe591b1c6e8bd65f0.png"
          mode="widthFix"
          class="wxvideo-guide-img"
          alt=""
        />
        <view class="wxvideo-guide-btn" @click="onWxVideoGuideClose">我知道了</view>
      </view>
    </van-popup>
  </view>
</template>

<script>
/* #ifdef web */
import { object } from '@youzan/tee-util';
/* #endif */
import SubscriptionDialog from './components/Subscription';
import NoticeBar from '@youzan/vant-tee/dist/notice-bar';
import { Popup } from '@vant/tee';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef web */
import { fetchYouzanFollowWhiteList } from './api';
/* #endif */

export default {
  name: 'order-tips',
  components: {
    'subscription-dialogs': SubscriptionDialog,
    'van-notice-bar': NoticeBar,
    'van-popup': Popup,
  },

  data() {
    return {
      order: {},
      orderExtra: {},
      mpAccount: {},
      paymentInfo: {},
      addressInfo: {},
      env: {},
      isSourceFromWx: false,
      isFans: false,
      showSubscription: false,
      showWxVideoGuide: false,
      // 有赞公众号提示条黑名单
      isHideYouzanFollowTips: false,
    };
  },

  computed: {
    userName() {
      // 收货人姓名
      return this.addressInfo && this.addressInfo.receiverName ? this.addressInfo.receiverName : '';
    },

    showWeixin() {
      // buy_way: 1:微信自有支付 10:大账号代销 11:受理模式
      return (
        this.mpAccount &&
        this.mpWeixin &&
        !this.isFans &&
        ([1, 10, 11].indexOf(this.order.buyWay) >= 0 || this.env.isWeixin)
      );
    },

    showYouzanQrcode() {
      const mpAccount = this.mpAccount || {};
      return (
        !this.isHideYouzanFollowTips &&
        !this.mpWeixin &&
        this.env.isWeixin &&
        !mpAccount.id &&
        !mpAccount.nickname
      );
    },

    isAlipayApp() {
      let isAlipayApp = false;
      // 是否支付宝小程序里的 webview
      /* #ifdef web */
      isAlipayApp = object.get(window, '_global.miniprogram.isAlipayApp', false);
      /* #endif */
      return isAlipayApp;
    },

    isQQApp() {
      let isQQApp = false;
      //  是否支付宝小程序里的 webview
      /* #ifdef web */
      isQQApp = object.get(window, '_global.miniprogram.isQQApp', false);
      /* #endif */
      return isQQApp;
    },

    isFxZpp() {
      //  是否赞拼拼小程序里的 webview
      let isFxZpp = false;
      /* #ifdef web */
      isFxZpp = object.get(window, '_global.env.isFxZpp', false);
      /* #endif */
      return isFxZpp;
    },

    showBookmarkText() {
      return (
        !this.env.isWeixin &&
        !this.isAlipayApp &&
        !this.isQQApp &&
        !this.env.isYouzanwxd &&
        !this.env.isThirdApp
      );
    },

    isShowBuyWayTips() {
      // 购买方式提示：buy_way=9(货到付款) && express_type==0(快递发货) && receiver_name(收货人姓名)非空
      return this.order.buyWay === 9 && this.order.expressType === 0 && this.userName;
    },

    // refund_type字段还未提供有效值 2017-10-10
    isShowChangePriceTips() {
      return this.order.refundType && +this.order.refundType === 11;
    },

    // 是否禁止显示顶部提示
    forbidTips() {
      let isTTApp = false;
      /* #ifdef web */
      const res = object.get(window, '_global.miniprogram', {});
      isTTApp = res.isTTApp;
      /* #endif */
      return isTTApp;
    },

    shouldShowMsg() {
      if (this.forbidTips || this.isFxZpp) {
        return false;
      }
      return (
        this.isShowBuyWayTips ||
        this.isShowChangePriceTips ||
        this.showWeixin ||
        this.showBookmarkText ||
        this.showYouzanQrcode
      );
    },

    buyWayStr() {
      return this.order.buyWayStr || '';
    },

    realPay() {
      return this.paymentInfo.deductedRealPay || 0;
    },

    mpId() {
      return this.mpAccount.id || 0;
    },

    mpWeixin() {
      return this.mpAccount.weixin_account || '';
    },

    // 交易组件3.0屏蔽相关操作后面会下线!
    isTradeComponent3() {
      const { orderExtra } = this;

      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}');
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION === 'TRADE_COMPONENT_3_0' ||
          BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN'
        );
      } catch (error) {
        return false;
      }
    },
  },

  created() {
    mapData(this, [
      'order',
      'mpAccount',
      'paymentInfo',
      'addressInfo',
      'env',
      'isSourceFromWx',
      'isFans',
      'orderExtra',
    ]);
    /* #ifdef web */
    this.showWeixin && import('@youzan/tee-biz-fullguide');

    fetchYouzanFollowWhiteList().then((res) => {
      this.isHideYouzanFollowTips = res;
    });

    /* #endif */
    // this.showWeixin &&
    //   import(
    //     /* webpackChunkName: 'order/detail/fullguide' */ '@shared/components/fullguide'
    //   );
  },

  methods: {
    handleOpenMerchantFollow() {
      this.ctx.logger.log({
        et: 'view',
        ei: 'seller_qr_display',
        en: '商家二维码曝光',
        params: {
          component: 'seller_qr_display',
        },
      });
    },

    handleOpenYouzanFollow() {
      this.ctx.logger.log({
        et: 'view',
        ei: 'youzan_qr_display',
        en: '有赞大号二维码曝光',
        params: {
          component: 'youzan_qr_display',
        },
      });
      this.onShowSubscription(true);
    },

    // 是否展示订阅物流弹窗
    onShowSubscription(show) {
      this.showSubscription = show;
    },

    handleShowWxVideoGuide() {
      this.showWxVideoGuide = true;
    },

    onWxVideoGuideClose() {
      this.showWxVideoGuide = false;
    },
  },
};
</script>

<style lang="scss">
.order-tips {
  font-size: 14px;
  line-height: 20px;
  padding: 10px 16px;
  color: #ed6a0c;
  background-color: #fffbe8;

  &__mp-weixin {
    color: #38f;
  }
}

.wxvideo-guide-pop {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pop-header {
    font-weight: 500;
    font-size: 16px;
    line-height: 44px;
    margin-bottom: 8px;
  }

  .wxvideo-guide-btn {
    width: 91.4%;
    margin-bottom: 5px;
    height: 40px;
    margin-top: 16px;
    background: linear-gradient(270deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 20px;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    color: #fff;
  }

  .wxvideo-guide-img {
    width: 318px;
  }
}
</style>
