import {
  SelectAll,
  SelectGoods as SelectGoodsType,
  SetGoodsNumParams,
} from '@youzan-cloud/cloud-biz-types';

export interface ThemeColors {
  themeMainBgColor: string;
  themeMainBgAlpha10Color: string;
}

type DeleteGoodsItem = {
  kdtId: number;
  cartId: string;
  goodsId: number;
  skuId: number;
  activityId: number;
};

export type DeleteGoodsParamsV1 = {
  goods: DeleteGoodsItem;
  isActivity: boolean;
};

export type BatchDeleteGoodsParams = {
  goodsList: DeleteGoodsItem[];
};

export type SelectGoodsParamsV1 = {
  rangeType: 'single' | 'shop' | 'all';
  goods?: {
    kdtId: number;
    cartId: number;
  };
  kdtId?: number;
  isActivity?: boolean;
};

export type SetGoodsNumParamsV1 = {
  val: number;
  goods: {
    kdtId: number;
    cartId: number;
  };
};

export type ReselectGoodsParams = {
  goods: {
    cartId: string; // 商品唯一 Id
    skuId: number; // 新的 sku id
    propertyIds: number[]; // 新的商品属性 id 列表
    messages?: string; // 新的商品留言
    kdtId: number;
  };
};

export type GoodsListParams = {
  cartId: number;
  kdtId: number;
  goodsId: number;
  skuId: number;
  sku: string;
  checked: boolean;
  activityId: number;
  storeId: number;
  channelId: number;
  canyinId: number;
  alias: string;
  title: string;
  attachmentUrl: string;
  num: number;
  payPrice: string;
  originPrice: number;
  stock: number;
  limitNum: number;
  activityType: number;
  activityAlias: string;
  messages: string;
  unique: string;
  revive: number;
  weight: number;
};

export type PopupActivityInfo = {
  activityType: number;
  activityDuration: string;
  activityDesc: string;
  activityTags: string[];
};

export type ActivityBtnParams = {
  activityInfo: {
    activityAlias: string;
    activityId: number;
    activityType: string;
    activityTypeAlias: string;
  };
  goodsList: any;
};

export interface TradeCardSetGoodsNumParams extends SetGoodsNumParams {
  /** 当前商品的加购记录ID */
  cartId: number;
}

export type TradeCartSetGoodsNumParams = TradeCardSetGoodsNumParams;
export type SelectGoods = {
  /** 需要选中的商品列表 */
  goodsList: SelectGoodsType[];
};
export type SelectGoodsParams = SelectAll | SelectGoods;
