<template>
  <view :style="themeCSS">
    <!-- #ifdef web -->
    <stock-coupon-bottom-action v-if="showCouponAction" :goods="stockCouponGoods" />
    <bottom-action v-else />
    <!-- #else -->
    <bottom-action />
    <!-- #endif -->
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      stockCouponGoods: null,
      themeCSS: '',
    };
  },
  computed: {
    showCouponAction() {
      /* #ifdef web */
      const { useNewReserve, miniprogram = {} } = window._global;
      const { isTTApp, isXhsApp } = miniprogram;
      const canReserveNum = this.stockCouponGoods?.canReserveNum > 0;

      if (useNewReserve) return canReserveNum;

      return (isTTApp || isXhsApp) && canReserveNum;
      /* #endif */
    },
  },
  created() {
    mapData(this, ['themeCSS', 'stockCouponGoods']);
  },
};
</script>
