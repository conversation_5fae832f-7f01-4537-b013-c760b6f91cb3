<template>
  <view class="order-pay-prompt-expire-tip">
    <view class="order-pay-prompt-expire-tip__item" :key="item" v-for="item in text">
      <view
        :key="item"
        v-if="'${field1}' === item"
        class="order-pay-prompt-expire-tip__text"
        :style="{ color: themeColors.general }"
      >
        <text>{{ desc.field1 }}</text>
      </view>
      <view
        :key="item"
        v-else-if="'${field2}' === item"
        class="order-pay-prompt-expire-tip__text"
        :style="{ color: themeColors.general }"
      >
        <text>{{ desc.field2 }}</text>
      </view>

      <van-count-down
        use-slot
        :key="item"
        v-else-if="'${expireTime}' === item"
        class="order-pay-prompt-expire-tip__countdown"
        :time="remianingSeconds"
        @change="onChangeTime"
      >
        <text :style="{ color: themeColors.general }">
          {{ timeData.days ? timeData.days + '天 ' : '' }}{{ timeData.hours }}:{{
            timeData.minutes
          }}:{{ timeData.seconds }}
        </text>
      </van-count-down>
      <view class="order-pay-prompt-expire__text" v-else
        ><text>{{ item }}</text></view
      >
    </view>
  </view>
</template>

<script>
import CountDown from '@youzan/vant-tee/dist/count-down/index.vue';

export default {
  components: {
    'van-count-down': CountDown,
  },

  props: {
    desc: {
      type: Object,
      default: () => ({}),
    },
    themeColors: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      timeData: {},
    };
  },

  computed: {
    text() {
      let template = this.desc?.template;
      if (!template) {
        return [];
      }

      const regex = /(\${field1}|\${field2}|\${expireTime})/g;
      template = template.split(regex);
      return template;
    },

    remianingSeconds() {
      if (!this.desc?.expireTime) {
        return;
      }
      return Math.round(this.desc.expireTime - Date.now());
    },
  },

  methods: {
    onChangeTime(e) {
      const { days, hours, minutes, seconds } = e;
      this.timeData = {
        days,
        hours: hours >= 10 ? hours : `0${hours}`,
        minutes: minutes >= 10 ? minutes : `0${minutes}`,
        seconds: seconds >= 10 ? seconds : `0${seconds}`,
      };
      if (hours === 0 && minutes === 0 && seconds === 0) {
        this.$emit('close');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$text-color: #646566;
$primary-text-color: #ee0a24;

.order-pay-prompt-expire-tip {
  font-size: 14px;
  color: $text-color;
  width: 100%;
  text-align: center;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  &__item {
    display: inline-block !important;
  }

  &__text {
    color: $primary-text-color;
  }

  &__countdown {
    max-width: 98px;
    display: inline-block;
    color: $primary-text-color;
    padding: 0 4px;
  }
}
</style>
