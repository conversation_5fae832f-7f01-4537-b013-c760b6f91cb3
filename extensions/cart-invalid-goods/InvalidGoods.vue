<template>
  <view>
    <view class="goods-header--wrapper">
      <view class="goods-header">
        <view class="goods-header--title">失效商品</view>
        <view class="goods-header--btn" @click="clearGoods"> 清空失效商品 </view>
      </view>
    </view>
    <view class="goods-block">
      <goods-block
        v-for="(it, idx) in unavailableItems"
        :key="idx"
        :goods="it"
        @item-delete="handleItemDelete"
        @to-goods-detail="toGoodsDetail"
        :cloud-cart-id="it.cartId"
        :cloud-goods-id="it.goodsId"
        :cloud-index="idx"
      />
    </view>
  </view>
</template>

<script>
import { cdnImage, errorToast } from '@youzan/tee-biz-util';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import { mapData } from '@youzan/ranta-helper-tee';
import Api from './api';
import { GOODS_TAG_MAP } from './constants';

export default {
  data() {
    return {
      unavailableItems: [],
      GOODS_TAG_MAP,
      hasValidGoods: true,
    };
  },
  externalClasses: ['custom-class'],

  created() {
    mapData(this, ['hasValidGoods']);
    mapData(this, ['unavailableItems'], {
      callback: () => {
        this.getRecommendList();
      },
    });
  },

  methods: {
    clearGoods() {
      this.ctx.process.invoke('clearInvalidGoods');
    },
    handleItemDelete({ cartId }) {
      const { goods } = this._findGoodsInList(cartId);

      const currentGoods = goods;
      return Api.deleteCartItem(currentGoods)
        .then(() => {
          // 删除失效商品
          this.ctx.event.emit('updateCartGoodsList');
        })
        .catch((err) => {
          errorToast(err, { message: '商品删除失败，请稍后重试' });
        });
    },

    _findGoodsInList(cartId) {
      const goodsIndex = this.unavailableItems.findIndex((item) => item.cartId === cartId); // Optimization: Used findIndex
      return {
        goodsGroupIndex: -1,
        goodsIndex,
        goods: this.unavailableItems[goodsIndex],
      };
    },

    getRecommendList() {
      const goodsIds = this.unavailableItems.map((item) => item.goodsId);
      goodsIds.length &&
        Api.getMultiRecommendGoods(goodsIds)
          .then((reGoodsLists = []) => {
            const temp = JSON.parse(JSON.stringify(this.unavailableItems));
            this.unavailableItems = temp.map((item) => {
              /**
               * 注意：如果goodsIds有相同值，reGoodsLists中的第二个相同值的数据不会被使用
               * 举例：
               * 入参goodsIds = [123, 123],
               * 出参：reGoodsList = [{goodsId: 123, recommendList: []}, {goodsId: 123, recommendList: []}]
               * 代码逻辑：匹配goodsId 123时只会采用第一个数据的recommendList
               * 这个结果符合产品预期，相关人：尼莫、阿标、虹静
               * https://jira.qima-inc.com/browse/ONLINE-662239
               */
              const recommendListObj =
                reGoodsLists.find((listObj) => item.goodsId === listObj.goodsId) || {};
              return {
                ...item,
                recommendListObj: {
                  ...recommendListObj,
                  recommendList:
                    recommendListObj.recommendList?.map((recommendItem, index) => ({
                      ...recommendItem,
                      imageUrl: cdnImage(recommendItem.imageUrl, '!200x200.jpg'),
                      price: (recommendItem.price / 100).toFixed(2),
                      bannerId: `cart~expired_goods_recommend~${index}~${makeRandomString(8)}`,
                    })) || [],
                },
              };
            });
          })
          .catch();
    },

    toGoodsDetail(params) {
      this.ctx.process.invoke('navigateFromCart', params);
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-header {
  align-items: center;
  display: flex;
  font-size: var(--eo-font-size-14, 14px);
  line-height: 1.4;
  padding: var(--theme-page-card-padding-top, 17px) var(--theme-page-card-padding-right, 12px)
    var(--theme-page-card-padding-botton, 0) var(--theme-page-card-padding-left, 16px);
  position: relative;
  justify-content: space-between;

  &--wrapper {
    background: #fff;
  }

  &--title {
    font-size: 16px;
    font-weight: 500;
    color: #111;
  }

  &--btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: calc(88px * var(--font-size-scale, 1));
    height: calc(26px * var(--font-size-scale, 1));
    font-size: var(--eo-font-size-14, 14px);
    box-sizing: border-box;
    color: #d71609;
  }
}

.goods-block {
  overflow: hidden;
}

.goods-item {
  min-height: 96px;
  padding: 12px;

  &--right {
    position: relative;
    margin-left: 104px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.goods-title {
  margin-bottom: 8px;
}

.goods-title-not-exist {
  margin-bottom: 0;
}

.goods-title-text {
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  vertical-align: middle;
}

.goods-title-tag {
  margin-right: 2px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;

  &.HAITAO {
    width: 28px;
  }

  &.PERIOD_BUY {
    width: 38px;
  }
}

.goods-img {
  float: left;
  height: 96px;
  width: 96px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  background-size: cover;
  border-radius: var(--theme-radius-card, 8px);
  overflow: hidden;

  image {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    left: 0;
    height: auto;
    max-height: 100%;
    max-width: 100%;
    width: auto;
    background: #fff;
  }
}

.err-msg {
  font-size: 14px;
  color: #323233;
  line-height: 16px;
}
</style>
