<template>
  <view>
    <van-cell-group custom-class="service" :border="false">
      <!-- 配送方式 -->
      <van-cell v-if="showDeliveryType">
        <view class="express">
          <view class="express__col--left">
            <view class="express__way">配送方式</view>
            <view
              v-if="showCheckDeliveryScope"
              class="express__desc-link"
              @click="toggleExpressPopup"
            >
              同城配送－配送范围说明
            </view>
          </view>
          <view class="express__col--right">
            <view>{{ service.postage }}</view>
            <view style="font-size: 12px">{{ service.expressType }}</view>
          </view>
        </view>
      </van-cell>

      <!-- 定时达 -->
      <van-cell
        v-if="showDeliveryTime"
        title="送达时间"
        title-width="80px"
        title-class="service-cell__title"
        value-class="service-cell__value"
        :value="service.deliveryTime"
      />

      <!-- 发票状态 -->
      <van-cell
        v-if="invoice.show"
        :is-link="!!invoice.url"
        title="发票"
        title-class="service-cell__title"
        @click="onInvoiceClick"
      >
        <view class="invoice">
          <view>{{ invoice.value }}</view>
          <view v-if="invoice.tip" class="invoice__tip">{{ invoice.tip }}</view>
        </view>
      </van-cell>

      <!-- 买家留言 -->
      <van-cell
        v-if="showMessage && cloudConfig.isShowBuyerMsg"
        title="买家留言"
        title-width="80px"
        title-class="service-cell__title2"
        value-class="service-cell__value2"
        :value="service.buyerMemo"
        :border="true"
      />

      <!-- #ifdef web -->
      <!-- 团长留言 -->
      <van-cell
        v-if="isFxZpp"
        title="团长留言"
        title-width="80px"
        title-class="service-cell__title2"
        value-class="service-cell__value2"
        :value="service.founderToMemberMark"
        :border="true"
      />
      <!-- #endif -->

      <!-- 查看优惠券 -->
      <!-- #ifdef web -->
      <!-- 抖音点单小程序不需要展示，当前只有web模式，在这个模式做一下区分 -->
      <van-cell
        v-if="showLookCoupon && !isTTApp"
        title="查看已购优惠券"
        title-width="100px"
        title-class="service-cell__sub-title"
        value=" "
        is-link
        :border="true"
        @click="onCouponClick"
      />
      <!-- #else -->
      <van-cell
        v-if="showLookCoupon"
        title="查看已购优惠券"
        title-width="100px"
        title-class="service-cell__sub-title"
        value=" "
        is-link
        :border="true"
        @click="onCouponClick"
      />
      <!-- #endif -->
    </van-cell-group>

    <!-- 酒店退订提示 -->
    <view v-if="service.showHotelUnsubscribeRemind" class="hotel-unsubscribe-remind">
      <view class="hotel-unsubscribe-remind__title">
        <van-icon name="warning" class="hotel-unsubscribe-remind__warn-icon" />
        退订提示
      </view>
      <view class="hotel-unsubscribe-remind__text">
        由于房间售卖不可退订，特殊情况（住院，当日接到航班取消等）请联系酒店协商处理。
      </view>
      <view v-if="service.serviceTel" class="hotel-unsubscribe-remind__link" @click="onServiceCall">
        联系商家：{{ service.serviceTel }}
      </view>
    </view>

    <!-- 配送方式 popup -->
    <van-popup :show="showExpressPopup" round position="bottom" @close="toggleExpressPopup">
      <view class="express-popup">
        <view class="express-popup__title t-hairline--bottom">
          <text>同城配送－配送范围说明</text>
          <van-icon
            class="express-popup__close"
            size="18px"
            name="close"
            @click="toggleExpressPopup"
          />
        </view>

        <view class="express-popup__desc">
          <view class="express-popup__desc__title">配送范围：</view>
          <view class="express-popup__desc__content">
            {{ service.localDeliveryDesc }}
          </view>
          <image
            v-if="service.localDeliveryImg"
            class="express-popup__desc__img"
            mode="widthFix"
            :src="service.localDeliveryImg"
          />
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script>
// Components
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

// Dependencies
import Tee from '@youzan/tee';
import { mapState } from '@ranta/store';
import { buildUrl } from '@youzan/tee-biz-util';
import { makePhoneCall } from '@youzan/tee-api';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-icon': Icon,
    'van-popup': Popup,
  },

  props: {
    cloudConfig: {
      type: Object,
      default: () => ({
        isShowBuyerMsg: true,
      }),
    },
  },

  data() {
    return {
      showDeliveryType: false,
      showCheckDeliveryScope: false,
      showDeliveryTime: false,
      showLookCoupon: false,
      service: {},
      showExpressPopup: false,
      showMessage: true, // for 有赞云
      /* #ifdef web */
      isTTApp: window._global?.miniprogram?.isTTApp,
      /* #endif */
      ...mapState(this, ['kdtId', 'orderNo', 'invoice', 'invoiceStatus']),
    };
  },

  computed: {
    isFxZpp() {
      let isFxZpp = false;
      /* #ifdef web */
      isFxZpp = _global?.env?.isFxZpp;
      /* #endif */
      return isFxZpp;
    },
  },

  created() {
    mapData(this, [
      'showMessage',
      'showDeliveryType',
      'showDeliveryTime',
      'showLookCoupon',
      'service',
      'showCheckDeliveryScope',
    ]);
  },

  methods: {
    toggleExpressPopup() {
      this.showExpressPopup = !this.showExpressPopup;
    },

    onInvoiceClick() {
      Tee.navigate({ url: this.invoice.url });
    },

    onCouponClick() {
      let url = '/packages/user/coupon/list/index?type=promocard';
      /* #ifdef web */
      const { kdtId } = this;
      url = buildUrl(`/wscump/coupon/collection?kdt_id=${kdtId}`, 'h5', kdtId);
      /* #endif */
      Tee.navigate({ url });
    },

    onServiceCall() {
      makePhoneCall({ phoneNumber: this.service.serviceTel });
    },
  },
};
</script>

<style lang="scss">
.service-cell__title {
  color: #969799 !important;
}

.service-cell__value {
  color: #323233 !important;
}

.service-cell__title2 {
  color: #969799 !important;
}

.service-cell__value2 {
  color: #323233 !important;
}

.service-cell__sub-title {
  color: rgba(0, 0, 0, 0.5) !important;
}

.service {
  margin-top: 10px;
}

.express {
  display: flex;
  justify-content: space-between;
  line-height: 1.6;
  font-size: 14px;
  color: #323233;
}

.express__col--left {
  text-align: left;
}

.express__col--right {
  text-align: right;
}

.express__way {
  color: #969799;
}

.express__desc-link {
  color: #38f;
}

.express-popup {
  width: 100vw;
  background: #fff;
}

.express-popup__title {
  position: relative;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.express-popup__close {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.express-popup__desc {
  max-height: 350px;
  padding: 12px 15px;
  line-height: 1.4;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.express-popup__desc::-webkit-scrollbar {
  display: none;
}

.express-popup__desc__title {
  font-size: 14px;
}

.express-popup__desc__content {
  font-size: 14px;
  word-break: break-all;
  color: #666;
}

.express-popup__desc__img {
  width: 100%;
  margin-top: 12px;
}

.invoice {
  color: #111;
  line-height: 1.6;
}

.invoice__tip {
  font-size: 12px;
}

.hotel-unsubscribe-remind {
  background: #fff;
  margin-top: 10px;
  padding: 16px;
  font-size: 14px;

  &__title {
    line-height: 18px;
    font-weight: bold;
  }

  &__text {
    margin: 4px 0 6px;
  }

  &__warn-icon {
    color: #f44;
    vertical-align: -2px;
  }

  &__link {
    color: #1989fa;
  }
}

.t-hairline--bottom {
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    transform: scale(0.5);
    border-bottom-width: 1px;
  }
}
</style>
