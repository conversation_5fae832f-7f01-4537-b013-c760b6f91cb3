import OrderStatus from './OrderStatus.vue';
import { cloud } from '@youzan/ranta-helper';
import type { OrderStatusData } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class OrderStatusExtension {
  ctx: any;

  /**
   * orderStatus
   * @desc 订单状态
   * @type {OrderStatus}
   */
  @cloud('orderStatus', 'data')
  orderStatus: OrderStatusData;

  constructor(options) {
    this.ctx = options.ctx;
    mapData(this, ['orderStateInfo'], {
      callback: () => {
        const {
          orderState,
          orderStateTitle,
          autoReceiveOrderTime = 0,
          progressBar = [],
          closeType,
          closeTypeStr,
        } = this.ctx.data.orderStateInfo;
        const newOpenData = {
          orderStatus: cloudData.getOrderStatus({
            orderState,
            orderStateTitle,
            autoReceiveOrderTime,
            progressBar,
            closeType,
            closeTypeStr,
          }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    OrderStatus,
  };
}
