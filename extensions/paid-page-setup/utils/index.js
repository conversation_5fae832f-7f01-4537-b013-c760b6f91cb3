import each from '@youzan/weapp-utils/lib/each';
import { args } from '@youzan/tee-util/lib/common/url';
import { PAY_RESULT_TEMPLATE } from './constant';

// 根据kdtId goodsId skuId 在商品列表中找出对应勾选的商品列表
export function findGoodsBuyIds(goodsIdsList = [], goodsList = []) {
  return goodsList.filter((goods) => {
    const { kdtId, goodsId, skuId, activityId } = goods;
    const id = [kdtId, goodsId, skuId, activityId].join('-');
    return goodsIdsList.indexOf(id) !== -1;
  });
}

export function separateGoodsForLogistics(goods) {
  const map = {
    NORMAL_EXPRESS: {
      title: '快递发货',
      expressType: 0,
      list: [],
    },
    LOCAL_DELIVERY: {
      title: '同城配送',
      expressType: 2,
      list: [],
    },
    SELF_TAKE: {
      title: '到店自提',
      expressType: 1,
      list: [],
    },
  };
  const data = [];
  const itemsLength = goods.length;
  // 是否存在共同的物流方式
  let needSeparate = true;

  // 无物流商品 -- 非实物商品
  let noLogisticsGoodsNum = 0;

  each(goods, (item) => {
    const { logisticsTypeList } = item;

    // 虚拟商品没有物流方式，需要推到所有物流方式中
    if (Array.isArray(logisticsTypeList) && logisticsTypeList.length === 0) {
      noLogisticsGoodsNum++;
      each(map, (logistics) => {
        logistics.list.push(item);
      });
    }

    each(logisticsTypeList, (type) => {
      const logistics = map[type];
      if (!logistics) {
        return;
      }

      logistics.list.push(item);
    });
  });

  each(map, (val) => {
    const listLength = val.list.length;
    let totalPrice = 0;

    if (listLength > noLogisticsGoodsNum || listLength === itemsLength) {
      each(val.list, (item) => {
        totalPrice += item.payPrice * item.num;
      });

      val.totalPrice = totalPrice;
      data.push(val);
    }

    if (listLength === itemsLength) {
      needSeparate = false;
    }
  });

  return {
    needSeparate,
    data,
  };
}

export function requestQuery(data = {}) {
  const { orderNo: requestNo, phasePaymentStage: phase } = data;
  const query = { requestNo };
  if (phase) {
    query.phase = phase;
  }
  return query;
}

export function callbackGenerator(type) {
  /* #ifdef weapp */
  const app = getApp();
  return () => {
    app.logger.log({
      et: 'click',
      ei: 'share_result',
      en: '分享结果',
      params: {
        share_result: type,
      },
      si: app.getKdtId(),
    });
  };
  /* #endif */
}

export function routeHelper(route, pageTemplate, query) {
  let formatPath = '';
  // 在wait 状态下 应该跳转到中转页面的路径
  if (
    route.indexOf('success') < 0 &&
    (pageTemplate === PAY_RESULT_TEMPLATE.NORMAL || pageTemplate === PAY_RESULT_TEMPLATE.NEWHOPE)
  ) {
    // 在成功状态下 应该跳转到成功的路径
    const path = '/packages/paid/pay-result/success/index';
    formatPath = args.add(path, query);
  } else if (route.indexOf('auction') < 0 && pageTemplate === PAY_RESULT_TEMPLATE.AUCTION) {
    // 在降价拍状态下 应该跳转到降价拍的路径
    const path = '/packages/paid/pay-result/auction/index';
    formatPath = args.add(path, query);
  }
  return formatPath;
}

const BLIND_BOX_ACTIVITY_TYPE = 401;

export function isBlindBoxOrder(activityType) {
  return +activityType === BLIND_BOX_ACTIVITY_TYPE;
}
