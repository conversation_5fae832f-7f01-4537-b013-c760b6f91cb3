<template>
  <view class="messages">
    <van-field
      v-if="isEditing"
      class="input--edit"
      :border="false"
      placeholder-style="color: #ccc;"
      placeholder="写点祝福语吧～"
      custom-style="opacity: 0.7;border-radius: 5px;"
      :maxlength="20"
      :value="message"
      @input="handleInputUpdate"
      @blur="handleInputUpdate"
      :show-confirm-bar="false"
    >
    </van-field>
    <text v-else @click="handleMessageEdit" class="input-display-text">{{ message }}</text>

    <block v-if="presenterView">
      <van-icon
        v-if="!isEditing"
        @click="handleMessageEdit"
        name="edit"
        color="#B28D80"
        size="20"
        style="margin-top: 2px"
      />
      <view v-else class="message-edit-finish" @click="handleMessageFinish"> 完成 </view>
    </block>
  </view>
</template>

<script>
import { requestV2 } from '@youzan/tee-biz-request';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import debounce from '@youzan/weapp-utils/lib/debounce';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Field from '@youzan/vant-tee/dist/field/index';

let nickFn = null;
export default {
  components: {
    'van-icon': Icon,
    'van-field': Field,
  },
  props: {
    presenterView: Boolean,
    message: String,
    giftId: String,
  },

  data() {
    return {
      isEditing: false,
      newMemo: '',
    };
  },

  created() {
    nickFn = debounce((value) => {
      this.newMemo = value;
    }, 500);
    this.newMemo = this.message;
  },

  methods: {
    handleInputUpdate(e) {
      nickFn(e.value);
    },

    handleMessageEdit() {
      if (!this.presenterView) return;
      const isEditing = !this.isEditing;
      this.isEditing = isEditing;
    },

    handleMessageFinish() {
      // 未变更留言
      if (this.newMemo === this.message) {
        this.handleMessageEdit();
        return;
      }
      // 为空
      if (!this.newMemo) {
        Object.assign(this, {
          message: this.message,
          newMemo: this.message,
        });

        Toast('留言不能为空');
        return;
      }

      this.updateMemo().then(() => {
        Object.assign(this, {
          message: this.newMemo,
        });
        this.handleMessageEdit();
      });
    },

    updateMemo() {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/changegiftmemo.json',
          data: {
            giftAlias: this.giftId,
            memo: this.newMemo,
          },
        })
          .then((res) => {
            resolve(res);
          })
          .catch((res) => {
            const err = res.data || {};
            Toast(err.msg || '更新留言失败，请尝试重新编辑');
            Object.assign(this, {
              message: this.message,
            });
            reject(err);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.messages {
  margin-top: 5px;
  line-height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  .message-edit-finish {
    display: inline-block;
    line-height: 26.5px;
    color: #5f729a;
    margin-left: 280px;
    position: absolute;
  }

  .input--edit {
    width: 220px;
    text-align: left;
    padding: 5px;
  }

  .input-display-text {
    max-width: 580rpx;
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding-right: 5px;
    position: relative;
    font-size: 16px;
    color: #b19286;
  }
}
</style>
