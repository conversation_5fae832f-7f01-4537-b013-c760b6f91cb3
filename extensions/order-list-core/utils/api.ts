import { QUERY_PAGE_TYPE } from './constants';
import { requestV2 } from '@youzan/tee-biz-request';
/* #ifdef web */
import args from '@youzan/utils/url/args';
/* #endif */

interface ApiGetReasonListParams {
  orderNo: string;
  kdtId: number;
}
interface ApiGetReasonListReturn {
  isShowItemPutInCartButton: boolean;
}
const API_GET_REASON_LIST = ({ orderNo, kdtId }: ApiGetReasonListParams) => {
  return requestV2<ApiGetReasonListReturn>({
    path: '/wsctrade/order/cancel-order/reason-list.json',
    method: 'GET',
    data: {
      order_no: orderNo,
      kdt_id: kdtId,
    },
  }) as Promise<ApiGetReasonListReturn>;
};

interface ApiGetScrmLevelParams {
  kdtId: number;
  goodsId: number;
}
const API_GET_SCRM_LEVEL = (params: ApiGetScrmLevelParams) => {
  return requestV2({
    path: '/wsctrade/scrm/level.json',
    method: 'GET',
    data: {
      goodsId: params.goodsId,
      kdtId: params.kdtId,
    },
  });
};

/**
 * 获取海报图片入参
 */
interface GetPosterParams {
  /* 订单号 */
  orderNo: string;
  /* 分享的链接(用于海报上的二维码) */
  shareUrl: string;
}

/**
 * 获取海报图片
 * @param orderNo {string} 订单号
 * @param shareUrl {string} 分享的链接(用于海报上的二维码)
 * @return {string} 生成的海报图片地址
 */
const getPoster = ({ orderNo, shareUrl }: GetPosterParams) => {
  return requestV2({
    path: '/wsctrade/poster/order-share.json',
    method: 'GET',
    data: {
      url: shareUrl,
      order_no: orderNo,
    },
  }) as Promise<string>;
};

/**
 * 获取再来一单[直接进入下单页]配置
 */
const fetchDirectBuyAgainBtnConfig = () => {
  return requestV2({
    path: '/wsctrade/order/getDirectBuyAgainBtnConfig.json',
  });
};

interface ApiDirectBuyAgainParams {
  orderNo: string;
  kdtId: number;
  preToastDesc: string;
}
const API_DIRECT_BUY_AGAIN = (params: ApiDirectBuyAgainParams): Promise<Record<string, any>> => {
  return requestV2({
    path: '/wsctrade/order/directBuyAgain.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
      preToastDesc: params.preToastDesc,
    },
  });
};

interface ApiBuyAgainParams {
  orderNo: string;
  kdtId: number;
}
const API_BUY_AGAIN = (params: ApiBuyAgainParams) => {
  return requestV2({
    path: '/wsctrade/order/buyAgain.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
    },
  });
};

interface ApiGetVoucherParams {
  activityId: string;
}
const API_GET_VOUCHER = (params: ApiGetVoucherParams): Promise<unknown> => {
  return requestV2({
    path: '/wsctrade/order/detail/getVoucher.json',
    method: 'POST',
    contentType: 'application/json',
    errorMsg: '领取失败',
    data: {
      activityId: params.activityId,
      bizName: 'order_list',
      source: 'order_list',
    },
  });
};

interface ApiCancelOrderParams {
  orderNo: string;
  kdtId: number;
  reason: string;
  isBuyAgain: boolean;
}
const API_CANCEL_ORDER = (params: ApiCancelOrderParams) => {
  return requestV2({
    path: '/wsctrade/order/cancelOrder.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
      reason: String(params.reason),
      isBuyAgain: params.isBuyAgain ? 1 : 0,
    },
  });
};

interface ApiDeleteOrderParams {
  orderNo: string;
}
const API_DELETE_ORDER = (params: ApiDeleteOrderParams) => {
  return requestV2({
    path: '/wsctrade/order/deleteOrder.json',
    method: 'POST',
    data: { order_no: params.orderNo },
  });
};

interface ApiSelfFetchDetailParams {
  kdtId: number;
  orderNo: string;
}
const API_SELF_FETCH_DETAIL = (params: ApiSelfFetchDetailParams) => {
  return requestV2({
    path: '/wsctrade/order/selffetch/detail.json',
    method: 'GET',
    data: params,
  });
};

interface ApiCheckOrderDelayReceiveParams {
  orderNo: string;
  kdtId: number;
}
const API_CHECK_ORDER_DELAY_RECEIVE = (params: ApiCheckOrderDelayReceiveParams) => {
  return requestV2({
    path: '/wsctrade/order/checkOrderDelayReceive.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
    },
  });
};

interface ApiDelayReceiveParams {
  orderNo: string;
  kdtId: number;
}
const API_DELAY_RECEIVE = (params: ApiDelayReceiveParams) => {
  return requestV2({
    path: '/wsctrade/order/delayReceive.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
    },
  });
};

interface ApiRemindExpressParams {
  orderNo: string;
  kdtId: number;
}
const API_REMIND_EXPRESS = (params: ApiRemindExpressParams) => {
  return requestV2({
    path: '/wsctrade/order/remindExpress.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
    },
  });
};

interface ApiConfirmReceiveV2Params {
  orderNo: string;
  kdtId: number;
}
const API_CONFIRM_RECEIVE_V2 = (params: ApiConfirmReceiveV2Params) => {
  return requestV2({
    path: '/wsctrade/order/confirmReceiveV2.json',
    method: 'POST',
    data: {
      order_no: params.orderNo,
      kdt_id: params.kdtId,
    },
  });
};

interface ApiOneOrderParams {
  orderNo: string;
  canUseTradeUmpV1: boolean;
  isDrugList: 'true' | 'false';
  retailFulfillDetail: number;
}
const API_ONE_ORDER = (params: ApiOneOrderParams) => {
  return requestV2({
    path: '/wsctrade/order/oneOrder.json',
    method: 'GET',
    data: {
      resultFormat: 'camelCase',
      order_no: params.orderNo,
      /* #ifdef web */
      from: args.get('from', location.href),
      /* #endif */
      retailFulfillDetail: params.retailFulfillDetail,
    },
  });
};

interface ApiGetOrderListParams {
  // eslint-disable-next-line camelcase
  page_id: 'wsc' | '1';
  page: number;
  // eslint-disable-next-line camelcase
  page_size: number;
  type: string;
  orderMark: string;
  from: string;
  knowledgeGoods: string;
  receivertel: string;
  receivername: string;
  goodstitle: string;
  keyword: string;
  isDrugList: 'true' | 'false';
  caller: string;
  haveOfflineOrder: string;
  // eslint-disable-next-line camelcase
  page_type: string;
  canUseTradeUmpV1: boolean;
}
const API_GET_ORDER_LIST = (params: ApiGetOrderListParams) => {
  return requestV2({
    path: '/wsctrade/order/list.json',
    data: {
      resultFormat: 'camelCase',
      withRepurchaseCoupon: 'false',
      ...params,
      /* #ifdef web */
      from: args.get('from', location.href),
      /* #endif */
    },
    method: 'GET',
    config: {
      noStoreId: true,
      // @ts-ignore
      skipKdtId: params.page_type === QUERY_PAGE_TYPE.ALL,
      // @ts-ignore
      skipShopInfo: params.page_type === QUERY_PAGE_TYPE.ALL,
    },
  });
};

const API_QUERY_CPS_CONFIG = () => {
  return requestV2({
    path: '/wscstatcenter/cps/queryCpsConfig.json',
    data: {
      params: JSON.stringify({
        configKeys: ['cps_goods_recommend_order_list'],
      }),
    },
  });
};

const API_USE_TRADE_UMP_V1 = () => {
  return requestV2({
    method: 'GET',
    path: '/wscump/trade/use-trade-ump-v1.json',
  });
};

/**
 * 批量获取多个订单的复购券信息
 * @param params
 * @constructor
 */
const API_GET_ORDER_REPURCHASE_COUPON = (params: {
  orderList: Array<{
    orderNo: string;
    status: number;
    goodsIdList: number[];
  }>;
}) => {
  return requestV2({
    method: 'POST',
    path: '/wsctrade/order/getRepurchaseCoupon.json',
    data: params,
  });
};

export default {
  API_CONFIRM_RECEIVE_V2,
  API_ONE_ORDER,
  API_GET_ORDER_LIST,
  API_GET_REASON_LIST,
  API_GET_SCRM_LEVEL,
  API_QUERY_CPS_CONFIG,
  API_USE_TRADE_UMP_V1,
  API_GET_ORDER_REPURCHASE_COUPON,
  API_BUY_AGAIN,
  API_GET_VOUCHER,
  API_CANCEL_ORDER,
  API_DELETE_ORDER,
  API_SELF_FETCH_DETAIL,
  API_CHECK_ORDER_DELAY_RECEIVE,
  API_DELAY_RECEIVE,
  API_REMIND_EXPRESS,
  getPoster,
  fetchDirectBuyAgainBtnConfig,
  API_DIRECT_BUY_AGAIN,
};
