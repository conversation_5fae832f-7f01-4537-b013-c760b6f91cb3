<template>
  <view v-if="showWxSubscribe">
    <van-overlay mask :show="showTips" z-index="1" />
    <view class="wrapper" v-if="showTips" @click="closeTips">
      <image
        src="https://img.yzcdn.cn/upload_files/2020/03/25/Fq24gCcTJhE3bpk9giOf7LPqC-7H.gif"
        alt=""
        class="wrapper--image"
      />
    </view>
    <view @click="clickSubscription">
      <view class="wx-subscription">
        <image
          src="https://b.yzcdn.cn/pay-result/images/weixin2.png"
          alt="wechat"
          class="wx-subscription__image"
        />
        <view class="wx-subscription__text">
          <view class="wx-subscription__text-main">订阅微信通知</view>
          <view class="wx-subscription__text-des">及时获取订单进度提醒</view>
        </view>
        <view class="wx-subscription__btn" :style="themeColorStyle">
          {{ hasSubscribe ? '已订阅' : '立即订阅' }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Overlay from '@youzan/vant-tee/dist/overlay/index.vue';
import { requestTemplateIds } from './api';
import { checkRetailShop } from '@youzan/utils-shop';
import { mapData } from '@youzan/ranta-helper-tee';

const app = getApp();
const SCENE_ENUM = {
  0: 'deliveryDetail',
  1: 'order_pay_success_by_self_fetch',
  2: 'order_pay_success_by_local_delivery',
};

export default {
  components: {
    'van-overlay': Overlay,
  },
  data() {
    return {
      showTips: false,
      hasSubscribe: false,
      templateIds: {},
      themeColors: '',
      themeGeneralColor: '',
    };
  },
  computed: {
    showWxSubscribe() {
      const isShowWxSubscribe =
        this.templateIds.length > 0 && Tee.$native.canIUse('requestSubscribeMessage');
      if (isShowWxSubscribe) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'subscription_order_view', // 事件标识
            en: '订阅消息曝光', // 事件名称
            params: {
              component: 'subscribe_order',
            }, // 事件参数
          });
      }
      return isShowWxSubscribe;
    },
    themeColorStyle() {
      return `color: ${this.themeGeneralColor}`;
    },
  },
  created() {
    mapData(this, {
      themeColors: (val) => {
        const themeGeneralColor = val.general;
        this.themeColors = val;
        this.themeGeneralColor = themeGeneralColor;
      },
      payResult: (payResult) => {
        const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
        const isRetailShop = checkRetailShop(shopMetaInfo);
        this.isRetailShop = isRetailShop;
        let scene = '';
        if (isRetailShop) {
          const { expressType } = payResult;
          scene = SCENE_ENUM[expressType];
          this.curScene = scene;
        } else {
          scene = 'afterPaySuccess';
        }
        this.fetchTemplateIds(scene);
      },
    });
  },
  methods: {
    clickSubscription() {
      const { isRetailShop, curScene } = this;
      const { templateIds = [] } = this;
      if (this.hasSubscribe) return; // 已经订阅不在重复执行
      this.ctx.process.invokePipe('requestSubscribeMessagePush', {
        templates: templateIds,
        onFail: (err) => {
          let errorMsg = '订阅微信通知失败';
          if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
            errorMsg = '请在小程序设置中允许订阅消息';
          }
          app.logger.requestError({
            name: 'wxSubscribeError',
            message: errorMsg,
            alert: 'info',
            detail: err,
          });
        },
        onSuccess: (res) => {
          if (JSON.stringify(res).indexOf('accept') > -1) {
            this.hasSubscribe = true;
            app.logger &&
              app.logger.log({
                et: 'click', // 事件类型
                ei: 'allow_click', // 事件标识
                en: '点击允许', // 事件名称
                params: {
                  component: 'subscribe_order',
                }, // 事件参数
              });
          } else if (JSON.stringify(res).indexOf('reject') > -1) {
            Toast('请在小程序设置中允许订阅消息');
            app.logger.requestError({
              name: 'wxSubscribeError',
              message: '请在小程序设置中允许订阅消息',
              alert: 'info',
              detail: res,
            });

            app.logger &&
              app.logger.log({
                et: 'click', // 事件类型
                /* cspell:disable-next-line */
                ei: 'cancle_click', // 事件标识
                en: '点击取消', // 事件名称
                params: {
                  component: 'subscribe_order',
                }, // 事件参数
              });
          }
          if (isRetailShop) {
            const accept = JSON.stringify(res).indexOf('accept') > -1; // 这是我照着其他地方抄的，如果不对请自行调整，因为原先这个accept压根就没有定义eslint会报错
            app.logger?.log({
              et: 'click',
              ei: accept ? 'accept_msg_subscribe' : 'reject_msg_subscribe',
              en: accept ? '接受授权订阅小程序消息' : '拒绝授权订阅小程序消息',
              params: {
                subscribe_pos: '支付完成页', // 订阅点 从哪个位置发起的订阅
                // subscribe_source: subscribeSource, // 来源 是微信小程序还是24h货架
                delivery_way: curScene, // 配送方式  到店自提、同城配送、快递  若没有则不传
              },
            });
          }
        },
        onShowTips: () => {
          this.showTips = true;
        },
        onCloseTips: () => {
          this.showTips = false;
        },
      });

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_wx_subscription',
          en: '点击订阅消息',
          si: app.getKdtId(),
        });
    },
    closeTips() {
      this.showTips = false;
    },
    fetchTemplateIds(scene) {
      requestTemplateIds({ scene }).then((res) => {
        this.templateIds = res.templateIdList || [];
      });
    },
  },
};
</script>

<style lang="scss">
// 微信订阅弹窗样式
.wx-subscription {
  width: 93.6%;
  height: 64px;
  margin: 0 auto;
  padding: 9px 12px 9px 7px;
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  margin-top: 12px;
  border-radius: 4px;

  &__image {
    width: 52px;
    height: 40px;
  }

  &__text {
    flex: 1;
    margin-left: 8px;

    &-main {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      line-height: 20px;
    }

    &-des {
      font-size: 12px;
      color: #7d7e80;
      font-weight: 400;
      line-height: 16px;
      margin-top: 4px;
    }
  }

  &__btn {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    border-radius: var(--theme-radius-button, 15px);
  }
}

.wrapper {
  position: fixed;
  width: 200px;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;

  &--image {
    width: 200px;
    height: 146px;
    border-radius: 4px;
  }
}
</style>
