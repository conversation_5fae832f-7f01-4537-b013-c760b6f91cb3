# @wsc-tee-trade/cart-coupon-bar

有效商品

![UI呈现](https://img.yzcdn.cn/public_files/cf0998b9f42b3770842e2139c4fb66d9.png)

## 调试方式

购物车存在有效商品时

## 存疑

## Widget.Provide

| 名称                 | 说明 |
| -------------------- | ---- |
| ValidGoods `default` | ???  |
| Present              | ???  |

## Widget.Consume

| 名称         | 说明 |
| ------------ | ---- |
| PresentGoods | ???  |
| Present      | ???  |

## Component.Consume

| 名称          | 说明 |
| ------------- | ---- |
| GoodsGroupUmp | ???  |
| GoodsItemUmp  | ???  |

## Event.Emit

| 名称                | 说明 |
| ------------------- | ---- |
| changeSku           | ???  |
| updateCartGoodsList | ???  |

## Event.Listen

| 名称                   | 说明 |
| ---------------------- | ---- |
| skuChanged             | ???  |
| toggleCheckedAll       | ???  |
| deleteCartItems        | ???  |
| presentPopupClose      | ???  |
| presentPopupConfirm    | ???  |
| cartGoodsListDidUpdate | ???  |

## Lambda.Consume

| 名称     | 说明 |
| -------- | ---- |
| hexToRgb | ???  |

## Data.Provide

| 名称             | 类型 | 默认值 | 说明 |
| ---------------- | ---- | ------ | ---- |
| checkedGoodsList | ???  | ???    | ???  |
| isCheckedAll     | ???  | ???    | ???  |
| presentData      | ???  | ???    | ???  |
| presentPopupInfo | ???  | ???    | ???  |

## Data.Consume

| 名称             | 类型 | 默认值 | 说明 |
| ---------------- | ---- | ------ | ---- |
| editMode         | ???  | ???    | ???  |
| shopCart         | ???  | ???    | ???  |
| themeCSS         | ???  | ???    | ???  |
| themeColors      | ???  | ???    | ???  |
| canSelectPresent | ???  | ???    | ???  |
