import { requestV2, requestUseCdn } from '@youzan/tee-biz-request';

const GET_SHOP_CONFIG_URL = '/wscshop/shop/config.json';
const GET_WHOLESALE_ISWHOLESALE = '/wsctrade/wholesale/isWholesaler.json';

const fetch = (requestConfig) => requestV2({ ...requestConfig, config: {} });

// 获取店铺设置
// TODO: 看起来这个方法没有用到，需要后续再确认下，如果的确没有用到需要删除
function getShopConfig() {
  return fetch({
    path: GET_SHOP_CONFIG_URL,
    data: {
      key: 'subshop_online_shop_switch_show',
    },
  });
}

export const getTopBarSetting = () => {
  return requestUseCdn({
    path: '/wscshopcore/top-bar/get-top-bar-setting.json',
    method: 'GET',
    withCredentials: true,
  });
};

// 判断用户是否为批发商
function getIsWholesaler() {
  return fetch({
    path: GET_WHOLESALE_ISWHOLESALE,
  });
}

// 获取下单默认的配送方式
function getDefaultLogisticType(params) {
  return fetch({
    path: '/wsctrade/order/buy/getDefaultDeliveryData.json',
    data: params,
  });
}

export { getShopConfig, getIsWholesaler, getDefaultLogisticType };
