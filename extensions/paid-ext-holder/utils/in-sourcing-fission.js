import { args } from '@youzan/tee-util/lib/common/url';

export function getInSourcingCoupon(payResult = {}) {
  const { activityShareMessageDTOS = [] } = payResult;

  const inSourcingfissionInfo = activityShareMessageDTOS[0] || {};
  const isDisplay = inSourcingfissionInfo.isDisplay || false;
  const desc = inSourcingfissionInfo.desc || '';
  const imgUrl = inSourcingfissionInfo.imgUrl || '';
  const query = args.getAll(inSourcingfissionInfo.activityUrl);
  return {
    isDisplay,
    desc,
    imgUrl,
    activityId: query.activityId,
    umpAlias: query.umpAlias,
    inviterVoucherAlias: query.inviterVoucherAlias,
  };
}

export function getInSourcingCouponShare(inSourcingFission = {}) {
  const { activityId, umpAlias, inviterVoucherAlias, desc, imgUrl } = inSourcingFission;

  const path = args.add('/packages/ump/in-sourcing-fission/index', {
    activityId,
    umpAlias,
    inviterVoucherAlias,
  });

  return {
    path,
    imageUrl: imgUrl,
    title: desc,
  };
}
