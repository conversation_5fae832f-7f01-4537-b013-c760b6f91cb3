# @wsc-tee-trade/detail-self-fetch

自提信息

![UI呈现](https://img.yzcdn.cn/public_files/0bdee0980bdbbdeddc9b29aa398ed5db.png)

## 调试方式

### 调试 自提商品

#### 创建商品

- 前往 [商品-商品管理-创建商品](https://dian16911610.shangjia.youzan.com/v4/goods/edit#/)
  - 商品类型：实物商品
  - 商品名称：xxx 自提商品
  - 商品图片：任选一张
  - 价格：0.01
  - 库存：9999
  - 配送方式：到店自提
- 点击 下一步 - 保存并查看

#### 下单

- 找到一个 自提商品
- 商详页 - 点击 立即支付
- 下单页 - 提交订单 - 确认支付
- 支付成功页 - 点击 查看详情
- 订单详情页 - 查看效果

### 调试 扫码购自提商品

店铺推荐使用 骑马运动馆

#### 创建商品

- 前往 [商品-商品管理-创建商品](https://dian16911610.shangjia.youzan.com/v4/goods/edit#/)
  - 商品类型：实物商品
  - 商品名称：xxx 自提商品
  - 商品图片：任选一张
  - 价格：0.01
  - 库存：9999
  - 条形码：请自定义一个随机字符串
  - 配送方式：到店自提
- 点击 下一步 - 保存并查看
- 搜索引擎搜索：生成条形码
- 在 `生成条形码` 平台输入刚才的 `条形码` - 生成条形码

#### 下单

- 应用 - 搜索 [扫码购](https://dian255245.shangjia.youzan.com/v4/shop/scan-shopping)
- 微信扫码 - 扫商品条形码(刚才生成的条形码)
- 自动跳转 购物车 - 自动加购 - 点击结算
- 下单页 - 提交订单 - 确认支付
- 支付成功页 - 点击 查看详情
- 订单详情页 - 查看效果

## 存疑

- isScanBuyOrder 只有 weapp 的 format 提供了 isScanBuyOrder，也就是说 web 是一直 false 的?
- 所依赖的 `@wsc-tee-trade/detail-take-goods-code` 这个包代码都被干掉了？
- 什么时候能拿到 `_global.sourceInfo` 的数据？

## Widget.Provide

| 名称           | 说明     |
| -------------- | -------- |
| Main `default` | 自提信息 |

## Widget.Consume

| 名称             | 说明                                                 |
| ---------------- | ---------------------------------------------------- |
| TakeGoodsCode    | ??? `@wsc-tee-trade/detail-take-goods-code`          |
| SelfFetchScanBuy | 自提信息 `@wsc-tee-trade/detail-self-fetch-scan-buy` |

## Data.Consume

| 名称 | 类型 | 说明 |
| --- | --- | --- |
| showSelfFetch | _boolean_ | 是否展示 `_global.orderBizExtra.showAddress && _global.orderBizExtra.isSelfFetch` |
| selfFetchInfo | ??? | ??? |
| orderNo | _string_ | 订单号 |
| kdtId | _number_ | 店铺 ID |
| isPeriodBuy | _boolean_ | 是否是周期购 |
| isScanBuyOrder | _boolean_ | 是否是扫码购 `sourceInfo.orderMark === 'online_scan_buy'` |
| env | ??? | 环境变量 |
| goods | ??? | ??? |
| rxStatus | ??? | ??? |
| orderExtra `weapp` | ??? | 订单扩展信息 |

## 关键字速查

| 关键字                             | 说明 |
| ---------------------------------- | ---- |
| \_global.orderBizExtra.showAddress | ???  |
| \_global.orderBizExtra.isSelfFetch | ???  |
