<template>
  <van-dialog
    :show="isShowBuyAgainFailGoodsDialog"
    use-slot
    title="以下商品不支持“再来一单”"
    confirm-button-text="知道了"
    @close="onClose"
    @confirm="onConfirm"
  >
    <view class="cap-fail-good">
      <view
        v-for="(item, index) in failBuyAgainGoodsList"
        :key="index"
        class="cap-fail-good__item van-hairline--bottom"
      >
        <p class="cap-fail-good__item__title van-ellipsis">
          {{ item.title }}
        </p>
        <text class="cap-fail-good__item__num"> X{{ item.num }}</text>
        <p v-if="item.sku" class="cap-fail-good__item__sku">
          {{ item.sku }}
        </p>
      </view>
    </view>
  </van-dialog>
</template>

<script>
// Components
import Dialog from '@youzan/vant-tee/dist/dialog/index.vue';

// Dependencies
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-dialog': Dialog,
  },

  data() {
    return {
      ...mapState(this, ['isShowBuyAgainFailGoodsDialog', 'failBuyAgainGoodsList']),
    };
  },

  methods: {
    onConfirm() {
      this.store.confirmBuyAgainFailGoods();
      this.store.toggleBuyAgainFailGoodsDialog({ isShow: false });
    },
    onClose() {
      this.store.toggleBuyAgainFailGoodsDialog({ isShow: false });
    },
  },
};
</script>

<style lang="scss" scoped>
.cap-fail-good {
  padding: 10px 20px;

  &__item {
    position: relative;
    padding: 10px 0;
    font-size: 12px;
    color: #666;

    &:last-child::after {
      display: none;
    }

    &__title {
      padding-right: 60px;
      font-size: 14px;
      color: #333;
    }

    &__num {
      position: absolute;
      top: 13px;
      right: 10px;
    }

    &__sku {
      margin-top: 6px;
    }
  }
}
</style>
