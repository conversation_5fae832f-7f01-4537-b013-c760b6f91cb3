import VirtualTicket from './VirtualTicket.vue';
import { cloud } from '@youzan/ranta-helper';
import type { EcardData } from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import { cloudData, isSameObject } from './utils';

export default class VirtualTicketExtension {
  ctx: any;

  /**
   * ecard
   * @desc 电子卡券
   * @type {EcardData}
   */
  @cloud('ecard', 'data')
  ecard: EcardData;

  constructor(options) {
    this.ctx = options.ctx;

    mapData(this, ['virtualTicket'], {
      callback: () => {
        const { virtualTicket } = this.ctx.data;
        const newOpenData = {
          ecard: cloudData.getEcard({ virtualTicket }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  static widgets = {
    VirtualTicket,
  };
}
