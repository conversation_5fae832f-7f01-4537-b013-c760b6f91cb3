<template>
  <van-popup :show="visible" :close-on-click-overlay="false" custom-class="paid-save-image">
    <image class="paid-save-image__img" fit="scale-down" :src="src" :width="width" />
    <view class="paid-save-image__tips">{{ text || '长按保存图片到本地相册' }}</view>
    <van-icon name="close" class="paid-save-image__close" @click="handleClose" />
  </van-popup>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index';
import Popup from '@youzan/vant-tee/dist/popup/index';

export default {
  name: 'long-click-save-image',

  components: {
    'van-icon': Icon,
    'van-popup': Popup,
  },

  props: {
    visible: Boolean, // 是否展示
    src: String, // 图片地址
    text: String, // 提示语
    width: Number, // 宽度
  },

  methods: {
    handleClose() {
      this.$emit('hide', false);
    },
  },
};
</script>

<style lang="scss">
.paid-save-image {
  background: transparent !important;

  &__img {
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 16px;
  }

  &__tips {
    font-size: 14px;
    line-height: 20px;
    color: #f7f8fa;
    text-align: center;
    margin-bottom: 13px;
  }

  &__close {
    display: block !important;
    width: 32px;
    height: 32px;
    color: #fff;
    font-size: 24px !important;
    box-sizing: border-box;
    padding: 4px;
    margin: 0 auto;
  }
}
</style>
