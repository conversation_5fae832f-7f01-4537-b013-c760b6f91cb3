# @wsc-tee-trade/cart-order-keep

下单挽留

![UI呈现](https://img.yzcdn.cn/public_files/3fb5f420596b9188aa1c9be13f277324.png)

## 调试方式

- 进入购物车页
- 选择商品 - 点击结算
- 进入下单页，控制台输入 `wx.navigateBack();`

## 存疑

## Widget.Provide

| 名称                | 说明 |
| ------------------- | ---- |
| OrderKeep `default` | ???  |

## Event.Listen

| 名称  | 说明 |
| ----- | ---- |
| open  | ???  |
| close | ???  |

## Process.Invoke

| 名称               | 说明 |
| ------------------ | ---- |
| navigateToTradeBuy | ???  |

## Data.Consume

| 名称        | 类型 | 默认值 | 说明 |
| ----------- | ---- | ------ | ---- |
| displayData | ???  | ???    | ???  |
| orderData   | ???  | ???    | ???  |
| themeColors | ???  | ???    | ???  |
