import { getTopBarSetting, getIsWholesaler, getShopConfig } from '../api';
import { EDIT_MODE_CONF } from '../constants';

export default function (ctx) {
  return {
    initData() {
      Promise.all([getTopBarSetting(), getIsWholesaler(), getShopConfig()]).then(
        ([shopConfigRes, wholesalerRes, storeConfigRes]) => {
          // 顶部栏切换开关
          this.isSwitchOpen = shopConfigRes.showSwitchBtn;
          this.isWholesaler = wholesalerRes || false;
          if (this.isChainShop) {
            // 如果连锁店铺subshop_online_shop_switch_show是 '0' 则是隔离店铺，不可切店， '1' 则是可切店
            this.isCanSwitchShop = storeConfigRes.subshop_online_shop_switch_show === '1';
          }
        }
      );
    },
    async switchEditMode() {
      const value = EDIT_MODE_CONF[this.editMode].nextMode;
      ctx.data.editMode = value;
      const isEdit = value === 'edit';
      if (isEdit) {
        // 取消选中状态
        const shopList = (ctx.data.shopList || []).map((shop) => {
          const goodsGroupList = (shop.goodsGroupList || []).map((goodsGroup) => {
            const goodsList = (goodsGroup.goodsList || []).map((goods) => ({
              ...goods,
              checked: false,
            }));
            return { ...goodsGroup, goodsList };
          });
          return { ...shop, goodsGroupList };
        });
        ctx.data.shopList = shopList;
      } else {
        await ctx.process.invokePipe('updateCartList');
      }
    },
  };
}
