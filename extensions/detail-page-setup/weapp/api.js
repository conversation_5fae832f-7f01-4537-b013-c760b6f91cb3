import { string } from '@youzan/tee-util';
import { requestV2 } from '@youzan/tee-biz-request';

export default {
  /**
   * 获取订单信息
   * @param orderNo
   * @param kdtId
   */
  fetchOrderInfo({ orderNo, kdtId = 0, supportBlindBox }) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/order/detail/getOrderInfo.json',
        method: 'POST',
        data: {
          orderNo,
          kdtId,
          supportBlindBox,
        },
      })
        .then((res = {}) => {
          if (Object.keys(res).length) {
            resolve(string.mapKeysToCamelCase(res));
          } else {
            reject(res);
          }
        })
        .catch((response) => {
          reject(response?.data);
        });
    });
  },
  checkCouponAvailable(data) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/order/voucher-check.json',
        method: 'POST',
        data,
      })
        .then((res = {}) => {
          if (res) {
            resolve(res);
          } else {
            reject(res);
          }
        })
        .catch((response) => {
          reject(response?.data);
        });
    });
  },
  getTradeUmpV1() {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wscump/trade/use-trade-ump-v1.json',
        method: 'GET',
      })
        .then((res) => resolve(res))
        .catch((e) => {
          reject(e);
        });
    });
  },
  convertPaymentOrderNo({ orderNo }) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/order/convert-payment-order-no.json',
        method: 'GET',
        data: {
          orderNo,
        },
      })
        .then((res) => resolve(res))
        .catch((e) => {
          reject(e);
        });
    });
  },
};
