import get from '@youzan/utils/object/get';
import { BUTTON_GROUP } from '../constants';

export default {
  url() {
    return this.payResult.url || {};
  },
  buttonGroup() {
    const payResultVO = this.payResult.payResultVO || {};
    const result = get(payResultVO, 'buttonGroup') || {};
    const shopPayResult = get(this.payResult, 'shopPayResult') || {};
    const firstOrderPayResult = get(shopPayResult, 'orderPayResultGroup[0]', {});
    const { activityType } = firstOrderPayResult;

    // 我要送礼订单
    if (activityType === 63) {
      return {
        SHARE_GIFT: {
          defaultText: '分享礼物',
          needJump: true,
          type: 'SHARE_GIFT',
        },
      };
    }
    return result;
  },
  buttonList() {
    const { buttonGroup } = this;
    const buttonGroups = BUTTON_GROUP.map((item) => item.key);
    const result = buttonGroups
      .filter((value) => !!buttonGroup[value])
      .sort(
        (item1, item2) => BUTTON_GROUP.indexOf(item1 as any) - BUTTON_GROUP.indexOf(item2 as any)
      );
    return result;
  },
  buttonItemList() {
    return this.buttonList.map((key) => {
      const isPrimary = this.getButtonType(key);
      return {
        isPrimary,
        type: key,
        text: this.buttonGroup[key]?.defaultText,
      };
    });
  },
  shareUrl() {
    const defaultValue = `${this.url.h5}/wsctrade/order/share?kdt_id=${this.kdtId}1&order_no=${this.orderNo}&show_share_guide=1`;
    return this.buttonGroup.SHARE ? this.buttonGroup.SHARE.url : defaultValue;
  },
};
