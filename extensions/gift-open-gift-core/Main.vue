<template>
  <view> </view>
</template>

<script>
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import Tee from '@youzan/tee';
import { getCurrentKdtId } from '@youzan/shop-core-tee';

export default {
  created() {
    this.ctx.env.getQueryAsync().then((query = {}) => {
      if (!query.gift_id) {
        Dialog.confirm({
          message: '缺少必要参数',
          confirmButtonText: '返回',
        }).then(() => {
          Tee.navigateBack();
        });
        return;
      }

      const { gift_id: giftId, kdt_id: kdtId } = query || {};

      this.ctx.dmc.redirectTo('GiftShare', {
        gift_id: giftId,
        kdt_id: kdtId || getCurrentKdtId(),
        is_from_open_gift_page: 1,
      });
    });
  },
};
</script>
