export interface PriceData {
  /** 实付金额(单位:分) */
  realPay: number;
  /** 订单金额(单位:分) */
  payPrice: number;
  /** 积分 */
  pointsPrice: number;
  /** 储值卡金额(单位:分) */
  prepayCardPay: number;
  /** 支付优惠(单位:分) */
  payUmpDiscountMoney: number;
  extraPrices: Array<{
    /** 附加费名称 */
    name: string;
    /** 附加费描述 */
    desc: string;
    /** 附加费(单位:分) */
    realPay: number;
  }>;
  /** 格式化后的数据 */
  formatted: {
    /** 商品金额(单位:元)(格式：¥1.00) */
    total: number;
    /** 配送费标题 */
    postageTitle: string;
    /** 配送费(单位:元) */
    postage: number;
    /** 支付优惠(单位：元)（格式：-¥1.00 或 +¥1.00） */
    payUmpDiscountMoney: string;
    /** 附加费列表 */
    extraPrices: Array<{
      /** 附加费名称 */
      name: string;
      /** 附加费描述 */
      desc: string;
      /** 附加费(单位:元)(格式:+¥1.00) */
      realPay: string;
    }>;
    /** 储值卡金额（单位：元）(格式：-¥1.00) */
    prepayCardPay: string;
    /** 实付金额（单位：元）(格式：¥1.00) */
    realPay: string;
  };
}
