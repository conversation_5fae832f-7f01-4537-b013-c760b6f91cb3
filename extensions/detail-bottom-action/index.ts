import BottomAction from './BottomAction.vue';
import WrapAction from './WrapAction.vue';
import Main from './Main.vue';
import StockCouponBottomAction from './StockCouponBottomAction.vue';
import createStore from './store';
import { mapCtxData } from '@ranta/store';
import { cloud, bridge, useAsHook } from '@youzan/ranta-helper';
import type { IOrderInfo } from './types';
import type {
  BottomActionData,
  HandleOrderActionParams,
  OrderDetailBeforeConfigConfirmDialog,
  OrderDetailBeforeDisableOrderBtns,
  OrderDetailBeforeHandleOrderAction,
} from '@youzan-cloud/cloud-biz-types';
import { mapData } from '@youzan/ranta-helper-tee';
import debounce from '@youzan/weapp-utils/lib/debounce';
import { getStorage, removeStorage } from '@youzan/tee-api';
/* #ifdef weapp */
import skynet from '@youzan/wsc-tee-trade-common/lib/utils/skynet';
import Event from '@youzan/weapp-utils/lib/event';
/* #endif */
import { BOTTOM } from './constants';
import { cloudData, isSameObject, getAllBtns } from './utils';
import { OrderActionEnum } from './types';

export default class BottomActionExtension {
  ctx: any;

  store: any;

  @bridge('extendOrder', 'process')
  extendOrder() {
    this.store.handleBtnClick({ value: BOTTOM.extend.value });
  }

  @bridge('confirmOrder', 'process')
  confirmOrder() {
    this.store.handleBtnClick({ value: BOTTOM.confirm.value });
  }

  @bridge('shareOrder', 'process')
  shareOrder() {
    this.store.handleBtnClick({ value: BOTTOM.share.value });
  }

  @bridge('viewEvaluate', 'process')
  viewEvaluate() {
    this.store.handleBtnClick({ value: BOTTOM.viewEvaluate.value });
  }

  @bridge('evaluateOrder', 'process')
  evaluateOrder() {
    this.store.handleBtnClick({ value: BOTTOM.evaluate.value });
  }

  @bridge('cancelOrder', 'process')
  cancelOrder() {
    this.store.handleBtnClick({ value: BOTTOM.cancel.value });
  }

  @bridge('buyAgain', 'process')
  buyAgain() {
    const { env, order } = this.ctx.data || {};
    if (order.isAllowDirectBuyAgain && order.directBuyAgainBtnConfig?.show && !env.isYouzanmars) {
      this.store.handleBtnClick({ value: BOTTOM.directBuyAgain.value });
    } else {
      this.store.handleBtnClick({ value: BOTTOM.buyAgain.value });
    }
  }

  @bridge('showBatchRefund', 'process')
  showBatchRefund() {
    this.ctx.process.invoke('showBatchRefund');
  }

  @cloud('bottomAction', 'data')
  bottomAction: BottomActionData;

  @bridge('orderInfo', 'data')
  orderInfoV1: IOrderInfo;

  @cloud('handleOrderAction', 'method', { allowMultiple: true })
  handleOrderAction(params: HandleOrderActionParams) {
    if (['buyAgain', 'directBuyAgain'].includes(params.type)) {
      const { env, order } = this.ctx.data || {};
      if (order.isAllowDirectBuyAgain && order.directBuyAgainBtnConfig?.show && !env.isYouzanmars) {
        params.type = BOTTOM.directBuyAgain.value as OrderActionEnum;
      } else {
        params.type = BOTTOM.buyAgain.value as OrderActionEnum;
      }
    }
    this.store.handleBtnClick({ value: params.type });
  }

  @cloud('beforeHandleOrderAction', 'hook', { allowMultiple: true })
  @bridge('beforeBottomBtnClickAsync', 'asyncEvent')
  beforeHandleOrderAction = useAsHook<OrderDetailBeforeHandleOrderAction>();

  @bridge('beforeBatchRefundClickAsync', 'asyncEvent')
  beforeBatchRefundClick = useAsHook<() => Promise<void>>();

  @cloud('beforeDisableOrderBtns', 'hook', { allowMultiple: false })
  beforeDisableOrderBtns = useAsHook<OrderDetailBeforeDisableOrderBtns>();

  @cloud('beforeConfigConfirmDialog', 'hook', { allowMultiple: false })
  beforeConfigConfirmDialog = useAsHook<OrderDetailBeforeConfigConfirmDialog>();

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    this.initData();
    this.initCloudData();
    /* #ifdef web */
    this.autoShowModifyAddressPopup();
    /* #endif */
    mapCtxData(this, [
      'order',
      'goods',
      'isIVR',
      'itemList',
      'isIvrOwner',
      'ivrRedirectUrl',
      'miniprogram',
      'guaranteeOrderInfo',
    ]);
    /* #ifdef weapp */
    this.onShowListener = this.onShowListener.bind(this);
    this.initEvent();
    /* #endif */
  }

  static widgets = {
    Main,
    BottomAction,
    WrapAction,
    StockCouponBottomAction,
  };

  // 返回时自动拉起修改地址弹窗,只有web需要，小程序因为返回页面栈不会重新初始化所以不需要
  async autoShowModifyAddressPopup() {
    const AUTO_SHOW_MODIFY_ADDRESS_POPUP = 'AUTO_SHOW_MODIFY_ADDRESS_POPUP'; // 自动拉起修改地址弹窗key
    const { data: showModifyAddress } = await getStorage(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
    this.ctx.data.showChooseAddressPopup = !!showModifyAddress;
    removeStorage(AUTO_SHOW_MODIFY_ADDRESS_POPUP);
  }

  /* #ifdef weapp */
  onShowListener(options = {}) {
    // @ts-ignore
    const { extraData } = options.referrerInfo || {};
    const { status, req_extradata: reqExtradata = {} } = extraData || {};
    if (extraData) {
      skynet.info('[确认收货组件-callback]', options);
    }
    // 微信确认收货组件回调参数
    // status可能值: success(确认收货成功)、fail(确认收货失败)、cancel(关闭组件)
    if (status && status !== 'cancel' && reqExtradata.transaction_id) {
      // 微信侧确认收货不管是否成功，都不影响有赞侧确认收货
      this.store.confirmReceiveAction({ type: '', isShowConfirm: status === 'fail' });
    }
  }

  initEvent() {
    Event.on('onShow', this.onShowListener);
  }

  pageDestroyed() {
    Event.off('onShow', this.onShowListener);
  }
  /* #endif */

  initData() {
    this.ctx.data.bottomBtns = [];
    mapData(
      this,
      [
        'order',
        'gift',
        'orderBizExtra',
        'orderBizUrl',
        'grouponLink',
        'invoice',
        'miniprogram',
        'env',
        'isIVR',
        'bottomBtnsDisplay',
      ],
      {
        callback: debounce(async function () {
          const {
            order,
            gift,
            orderBizExtra,
            orderBizUrl,
            grouponLink,
            invoice,
            miniprogram,
            env,
            isIVR,
            bottomBtnsDisplay,
          } = this.ctx.data;
          let btns = getAllBtns({
            order,
            gift,
            orderBizExtra,
            orderBizUrl,
            grouponLink,
            invoice,
            miniprogram,
            env,
            isIVR,
            bottomBtnsDisplay,
          }).filter((item) => {
            const needHideBtnValues = Object.keys(bottomBtnsDisplay).filter(
              (key) => !bottomBtnsDisplay[key]
            );
            return !needHideBtnValues.includes(item.value);
          });
          try {
            // 判断type中是否存在directBuyAgain,如果有，那如果
            const renderBtns = Array.from(
              new Set(
                btns.map(({ value }) => {
                  if (value === 'directBuyAgain') {
                    return 'buyAgain';
                  }
                  return value;
                })
              )
            );
            const { disableBtns = [] } =
              (await this.ctx.cloud.invoke('beforeDisableOrderBtns', { renderBtns })) || {};
            btns = btns.filter((_) => !disableBtns.includes(_.value));
          } catch (e) {
            console.warn(e);
          }
          /* #ifdef weapp */
          // page-container需要根据是否有btns，设置fixedBottom
          const app = getApp();
          app.trigger('ranta-detail-page-fixed-bottom', !!btns.length);
          /* #endif */
          this.store.updateAllBtns(btns);
          this.ctx.data.bottomBtns = btns;
        }, 300),
      }
    );
  }

  initCloudData() {
    this.bottomAction = { btns: [] };
    mapData(this, ['bottomBtns'], {
      callback: () => {
        const { bottomBtns } = this.ctx.data;
        const bottomAction = cloudData.getBottomAction({ bottomBtns });
        if (bottomAction.btns.length > 0) {
          this.bottomAction = bottomAction;
        }
      },
    });
    mapData(this, ['order', 'orderBizExtra', 'bottomBtns'], {
      callback: () => {
        const { order, orderBizExtra = {}, bottomBtns } = this.ctx.data;
        const newOpenData = {
          orderInfoV1: cloudData.getOrderInfoV1({ order, orderBizExtra, bottomBtns }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
