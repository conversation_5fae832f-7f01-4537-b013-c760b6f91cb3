<template>
  <view class="goods-item">
    <view :class="['img-wrap', isInvalid ? 'img-wrap-mask' : '']" @click="handleGoodsImgClick">
      <image class="img-wrap-item" :src="goodsItem.imgUrl" mode="aspectFit" />
      <text v-if="isInvalid" class="expired-text">已失效</text>
    </view>
    <view class="goods-detail">
      <view class="goods-title">{{ goodsItem.title }}</view>
      <view class="goods-sub-title">{{ goodsItem.skuStr }}</view>
      <view class="err-msg" v-if="goodsItem.errorMsg">{{ goodsItem.errorMsg }}</view>
      <view class="price" v-else>￥{{ goodsItem.price }}</view>
      <view class="stepper">
        <van-stepper
          v-if="!goodsItem.invalid"
          :value="goodsItem.num"
          :min="0"
          :max="goodsItem.maxNum"
          @change="handleStepperChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Stepper from '@youzan/vant-tee/dist/stepper/index.vue';

export default {
  components: {
    'van-stepper': Stepper,
  },

  props: {
    goodsItem: {
      type: Object,
      default: () => ({}),
    },
    isInvalid: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    handleStepperChange(num) {
      this.$emit('change', num);
    },

    handleGoodsImgClick() {
      const { alias } = this.goodsItem;
      let url = `/pages/goods/detail/index?alias=${alias}&type=gift`;
      /* #ifdef web */
      url = `/wscgoods/detail/${alias}?type=gift`;
      /* #endif */
      Tee.navigate({
        url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-item {
  margin-top: 9px;
  margin-bottom: 16px;
  position: relative;
  height: 80px;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .img-wrap {
    width: 80px;
    height: 80px;
    position: absolute;
    margin-left: 0;
    margin-right: 0;
    background: #f8f8f8;

    &-mask {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.7);
        /* 白色蒙版，透明度0.7 */
        z-index: 1;
      }
    }

    &-item {
      position: absolute;
      margin: auto;
      max-height: 100%;
      max-width: 100%;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      border-radius: 4px !important;
    }

    .expired-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 12px;
      font-weight: bold;
      z-index: 2;
      color: #fff;
      background: #0009;
      border-radius: 100px;
      padding: 4px 12px;
      width: 60px;
      box-sizing: border-box;
    }
  }

  .err-msg {
    color: #666;
    font-size: 14px;
  }

  .goods-detail {
    margin-left: 90px;
    padding: 4px 0;

    .price {
      position: absolute;
      color: #323233;
      bottom: 4px;
    }
  }

  .goods-title {
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .goods-sub-title {
    font-size: 14px;
    color: #666;
    margin-top: 6px;
  }

  .stepper {
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>
