<template>
  <button
    class="in-sourcing-coupon"
    open-type="share"
    data-detail="inSourcingCoupon"
    @click="clickInSourcingCoupon"
  >
    <image style="width: 100%" mode="widthFix" :src="weappImg" />
  </button>
</template>

<script>
import get from '@youzan/utils/object/get';

export default {
  props: {
    weappImg: {
      type: String,
    },
  },

  methods: {
    clickInSourcingCoupon(event) {
      const detail = get(event, 'target.dataset.detail', '');
      this.$emit('share', detail);
    },
  },
};
</script>

<style lang="scss">
.in-sourcing-coupon {
  width: 100%;
  padding: 0 12px;
  margin-top: 12px;
  position: relative;
  background: #f7f8fa;
  border: none;
  outline: none;
  text-align: left;
  display: flex;

  &::after {
    content: '';
    border: none;
  }
}
</style>
