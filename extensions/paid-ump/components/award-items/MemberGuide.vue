<template>
  <view v-if="showMemberGuide" class="award-container">
    <view class="member-guide-bg">
      <view class="member-guide-title">
        <view class="member-guide-name">
          <view class="member-guide-shop-name">{{ shopName }}</view>
          <text class="member-guide-welcome">邀你入会享专属权益</text>
        </view>
        <view class="member-guide-btn" @click="gotoMemberPage">立即入会</view>
      </view>
      <view class="member-guide-benefit-box" v-if="benefitList.length > 0">
        <view
          v-for="(benefit, index) in benefitList"
          :key="index"
          :class="[benefitOver3 ? 'member-guide-benefit' : 'member-guide-benefit-less']"
          @click="gotoMemberPage"
        >
          <view v-if="benefit.icon" class="member-guide-image-box">
            <image :src="benefit.icon" class="member-guide-image" />
          </view>
          <view v-if="benefit.benefitCount" class="member-guide-benefit-num"
            >{{ benefit.benefitCount }}项</view
          >
          <text :class="[!benefitOver3 ? 'member-guide-left' : '']">{{
            benefit.facadeShowName || benefit.appName
          }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import { formatBenefits, transformPlanBenefitsForDisplay } from '../../utils';

const app = getApp();

export default {
  props: {
    payResult: Object,
  },
  data() {
    return {
      shopName: app.getShopInfoSync().base.shop_name || '',
    };
  },
  computed: {
    afterPayGuide() {
      let afterPayGuide = {};
      try {
        afterPayGuide = JSON.parse(this.payResult?.extension?.AFTER_PAY_GUIDE || '{}');
      } catch (e) {
        console.log('数据获取失败');
      }
      return afterPayGuide;
    },
    benefitList() {
      return this.formatData(this.afterPayGuide);
    },
    showMemberGuide() {
      const showMemberGuide = this.afterPayGuide?.needGuide;
      showMemberGuide && this.logger('view');
      return showMemberGuide;
    },
    benefitOver3() {
      return this.benefitList.length > 3;
    },
  },
  methods: {
    formatData(data) {
      if (Object.keys(data || {}).length === 0) {
        return [];
      }
      const {
        levelBenefit,
        birthdayInfoList,
        festivalInfoList,
        memberdayInfoList,
        memberVoucherInfoList,
      } = data;
      const planBenefit = transformPlanBenefitsForDisplay({
        birthdayInfoList,
        festivalInfoList,
        memberdayInfoList,
        memberVoucherInfoList,
      });
      const allBenefit = formatBenefits(levelBenefit, planBenefit);

      if (allBenefit.length > 4) {
        return allBenefit.slice(0, 3).concat({
          appName: '全部权益',
          benefitCount: allBenefit.length,
        });
      }
      return allBenefit;
    },
    gotoMemberPage() {
      this.logger('click');
      Tee.navigate({
        url: '/packages/levelcenter/free/index',
      });
    },
    logger(type) {
      const logData =
        type === 'view'
          ? {
              et: 'view',
              ei: 'paysuccess_mj',
              en: '支付成功页入会组件曝光',
            }
          : {
              et: 'click',
              ei: 'paysuccess_mj_click',
              en: '支付成功页入会组件点击',
            };
      app.logger && app.logger.log(logData);
    },
  },
};
</script>

<style lang="scss">
.award-container {
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}
.member-guide {
  &-bg {
    background-image: url('https://img01.yzcdn.cn/upload_files/2021/09/26/FtS3HuPQCLPgoxtSQp_QW4PhLdml.png');
    width: calc(100vw - 48px);
    background-size: cover;
    padding: 12px;
    box-sizing: border-box;
    border-radius: 8px;
  }

  &-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &-name {
    color: #74410c;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  &-shop-name {
    max-width: calc(100vw - 290px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-welcome {
    margin-left: 5px;
  }

  &-btn {
    width: 82px;
    height: 24px;
    border-radius: 12px;
    background: linear-gradient(to right, #fe9c70, #ff4943);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
  }

  &-benefit-box {
    display: flex;
    flex-direction: row;
    margin-top: 12px;
    justify-content: space-between;
  }

  &-benefit {
    display: flex;
    flex-direction: column;
    height: 54px;
    justify-content: space-between;
    align-items: center;
    color: rgba(116, 65, 12, 0.9);
    font-size: 12px;
  }

  &-benefit-less {
    display: flex;
    flex-direction: row;
    height: 32px;
    justify-content: space-between;
    align-items: center;
    color: rgba(116, 65, 12, 0.9);
    font-size: 12px;
  }

  &-image-box {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: #fff;
  }

  &-image {
    width: 32px;
    height: 32px;
  }

  &-benefit-num {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: #fbf6eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #d5a150;
    font-weight: bold;
  }

  &-desc {
    max-width: 65px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-left {
    margin-left: 8px;
  }
}
</style>
