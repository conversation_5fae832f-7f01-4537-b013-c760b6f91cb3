import { object } from '@youzan/tee-util';

import {
  getDeliveryAddress,
  getSelfFetchAddress,
  getSelfFetchLngLat,
  getVerifyAddress,
  getVerifyAddressLngLat,
} from '../common/address';

import { parseItemInfo } from '../common/format';
import { formatPrice } from '../common/price';
import { getIsNewHotelGood } from '../common/hotel';
import { formatDeliveryPostage } from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';
import fullfillImage from '@youzan/utils/url/fullfillImage';

// 格式化数据
// TODO: logger在排查到问题后删除
export function formatOrder(data, logger) {
  const {
    mainOrderInfo: order,
    orderAddressInfo: address,
    paymentInfo: payment,
    itemInfo: goods,
    invoiceInfo: invoice,
    shopInfo = {},
    gift = {},
    orderBizUrl = {},
    orderBizExtra = {},
    giving_duration: givingDuration,
    ump = {},
    marketingInfo = {},
    microTransferInfo = {},
    sourceInfo = {},
    paidPromotion = {},
    directBuyAgainBtnConfig = {},
    buyerInfo = {},
    privacyWaybill = {},
    outerPromotion = {},
  } = data;
  logger('格式化订单信息start', { order });
  order.directBuyAgainBtnConfig = directBuyAgainBtnConfig;
  order.buyerInfo = buyerInfo;
  const goodsPackages = order.goodsPackages || [];
  const attributeItems = order.attributeItems || [];

  const logistics = goodsPackages[0] || {};
  const { selfFetchResult = {}, selfFetchInfo = {} } = address;
  const localDelivery = address.localDeliveryScope || {};
  const { storeAddress = {} } = shopInfo;

  const { cashBackUmp = {}, couponUmp = {}, couponUmps = [], couponCodeUmp = {} } = order.ump || {};

  const isNewHotelGood = getIsNewHotelGood(goods);
  // 扫码购订单
  const isScanBuyOrder = object.get(sourceInfo, 'orderMark', '') === 'online_scan_buy';

  // 多周期购判断，新希望独有
  const isMultiPeriodBuy =
    order.goodsPackages &&
    order.goodsPackages.length > 1 &&
    goods.length > 0 &&
    goods[0].goodsType === 24;
  logger('格式化订单信息parseItemInfo', { goods });
  const itemList = parseItemInfo(
    goods,
    order,
    orderBizExtra,
    goodsPackages,
    order.periodDetail,
    givingDuration || false,
    isMultiPeriodBuy,
    orderBizExtra.isKosTokenVerified || false
  );

  const useBeforePayData = {
    is: +order.buyWay === 49,
    status: order?.extra?.PRIOR_USE_COMPLETED === 1,
  };

  logger('格式化订单信息return', { storeAddress, selfFetchInfo });

  const formatOrderData = {
    // 先用后付
    useBeforePayData,
    // 加购后推荐商品区
    recommendTitle: '更多精选商品',
    dialogRecommendTitle: '为你推荐相关优惠商品',
    recommendPageSize: 20,
    dialogRecommendPageSize: 6,
    recommendLayoutConfig: {
      layout: 1,
      imageFillStyle: 1,
    },
    dialogRecommendLayoutConfig: {
      imageFillStyle: 1,
      layout: 5,
    },
    requestExtraParams: ['note', 'coupon'],
    dialogRequestExtraParams: [],

    isRetailOrder: order.isRetailOrder,

    showLogisticsInfo: !!(goodsPackages && goodsPackages.length),
    isMultiPeriodBuy,
    showVirtualTicket: orderBizExtra.showVirtualTicket || false,
    showShopVerifyAddress: order.showShopVerifyAddress || false,
    showReceiverInfo: !!(orderBizExtra.showAddress && !orderBizExtra.isSelfFetch),
    // 扫码购项目，该字段收到后端
    showSelfFetch: orderBizExtra.showSelfFetch || false,
    showRefundInfo: order.isShowRefundInfo || false,
    showQrCode: !!order.virtualResponse && Object.keys(order.virtualResponse).length > 0,
    showGrouponCollection: order.showGroupAgencyReceive || false,
    showIdCard: order.showIdCard || false,
    showPresaleSteps: !!(payment.phasePays && payment.phasePays.length),
    showCheckDeliveryScope: order.showCheckDeliveryScope || false,
    showDeliveryType: orderBizExtra.showDeliveryType || false,
    showDeliveryTime: order.showDeliveryTime || false,
    showAfterSaleMobile: order.showAfterSaleMobile || false,
    showWxShoppingList: !!(
      order.extra &&
      order.extra.WECHAT_SYNC_SHOPPING_LIST === 1 &&
      goods[0].goodsType !== 31 &&
      order.state > 4
    ),
    showEducation: order.showEducation || false,
    showLookCoupon: orderBizExtra.hasPayCouponsOrder || false,
    isUsingNewSnapshot: orderBizExtra.isUsingNewSnapshot || false,
    isScanBuyOrder,

    // 是否是新酒店商品
    isNewHotelGood,

    itemList,
    addressInfo: address,

    // 订单状态
    orderStateInfo: {
      orderState: order.state,
      orderStateTitle: order.stateStr,
      autoReceiveOrderTime: order.autoReceiveOrderTime,
      showOrderStatusToGroup: order.showOrderStatusToGroup,
      showOrderStatusSended: order.showOrderStatusSended,
      closeType: order.closeType,
      closeTypeStr: order.closeTypeStr,
      progressBar: order.progressBar,
    },

    // 物流
    logistics,

    // 电子卡券
    virtualTicket: order.virtualTicketResponse || {},

    // 核销网点信息
    verifyAddressInfo: {
      name: storeAddress.name,
      address: getVerifyAddress(storeAddress),
      ...getVerifyAddressLngLat(storeAddress),
    },

    // 收货人或自提信息
    receiverInfo: {
      receiverName: address.receiverName,
      receiverTel: address.receiverTel,
      receiverLabel: orderBizExtra.showVirtualTicket ? '联系人' : '收货人',
      deliveryAddress: getDeliveryAddress(address),
      orderAddress: address,
    },

    // 获取自提信息
    selfFetchInfo: {
      qrcode: selfFetchResult.qrCodeBase64 || '',
      fetchNo: selfFetchResult.selfFetchNo || '',
      selfFetchState: selfFetchResult.selfFetchState,
      fetcher: selfFetchInfo.userName + selfFetchInfo.userTel || '',
      time: selfFetchInfo.userTime || '',
      tel: selfFetchInfo.tel || '',
      address: getSelfFetchAddress(selfFetchInfo),
      ...getSelfFetchLngLat(selfFetchInfo),
      shopName: order.shopName,
      name: selfFetchInfo.name || '',
    },

    // 知识付费送礼退款
    giftInvite: gift.giftInvite || {},
    gift,

    // 虚拟商品二维码
    qrCode: order.virtualResponse || {},

    // 拼团
    groupon: order.groupBuy || {},

    // 身份证号
    idCardNumber: order.idCardNumber || '',

    // 活动类型
    activityType: order.activityType || '',

    // 返现
    cashBack: cashBackUmp,

    // 返储值金
    returnValueCard: {
      assertBusinessDetail: object.get(orderBizExtra, 'assertBusinessDetail', []),
      cardAssetIsActive: orderBizExtra.cardAssetIsActive,
    },

    // 优惠券信息
    coupon: couponUmp,
    // 优惠券叠加
    coupons: couponUmps,
    // 优惠码
    couponCode: couponCodeUmp,
    ump,
    marketingInfo,

    // 价格相关
    payment,
    showPostage: orderBizExtra.showPostage,
    buyWayDetailUrl: orderBizUrl.buyWayDetailUrl,

    activitiesList: ump.orderActivities,
    deliveryActivitiesList: ump.orderDeliveryActivities,
    decreaseAmount: ump.decreaseAmount,

    // 外部订单优惠信息（CRM线下订单）
    outerPromotion,

    // 定金预售
    presaleSteps: payment.phasePays || [],

    // 订单时间相关
    time: {
      createTime: order.createTime,
      payTitle: order.buyWay === 9 ? '下单时间' : '付款时间',
      payTime: order.payTime,
      expressTime: order.expressTime,
      successTime: order.successTime,
    },

    orderBizUrl,

    order,
    shopInfo,
    totalPrice: formatPrice(payment.payPrice),
    isPeriodBuy: orderBizExtra.isPeriodBuy,
    isSelfFetch: orderBizExtra.isSelfFetch,
    isShowPostponeShip: order.isShowPostponeShip,

    // service
    service: {
      postage: formatDeliveryPostage(payment.postage, payment.expressPayMode),
      expressType: order.expressTypeDesc || '',
      localDeliveryDesc: localDelivery.desc || '',
      localDeliveryImg: fullfillImage(localDelivery.attachPic, 'middle'),
      deliveryTime: address.deliveryTimeDisplay || '',
      buyerMemo: order.buyerMemo || '无',
      showHotelUnsubscribeRemind: orderBizExtra.isHotel,
      serviceTel: object.get(goods[0], 'goodsInfo.serviceTel') || '',
    },
    isHotel: orderBizExtra.isHotel,

    // 教育信息
    education: order.education,
    eduAttributeItems: attributeItems,

    // invoice
    invoice,
    invoiceStatus: invoice.status,

    goods,

    // 售后联系
    contact: order.afterSaleContact || {},

    // 运费险
    freight: order.freight,
    isOrderPage: false, // 是否是下单页
    isChoosedCard: false, // 是否选择了预付卡
    // 有赞担保
    guaranteeOrderInfo: {
      aliases: goods.map((res) => res.goodsInfo.alias),
      feedback: order.feedback, // 是否售后中
      hasYzSecured: order.yzGuarantee, // 是否是有赞担保订单
      orderNo: order.orderNo, // E单号
      orderSuccessTime: order.successTime, // 订单完成时间
      orderStatus: order.state, // 订单状态
      orderPayTime: order.payTime, // 订单支付时间
      payWay: order.buyWay, // 支付方式
      orderCreateTime: order.createTime, // 订单创建时间
    },

    // 分享订单items
    isAllowShareOrder: order.isAllowShareOrder,
    orderBizExtra,

    // 订单是否使用内购券
    fissionTicketNum: object.get(orderBizExtra, 'fissionTicketNum', 0),

    // 积分自定义名称
    pointsName: payment.pointsName,

    // 小额打款信息
    microTransferInfo,
    styleSet: 2,

    // 订单来源
    sourceInfo,
    orderMark: sourceInfo.orderMark,
    env: {},
    miniprogram: {},
    isEduOrder: goods[0].goodsType === 31,
    showPaidPromotion: paidPromotion && paidPromotion.detailUrl,
    paidPromotion,
    shopName: order.shopName,
    payTime: order.payTime,
    isIVR: false,
    isIvrOwner: false,
    IVRRedirectUrl: '',

    isShowStoreInfo: false,
    pageBgColor: '',
    platformInfo: {},
    // 是否是批发订单
    isWholesaleOrder: order?.isWholesaleOrder || false,
    // 线下三方门店，外部订单编号
    outBizNo: order.outBizNo,
    // 隐私面单-号码保护
    privacyWaybill,
  };

  if (!order?.isWholesaleOrder) {
    formatOrderData.recommendBizName = 'order_detail';
    formatOrderData.dialogRecommendBizName = 'od~sg';
  }

  return formatOrderData;
}
