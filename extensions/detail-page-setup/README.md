# @wsc-tee-trade/detail-page-setup

page-setup

![UI呈现]()

## 调试方式

## 存疑

## Event.Listen

| 名称             | 说明         |
| ---------------- | ------------ |
| refreshOrderInfo | 刷新订单信息 |

## Event.Emit

| 名称            | 说明                    |
| --------------- | ----------------------- |
| onLoadOrderInfo | 发送 `订单信息加载完成` |

## Process.Invoke

| 名称                 | 说明 |
| -------------------- | ---- |
| getShareAppMessage   | ???  |
| waitingOrderProgress | ???  |

## Data.Consume

参考来源：`/wsc-h5-trade/definitions/order/detail/*` zanapi: [com.youzan.ebiz.trade.api.manage.detail.DetailService.getOrderInfoFormat](http://zanapi.qima-inc.com/site/service/view/592195)

| 名称 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| kdtId | _number_ | ??? | 店铺 ID |
| headKdtId | _number_ | ??? | 总店 ID |
| orderNo | _string_ | ??? | 订单号 |
| orderStateInfo | _OrderStateInfo_ | ??? | 订单状态 |
| groupon | ??? | ??? | 拼团信息 `_global.orderInfo.groupBuy` |
| logistics | ??? | ??? | 物流信息 `_global.orderInfo.goodsPackages[0]` |
| receiverInfo | _ReceiverInfo_ | ??? | 收货人 or 自提信息 |
| showReceiverInfo | _boolean_ | ??? | 是否展示(收货人 \| 自提信息) `_global.orderBizExtra.showAddress && !_global.orderBizExtra.isSelfFetch` |
| selfFetchInfo | _SelfFetchInfo_ | ??? | 自提信息 |
| isPeriodBuy | _boolean_ | ??? | 是否是周期购 `_global.orderBizExtra.isPeriodBuy` |
| showSelfFetch | _boolean_ | ??? | 是否显示自提信息 `_global.orderBizExtra.showAddress && _global.orderBizExtra.isSelfFetch` |
| showVirtualTicket | _boolean_ | false | 是否显示虚拟卡券 `_global.orderBizExtra.showVirtualTicket` |
| isScanBuyOrder `weapp` | _boolean_ | false | 是否是扫码购订单 `<weappOrderDetail>.sourceInfo.orderMark === 'online_scan_buy'` |
| virtualTicket | ??? | {} | 虚拟卡券信息 `_global.orderInfo.virtualTicketResponse` |
| showLogisticsInfo | _boolean_ | false | 是否显示物流信息 |
| showWxShoppingList `weapp` | _boolean_ | false | 是否展示微信好物圈入口 `PS:状态有点复杂` |
| styleSet `weapp` | _number_ | 2 | 微信好物圈入口样式 |
| showGrouponCollection | ??? | false | `_global.orderInfo.showGroupAgencyReceive` |
| activityType | _number_ | ??? | 拼团活动类型 `_global.orderInfo.activityType` |
| cashBack | ??? | ??? | 返现 `_global.orderInfo.ump.cashBackUmp` |
| returnValueCard | _ReturnValueCard_ | ??? | 返储值金 |
| idCardNumber | _string_ | '' | 身份证号码 `_global.orderInfo.idCardNumber` |
| showIdCard | _boolean_ | false | 是否显示身份证信息 `_global.orderInfo.showIdCard` |
| showQrCode | _boolean_ | ??? | 是否显示二维码, 依赖`_global.orderInfo.virtualResponse` |
| qrCode | ??? | {} | 虚拟商品二维码 |
| showDeliveryType | _boolean_ | false | 是否显示显示配送方式 |
| showCheckDeliveryScope | _boolean_ | false | 是否显示查看同城配送范围 `_global.orderInfo.showCheckDeliveryScope` |
| showDeliveryTime | _boolean_ | false | 是否显示显示送达时间 |
| showLookCoupon | _boolean_ | false | `_global.orderBizExtra.hasPayCouponsOrder` **这个不知道是什么意思** |
| service | _Service_ | ??? | 服务信息 |
| invoiceStatus | ??? | ??? | 发票状态 `_global.invoiceInfo.status` |
| freight | ??? | ??? | 运费险 `_global.orderInfo.freight` |
| coupon | ??? | ??? | 优惠券信息 `_global.orderInfo.ump.couponUmp` |
| couponCode | ??? | ??? | 优惠码 `_global.orderInfo.ump.couponCodeUmp` |
| time | _Time_ | ??? | 订单时间集合 |
| orderBizUrl | ??? | ??? | `_global.orderBizUrl` |
| isUsingNewSnapshot | _boolean_ | false | `_global.orderBizExtra.isUsingNewSnapshot` |
| showShopVerifyAddress | _boolean_ | false | 是否显示虚拟商品核销网点 `_global.orderInfo.showShopVerifyAddress` |
| verifyAddressInfo | _VerifyAddressInfo_ | ??? | 核销网点信息 |
| showPresaleSteps | _boolean_ | false | 是否显示阶段支付信息，依赖 `_global.paymentInfo.phasePays` |
| presaleSteps | _Array<any>_ | [] | 阶段支付信息 `_global.paymentInfo.phasePays` |
| payment | ??? | ??? | `_global.paymentInfo` |
| showPostage | _boolean_ | false | 是否显示显示邮费 `_global.orderBizExtra.showPostage` |
| buyWayDetailUrl | ??? | ??? | ??? `_global.orderBizUrl.buyWayDetailUrl` |
| fissionTicketNum | _number_ | 0 | ??? `_global.orderBizExtra.fissionTicketNum` |
| microTransferInfo | _MicroTransferInfo_ | ??? | 小额打款信息 `_global.microTransferInfo` |
| activitiesList | ??? | ??? | ??? `_global.ump.orderActivities` |
| deliveryActivitiesList | ??? | ??? | ??? `_global.ump.orderDeliveryActivities` |
| decreaseAmount | ??? | ??? | ??? `_global.ump.decreaseAmount` |
| pointsName | _string_ | 积分 | 积分自定义名称 `_global.paymentInfo.pointsName` |
| isAllowShareOrder | _boolean_ | ??? | 是否允许分享订单 `_global.orderInfo.isAllowShareOrder` |
| isNewHotelGood | _boolean_ | ??? | 是否是新酒店订单 |
| goods **重要** | _IItemInfoFormatDTO_ | ??? | 商品信息 `_global.itemInfo` |
| showAfterSaleMobile | _boolean_ | false | 是否显示售后电话 `_global.orderInfo.showAfterSaleMobile` |
| contact | ??? | ??? | 售后电话信息(商家服务) `_global.orderInfo.afterSaleContact` |
| guaranteeOrderInfo | _GuaranteeOrderInfo_ | ??? | 有赞担保 |
| order **重要** | _IMainOrderInfoFormatDTO_ | ??? | 订单信息 `_global.orderInfo` |
| totalPrice | string | ??? | 格式化后的金额，依赖 `_global.paymentInfo.payPrice` |
| shopInfo **重要** | _IShopInfoDto_ | ??? | 店铺信息 `_global.shopInfo` |
| isSelfFetch | _boolean_ | ??? | 是否是自提订单 `_global.orderBizExtra.isSelfFetch` |
| isShowPostponeShip | _boolean_ | false | 是否显示周期购顺延 `_global.orderInfo.isShowPostponeShip` |
| isMultiPeriodBuy | _boolean_ | ??? | 是否是多周期购 **新希望独有** page-setup 中做的判断 |
| gift | _IChildInfoFormatDTO_ | ??? | ??? `_global.gift` |
| invoice **重要** | _IInvoiceFormatDTO_ | ??? | 发票信息 `_global.invoiceInfo` |
| orderBizExtra **重要** | _IOrderBizExtra_ | ??? | 订单扩展信息 `_global.orderBizExtra` |
| orderExtra `weapp` | ??? | ??? | ??? `<weappOrderDetail>.orderExtra` 只有在 weapp/format.js 中出现 |
| bizName | _string_ | order_detail | 在构造函数中写死 |
| buyerId | _number_ | ??? | 买家 ID `_global.buyerId` |
| cpsConfigKey | _string_ | cps_goods_recommend_order_detail | 在构造函数中写死 |
| wrapperType `web` | _string_ | card | 在 `web/format.js` 中写死 |
| requestExtraParams | _string[]_ | ['note', 'coupon'] | ??? 在 format.js 中写死 |
| dialogRequestExtraParams | _array_ | [] | ??? 在 format.js 中写死 |
| isChoosedCard | _boolean_ | false | 是否选择了预付卡 在 format.js 中写死 |
| isOrderPage | _boolean_ | false | 是否是下单页 在 format.js 中写死 |
| eduAttributeItems | _array_ | [] | 教育自定义资料项 `_global.orderInfo.attributeItems` |
| education | ??? | ??? | 课程商品信息 `_global.orderInfo.education` |
| showEducation | _boolean_ | false | 显示课程信息 `_global.orderInfo.showEducation` |
| giftInvite | _IGiftInviteInfo_ | {} | 知识付费送礼退款(礼单详情) `_global.gift.giftInvite` |
| showRefundInfo | _boolean_ | false | 是否展示退款信息，知识付费送礼退款信息展示用 `_global.orderInfo.isShowRefundInfo` |
| orderMark `weapp` | _string_ | ??? | 订单来源 `_global.sourceInfo.orderMark` |
| env **重要** `web` | ??? | ??? | 环境变量 `_global.env` |
| miniprogram `web` | ??? | {} | 小程序环境变量 `_global.miniprogram` |
| isEduOrder | _boolean_ | ??? | 是否是教育订单 `_global.itemInfo[0].goodsType === 31` |
| showPaidPromotion | _boolean_ | ??? | 是否显示付费退款，依赖 `_global.paidPromotion` |
| paidPromotion | ??? | {} | 付费退款信息 `_global.paidPromotion` |
| shopName | _string_ | ??? | ??? `_global.orderInfo.shopName` |
| payTime | _string_ | ??? | 支付时间 `_global.orderInfo.payTime` |
| isIVR `web` | _boolean_ | false | 是否是 IVR 场景 `<uri>?from=ivr` |
| isIvrOwner `web` | _boolean_ | false | ??? `_global.isIvrOwner` |
| ivrRedirectUrl `web` | _string_ | ??? | IVR 场景重定向链接 `web/format.js`提供 |
| isHotel | _boolean_ | ??? | 酒店订单标记 `_global.orderBizExtra.isHotel` |
| mpAccount `web` | ??? | ??? | ??? `_global.mp_account` |
| address | ??? | ??? | ??? **完全不知道在哪里使用过** |
| isSourceFromWx `web` | _boolean_ | ??? | 是否是从微信订单列表过来的查询 `<uri>?source=wx` |
| goodsType | ??? | ??? | ??? **完全不知道在哪里使用过** |
| isFans `web` | _boolean_ | ??? | ??? `_global.isFans` |
| courses `教育` | _FaceToFaceCourseInfo[]_ | [] | 线下报名课程信息 `_global.orderInfo.faceToFaceCourseInfo.courses` |
| addressInfo | _IOrderAddressInfoFormatDTO_ | ??? | 地址信息 `_global.orderAddressInfo` |
| itemList | ??? | ??? | 格式化后的商品信息 `common/format/parseItemInfo` |
| recommendTitle | _string_ | 更多精选商品 | 精选商品标题 在 format.js 中写死 |
| dialogRecommendTitle | _string_ | 为你推荐相关优惠商品 | ??? 在 format.js 中写死 |
| recommendBizName | _string_ | order_detail | ??? 在 format.js 中写死 |
| dialogRecommendBizName | _string_ | od~sg | ??? 在 format.js 中写死 |
| recommendPageSize | _number_ | 20 | 更多精选商品每页加载数量 在 format.js 中写死 |
| recommendLayoutConfig | _RecommendLayoutConfig_ | ??? | ??? |
| dialogRecommendPageSize | _number_ | 6 | ??? |
| dialogRecommendLayoutConfig | _DialogRecommendLayoutConfig_ | ??? | ??? |
| buyAgainCouponAvailable | ??? | ??? | 复购券可用性 `_global.buyAgainCouponAvailable` |
| ump **重要** | _IUMPInfoFormatDTO_ | ??? | ump 信息 `_global.ump` |
| marketingInfo | _IMarketingInfoFormatDTO_ | ??? | 营销信息 `_global.marketingInfo` |
| webviewPath `weapp` | _string_ | '' | ??? **不知道数据来源** |
| isPrePayPage `web` | _boolean_ | false | ??? **不知道有什么用** |
| isShowStoreInfo | _boolean_ | false | ??? |
| pageBgColor | _string_ | '' | ??? |
| platformInfo `web` | _boolean_ | ??? | 是否展示小程序底部 footer 的店铺导航链接 `_global.platformInfo` |
| useBeforePayData | _UseBeforePayData_ | ??? | 先用后付 |
| rxNo `医药` | _string_ | ??? | 有赞医药处方单号 `_global.rxNo` |
| canUseTradeUmpV1 | _boolean_ | ??? | 营销优化白名单切流 `/wscump/trade/use-trade-ump-v1.json` |
| outerPromotion | _IOuterPromotion_ | {} | CRM 线下订单的优惠信息 `_global.outerPromotion` |
