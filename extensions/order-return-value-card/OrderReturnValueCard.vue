<template>
  <view v-if="!canUseTradeUmpV1">
    <van-cell
      v-for="(item, index) in returnValueCard.assertBusinessDetail"
      :key="index"
      custom-class="cell-custom-component"
      value-class="cell-custom-value"
      :is-link="returnValueCard.cardAssetIsActive"
      @click="handleClick"
    >
      <van-icon
        slot="icon"
        name="https://img.yzcdn.cn/public_files/2019/10/10/3d60dd2bb072a861a63fe72bba1146b8.png"
        class="icon-wrap"
        custom-class="custom-icon-class"
      />
      本单已返现储值金 ¥ {{ item.assertBusiness }}
      <van-button
        v-if="!returnValueCard.cardAssetIsActive"
        size="mini"
        plain
        type="danger"
        round
        custom-class="activation-button"
      >
        立即激活
      </van-button>
    </van-cell>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Button from '@youzan/vant-tee/dist/button/index';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import { mapData } from '@youzan/ranta-helper-tee';
import { buildUrl } from '@youzan/tee-biz-util';

export default {
  components: {
    'van-cell': Cell,
    'van-button': Button,
    'van-icon': Icon,
  },

  data() {
    return {
      returnValueCard: {},
      kdtId: '',
      canUseTradeUmpV1: false,
    };
  },

  created() {
    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    }).catch((e) => {
      console.log(e);
    });
    /* #endif */

    mapData(this, ['returnValueCard', 'kdtId', 'canUseTradeUmpV1']);
  },

  destroyed() {
    this.unwatchReturnValueCard && this.unwatchReturnValueCard();
  },

  methods: {
    handleClick() {
      const giftCardUrl = buildUrl(
        `/wscassets/card/index?acp_kdt_id=${this.kdtId}&kdt_id=${this.kdtId}&pub_kdt_id=${this.kdtId}`,
        'h5',
        this.kdtId
      );
      const weappUrl = '/packages/pre-card/home/<USER>';

      navigate({
        url: weappUrl,
        type: 'navigateTo',
        web: {
          type: 'znb',
          znb: {
            url: giftCardUrl,
            weappUrl,
          },
        },
      });
    },
  },
};
</script>

<style lang="scss">
.cell-custom-component {
  margin: 10px 0;
}

.icon-wrap {
  display: flex;
  align-items: center;
  height: 24px;
  font-size: 16px;
  margin-right: 5px;
}

.custom-icon-class {
  vertical-align: middle;
  line-height: 16px;
}

.cell-custom-value {
  text-align: left !important;
  color: #333 !important;
}

.activation-button {
  display: flex !important;
  float: right;
}
</style>
