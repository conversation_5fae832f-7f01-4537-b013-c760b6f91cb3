<template>
  <view class="empty-tip" :style="emptyRecommendGoodsStyle">
    <view class="empty-tip__img" :style="emptyImgStyle"></view>
    <block v-if="isAuthProtocol">
      <view class="empty-tip__title">暂无商品</view>
      <view class="empty-tip__desc" v-if="showDesc && !isPdLive">选择喜欢的商品加入购物袋</view>
      <van-button custom-class="visit" v-if="!isPdLive" @click="goNative">
        {{ goBuyText }}
      </van-button>
    </block>
    <van-button v-else custom-class="visit" @click="getCartGoodsList"> 查看购物车 </van-button>
  </view>
</template>

<script>
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
/* #ifdef web */
import { action } from '@youzan/zan-jsbridge';
/* #endif */

export default {
  components: {
    'van-button': VanButton,
  },
  data() {
    return {
      emptyCartPath: '',
      isEduShop: false,
      isAuthProtocol: false,
      isShowRecommendGoods: '',
      shopMetaInfo: {},
      buyCartLogoUrl: '',
      isPdLive: false,
    };
  },

  computed: {
    goBuyText() {
      return this.isEduShop ? '去选购' : '去逛逛';
    },
    // 教育店铺不展示
    showDesc() {
      return !this.isEduShop;
    },
    emptyImgStyle() {
      return this.buyCartLogoUrl ? `background-image: url(${this.buyCartLogoUrl})` : '';
    },
    emptyRecommendGoodsStyle() {
      // 刘海屏 加上导航 89px
      // 门店的小程序还有选店组件
      // 底下的tab 加上 iPhone底下需要留一部分空白 最高118px
      // 有赞提供技术支持本身的高度 在h5能到142px
      return this.isShowRecommendGoods === false
        ? 'box-sizing: border-box; height: calc(100vh - 349px)'
        : '';
    },
  },

  created() {
    mapData(this, ['isAuthProtocol', 'emptyCartPath', 'isShowRecommendGoods']);
    mapData(this, {
      shopMetaInfo: (shopMetaInfo) => {
        this.isEduShop = shopMetaInfo.shopTopic === 1 && shopMetaInfo.shopType === 0;
      },
    });
    mapData(this, {
      shopConfigs: (shopConfigs) => {
        this.buyCartLogoUrl = shopConfigs.buy_cart_logo_url || '';
      },
    });
    /* #ifdef web */
    // 私域直播间来源的购物车
    this.isPdLive = !!window._global?.pdlive;
    /* #endif */
  },

  methods: {
    getCartGoodsList() {
      this.ctx.process.invoke('authProtocol');
    },
    goNative() {
      const defaultHomePageUrl = '/pages/home/<USER>/index';
      let link = this.emptyCartPath
        ? `/${this.emptyCartPath}`.replace('//', '/')
        : defaultHomePageUrl;
      let navigateType = link === defaultHomePageUrl ? 'switchTab' : 'reLaunch';
      let znbType = 'reLaunch';
      /* #ifdef web */
      const {
        platform,
        url: { wap },
        kdtId,
      } = window._global;
      // 有赞精选
      if (platform === 'youzanmars') {
        action.doAction({ action: 'goHome' });
      } else {
        link = this.emptyCartPath ? this.emptyCartPath : `${wap}/showcase/homepage?kdt_id=${kdtId}`;
        navigateType = '';
        const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isSwanApp, isKsApp } =
          window._global?.miniprogram || {};
        if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
          link = '/pages/home/<USER>/index';
          if (isTTApp) {
            link = '/pages/home/<USER>/main';
          }
          znbType = 'switchTab';
        }
      }
      /* #endif */
      this.ctx.process.invoke('navigateFromCart', { link, navigateType, znbType });
    },
  },
};
</script>

<style lang="scss" scoped>
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 26px;
  background-color: var(--theme-page-page-bg-color, #f7f8fa);

  &__img {
    width: 100px;
    height: 100px;
    background: url(https://b.yzcdn.cn/public_files/24b72760038de1d3224a7e637f9da7e5.png) center
      no-repeat;
    background-size: 100%;
  }

  &__title {
    margin-top: 20px;
    font-size: 16px;
    color: #111;
    font-weight: 500;
  }

  &__desc {
    margin-top: 8px;
    color: #999;
    font-size: 14px;
  }
}

.visit {
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  height: 48px !important;
  line-height: 46px;
  padding: 0 35px !important;
  margin: 20px auto;
  color: #fff !important;
  border-radius: var(--theme-radius-button, 24px) !important;
  background-color: var(--theme-tag-color, --icon) !important;
  box-sizing: border-box;
}
</style>
