{"extensionId": "@wsc-tee-trade/retail-order", "name": "@wsc-tee-trade/retail-order", "version": "0.4.4-beta.3+hotfix-subscribe-order-20221019", "bundle": "<builtin>", "data": {"provide": {"isRetailOrderScene": ["r"], "retailOrderScene": ["r"], "jointId": ["r"], "warehouseId": ["r"], "retailPickUpWayName": ["r"], "retailPickUpWayValue": ["r"], "processData": ["r"], "showSubscribeGuide": ["r"], "retailSelfFetchContact": ["r"], "isShowRetailDeliveryAddress": ["r"], "houseNumberRequired": ["r"], "attrRefreshTimeBucket": ["r", "w"], "tradeAddressVisible": ["r", "w"], "retailPickUpWays": ["r"], "isRetailWeappScene": ["r"], "showRetailPickUpWayErrorToast": ["r", "w"], "reserves": ["r", "w"]}, "consume": {"isRetailOrderScene": ["r"], "retailPickUpWayName": ["r"], "retailPickUpWayValue": ["r"], "prepareData": ["r"], "address": ["r", "w"], "state": ["r"], "expressType": ["r"], "themeColors": ["r"], "prepareLoading": ["r"], "themeCSS": ["r"], "postage": ["r"], "processData": ["r"], "currentAddress": ["r"], "goods": ["r"], "shop": ["r"], "retailSelfFetchContact": ["r"], "kdtId": ["r"], "selfFetch": ["r"], "extra": ["r"], "dine": ["r"], "pickUpWay": ["r"], "retailPickUpWays": ["r"], "isRetailWeappScene": ["r"], "showRetailPickUpWayErrorToast": ["r", "w"], "confirmData": ["r", "w"], "coreReady": ["r"], "orderSkuShow": ["r"], "isRetailShelfPage": ["r"], "orderCreation": ["r"]}}, "widget": {"provide": ["PickUpWay", "WaitingProcess", "SelfFetchShopSecondConfirmDialog", "GetPhoneButton", "<PERSON>e", "PickUpWayCard"], "consume": ["UserAuthorize"]}, "event": {"emit": ["dine:after<PERSON><PERSON><PERSON><PERSON><PERSON>"], "listen": ["skuOrder:reset"]}, "component": {"provide": [], "consume": ["ExpressWayCard"]}, "process": {"define": ["updatePickUpWay", "callHookBeforeUpdateAddress", "callHookBeforeFetchShowByBookKey", "callHookBeforeFetchShow", "callHookBeforeCreateOrder", "callHookBeforeAsyncCreateOrderBookKey", "callHookBeforeGetDefaultSelfFetch", "callHookAfterFetchShow", "validateSelfFetchShop", "callHookBeforeEvokeSubscribeDialog", "selectRetailContact", "genPrepareParamsByRetail"], "invoke": ["mutateState", "updatePickUpWay", "fetchShow", "queryWechatSubscribeResult", "requestSubscribeMessagePush", "updateDinePickUpWay"]}, "platform": ["weapp", "web"]}