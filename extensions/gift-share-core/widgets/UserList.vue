<template>
  <view class="receiver-list-container">
    <view class="receiver-list-wrap">
      <view class="receiver-list-wrap-header">
        <block v-if="gameType === 1"
          >{{ givenNum === giftNum ? '全部已领完，' : '' }}已领取 {{ givenNum }} /
          {{ giftNum }} 个</block
        >
        <block v-else>
          <block v-if="lotteryInfo.isEnd">
            已参与{{ lotteryInfo.engageNum }}人， {{ lotteryInfo.awardNum }}人抽中礼物
          </block>
          <block v-else> 已参与{{ lotteryInfo.engageNum }}人，等待开奖 </block>
        </block>
      </view>
      <view
        class="divider"
        v-if="(gameType === 1 && givenNum > 0) || (gameType === 2 && lotteryInfo.isEnd)"
      ></view>

      <view class="receiver-list" v-if="receiverList.length">
        <block v-if="gameType === 2 && !lotteryInfo.isEnd">
          <view class="receiver receiver-extra">
            <image
              v-for="(user, index) in receiverList"
              :key="index"
              :src="user.avatar"
              class="receiver-avatar receiver-avatar-lottery"
            />
          </view>
        </block>

        <block v-else>
          <view class="receiver" v-for="(user, index) in receiverList" :key="index">
            <view class="receiver-info">
              <image :src="user.avatar" class="receiver-avatar" />
              <view class="receiver-name">{{ user.nickName }}</view>
            </view>
            <view class="receiver-goods">{{ user.goodsName }}</view>
          </view>
        </block>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    gameType: Number,
    receiverList: Array,
    lotteryInfo: Object,
    givenNum: Number,
    giftNum: Number,
  },
};
</script>

<style lang="scss" scoped>
.divider {
  height: 1px;
  transform: scaleY(0.5);
  background: #e0e0e0;
  margin: 16px 0;
}
.receiver-list-container {
  max-height: 250px;
}
.receiver-list-wrap {
  background: #fff;
  margin: 12px;
  border-radius: 8px;
  padding: 16px;
}
.receive-info {
  padding: 10px 0;
  padding-left: 12px;
  text-align: left;
}

.receiver-list {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.receiver {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  margin-bottom: 14px;

  &-extra {
    justify-content: flex-start;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &-info {
    display: flex;
    align-items: center;
  }
}

.receiver-name {
  float: left;
  max-width: 146rpx;
  margin-left: 8px;
}

.receiver-goods {
  display: inline-block;
  max-width: 344rpx;
  color: #000;
  // todo
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.receiver-avatar {
  width: 24px;
  height: 24px;
  border-radius: 35px;

  &-lottery {
    width: 32px;
    height: 32px;
    margin-top: 16px;
    margin-right: 8px;
  }
}
</style>
