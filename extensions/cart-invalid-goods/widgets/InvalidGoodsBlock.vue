<template>
  <view :class="rootClass" v-if="isShow">
    <invalid-goods />
  </view>
</template>

<script>
import Api from '../api';
import { errorToast } from '@youzan/tee-biz-util';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      show: false,
      unavailableItems: [],
      hasValidGoods: true,
      isControlRecommendShow: true,
      isValidGoodsLoadFinish: null,
    };
  },
  externalClasses: ['custom-class'],

  computed: {
    rootClass() {
      return `custom-class invalid-block ${!this.hasValidGoods ? 'no-valid-goods' : ''}`;
    },
    isShow() {
      const { isValidGoodsLoadFinish, isControlRecommendShow, show } = this;
      return isControlRecommendShow ? isValidGoodsLoadFinish && show : show;
    },
  },

  created() {
    mapData(this, ['hasValidGoods', 'isValidGoodsLoadFinish', 'isControlRecommendShow']);
    mapData(this, {
      unavailableItems: (val) => {
        this.show = val.length > 0;
      },
    });
    mapProcess(this, {
      clearInvalidGoods: () => {
        return this.ctx.process
          .invokePipe('beforeCartClearHook', { clearGoodsType: ['invalidGoods'] })
          .then(() => Api.deleteCartBatch(this.unavailableItems))
          .then(() => {
            this.ctx.event.emit('updateCartGoodsList');
          })
          .catch((err) => {
            errorToast(err, { message: '商品删除失败，请稍后重试' });
            return Promise.reject(err);
          });
      },
    });
  },
};
</script>
<style lang="scss" scoped>
.invalid-block {
  border-radius: var(--theme-page-card-border-radius, 8px);
  overflow: hidden;
  margin: 0 12tpx;
}
.no-valid-goods {
  margin-top: var(--theme-page-card-margin-top, 12px) !important;
}
</style>
