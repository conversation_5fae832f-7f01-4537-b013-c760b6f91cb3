import { ImcUniform } from '@youzan/imc-components/js/uniform/imc-uniform';
import '@youzan/imc-components/css/imc-uniform';
import env from '@youzan/wsc-tee-trade-common/lib/env';

try {
  // 抖音用户支付结果页出现白屏，排查发现ImcUniform内部请求/im/index/layer时sid更换了，
  // 抖音环境目前屏蔽
  if (!env.isTTApp) {
    new ImcUniform({
      kdtId: window._global.kdtId,
      biz: 'trade',
      lazyTime: 1,
    });
  }
} catch (err) {
  console.error(err);
}
