<template>
  <view>
    <view class="gift-list">
      <van-tabs
        pid="gift-tab"
        :active="selectedId"
        @click="handleTabsClick"
        custom-class="fixed-tabs"
      >
        <van-tab
          v-for="item in tabList"
          :key="item.id"
          pid="gift-tab"
          :title="item.title"
          :name="item.id"
        />
      </van-tabs>

      <empty v-if="orderList.length === 0" text="暂无数据" />
      <block v-else>
        <order-cell
          v-for="(item, index) in orderList"
          :key="index"
          :items="item.itemList"
          :mode="item.mode"
          :status="item.statusDesc"
          :action="item.actionDesc"
          :gift-alias="item.alias"
          :action-type="item.actionType"
          @cellaction="doAction"
          @celltap="onOrderClick"
        />
      </block>
    </view>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Tab from '@youzan/vant-tee/dist/tab/index.vue';
import Tabs from '@youzan/vant-tee/dist/tabs/index.vue';
import { requestV2 } from '@youzan/tee-biz-request';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { cdnImage } from '@youzan/tee-biz-util';
import formatDate from '@youzan/utils/date/formatDate';
import { getCurrentKdtId } from '@youzan/shop-core-tee';

export default {
  components: {
    'van-tab': Tab,
    'van-tabs': Tabs,
  },

  data() {
    return {
      scroll: false,
      tabList: [
        {
          id: 'donate',
          title: '我送出的',
        },
        {
          id: 'receive',
          title: '我领取的',
        },
        {
          id: 'lottery',
          title: '我参与的',
        },
      ],
      selectedId: 'donate',
      orderList: [],

      tabPage: {
        donate: {
          list: [],
          page: 1,
          pageSize: 20,
          finished: false,
        },
        receive: {
          list: [],
          page: 1,
          pageSize: 20,
          finished: false,
        },
        lottery: {
          list: [],
          page: 1,
          pageSize: 20,
          finished: false,
        },
      },
    };
  },

  beforeMount() {
    this.loadOrderList(this.selectedId);
  },

  created() {
    const query = this.ctx.env.getQuery();
    const { kdt_id: kdtId } = query || {};
    this.kdtId = kdtId || getCurrentKdtId();
  },

  methods: {
    loadOrderList(_selectedId) {
      let reqUrl = null;
      const selectedId = (typeof _selectedId === 'string' && _selectedId) || this.selectedId;
      const selectedPage = this.tabPage[selectedId] || {};
      if (selectedPage.finished) {
        return;
      }
      const reqData = {
        page_no: selectedPage.page,
        page_size: selectedPage.pageSize,
      };
      switch (selectedId) {
        case 'donate':
          reqUrl = '/wscump/gift/donor_gift_byuserid.json';
          break;
        case 'receive':
          reqUrl = '/wscump/gift/receive_gift_byuserid.json';
          break;
        default:
          reqUrl = '/wscump/gift/gift_record_byuserid.json';
          break;
      }
      Toast.loading();
      requestV2({
        path: reqUrl,
        data: reqData,
      })
        .then((res) => {
          Toast.clear();
          if (selectedId === 'donate') {
            this.formatDonateOrderData(mapKeysCase.toCamelCase(res), selectedId);
          } else if (selectedId === 'receive') {
            this.formatReceiveOrderData(mapKeysCase.toCamelCase(res), selectedId);
          } else if (selectedId === 'lottery') {
            this.formatRecordData(mapKeysCase.toCamelCase(res), selectedId);
          }
        })
        .catch((res) => {
          console.log('res', res);
          const err = res.data || {};
          Toast.clear();
          const errCodeList = [101500003, 101504014, 101504019, 101504017];
          if (errCodeList.indexOf(err.code) > -1) {
            // 没有数据
            this.pushOrderList([], selectedId); // 置finished=true处理
          } else {
            Toast(err.msg || '请求出错了');
          }
        });
    },

    formatDonateOrderData(data, selectedId) {
      const orderList = (Array.isArray(data) && data) || [];
      orderList.forEach((order) => {
        const orderInfo = order.mainInfo || {};
        order.mode = 'donate';
        order.alias = orderInfo.alias;
        const isLottery = orderInfo.activityType === 1;
        const lotteryEnd = orderInfo.lotteryTime < Date.now();
        if (!isLottery) {
          // 非抽奖
          if (orderInfo.isReceive) {
            order.statusDesc = '已领完';
            order.actionDesc = '';
            order.actionType = '';
          } else {
            order.statusDesc = `已领取${orderInfo.givenNum}/${orderInfo.num}份`;
            order.actionDesc = '继续赠送';
            order.actionType = 'donate';
          }
        } else if (lotteryEnd) {
          // 抽奖
          order.statusDesc = '已开奖';
          order.actionDesc = '';
          order.actionType = '';
        } else {
          order.statusDesc = '待开奖';
          order.actionDesc = '继续赠送';
          order.actionType = 'donate';
        }

        if (order.itemList && order.itemList.length > 0) {
          order.itemList = order.itemList.map((item) => {
            if (item.goodsInfo && typeof item.goodsInfo === 'string') {
              try {
                item.goodsInfo = JSON.parse(item.goodsInfo);
              } catch (e) {
                item.goodsInfo = {};
              }
            }
            return {
              title: item.goodsName || '',
              thumb: cdnImage(item.goodsInfo.img_url, '!200x200.jpg'),
              price: Number((item.unitPrice || 0) / 100).toFixed(2),
              num: item.num || '1',
              status: `领取截止日期: ${this.formatTime(orderInfo.effectiveTime)}`,
              isLottery: orderInfo.isLottery,
            };
          });
        }
      });
      this.pushOrderList(orderList, selectedId);
    },

    formatReceiveOrderData(data, selectedId) {
      const orderList = (Array.isArray(data) && data) || [];
      orderList.forEach((order) => {
        order.mode = 'receive';
        order.actionDesc = '';
        switch (order.state) {
          case 10:
            order.statusDesc = '未填写地址';
            order.actionDesc = '填写地址';
            order.actionType = 'address';
            break;
          case 50:
            order.statusDesc = '待发货';
            break;
          case 60:
            order.statusDesc = '已发货';
            break;
          case 99:
            order.statusDesc = '已转赠';
            break;
          default:
            break;
        }

        if (order.itemList && order.itemList.length > 0) {
          order.itemList = order.itemList.map((item) => {
            if (item.goodsInfo && typeof item.goodsInfo === 'string') {
              try {
                item.goodsInfo = JSON.parse(item.goodsInfo);
              } catch (e) {
                item.goodsInfo = {};
              }
            }
            return {
              title: item.goodsName || '',
              thumb: cdnImage(item.goodsInfo.img_url, '!200x200.jpg'),
              /* price: Number((item.unitPrice || 0) / 100).toFixed(2), */
              /* num: item.num || 1, */
              status: `领取截止日期: ${this.formatTime(order.effectiveTime)}`,
            };
          });
        }
      });
      this.pushOrderList(orderList, selectedId);
    },

    formatRecordData(data, selectedId) {
      const orderList = (Array.isArray(data) && data) || [];
      orderList.forEach((order) => {
        order.mode = 'lottery';
        order.actionDesc = '';
        switch (order.lotteryStatus) {
          case 0:
            order.statusDesc = '待开奖';
            break;
          case 1:
            order.statusDesc = '已中奖';
            break;
          case 2:
            order.statusDesc = '未中奖';
            break;
          default:
            order.statusDesc = '开奖中';
            break;
        }

        if (order.itemList && order.itemList.length > 0) {
          order.itemList = order.itemList.map((item) => {
            if (item.goodsInfo && typeof item.goodsInfo === 'string') {
              try {
                item.goodsInfo = JSON.parse(item.goodsInfo);
              } catch (e) {
                item.goodsInfo = {};
              }
            }
            return {
              title: item.goodsName || '',
              thumb: cdnImage(item.goodsInfo.img_url, '!200x200.jpg'),
              price: Number((item.unitPrice || 0) / 100).toFixed(2),
              num: item.num || '1',
              status: `开奖时间: ${formatDate(order.lotteryTime, 'YYYY-MM-DD HH:mm')}`,
            };
          });
        }
      });
      this.pushOrderList(orderList, selectedId);
    },

    pushOrderList(orderList, selectedId) {
      const { tabPage } = this;
      const selectedPage = tabPage[selectedId] || {};
      if (orderList && orderList.length > 0) {
        selectedPage.list = selectedPage.list.concat(orderList);
        selectedPage.finished = false;
        selectedPage.page++;
        this.orderList = selectedPage.list;
      } else {
        // 如果列表本身原来就为空
        if (selectedPage.list.length === 0) {
          this.orderList = [];
        }

        selectedPage.finished = true;
      }
      this.tabPage = tabPage;
    },

    formatTime(timestamp) {
      let dateStr = '';
      if (timestamp > 0 && timestamp < 5000000000000) {
        dateStr = formatDate(timestamp, 'YYYY-MM-DD HH:mm');
      } else {
        dateStr = '永久有效';
      }
      return dateStr;
    },

    handleTabsClick({ index }) {
      const selectedId = this.tabList[index].id;
      this.selectedId = selectedId;
      const { tabPage } = this;
      const selectedPage = tabPage[selectedId] || {};
      if (selectedPage.list.length > 0 || selectedPage.finished) {
        this.orderList = selectedPage.list;
        return;
      }
      this.loadOrderList(selectedId);
    },

    onOrderClick(detail) {
      this.toShare({ gift_id: detail.alias, is_from_open_gift_page: 1 });
    },

    doAction(detail) {
      const params = { gift_id: detail.alias };
      if (detail.actionType === 'donate') {
        params.presenter_view = 1;
      }
      this.toShare(params);
    },

    toShare(params) {
      this.ctx.dmc.redirectTo('GiftShare', {
        ...params,
        kdt_id: this.kdtId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fixed-tabs .van-tabs__wrap {
  position: fixed !important;
  width: 100%;
}
</style>
