import { updateSelectAllGoods } from '../../../api';
import { errorToast } from '@youzan/tee-biz-util';

// 前端伪分页参数

let LOAD_PAGE_SIZE = 6;
/* #ifdef web */
LOAD_PAGE_SIZE = 3000; // web不需要分页，渲染全部
/* #endif */
const LOAD_TIME_OUT = 300;

export default function (ctx) {
  return {
    handlePullDownRefresh() {
      this.loadLoading = true;
      this.finished = false;
      this.pageNum = 1;
      this.loadGoodsList = [];
    },
    handleStopLoading() {
      this.loadLoading = false;
      // 结束loading后，数量可改变
      this.numChangeInvalid = false;
    },
    handleToggleCheckedAll() {
      const { shopCart: shopGoods, editMode, isCheckedAll } = ctx.data;
      if (editMode === 'edit') {
        // 如果已全选，则取消选中
        if (isCheckedAll) {
          this.editCheckedGoods = {};
        } else {
          // 如果未全选，则全部选中
          (shopGoods.goodsGroupList || []).forEach((goodsGroup) => {
            (goodsGroup.goodsList || []).forEach((goods) => {
              this.editCheckedGoods = {
                ...this.editCheckedGoods,
                [goods.cartId + '']: 1,
              };
            });
          });
        }
      } else {
        if (this.loading) return;
        this.loading = true;
        this.onlineCourseUnselectFlag = false;
        // 结算模式: 批量选中或取消选中
        this.handleBatchUpdateSelectGoods({
          rangeType: 'shop',
          type: isCheckedAll ? 'remove' : 'add',
        });
      }
    },

    updateShopGoods(newVal) {
      this.shopGoods = newVal;
    },
    resetEditCheckedGoods() {
      this.editCheckedGoods = {};
    },
    handleWatchProvideData({ checkedGoodsList, isCheckedAll }) {
      ctx.data.checkedGoodsList = checkedGoodsList;
      ctx.data.isCheckedAll = isCheckedAll;
    },

    // 滚动加载调用方法
    getLoadGoodsList(isFirst = false) {
      console.log('getLoadGoodsList fire', isFirst);
      // 首次加载不执行判断
      if (isFirst && this.pageNum === 1) {
        this.solveLoadGoodsList();
      } else if (this.computedGoodsGroupList?.length > 0) {
        const totalPageNum = Math.floor(this.computedGoodsGroupList.length / LOAD_PAGE_SIZE) + 1;
        const loadLength = this.loadGoodsList.length;
        // loading为true时，不会触发load事件
        this.loadLoading = true;
        // 需要保护一层，防止load重复触发，进行重复push
        if (this.pageNum <= totalPageNum && this.pageNum * LOAD_PAGE_SIZE > loadLength) {
          setTimeout(() => {
            this.solveLoadGoodsList();
          }, LOAD_TIME_OUT);
        }
      }
    },
    // 需要实时更新loadGoodsList
    updateLoadGoodsList() {
      const loadLength = this.loadGoodsList.length;
      if (loadLength > 0) {
        // 除了首次加载之外，shopCart更新的时候，直接渲染全部商品列表，取消虚拟滚动
        this.loadGoodsList = this.computedGoodsGroupList;
        this.setCartListFinish();
        this.loadLoading = false;
      } else {
        // 首次加载
        this.getLoadGoodsList(true);
      }
    },
    // 需要保护一层，防止load重复触发，进行重复push
    solveLoadGoodsList() {
      const totalPageNum = Math.floor(this.computedGoodsGroupList.length / LOAD_PAGE_SIZE) + 1;
      this.loadGoodsList = [
        ...this.loadGoodsList.concat(
          this.computedGoodsGroupList?.slice(
            (this.pageNum - 1) * LOAD_PAGE_SIZE,
            this.pageNum * LOAD_PAGE_SIZE
          )
        ),
      ];
      this.pageNum++;
      this.loadLoading = false;
      console.log('this.pageNum > totalPageNum', this.pageNum > totalPageNum);
      // 判断加载完成后 不再触发load事件
      if (this.pageNum > totalPageNum) {
        this.setCartListFinish();
        console.log('ctx.data.isValidGoodsLoadFinish - update', true);
      }
    },

    // finished是van-list的标识
    // isValidGoodsLoadFinish是有效商品加载完成标识，这个标识设置为true才展示失效商品和更多精选商品
    setCartListFinish() {
      this.finished = true;
      ctx.data.isValidGoodsLoadFinish = true;
    },

    handleBatchUpdateSelectGoods(payload) {
      const { rangeType, type } = payload;

      const params = {
        kdtId: this.kdtId,
        type,
      };

      // 选中或取消选中店铺所有商品
      if (rangeType === 'shop') {
        updateSelectAllGoods(params)
          .then(() => {
            this.loading = false;
            //
            ctx.event.emit('updateCartGoodsList');
          })
          .catch((e) => {
            this.loading = false;
            errorToast(e, { message: `${e?.msg || '操作失败'}, 请刷新或者重试` });
          });
      }
    },

    // 换购商品加购后刷新列表
    refreshCartGoodsList() {
      ctx.event.emit('updateCartGoodsList');
    },
    updateListObNeedReLoad(newVal) {
      this.listObNeedReLoad = newVal;
    },
    changeCartActivityPopupShow(activityInfo) {
      this.cartActivityPopupShow = true;
      this.cartActivityInfo = activityInfo || {};
    },
    showExchangeModal({ activityInfo, goodsList }) {
      this.currentActivityInfo = activityInfo;
      this.currentActivityGoodsList = goodsList;
    },
    handleCloseCartActivityPopup() {
      this.cartActivityPopupShow = false;
    },
  };
}
