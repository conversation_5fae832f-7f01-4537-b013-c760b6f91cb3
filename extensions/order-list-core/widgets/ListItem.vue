<template>
  <!----------------------------------------------------------------------------
  NOTICE: `<cloud-* />` 前缀的widget，是为了做开放1.0的云定制兼容。
  NOTICE: 标品不要对这类组件进行处理，包括它们的位置也不要调整。
  NOTICE: 何时可以移除？如果没有三方在使用开放1.0定制订单列表时可以移除。
  NOTICE: H5 主要涉及到的三方应用：
  NOTICE: - 法大大-电子合同(52008) --------- cloud-button-demo
  NOTICE: - 荷尔健康(168) ----------------- cloud-order-list-item-inquiry
  NOTICE: - 钻石世家客户服务中心(10002719) -- cloud-zhibaodan
  NOTICE: - 劲牌精彩购应用(47108) ---------- cloud-order-info
  ------------------------------------------------------------------------------>
  <view class="list-item">
    <list-item-tag :list-item="listItem" />
    <list-item-header
      v-if="showComponent.listItemHeader"
      :list-index="listIndex"
      :list-item="listItem"
      :cloud-index="listIndex"
      :cloud-order-no="listItem.orderNo"
    />
    <list-item-body
      v-if="showComponent.listItemBody"
      :list-index="listIndex"
      :list-item="listItem"
      :cloud-index="listIndex"
      :cloud-order-no="listItem.orderNo"
    />
    <order-info v-if="showComponent.orderInfo" :list-index="listIndex" :list-item="cloudListItem" />
    <order-list-item-inquiry
      v-if="showComponent.orderListItemInquiry"
      :list-index="listIndex"
      :list-item="cloudListItem"
    />
    <after-list-item-body-by-yun v-if="showComponent.afterListItemBodyByYun" :data="cloudData" />
    <list-item-footer
      v-if="showComponent.listItemFooter"
      :list-index="listIndex"
      :list-item="listItem"
      :cloud-index="listIndex"
      :cloud-order-no="listItem.orderNo"
    />
    <button-demo
      v-if="showComponent.buttonDemo"
      :list-index="listIndex"
      :list-item="cloudListItem"
    />
    <zhibaodan v-if="showComponent.zhibaodan" :list-index="listIndex" :list-item="cloudListItem" />
    <after-list-item-footer-by-yun
      v-if="showComponent.afterListItemFooterByYun"
      :data="cloudData"
    />
  </view>
</template>

<script>
import get from '@youzan/utils/object/get';
import { mapState } from '@ranta/store';
import formatMoney from '@youzan/utils/money/format';

const DEFAULT_SHOW_COMPONENT = {
  listItemHeader: true,
  listItemBody: true,
  listItemFooter: true,
};
const getShowComponent = (yunDesignConfig) => {
  const design = get(yunDesignConfig, 'design', []);
  const listDesign = design.find((_) => _.type === 'list')?.children ?? [];
  const designMap = listDesign.reduce((prev, { type }) => {
    return {
      ...prev,
      [type]: true,
    };
  }, {});
  return {
    listItemHeader: get(designMap, 'list-item-header', false),
    listItemBody: get(designMap, 'order-list', false),
    listItemFooter: get(designMap, 'list-item-footer', false),
    orderInfo: get(designMap, 'jp-app1_cloud_order-info', false),
    orderListItemInquiry: get(designMap, 'bluedhealth_cloud_order-list-item-inquiry', false),
    buttonDemo: get(designMap, 'fdd_cloud_button-demo', false),
    zhibaodan: get(designMap, 'shcrm_cloud_zhibaodan', false),
    afterListItemBodyByYun: !!yunDesignConfig,
    afterListItemFooterByYun: !!yunDesignConfig,
  };
};
export default {
  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: {
      type: Object,
      default: () => ({ orderItems: [] }),
    },
  },
  data() {
    return {
      showComponent: {
        ...DEFAULT_SHOW_COMPONENT,
      },
      ...mapState(this, ['activityTab']),
    };
  },
  computed: {
    // NOTICE: 专供开放1.0使用的listItem，除非修复bug，否则不要动
    cloudData() {
      const data = {
        headerData: {
          orderStateStr: this.listItem.orderStateStr,
          shopName: this.listItem.shopName,
          listType: this.activityTab.type,
          listIndex: this.listIndex,
          isWholesaleOrder: get(this, 'listItem.orderItems[0].orderExtra.isWholesaleOrder', false),
        },
        itemData: {
          listType: this.activityTab.type,
          listIndex: this.listIndex,
          orderList: this.listItem.orderItems.map((orderItem, orderItemIndex) => {
            return {
              orderNo: orderItem.orderNo,
              outBizNo: orderItem.outBizNo,
              orderIndex: orderItemIndex,
              listType: this.activityTab.type,
              hotel: {
                show: !!(orderItem.hotel && orderItem.orderType === 35),
                num: orderItem.items.length,
              },
              goodsList: orderItem.items.map((item) => {
                return {
                  sku: item.sku,
                  tagList: item.tagList,
                  price: '¥' + formatMoney(item.price),
                  isShipped: !!item.isShipped,
                  activityId: item.activityId,
                  activityType: item.activityType,
                  alias: item.alias,
                  goodsId: item.goodsId,
                  goodsType: item.goodsType,
                  itemId: item.itemId,
                  thumb: item.image,
                  title: item.title,
                  num: item.num,
                  goodsImgTag: get(orderItem, 'orderExtra.isCrossBorder', false) ? '海淘' : '',
                  isPrescriptionDrugGoods: orderItem.items.some(
                    (item) => item.isPrescriptionDrugGoods
                  ),
                  payGradeCard: item.payGradeCard,
                  skuVid: get(item, 'sku[0].vId', ''),
                  preSaleExpressTimeDesc: item.preSaleExpressTimeDesc,
                };
              }),
              showPayInfo: this.listItem.orderPermission.isShowTotalPrice,
              payDesc: orderItem.payInfo.amountDesc,
              payLast: orderItem.payInfo.last,
              payPrice: '¥' + formatMoney(orderItem.payInfo.payAmount),
              pickUpCode: (orderItem.orderDesc || {}).pickUpCode || '',
            };
          }),
        },
        footerData: {
          listType: this.activityTab.type,
          listIndex: this.listIndex,
          btnList: this.listItem.btnList.map((item, index) => {
            return {
              disabled: item.disabled,
              value: item.value,
              text: item.text,
              btnIndex: index,
              showCoupon: item.showCoupon,
            };
          }),
        },
      };
      return data;
    },
    cloudListItem() {
      return {
        out_biz_no: this.listItem.outBizNo,
        status: this.listItem.status,
        kdt_id: this.listItem.kdtId,
        order_state_str: this.listItem.orderStateStr,
        order_no: this.listItem.orderNo,
        order_items: this.listItem.orderItems.map((orderItem) => {
          return {
            items: orderItem.items.map((item /* 商品项 */) => {
              return {
                image: item.image,
                title: item.title,
                price: item.price,
                num: item.num,
                sku: item.sku,
                goods_id: item.goodsId,
              };
            }),
            create_time: orderItem.createTime,
            total_price: orderItem.totalPrice,
            detail_url: orderItem.detailUrl,
            status_code: orderItem.statusCode,
          };
        }),
      };
    },
  },
  created() {
    // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
    const { hasDesignV1 } = this.ctx.cloud.hasDesign('order-list');
    if (hasDesignV1) {
      this.showComponent = getShowComponent(this.ctx.data.yunDesignConfig);
    }
  },
};
</script>

<style lang="scss" scoped>
.list-item {
  margin: 12px;
  padding-bottom: 16px;
  border-radius: 8px;
  background: #fff;

  &:first-child {
    margin-bottom: 0;
  }
}
</style>
