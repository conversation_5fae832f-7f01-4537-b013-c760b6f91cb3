import { cloud, bridge } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import type { TradeCartGoods } from '@youzan-cloud/cloud-biz-types';
import InvalidGoods from './InvalidGoods.vue';
import Goods from './Goods.vue';
import GoodsBlock from './GoodsBlock.vue';
import InvalidGoodsBlock from './widgets/InvalidGoodsBlock.vue';
import InvalidMask from './widgets/InvalidMask.vue';
import { UnAvailableGoodsListV1 } from './types';
import { cloudData, isSameObject } from './utils';

export default class InvalidGoodsExtension {
  ctx: any;

  /**
   * invalidGoodsList
   * @desc 失效商品列表
   */
  @cloud('invalidGoodsList', 'data')
  invalidGoodsList: TradeCartGoods[];

  /**
   * unAvailableGoodsList
   * @desc 失效商品数据
   */
  @bridge('unAvailableGoodsList', 'data')
  unAvailableGoodsListV1: UnAvailableGoodsListV1;

  constructor(options) {
    this.ctx = options.ctx;
    this.initCloudData();
  }

  static widgets = {
    Goods,
    GoodsBlock,
    InvalidGoods,
    InvalidMask,
    InvalidGoodsBlock,
  };

  initCloudData() {
    mapData(this, ['unavailableItems'], {
      isSetData: false,
      callback: () => {
        const { unavailableItems } = this.ctx.data;
        const newOpenData = {
          unAvailableGoodsListV1: cloudData.getUnAvailableGoodsListV1({ unavailableItems }),
          invalidGoodsList: cloudData.getInvalidGoodsList({ unavailableItems }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
