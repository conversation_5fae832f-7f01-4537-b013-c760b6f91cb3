import CouponBar from './CouponBar.vue';
import CouponListPopup from './CouponListPopup.vue';
import CouponBarBlock from './CouponBarBlock.vue';
import { getAvlCouponList } from './api';
import { cloud } from '@youzan/ranta-helper';
import { mapCtxData } from '@ranta/store';
import createStore from './store';
import { CouponTagList, ToggleDialogParams } from './types';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import args from '@youzan/utils/url/args';
import { cloudData } from './utils';

export const GUIDE_TYPE = {
  FREE: 1,
  PAID: 2,
  CARD: 3,
};

export default class Coupon {
  ctx: any;

  store: any;

  /**
   * toggleCouponListPopup
   * @desc 展示优惠券列表弹窗
   * @type {ToggleDialogParams}
   */
  @cloud('toggleCouponListPopup', 'method', { allowMultiple: true })
  toggleCouponListPopup(params: ToggleDialogParams) {
    if (params.isShow) {
      this.ctx.event.emit('showCouponList');
    } else {
      this.ctx.event.emit('closeCouponList');
    }
  }

  /**
   * couponTagList
   * @desc 购物车优惠券标签数据
   * @type {CouponTagList}
   */
  @cloud('couponTagList', 'data')
  couponTagList: CouponTagList;

  static widgets = {
    CouponBar,
    CouponListPopup,
    CouponBarBlock,
  };

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore();
    this.initData();
    this.initCloudData();
    this.initEvents();
  }

  initData() {
    this.ctx.data.couponList = [];
    const params = {
      kdtId: this.ctx.data.kdtId,
      limit: 20,
      filterByIdentityLimit: true,
      pdlive: '',
    };
    /* #ifdef web */
    // 私域直播购物车下单
    const pdlive = args.get('pdlive', window.location.href);
    if (pdlive) {
      params.pdlive = pdlive;
    }
    /* #endif */
    getAvlCouponList(params)?.then((res) => {
      this.ctx.data.couponList =
        res?.map((coupon) => {
          return {
            ...coupon,
            guideType: coupon?.optimal_applicable_limit_identity_type,
            isVip: [GUIDE_TYPE.FREE, GUIDE_TYPE.PAID].includes(
              coupon?.optimal_applicable_limit_identity_type
            ),
          };
        }) || [];
    });

    mapCtxData(this, [
      'themeCSS',
      'couponList',
      'hasValidGoods',
      'themeColors',
      'themeStyle',
      'checkedGoodsList',
      'dataLoaded',
    ]);
  }

  initEvents() {
    mapEvent(this, {
      showCouponList: () => this.store.changeCouponPopupShow(true),
      closeCouponList: () => this.store.changeCouponPopupShow(false),
    });
  }

  initCloudData() {
    this.couponTagList = [];
    mapData(this, ['couponList'], {
      callback: () => {
        const { couponList } = this.ctx.data;
        const couponTagList = cloudData.getCouponTagList({ couponList });
        if (couponTagList.length > 0) {
          this.couponTagList = couponTagList;
        }
      },
    });
  }
}
