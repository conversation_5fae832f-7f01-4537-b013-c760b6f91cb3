<template>
  <view class="gift-cart">
    <van-icon
      v-if="navArrowTop"
      name="arrow-left"
      color="#111111"
      size="20px"
      :style="{ top: navArrowTop + 'px' }"
      class="gift-cart__nav-arrow"
      @click="goBack"
    />
    <view v-if="navPaddingTop" class="gift-cart__nav" :style="{ paddingTop: navPaddingTop + 'px' }"
      >送礼物</view
    >
    <view class="gift-cart__body-wrap">
      <view class="gift-cart__body">
        <view
          v-if="!goodsList.length && !invalidGoodsList.length"
          class="gift-cart__body--empty"
          @click="handlePickGoodsClick"
        >
          <image class="gift-cart__body-gift-icon" src="https://b.yzcdn.cn/icon/gift-icon.png" />
          <view>
            <view class="gift-cart__title">开始挑选礼物</view>
            <view class="gift-cart__sub-title" style="font-size: 14px"
              >点击这里，挑选喜欢的礼物</view
            >
          </view>
          <van-icon name="arrow" color="#323233" size="14px" class="gift-cart__body--empty-arrow" />
        </view>

        <block v-else>
          <view v-if="goodsList.length || invalidGoodsList.length" class="gift-cart__goods-list">
            <view class="gift-cart__goods-list-header">
              <view class="gift-cart__title">共{{ allGiftNum }}件礼物</view>
              <view class="add-more" @click="handlePickGoodsClick"
                ><van-icon
                  name="add-o"
                  color="#576B95"
                  size="16px"
                  style="margin-right: 4px"
                />添加礼物</view
              >
            </view>
            <view class="gift-cart__divider"></view>
            <view class="gift-cart__goods-list-body">
              <view v-for="(item, index) in filteredGoodsList" :key="index">
                <view class="gift-cart__divider" v-if="index !== 0"></view>
                <goods-item :goods-item="item" @change="handleGoodsChange(index, $event)" />
              </view>
            </view>
            <block v-if="invalidGoodsList.length">
              <view class="gift-cart__divider" v-if="goodsList.length"></view>
              <view class="gift-cart__invalid-goods">
                <view class="gift-cart__invalid-goods--title">
                  <view class="van-pull-left"> 以下商品无法一起购买 </view>
                  <view @click="clearInvalidGoods" class="gift-cart__clear-btn">
                    清空失效商品
                  </view>
                </view>
                <view v-for="(item, index) in invalidGoodsList" :key="index">
                  <view class="gift-cart__divider" v-if="index !== 0"></view>
                  <goods-item
                    :goods-item="item"
                    @change="handleGoodsChange(index, $event)"
                    :is-invalid="true"
                  />
                </view>
              </view>
            </block>
            <view class="gift-cart__divider"></view>
            <view class="gift-cart__goods-list-footer">
              <view class="footer-title">总计</view>
              <view class="gift-cart__title">￥{{ totalPriceStr }}</view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <view class="gift-cart__methods">
      <view class="gift-cart__methods-container">
        <view class="gift-cart__methods-title">选择一种赠送方式</view>
        <view class="gift-cart__methods-list">
          <view
            v-for="item in methodsList"
            :key="item.type"
            :class="[
              'gift-cart__methods-list-item',
              item.type === gameType ? 'gift-cart__methods-list-item-active' : '',
            ]"
            @click="changeGameType(item.type)"
          >
            <view class="gift-cart__title">{{ item.title }}</view>
            <view class="gift-cart__sub-title">{{ item.subTitle }}</view>
          </view>
        </view>
      </view>

      <view v-if="gameType === 2">
        <view class="gift-cart__methods-content">
          <view class="gift-cart__title">开奖时间</view>
          <view @click="toggleShowPicker"
            >{{ lotteryTimeStr }}
            <van-icon name="arrow" color="#323233" size="16px" style="margin-left: 2px"
          /></view>
        </view>
      </view>
    </view>
    <van-popup :show="showPicker" position="bottom">
      <van-datetime-picker
        type="datetime"
        :value="currentDate"
        :max-date="maxDate"
        :min-date="minDate"
        :min-hour="minHour"
        :max-hour="maxHour"
        :formatter="formatter"
        @input="onChangeTime"
        @confirm="handlePickerTimeChange"
        @cancel="toggleShowPicker"
      />
    </van-popup>

    <view class="gift-cart__message">
      <view class="gift-cart__title">祝福语</view>
      <view class="gift-cart__divider"></view>
      <van-field
        class="gift-cart__message-textarea"
        type="textarea"
        :border="false"
        placeholder-style="color: #ccc;font-size: 16px;"
        placeholder="送你一份礼物，祝你天天开心"
        :maxlength="20"
        @blur="handleTextareaInput"
        :show-confirm-bar="false"
      >
      </van-field>
    </view>

    <view class="gift-cart__bottom">
      <view class="gift-cart__bottom-anonymous">
        <van-checkbox
          @change="handleCheckboxClick"
          :value="anonymous"
          icon-size="18px"
          checked-color="#ECC085"
        />
        <text @click="handleCheckboxClick" class="gift-cart__bottom-anonymous-text">匿名收礼</text>
      </view>
    </view>
    <bottom-links :kdt-id="kdtId" />

    <view class="gift-cart__bottom-btn-container">
      <view @click="goPay" :class="['bottom-btn', !goodsList.length ? 'bottom-btn-invalid' : '']"
        >立即支付 ￥{{ totalPriceStr }}</view
      >
    </view>
  </view>
</template>

<script>
import mapKeysToCameLCase from '@youzan/utils/string/mapKeysToCamelCase';
import get from '@youzan/utils/object/get';
import { requestV2 } from '@youzan/tee-biz-request';
import { setStorageSync, getStorage } from '@youzan/tee-api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Tee from '@youzan/tee';
import money from '@youzan/weapp-utils/lib/money';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import DatetimePicker from '@youzan/vant-tee/datetime-picker/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Field from '@youzan/vant-tee/dist/field/index';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import VanCheckbox from '@youzan/vant-tee/dist/checkbox/index.vue';
import { getCurrentKdtId } from '@youzan/shop-core-tee';

export default {
  components: {
    'van-icon': Icon,
    'van-popup': Popup,
    'van-field': Field,
    'van-datetime-picker': DatetimePicker,
    'van-checkbox': VanCheckbox,
  },
  data() {
    return {
      messages: '',
      totalPriceStr: '',
      navPaddingTop: 0,
      navArrowTop: 0,
      totalPrice: 0,
      allGiftNum: 0,
      goodsList: [],
      invalidGoodsList: [],
      couponList: [],
      isFetching: true,
      showAllCartGoods: false,
      anonymous: false,
      gameType: 1,
      lotteryTime: 0,
      lotteryTimeStr: '',
      timePickerRange: [],
      defaultPickerValue: [],
      cartGoodsList: {},
      showPicker: false,
      minHour: 0,
      maxHour: 24,
      minDate: new Date().getTime(),
      maxDate: new Date().getTime() + 86400000,
      currentDate: new Date().getTime(),
      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        return value;
      },
      methodsList: [
        {
          type: 1,
          title: '先到先得',
          subTitle: '每人最多领取一份',
        },
        {
          type: 2,
          title: '定时开奖',
          subTitle: '到达时间自动开奖',
        },
      ],
    };
  },

  computed: {
    filteredGoodsList() {
      if (this.showAllCartGoods) {
        return this.goodsList;
      }
      return this.goodsList.slice(0, 3);
    },
  },

  beforeMount() {
    // 从内存中读取匿名状态
    getStorage('gift-cart-anonymous').then((res) => {
      this.anonymous = res.data;
    });

    getStorage('gift-play-type').then((res) => {
      this.gameType = res.data || 1;
    });

    this.initGiftCart();
    this.initPicker();
    /* #ifdef weapp */
    // 设置自定义导航栏样式
    const { top } = wx.getMenuButtonBoundingClientRect();
    this.navPaddingTop = top + 3;
    this.navArrowTop = top + 5;
    /* #endif */
  },

  mounted() {
    // 设置沉浸式导航栏样式
    this.ctx.data.barConfig = {
      navigationbar_type: 'immersion',
      style_color_custom_background_color: '#ffffff',
      style_color_type: 'custom',
      type: 'navigationbar_config',
      title_switch: '1',
      style_color_custom_type: 'purecolor',
      navigationbar_config_type: 'custom',
      style_color_custom_font_color: 'black',
      title_position: 'center',
      title_content_type: 'text',
      back_icon_style: 'background: none;',
    };
    this.ctx.data.pageTitle = '送礼物';
  },

  created() {
    const query = this.ctx.env.getQuery();
    const { kdt_id: kdtId } = query || {};
    this.kdtId = kdtId || getCurrentKdtId();
  },

  methods: {
    goBack() {
      if (getCurrentPages().length > 1) {
        Tee.navigateBack();
      } else {
        // 返回首页
        wx.reLaunch({
          url: '/pages/home/<USER>/index',
        });
      }
    },
    onChangeTime(event) {
      this.currentDate = event;
    },

    toggleShowPicker() {
      this.showPicker = !this.showPicker;
    },

    activityEnabled() {
      Dialog.confirm({
        message: '该活动已到期，可咨询商家',
        confirmButtonText: '返回',
      }).then(() => {
        Tee.navigateBack();
      });
    },

    initGiftCart() {
      // 购物车数据加载前检查插件是否开启
      const res = get(this.ctx.data, 'shopConfigs', {});
      if (+res.gift_plug_status === 0) {
        this.activityEnabled();
      }

      Toast.loading({ message: '加载中' });
      requestV2({
        path: '/wscump/gift/giftcart.json',
      })
        .then((res) => mapKeysToCameLCase(res))
        .then((res) => {
          const { cartGoodsList = {}, giftActivityInfo } = res;
          const goodsList = this._mapCartGoods(cartGoodsList.items || []);
          const invalidGoodsList = this._mapCartGoods(cartGoodsList.unavailableItems || [], true);
          // 如果活动没有开启，提示活动错误并回退
          if (!giftActivityInfo.enabled) {
            this.activityEnabled();
          }
          Toast.clear();
          Object.assign(this, {
            goodsList,
            invalidGoodsList,
            giftActivityInfo,
            isFetching: false,
            cartGoodsList,
          });
          this.updateTotalPrice();
        })
        .catch((err) => {
          Toast(err.msg || '加载购物车数据失败，请刷新重试');
        });
    },

    initPicker() {
      let lotteryTime = Date.now() + 20 * 60 * 1000;
      const lotteryTimeArr = this._parseDateStr(lotteryTime);
      const tomorrowTimeArr = this._parseDateStr(Date.now() + 24 * 60 * 60 * 1000);
      const timePickerRange = [
        [lotteryTimeArr[0], tomorrowTimeArr[0]],
        this._hoursRange(0),
        this._minutesRange(0),
      ];

      let defaultPickerValue = this._parseDateToPicker(lotteryTime);
      let lotteryTimeStr = lotteryTimeArr.join(' ');

      // 若之前设置过送礼时间，读取老的时间填充
      getStorage('gift-play-time').then((res) => {
        console.log(res);
        if (this.checkLotteryTimeValid(res.data)) {
          defaultPickerValue = this._parseDateToPicker(res.data);
          lotteryTimeStr = this._parseDateStr(res.data).join(' ');
          lotteryTime = res.data;

          Object.assign(this, {
            defaultPickerValue,
            lotteryTimeStr,
            lotteryTime,
          });
        }
      });

      Object.assign(this, {
        timePickerRange,
        defaultPickerValue,
        lotteryTime,
        lotteryTimeStr,
      });
    },

    handlePickGoodsClick() {
      this.ctx.dmc.redirectTo('GiftGoodsList', {
        alias: this.giftActivityInfo.alias,
        kdt_id: this.kdtId,
      });
    },

    // 变更购物车商品数量
    updateGiftCartData(goodsItem) {
      return new Promise((resolve, reject) => {
        const { goodsId, skuId, num } = goodsItem;
        requestV2({
          path: '/wscump/gift/updategiftcart.json',
          data: {
            goodsId,
            skuId,
            num,
          },
        })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err.data);
          });
      });
    },

    // 商品数量变更
    handleGoodsChange(index, event) {
      const num = typeof event === 'string' ? +event : +event.detail;
      const goodsItem = this.goodsList[index];
      // 当数据没有变化时，不做请求
      if (goodsItem.num === num) return;
      goodsItem.num = num;
      if (num === 0) {
        this.deleteCartGoods([goodsItem]).then((res) => {
          if (!res) {
            Toast('删除商品失败，请重试');
          } else {
            const { goodsList } = this;
            goodsList.splice(index, 1);
            Object.assign(this, {
              goodsList,
            });
            this.updateTotalPrice();
          }
        });
      } else {
        this.updateGiftCartData(goodsItem).then(() => {
          Object.assign(this, {
            [`goodsList[${index}].num`]: num,
          });
          this.updateTotalPrice();
        });
      }
    },

    // 删除购物车中的商品
    deleteCartGoods(goodsList) {
      return new Promise((resolve, reject) => {
        requestV2({
          path: '/wscump/gift/deletecartgoods.json',
          data: {
            goodsList: JSON.stringify(goodsList),
          },
        })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err.data);
          });
      });
    },

    goPay() {
      const { gameType, lotteryTime, anonymous = false, cartGoodsList } = this;
      const { kdtId } = cartGoodsList || {};

      // 抽奖玩法的时间如果存在问题，提示并且不允许下单
      if (gameType === 2 && !this.checkLotteryTimeValid(lotteryTime)) {
        const content =
          lotteryTime - Date.now() > 10 * 60 * 1000
            ? '选择时间需要在24小时内'
            : '选择时间需要在当前10分钟后';
        Dialog.confirm({
          message: `${content}, 请调整开奖时间`,
          confirmButtonText: '确定',
        });
        return;
      }

      const config = {
        canyinChannel: 1,
        canyinIds: [],
      };
      const items = [];
      const itemSources = [];
      const activities = [];

      const goodsList = (cartGoodsList.items || [])
        .map((item) => {
          const current = this.goodsList.find((goods) => item.goodsId === goods.goodsId);
          if (!current) return item;
          return {
            ...item,
            num: current.num,
          };
        })
        .filter((item) => this.goodsList.find((goods) => item.goodsId === goods.goodsId));

      goodsList.forEach((goods) => {
        let bizData;
        try {
          /* eslint-disable-next-line */
          bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
        } catch (e) {
          /* eslint-disable-next-line */
          console.log(e);
        }
        const baseId = {
          kdtId,
          goodsId: goods.goodsId,
          skuId: goods.skuId,
          propertyIds: goods.propertyIds || [],
          activityId: goods.activityId,
          activityType: +goods.activityType || 0,
        };

        // 外部订单来源
        const tpps = get(goods, 'bizExtension.cartBizMark.tpps');
        const item = {
          ...baseId,
          storeId: goods.storeId || 0,
          price: goods.payPrice || 0,
          pointsPrice: goods.pointsPrice || 0,
          num: goods.limitNum ? Math.min(goods.num, goods.limitNum) : goods.num,
          itemMessage: goods.messages || '',
          extensions: {
            tpps,
            CART_ID: goods.cartId,
          },
          isSevenDayUnconditionalReturn: goods.isSevenDayUnconditionalReturn || false,
        };

        if (goods.deliverTime) item.deliverTime = goods.deliverTime;

        const itemSource = {
          ...baseId,
          bizTracePointExt: bizData,
          cartCreateTime: goods.createdTime,
          cartUpdateTime: goods.updatedTime,
        };

        const activity = {
          ...baseId,
          activityAlias: goods.activityAlias,
        };

        items.push(item);
        itemSources.push(itemSource);
        activities.push(activity);

        if (goods.canyinId) {
          config.canyinIds.push(goods.canyinId);
        }
      });

      if (items.length === 0) return;

      requestV2({
        method: 'POST',
        path: '/wsctrade/order/book.json',
        data: {
          config: {
            gift: {
              giftOrder: true,
              alias: this.giftActivityInfo.alias,
              senderMessage: this.message || this.giftActivityInfo.defaultPrompt,
              anonymous,
              activityType: gameType === 1 ? 'FIFA' : 'TIME_LOTTERY',
              lotteryTime,
            },
          },
          items,
          sellers: [{ kdtId, storeId: 0 }],
          source: {
            itemSources,
          },
          ump: {
            activities,
          },
          extensions: {},
        },
      })
        .then((res) => {
          /* #ifdef weapp */
          this.ctx.process.invoke('navigateToTradeBuy', {
            navigateParams: {
              bookKey: res.bookKey,
            },
          });
          /* #else */
          Tee.navigate({
            // eslint-disable-next-line
            url: `https://cashier.youzan.com/pay/wsctrade_buy?book_key=${res.bookKey}&showwxpaytitle=1&kdt_id=${kdtId}`,
          });
          /* #endif */
        })
        .catch(() => {
          Toast('下单失败，请稍后重试');
        });

      // 将勾选状态存储
      setStorageSync('gift-cart-anonymous', false);
    },

    _mapCartGoods(goodsList, invalid = false) {
      if (!goodsList.length) return [];
      return goodsList.map((item) => {
        const {
          title,
          alias,
          goodsId,
          limitNum,
          payPrice,
          stockNum,
          skuId,
          num,
          sku,
          ext: extraAttribute,
        } = item;

        return {
          maxNum: limitNum ? Math.min(limitNum, stockNum) : stockNum,
          imgUrl: item.thumbUrl,
          payPrice,
          price: money(payPrice).toYuan(),
          skuStr: this._getSkuStr(sku),
          alias,
          goodsId,
          skuId,
          limitNum,
          stockNum,
          title,
          num,
          extraAttribute,
          invalid,
          errorMsg: item.errorMsg || '',
        };
      });
    },

    _getSkuStr(sku) {
      return (JSON.parse(sku) || [])
        .map((item) => {
          return item.v;
        })
        .join('，');
    },

    updateTotalPrice() {
      const result = this.goodsList.reduce(
        (result, item) => {
          return {
            totalPrice: result.totalPrice + item.payPrice * item.num,
            allGiftNum: result.allGiftNum + item.num,
          };
        },
        {
          totalPrice: 0,
          allGiftNum: 0,
        }
      );

      const totalPriceStr = money(result.totalPrice).toYuan();

      Object.assign(this, {
        totalPrice: result.totalPrice,
        allGiftNum: result.allGiftNum,
        totalPriceStr,
      });
    },

    handleCheckboxClick() {
      const { anonymous } = this;
      Object.assign(this, {
        anonymous: !anonymous,
      });
      // 将勾选状态存储
      setStorageSync('gift-cart-anonymous', !anonymous);
    },

    handleTextareaInput(e) {
      this.message = e.target.value;
    },

    clearInvalidGoods() {
      this.deleteCartGoods(this.invalidGoodsList)
        .then((res) => {
          if (!res) {
            Toast('删除失效商品失败，请重试');
          } else {
            Object.assign(this, {
              invalidGoodsList: [],
            });
          }
        })
        .catch((err) => {
          Toast(err.msg || '删除失效商品失败，请重试');
        });
    },

    handlePickerTimeChange(timeMap) {
      const result = this._parsePickerToDate(timeMap);
      if (this.checkLotteryTimeValid(result.date)) {
        Object.assign(this, {
          lotteryTime: result.date,
          lotteryTimeStr: result.dateStr,
        });
        // 选择后的时间同步至内存
        setStorageSync('gift-play-time', result.date);
        this.showPicker = false;
      } else {
        const content =
          result.date > Date.now() + 10 * 60 * 1000
            ? '开奖时间应当在24小时内'
            : '开奖时间需在当前时间10分钟后';

        Dialog.confirm({
          title: '请选择正确开奖时间',
          message: content,
        });
      }
    },

    checkLotteryTimeValid(time) {
      return (
        time > Date.now() + 10 * 60 * 1000 &&
        time < new Date(Date.now() + 24 * 60 * 60 * 1000).getTime()
      );
    },

    changeGameType(newGameType) {
      if (newGameType === this.gameType) return;

      Object.assign(this, {
        gameType: newGameType,
      });

      // 刷新本地存储中玩法状态
      setStorageSync('gift-play-type', newGameType);
    },

    _parseDateStr(timeStamp) {
      const dateInstance = new Date(timeStamp);
      const weekMap = ['日', '一', '二', '三', '四', '五', '六'];
      const m = dateInstance.getMonth() + 1;
      const d = dateInstance.getDate();
      const week = weekMap[dateInstance.getDay()];
      const h = dateInstance.getHours();
      const minutes = dateInstance.getMinutes();

      return [`${m}月${d}日 周${week}`, `${this._leftPad(h)}:${this._leftPad(minutes)}`];
    },

    _parseDateToPicker(date) {
      const dateInstance = new Date(date);
      // 将日期转为picker
      const isToday = dateInstance.toDateString() === new Date(Date.now()).toDateString();
      const hours = dateInstance.getHours();
      const minutes = dateInstance.getMinutes();

      return [isToday ? 0 : 1, hours, minutes];
    },

    _parsePickerToDate(timestamp) {
      // 获取日期字符串
      const dateStr = this._parseDateStr(timestamp).join(' ');

      return {
        date: timestamp, // 原始时间戳
        dateStr, // 格式化后的日期字符串
      };
    },

    _leftPad(num) {
      return num < 10 ? '0' + num : '' + num;
    },

    _hoursRange(begin) {
      const arr = [];
      for (let i = begin; i <= 23; i++) {
        const time = this._leftPad(i) + '时';
        arr.push(time);
      }
      return arr;
    },

    _minutesRange(begin) {
      const arr = [];
      for (let i = begin; i <= 59; i++) {
        const time = this._leftPad(i) + '分';
        arr.push(time);
      }
      return arr;
    },
  },
};
</script>

<style lang="scss" scoped>
.gift-cart {
  background: url('https://b.yzcdn.cn/gift/gift-bg-1.png');
  background-repeat: no-repeat;
  background-size: 100%;

  &__nav {
    height: 84px;
    text-align: center;
    color: #111;
    font-weight: 500;
    font-size: 17px;
    box-sizing: border-box;

    &-arrow {
      position: absolute;
      left: 18px;
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
  }

  &__sub-title {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }

  &__body {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    position: relative;
    box-sizing: border-box;
  }

  &__body-wrap {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    position: relative;
    padding-top: 12px;
  }

  &__body-gift-icon {
    width: 26px;
    height: 24px;
    padding-left: 18px;
    padding-right: 14px;
    display: inline-block;
  }

  &__empty-text-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
  }

  &__empty-text-sub-title {
    font-size: 14px;
    color: #b3b3b3;
  }

  &__body--empty {
    height: 94px;
    display: flex;
    align-items: center;
    background-image: url(https://b.yzcdn.cn/gift/pick-bg.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &__body--empty-arrow {
    position: absolute;
    right: 18px;
  }

  &__body-gap-line {
    margin: 0 15px;
  }

  &__message {
    padding: 16px;
    box-sizing: border-box;
    margin: 12px;
    background: #fff;
    border-radius: 8px;
    margin-top: 0;

    &-line {
      height: 0;
      width: 100%;
      margin-top: 16px;
      transform: scaleY(0.5);
      border: 1px solid #e0e0e0;
    }
  }

  &__methods {
    padding: 12px;

    &-container {
      padding: 16px;
      background: #fff;
      border-radius: 8px;
    }

    &-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    &-list {
      display: flex;

      &-item {
        padding: 16px;
        background: #f7f8fa;
        width: 151px;
        border-radius: 4px;
        position: relative;
        border: 2px solid #f7f8fa;

        &:first-child {
          margin-right: 17px;
        }

        &-active {
          border: 2px solid #ecc085;

          &::after {
            content: '';
            position: absolute;
            right: -1px;
            bottom: -1px;
            background-color: #ecc085;
            color: white;
            border-top-left-radius: 4px;
            border-bottom-right-radius: 4px;
            background-image: url(https://b.yzcdn.cn/gift/gift-check-icon.png);
            height: 16px;
            width: 16px;
            background-size: 10px 10px;
            background-repeat: no-repeat;
            background-position: center;
          }
        }
      }
    }
  }

  &__methods-content {
    height: 55px;
    color: #333;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    font-size: 16px;
    justify-content: space-between;
    box-sizing: border-box;
    margin-top: 12px;
  }

  &__divider {
    height: 1px;
    transform: scaleY(0.5);
    background: #e0e0e0;
    margin: 16px 0;
  }

  &__message-textarea {
    width: 100%;
    height: 80px;
    padding-bottom: 9px;
    box-sizing: border-box;
    border-radius: 5px;
    margin-left: -16px;
    margin-top: -10px;
    /* #ifdef weapp */
    display: block;
    /* #endif */
  }

  &__bottom {
    width: 100%;
    padding: 0 30px;
    box-sizing: border-box;
    text-align: center;
    position: relative;

    &-anonymous {
      display: flex;

      &-text {
        margin-top: -2px;
        margin-left: 5px;
        font-size: 16px;
      }
    }

    &-btn-container {
      background: #fff;
      position: fixed;
      bottom: 0;
      padding: 0 16px;
      padding-bottom: env(safe-area-inset-bottom);
      width: 100%;
      box-sizing: border-box;
      margin-top: 4px;
      border-top: 0.5px solid #e0e0e0;

      .bottom-btn {
        color: #5f3f36;
        text-align: center;
        border-radius: 8px;
        width: 100%;
        height: 50px;
        line-height: 50px;
        background: #ecc085;
        font-size: 16px;
        margin: 8px 0;
        font-weight: 500;

        &-invalid {
          background: #f0f0f0;
          color: #999;
        }
      }
    }
  }

  &__goods-list {
    padding: 16px;

    &-header,
    &-footer {
      display: flex;
      justify-content: space-between;

      .add-more {
        color: #576b95;
        font-size: 16px;
      }
    }

    &-footer-title {
      font-size: 14px;
    }
  }

  &__invalid-goods--title {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }

  &__clear-btn {
    color: #d71609;
  }
}
</style>
