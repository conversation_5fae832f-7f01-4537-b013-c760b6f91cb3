<template>
  <van-popup position="bottom" @close-on-click-overlay="close" :show="show" class="subscription">
    <view class="subscription__title">订阅物流</view>
    <image
      class="subscription__qrcode"
      src="https://b.yzcdn.cn/public_files/f54804f26fe453fb4766001c9630eba7.png"
    />
    <view class="subscription__des">长按识别二维码关注有赞公众号，查看订单物流</view>
    <van-button @click="close" class="subscription__btn"> 我知道了 </van-button>
  </van-popup>
</template>

<script>
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

export default {
  name: 'subscription-dialogs',

  components: {
    'van-popup': Popup,
    'van-button': VanButton,
  },

  props: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },

  methods: {
    close() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss">
.subscription {
  border-radius: 20px 20px 0 0;
  text-align: center;
  // padding: 0 24px 28px;
  box-sizing: border-box;
  color: #323233;

  &__title {
    padding: 11px 0;
    font-weight: 500;
    font-size: 16px;
  }

  &__qrcode {
    width: 178px;
    height: 178px;
    border: 0.5px solid #dcdee0;
    margin-top: 22px;
    padding: 4px;
  }

  &__des {
    font-size: 14px;
    margin-top: 12px;
  }

  &__btn {
    font-size: 16px;
    border-radius: 22px;
    background: linear-gradient(to right, #ff6034, #ee0a24);
    color: #fff;
    height: 44px;
    line-height: 44px;
    width: 100%;
    margin-top: 16px;
    border: none;
    font-weight: 500;
  }
}
</style>
