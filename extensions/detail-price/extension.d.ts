type UseBeforePayData = {
  is: boolean; // 当前订单是否是先用后付的场景
  status: 'PENDING' | 'PAID'; // 履约状态，PENDING=待履约，PAID=已支付
};

type Payment = {
  /**
   * 支付优惠金额，比如微信支付92折这些
   */
  payUmpDiscountMoney: number;
  /**
   * 新的实际支付金额，realPay减去支付优惠
   */
  buyerRealPay: number;
  /**
   * 额外费用
   */
  orderExtraPrices: Array<{
    realPay: number;
    name: string;
    /** 额外费用描述 */
    desc: string;
  }>;
  /**
   * 抵扣金额(礼品卡、储值卡等)
   */
  deductionPay: number;

  /**
   * 实付金额
   */
  realPay: number;
  /**
   * 抵扣后的实付金额
   */
  deductedRealPay: number;
  /**
   * 商品单价合计
   */
  payPrice: number;
  /**
   * 积分优惠
   */
  pointsPrice: number;
  /**
   * 邮费
   */
  postage: number;
  /**
   * 阶段支付信息
   */
  phasePays: any[];
  /**
   * 代付
   */
  peerpayResult: IPeerpayInvite;
  /**
   * 积分名称
   */
  pointsName?: string;
  /**
   * 订单总价
   */
  pay?: number;
};

type Order = {
  /** 是否显示支付方式 */
  isAllowShowBuyWay: boolean;
  /** 支付方式文案 */
  buyWayStr: string;
};

export default interface ExtensionMetadata {
  data: {
    consume: {
      useBeforePayData: UseBeforePayData;
      payment: Payment;
      order: Order;
    };
  };
}
