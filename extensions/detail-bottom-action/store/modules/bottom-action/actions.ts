import { url } from '@youzan/tee-util';
import get from '@youzan/utils/object/get';
import Tee from '@youzan/tee';
import api from '../../../api';
import { confirmGoodsLogData } from '../../../log';
import { BOTTOM, AUTO_GO_EVALUATE_FLAG } from '../../../constants';
import { buildUrl, errorToast } from '@youzan/tee-biz-util';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import { beforeConfirmReceive } from '@youzan/wsc-tee-trade-common/lib/order-utils/wx-confirm-receive-handler';
import { buildGoodsList } from '../../../utils';

const { args } = url;

export default function (ctx) {
  return {
    updateKdtId() {
      const {
        order: { kdtId },
      } = this;
      ctx.process.invokePipe(
        'setKdtId',
        +kdtId,
        '[@wsc-tee-trade/detail-bottom-action]updateKdtId'
      );
    },

    getBannerId(index = 0) {
      const pageRandomNumber = makeRandomString(8);
      const loggerSpm = ctx.logger.getPageSpm();
      return `${loggerSpm}~new_buy_again_click~${index}~${pageRandomNumber}`;
    },

    updateAllBtns(val) {
      this.allBtns = val;
    },

    toggleActionSheet() {
      this.showActionSheet = !this.showActionSheet;
    },

    toggleFailGoodsDialog({ isShow }) {
      this.showFailGoodsDialog = isShow;
    },

    confirmFailGoodsDialog() {
      this.showFailGoodsDialog = false;
      if (this.itemList.length === this.buyAgainData.length) {
        // 什么都不做
      } else {
        const { kdtId } = this.order;
        const cartUrl = '/wsctrade/cart?kdt_id=' + kdtId;
        Tee.navigate({
          url: cartUrl,
          type: 'redirectTo',
        });
      }
    },

    // 处理按钮点击
    handleBtnClick(btn: { value: string }) {
      const { value } = btn;
      ctx.cloud.invoke('beforeHandleOrderAction', { type: value }).then(() => {
        if (this.isIVR && !this.isIvrOwner) {
          Tee.navigate({ url: `${this.ivrRedirectUrl}&type=${value}` });
          return;
        }
        switch (value) {
          case BOTTOM.groupon.value:
          case BOTTOM.peerpay.value:
          case BOTTOM.invoice.value:
          case BOTTOM.gift.value:
          case BOTTOM.courseDetail.value:
          case BOTTOM.evaluate.value:
          case BOTTOM.batchRefund.value:
          case BOTTOM.wholeAfterSales.value:
          case BOTTOM.viewEvaluate.value:
            this.handleBtnNavigate(value);
            break;
          case BOTTOM.coupon.value:
            this.handleCouponClick(btn);
            break;
          case BOTTOM.extend.value:
            this.extendOrder();
            break;
          case BOTTOM.confirm.value:
            this.handleConfirmReceive('');
            break;
          case BOTTOM.confirmHotel.value:
            this.handleConfirmReceive(BOTTOM.confirmHotel.value);
            break;
          case BOTTOM.cancel.value:
            this.cancelOrder();
            break;
          case BOTTOM.buyAgain.value:
            this.buyAgain();
            break;
          case BOTTOM.directBuyAgain.value:
            // 点击进入商详页面会自动进店，所以在直接购买的时候做一下切店的逻辑
            this.updateKdtId();
            this.directBuyAgain();
            break;
          case BOTTOM.more.value:
            this.toggleActionSheet();
            break;
          case BOTTOM.share.value:
            this.openShareSheet();
            break;
          case BOTTOM.deleteOrder.value:
            this.deleteOrderClick();
            break;
          /* #ifdef web */
          case BOTTOM.qttOrderComment.value:
            this.onQttOrderComment();
            break;
          case BOTTOM.qttOrderCommentted.value:
            this.onQttOrderCommentted();
            break;
          case BOTTOM.qttShare.value:
            this.onQttShare();
            break;
          case BOTTOM.qttBuyAgain.value:
            this.onQttBuyAgain();
            break;
          /* #endif */
          default:
            break;
        }
      });
    },

    handleBtnNavigate(value) {
      const doNavigate = (value) => {
        const btn = this.allBtns.find((item) => item.value === value);
        if (btn && btn.link) {
          Tee.navigate({ url: btn.link });
        }
      };

      if (value === BOTTOM.batchRefund.value) {
        ctx.cloud
          .invoke('beforeBatchRefundClick', {
            orderNo: this.order.orderNo || '',
            type: 'batchRefund',
          })
          .then(() => {
            doNavigate(value);
          });
      } else {
        doNavigate(value);
      }
    },

    // 点击领取优惠券
    handleCouponClick() {
      // 裂变优惠券埋点并转跳原生
      const { miniprogram = {}, order } = this;
      const { isAlipayApp = false, isQQApp = false, isXhsApp = false, isKsApp } = miniprogram;

      if (order.shareCouponStyleVersion === 'v1') {
        ctx.logger.log({
          et: 'click',
          ei: 'click_ordercompletion',
          en: '点击底部领取优惠券按钮',
          pt: 'od',
          params: {},
        });
      } else if (order.shareCouponStyleVersion === 'v2') {
        ctx.logger.log({
          et: 'click', // 事件类型
          ei: 'click', // 事件标识
          en: '点击', // 事件名称
          params: {
            page_click: 1,
            component: 'divide_coupon_floating_window',
          }, // 事件参数
        });
      }

      let link = `/packages/ump/fission/index?sharer=1&order_no=${order.bizNo}`;
      /* #ifdef web */
      link = buildUrl(
        `/ump/fission/list?isshare=1&kdt_id=${order.kdtId}&order_no=${order.bizNo}`,
        'wap',
        order.kdtId
      );
      /* #endif */
      if (isAlipayApp || isQQApp || isXhsApp || isKsApp) {
        navigate({
          web: {
            type: 'znb',
            znb: {
              aliappUrl: link,
              qqUrl: link,
              xhsUrl: link,
              ksUrl: link,
            },
          },
        });
        return;
      }
      Tee.navigate({ url: link });
    },

    // 延长收货
    extendOrder() {
      ctx.logger.log({
        et: 'click',
        ei: 'delay_goods',
        en: '延长收货',
      });

      const { orderNo, kdtId } = this.order;
      const container = Tee.getGlobal('van-dialog-detail-action');
      const options = {
        checkUrl: '/wsctrade/order/checkOrderDelayReceive.json',
        url: '/wsctrade/order/delayReceive.json',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        method: 'POST',
        container,
      };
      return api.extend(options).then(() => {
        /* #ifdef web */
        window.location.reload();
        /* #endif */

        /* #ifdef weapp */
        ctx.event.emit('refreshOrderInfo');
        /* #endif */
      });
    },

    confirmReceiveAction(options) {
      this.confirmReceive(options)
        .then(() => {
          // @ts-ignore
          if (options.type === 'confirmHotel') {
            // 酒店商品 请求成功后打点
            ctx.logger.log({
              et: 'click', // 事件类型
              ei: 'click_checkin', // 事件标识
              en: '点击入住', // 事件名称
              si: this.order.kdtId,
            });
          }
          ctx.event.emit('refreshOrderInfo');
        })
        .catch((e) => {
          console.warn('confirmReceive 异常：', e);
          ctx.event.emit('refreshOrderInfo');
        });
    },

    handleConfirmReceive(type = '') {
      const { orderExtra = {} } = ctx.data || {};
      const { orderNo } = this.order;
      beforeConfirmReceive({ ...orderExtra, orderNo }).then((isContinue = true) => {
        if (isContinue) {
          this.confirmReceiveAction({ type, isShowConfirm: true });
        }
      });
    },

    // 确认收货
    async confirmReceive({ type, isShowConfirm = true, payWay: propsPayWay }) {
      let message = '';
      let title = '确认收货';
      let confirmButtonText = '确认收货';
      let toastMessage = '收货成功';
      let { payWay } = this.guaranteeOrderInfo;
      if (propsPayWay) {
        payWay = propsPayWay;
      }
      /* #ifdef web */
      message =
        payWay === 1
          ? '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。'
          : '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
      if (type === 'confirmHotel') {
        message =
          payWay === 1
            ? '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。'
            : '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
        title = '确认入住';
        confirmButtonText = '确认入住';
        toastMessage = '入住成功';
      }

      if (this.order.marketingOrderSource === 'KUAI_SHOU') {
        message = '确认收货后，订单交易成功，钱款将在7天消费者无理由维权期后到达商家账户。';
      }
      /* #endif */

      /* #ifdef weapp */
      message =
        payWay === 1
          ? '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。'
          : '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';

      if (type === 'confirmHotel') {
        message =
          payWay === 1
            ? '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。'
            : '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
        title = '确认入住';
        confirmButtonText = '确认入住';
        toastMessage = '入住成功';
      }
      /* #endif */
      const { receivingContent = '' } = ctx.data;
      if (receivingContent !== '') {
        message = receivingContent;
      }

      try {
        const { config = { title, message, confirmButtonText, toastMessage } } =
          (await ctx.cloud.invoke('beforeConfigConfirmDialog', {
            config: { title, message, confirmButtonText, toastMessage },
          })) || {};
        title = config.title;
        message = config.message;
        confirmButtonText = config.confirmButtonText;
        toastMessage = config.toastMessage;
      } catch (e) {
        console.warn(e);
      }

      const { orderNo, kdtId } = this.order;

      return new Promise<void>((resolve, reject) => {
        const requestConfirm = () => {
          api
            .confirmReceive({ orderNo, kdtId })
            .then((res) => {
              // 若自动跳转发布评价页则不需要等级提升弹窗功能
              if (res?.isAutoGoEvaluate) {
                Toast.clear();
                Toast.success(`${title}成功`);
                if (this.isFxZpp) {
                  // 群团团只刷新当前页
                  window.location.reload();
                } else {
                  let link = `/wsctrade/order/evaluate/create?kdt_id=${kdtId}&order_no=${orderNo}&from=${AUTO_GO_EVALUATE_FLAG}`;
                  /* #ifdef weapp */
                  link = `/packages/evaluation/order/create/index?order_no=${orderNo}&from=${AUTO_GO_EVALUATE_FLAG}`;
                  /* #endif */
                  Tee.navigate({ url: link });
                }
                return;
              }
              ctx.event.once('onFetchLevelupTip', (res) => {
                Toast.clear();
                !res.show && Toast.success(`${title}成功`);
                resolve();
              });

              // eslint-disable-next-line no-template-curly-in-string
              ctx.data.levelupTiptitle = '收货成功，会员升级至${level}';
              ctx.event.emit('checkLevelupTip');
            })
            .catch((err) => {
              Toast.clear();
              errorToast(err, { message: '确认收货失败' });
              reject();
            });
        };

        // 不需要展示弹窗的话，就直接调用接口
        if (!isShowConfirm) {
          requestConfirm();
          return;
        }

        Dialog.confirm({
          title,
          ref: Tee.getGlobal('van-dialog-detail-action'),
          message,
          toastMessage,
          confirmButtonText,
        })
          .then(() => {
            Toast.loading();

            requestConfirm();

            ctx.logger.log(confirmGoodsLogData(orderNo));

            Dialog.close();
          })
          .catch(() => {
            Dialog.close();
          });
      });
    },

    // 取消订单
    cancelOrder() {
      ctx.logger.log({
        et: 'click',
        ei: 'cancel_order',
        en: '取消订单',
      });
      // 1.只有货到付款的订单才会显示【取消】按钮，
      // 2.买家无法取消货到付款订单，只能自己联系商家来取消
      Dialog.alert({
        message: '商家可能已经接单配货，你需联系客服申请取消订单哟',
        confirmButtonText: '我知道了',
        ref: Tee.getGlobal('van-dialog-detail-action'),
      });
    },

    // 再来一单
    buyAgain() {
      const { orderNo, kdtId } = this.order;
      const goods = this.itemList || [];

      const goodsIds = [];
      goods.forEach((item) => {
        goodsIds.push(item.goodsId);
      });
      const params = {
        last_order_no: orderNo,
        goods_id: goodsIds,
        order_no: orderNo,
        is_new: false,
      };
      ctx.logger.log({
        et: 'click',
        ei: 'new_buy_again_click',
        en: '新再来一单点击',
        pt: 'od',
        params,
      });

      // 订单号、店铺ID、商品不存在就直接返回
      if (!orderNo || !kdtId || !(Array.isArray(goods) && goods.length)) {
        return;
      }

      const options = {
        url: '/wsctrade/order/buyAgain.json',
        data: {
          orderNo,
          kdtId,
        },
      };

      const goodsList = mapKeysToSnakeCase(this.itemList || []);
      return api
        .buyAgain(options)
        .then((resp) => {
          /* #ifdef web */
          const { orderMark, originSource: { platform } = {} as Record<string, any> } = get(
            window,
            '_global.sourceInfo',
            {}
          );
          const { isTTApp } = get(window, '_global.miniprogram', {});
          if (isTTApp && orderMark !== 'fulfill_tool' && platform === 'dy_mini_program') {
            // @ts-ignore
            ZNB.navigate({
              ttUrl: '/packages/retail-shelf/shelf/index',
              // znb 好像没有判断抖音环境，先这样处理一下
              url: '/packages/retail-shelf/shelf/index',
            });
            return;
          }
          /* #endif */

          /* #ifdef web */
          if (resp.code === 0) {
            const data = Array.isArray(resp.data) ? resp.data : [];
            let cartUrl = '/wsctrade/cart';
            this.buyAgainData = data;
            cartUrl = cartUrl + '?kdt_id=' + kdtId;

            // 返回的数据是添加到购物车失败的商品数组，全部成功时返回空数组
            if (data.length === 0) {
              Tee.navigate({
                url: cartUrl,
                type: 'redirectTo',
              });
            } else {
              // 商品sku与数量信息需要从订单商品列表中获取，然后构造一个新的数组
              const parsedGoodsList = buildGoodsList(goodsList, data);
              this.showFailGoodsDialog = true;
              this.failGoodsList = parsedGoodsList.failList;
            }
          } else {
            Toast(resp.msg);
          }
          /* #endif */

          /* #ifdef weapp */
          if (resp.data) {
            // 跳转到购物车页面，小程序端不需要带参数
            const url = '/packages/goods/cart/index';
            Tee.navigate({ url });
          } else {
            Toast('再来一单请求失败。');
          }
          /* #endif */
        })
        .catch((err) => {
          errorToast(err, { message: '再来一单请求失败。' });
        });
    },

    // 再来一单，直接进下单页
    directBuyAgain() {
      const { orderNo, kdtId, buyerInfo = {} } = this.order;
      const goodsIds = [];
      this.goods.forEach((item) => {
        goodsIds.push(item.goodsId);
      });
      const params = {
        banner_id: this.getBannerId(),
        buyer_id: buyerInfo.buyerId,
        goods_id: goodsIds,
        order_no: orderNo,
        is_new: true,
      };
      ctx.logger.log({
        et: 'click',
        ei: 'new_buy_again_click',
        en: '新再来一单点击',
        pt: 'od',
        params,
      });
      const options = {
        url: '/wsctrade/order/directBuyAgain.json',
        data: {
          orderNo,
          kdtId,
        },
      };
      return api
        .directBuyAgain(options)
        .then((resp) => {
          /* #ifdef web */
          const { orderMark, originSource: { platform } = {} as Record<string, any> } = get(
            window,
            '_global.sourceInfo',
            {}
          );
          const { isTTApp } = get(window, '_global.miniprogram', {});
          if (isTTApp && orderMark !== 'fulfill_tool' && platform === 'dy_mini_program') {
            // @ts-ignore
            ZNB.navigate({
              ttUrl: '/packages/retail-shelf/shelf/index',
              // znb 好像没有判断抖音环境，先这样处理一下
              url: '/packages/retail-shelf/shelf/index',
            });
            return;
          }
          /* #endif */

          const result = resp.data;
          const { destination } = result;
          // 跳转商详页
          if (+destination === 2) {
            const { alias } = result;
            /* #ifdef web */
            Tee.navigate({
              url: `/wscgoods/detail/${alias}`,
              type: 'redirectTo',
            });
            /* #endif */
            /* #ifdef weapp */
            Tee.navigate({ url: `/pages/goods/detail/index?alias=${alias}` });
            /* #endif */
            return;
          }
          // 跳转下单页
          const { buy_url: buyUrl, book_key: bookKey } = result;

          let url = '';
          /* #ifdef web */
          url = args.add(buyUrl, {
            kdt_id: kdtId,
            orderNo,
            banner_id: this.getBannerId(),
          });
          /* #endif */

          /* #ifdef weapp */
          url = args.add('/packages/order/index', {
            bookKey,
            orderNo,
            banner_id: this.getBannerId(),
          });
          /* #endif */
          Tee.navigate({ url });
        })
        .catch((err) => {
          errorToast(err, { message: '再来一单请求失败。' });
        });
    },

    // 打开分享
    openShareSheet() {
      ctx.event.emit('showTradeShareDialog');
    },

    deleteOrderClick() {
      const {
        order: { orderNo, kdtId, headKdtId, channelType },
        goods,
      } = this;
      // 埋点日志
      ctx.logger.log({
        et: 'click',
        ei: 'order_delete_click',
        en: '删除订单按钮点击', // 事件名称
        params: {
          order_no: orderNo,
        },
      });
      // 订单号、店铺ID、商品不存在就直接返回
      if (!orderNo || !kdtId || !(Array.isArray(goods) && goods.length)) {
        return;
      }
      Dialog.confirm({
        title: '确定删除订单？',
        message: '删除订单后无法恢复，无法处理您的售后问题，请慎重考虑。',
        cancelButtonText: '取消',
        confirmButtonText: '删除',
        confirmButtonColor: '#f23d47',
      }).then((_action) => {
        api
          .deleteOrder({ orderNo })
          .then((_res) => {
            Toast('订单已删除');
            try {
              let url = '';
              // 跳转到订单列表，H5和小程序分别怎么处理 应当是判断上一个页面是否是订单列表 是的话执行回退 否则跳转
              /* #ifdef weapp */
              const app = getApp();
              url = '/packages/trade/order/list/index?refresh=true';
              const pages = getCurrentPages();
              const prevpage = pages[pages.length - 2] || ({} as Record<string, any>);
              if (prevpage.route === 'packages/trade/order/list/index') {
                setTimeout(() => {
                  wx.navigateBack({
                    success: () => {
                      app.trigger('ranta-detail-page-back');
                    },
                  });
                }, 1000);
                return;
              }
              /* #endif */
              /* #ifdef web */
              // crm线下门店订单的kdtid不能用于返回查询订单列表，返回总店的订单列表
              const targetKdtId = channelType === 100 && headKdtId ? headKdtId : kdtId;
              url = `/wsctrade/order/list?kdt_id=${targetKdtId}`;
              /* #endif */
              setTimeout(() => {
                Tee.navigate({ url, type: 'redirectTo' });
              }, 1000);
            } catch (error) {}
          })
          .catch((err) => {
            errorToast(err, { message: '订单删除失败' });
          });
      });
    },
    /* #ifdef web */
    onQttOrderComment() {
      const { orderNo, kdtId, state, isVirtual } = this.order;
      if (state <= 80 && !isVirtual) {
        // 未确认收货的订单不能晒单
        return Toast('请先将订单确认收货后再晒单');
      }
      // @ts-ignore
      ZNB.navigate({
        url: '',
        weappUrl: args.add(`/packages/order-comment/create/index`, {
          orderNo,
          kdtId,
        }),
      });
    },
    onQttOrderCommentted() {
      const { orderNo, kdtId } = this.order;
      const { forQttOrderExtra } = window._global;
      const {
        orderCommentShow: { commentShowId },
      } = forQttOrderExtra;
      this.showQttCommentTip = false;
      // @ts-ignore
      ZNB.navigate({
        url: '',
        weappUrl: args.add(`/packages/order-comment/detail/index`, {
          orderNo,
          kdtId,
          id: commentShowId,
        }),
      });
    },
    onQttShare() {
      this.showQttShare = true;
      const { ATTR_FX_ZPP_NOTE_ID } = window._global.forOrderExtra;
      // @ts-ignore
      ZNB.postMessage({
        data: { type: 'order-list-share', noteId: ATTR_FX_ZPP_NOTE_ID },
      });
      return Promise.resolve();
    },
    onQttBuyAgain() {
      const { ATTR_FX_ZPP_NOTE_ID } = window._global.forOrderExtra;
      // @ts-ignore
      ZNB.navigate({
        url: '',
        weappUrl: args.add(`/pages/detail-page/view/index`, {
          noteId: ATTR_FX_ZPP_NOTE_ID,
        }),
      });
      return Promise.resolve();
    },
    closeQttShare() {
      this.showQttShare = false;
    },
    /* #endif */

    // 修改地址兼容定制化需求
    // 背景：标品业务逻辑优化修改地址按钮去除，因此定制无法继续走action触发，在定制执行方法时判断如果是原来的修改地址逻辑，则用此方法兼容
    handleCloudModifyAddress() {
      const { allowShowModifyTimeBuckets, allowShowModifyAddress } =
        this.order.deliveryAllowShow || {};
      if (!allowShowModifyAddress) {
        return Toast('当前订单不支持修改收货地址');
      }
      if (allowShowModifyTimeBuckets) {
        this.ctx.data.showReceiverModifyPopup = true;
      } else {
        this.ctx.data.showAddressModifyPopup = true;
      }
    },
  };
}
