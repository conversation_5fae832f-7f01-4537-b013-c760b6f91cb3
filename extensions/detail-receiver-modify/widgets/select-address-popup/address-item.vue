<template>
  <view :class="className" @click="handleClick">
    <van-icon name="success" v-if="selected" class="selected-icon" />
    <view class="content">
      <!-- 标签区域 -->
      <view class="title">
        <text class="tag default-tag" v-if="address.isDefault">默认</text>
        <text class="tag custom-tag" v-if="address.label">{{ address.label }}</text>
        <text class="address-title">{{ address.location || address.addressDetail }}</text>
        <van-icon v-if="!disabled" name="edit" @click.stop="handleEdit" />
      </view>

      <!-- 地址信息 -->
      <view class="address-info" v-if="!address.location">{{ address.addressDetail }}</view>

      <!-- 联系人信息 -->
      <view class="contact-info"> {{ address.userName }} {{ address.tel }} </view>

      <!-- 操作区域 -->
      <view class="select-btn"> 选择 </view>
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

export default {
  components: {
    'van-icon': Icon,
  },
  props: {
    address: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    selected: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    className() {
      const name = ['detail-address-item'];
      const { disabled, selected } = this;
      if (disabled) {
        name.push('disabled');
      }
      if (selected) {
        name.push('selected');
      }
      return name;
    },
  },
  methods: {
    handleClick() {
      const { disabled, address } = this;
      if (disabled) return;
      this.$emit('selected', address);
    },
    handleEdit() {
      // 禁用状态下不允许编辑
      if (this.disabled) return;

      // 触发父组件的编辑事件，传递当前地址信息
      this.$emit('edit', this.address);
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-address-item {
  position: relative;
  width: 100%;
  padding: 16px 20px;
  margin-bottom: 12px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid transparent;

  &.disabled {
    .content {
      opacity: 0.3;
    }
  }
  &.selected {
    overflow: hidden;
    border-color: var(--main-bg, #7d6a57);
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 28px 28px 0;
      border-color: transparent var(--main-bg, #7d6a57) transparent transparent;
    }
  }
  .selected-icon {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 14px;
    color: #fff;
  }

  .title {
    margin-bottom: 8px;
    padding-right: 48px;

    .tag {
      padding: 1px 4px;
      font-size: 12px;
      border-radius: 2px;
      position: relative;
      top: -1px;
      display: inline;

      &.custom-tag {
        background-color: rgba(125, 106, 87, 0.1);
        color: #7d6a57;
      }

      &.default-tag {
        background-color: var(--main-bg, #7d6a57);
        color: #fff;
      }
    }
  }

  .address-title {
    display: inline;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    margin: 0 2px;
  }

  .address-info {
    padding-right: 48px;
    margin-bottom: 4px;
    font-size: 12px;
    color: #666;
  }

  .contact-info {
    font-size: 12px;
    color: #666;
    line-height: 18px;
  }

  .select-btn {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    font-weight: 500;
    font-size: 14px;
    color: var(--main-bg, #7d6a57);
  }
}
</style>
