/* 时间切片 */
import { TAKE_TIME_TYPE } from '../contants';
import {
  getAvailableTime as timePickerGetAvailableTime,
  getRange as timePickerGetRange,
  getFirstFewDaysAsync as timePickerGetFirstFewDaysAsync,
} from '@youzan/wsc-tee-trade-common/lib/order-utils/time/index';
import { format } from '@youzan/wsc-tee-trade-common/lib/order-utils/time/date';

function getWarehouseId(defaultValue, extra) {
  try {
    return JSON.parse(extra.ATTR_DISPATCHER_WAREHOUSE_IDS).response.assignLocalWarehouseIds[0];
  } catch (error) {
    return defaultValue;
  }
}

export function getDefaultTime(ctx, config, { address, selfFetch, extra }) {
  const { orderCreation: { extensions: { APPOINTMENT_INFO: appointmentInfo } = {} } = {} } =
    ctx.data;

  let appointmentInfoObj = {};
  if (appointmentInfo) {
    delete ctx.data.orderCreation.extensions.APPOINTMENT_INFO;
    try {
      const appointmentInfoTemp = JSON.parse(appointmentInfo);

      const { startTime, endTime } = appointmentInfoTemp;

      const { startTime: startTime2, endTime: endTime2 } = config.deliveryTimeBucket;
      if (startTime2 && endTime2) {
        if (
          new Date(startTime2).getTime() < new Date(startTime).getTime() &&
          new Date(endTime2).getTime() > new Date(endTime).getTime()
        ) {
          appointmentInfoObj.startTime = startTime;
          appointmentInfoObj.endTime = endTime;
        }
      }
    } catch (error) {
      console.error('appointmentInfo parse error', error);
    }
  }

  const newConfig = {
    ...config,
    deliveryTimeBucket: {
      ...config.deliveryTimeBucket,
      ...appointmentInfoObj,
    },
  };
  const range = timePickerGetRange(newConfig);
  console.log('🚀range', range);
  const { shop } = selfFetch || {};
  return timePickerGetFirstFewDaysAsync(config, range, 7, {
    bizType: address.activeTab,
    offlineId: address.activeTab === 1 ? shop?.id : undefined,
    dispatchWarehouseId:
      address.activeTab === 1 ? shop?.kdtId : getWarehouseId(ctx.data.kdtId, extra),
  }).then((days) => {
    console.log('🚀days', days[0]);
    return days[0]?.children[0] && !days[0]?.children[0]?.disabled
      ? days[0]?.children[0]?.value
      : {};
  });
}

// 获取默认自提点后获取默认自提时间
export function setRetailGetSelfFetchTime(ctx, state, deliveryConfig = {}) {
  const { autoBookSelfTakeTime } = deliveryConfig;

  const { selfFetch } = state;

  const { orderCreation: { extensions: { APPOINTMENT_INFO: appointmentInfo } = {} } = {} } =
    ctx.data;

  const { address, extra } = state;

  if (!appointmentInfo) {
    // 没有开启配置时直接返回 || 已有时间配置时直接返回
    if (autoBookSelfTakeTime !== TAKE_TIME_TYPE.AUTO || selfFetch.time) return state;

    const {
      timeSpan,
      deliveryTimeBucket: { startTime = '', endTime = '' },
    } = selfFetch;
    const startDate = format.timeSpanToDate(startTime);
    if (
      ctx.data.isRetailOrderScene &&
      (timeSpan === 'hour' || timeSpan === 'halfhour') &&
      startTime &&
      format.timeSpanToDate(+new Date()).getDate() === startDate.getDate()
    ) {
      const endDate = format.timeSpanToDate(endTime);
      state.selfFetch = {
        ...selfFetch,
        time: format.dateTime(startDate),
        timeWithWeekday: `立即自提（预计 ${format.time(startDate)}）`,
        selfFetchStartTime: format.date(startDate) + ' ' + format.time(startDate) + ':00',
        selfFetchEndTime: endTime ? format.date(endDate) + ' ' + format.time(endDate) + ':00' : '',
      };
      return state;
    }
  }

  // 计算默认时间
  if (timePickerGetAvailableTime(selfFetch)) {
    return getDefaultTime(ctx, selfFetch, { address, selfFetch, extra }).then((time) => {
      state.selfFetch = {
        ...selfFetch,
        time: time.text,
        timeWithWeekday: time.textWithWeekday,
        selfFetchStartTime: time.startTime || '',
        selfFetchEndTime: time.endTime || '',
      };
      return state;
    });
  }
  return state;
}

// 外送设置默认时间
export function setRetailGetDeliveryTime(ctx, state, deliveryConfig = {}) {
  const { orderCreation: { extensions: { APPOINTMENT_INFO: appointmentInfo } = {} } = {} } =
    ctx.data;

  const { autoBookDeliveryTime } = deliveryConfig;
  const { delivery } = state;
  const instantTimePoint = delivery.deliveryTimeBucket?.instantTimePoint
  // const {
  //   deliveryTimeBucket: { instantTimePoint },
  // } = delivery;
  if (!appointmentInfo) {
    // 没有开启配置时直接返回 || 已有时间配置时直接返回
    if (autoBookDeliveryTime !== TAKE_TIME_TYPE.AUTO || delivery.text) return state;
  }
  const { address, selfFetch, extra } = state;
  return getDefaultTime(ctx, delivery, {
    address,
    selfFetch,
    extra,
  }).then((time) => {
    Object.assign(state.delivery, time);
    // 非营业时间预约下单，没有加进去的参数，需要还原
    if (instantTimePoint) {
      Object.assign(state.delivery, {
        instantTimePoint,
      });
    }
    return state;
  });
}
