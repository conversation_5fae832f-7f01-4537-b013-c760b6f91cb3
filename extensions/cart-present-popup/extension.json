{"extensionId": "@wsc-tee-trade/cart-present-popup", "name": "@wsc-tee-trade/cart-present-popup", "bundle": "<builtin>", "widget": {"default": "PresentPopup", "consume": ["PresentGoods"]}, "data": {"consume": {"presentPopupInfo": ["r"], "themeColors": ["r"]}, "provide": {"presentGoodsId": ["r"]}}, "event": {"emit": ["present-sku:after<PERSON><PERSON><PERSON>", "present-sku:changed", "presentPopupClose", "presentPopupConfirm"], "listen": ["sku:custom", "sku:selected", "present-sku:fetch"]}, "process": {"invoke": ["setSkuInfo"]}, "version": "2.0.2", "platform": ["weapp", "web"]}