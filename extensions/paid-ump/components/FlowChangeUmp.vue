<template>
  <view class="exchange-activity" @click="shopAdClick">
    <image :src="shopAd.imageUrl" class="exchange-activity__image" mode="widthFix" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';

const app = getApp();

export default {
  props: {
    shopAd: {
      type: Object,
      default: () => {},
    },
  },

  mounted() {
    // 增加流量互换埋点
    if (this.shopAd) {
      const { psCode, cooperateKdtId } = this.shopAd;
      if (psCode && cooperateKdtId) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'ad_exchange_view', // 事件标识
            en: '图片曝光', // 事件名称
            params: {
              ps_code: psCode,
              activity_kdt_id: cooperateKdtId,
              component: 'ad_exchange_banner',
            }, // 事件参数
          });
      }
    }
  },

  methods: {
    shopAdClick() {
      if (this.shopAd && this.shopAd.weappNavigateTo) {
        const { appId, weappNavigateTo } = this.shopAd;
        app.logger.log({
          et: 'click',
          ei: 'click_pay_result_ad',
          en: '支付成功页-广告点击',
          si: app.getKdtId(),
        });
        Tee.$native.navigateToMiniProgram({
          appId,
          path: weappNavigateTo,
          envVersion: 'trial',
        });
      }
    },
  },
};
</script>

<style lang="scss">
.exchange-activity {
  margin-top: 12px;
  padding: 0 12px;
  height: 88px;
  &__image {
    width: 100%;
    border-radius: 4px;
    height: 88px;
    image {
      border-radius: 4px !important;
      height: 88px !important;
    }
  }
}
</style>
