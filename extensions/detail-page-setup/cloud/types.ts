/**
 * 底部按钮类型
 * 'confirmHotel' = 确认入住
 * 'refund' = 申请退款
 * 'message' = 查看留言
 * 'postpone' = 我要顺延
 * 'batchRefund' = 批量退款
 */
type BottomBtnType =
  | 'confirmHotel' /** 确认入住 */
  | 'refund' /** 申请退款 */
  | 'message' /** 查看留言 */
  | 'postpone' /** 我要顺延 */
  | 'batchRefund'; /** 批量退款 */
type SetBottomBtnsDisplay = {
  /** 确认入住，入参为false时隐藏（暂不支持隐藏后动态显示） */
  confirmHotel: boolean;
  /** 需要隐藏的按钮 */
  type: BottomBtnType[];
};

interface IPaymentInfo {
  /** 邮费（单位：分） */
  postage: number;
  /** 实付金额（单位：分） */
  realPay: number;
}

export { SetBottomBtnsDisplay, IPaymentInfo };
