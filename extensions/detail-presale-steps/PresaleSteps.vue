<template>
  <view v-if="showPresaleSteps">
    <view class="presale-steps">
      <view
        v-for="(item, index) in presaleSteps"
        :key="index"
        :class="['presale-steps__item', item.phaseArrived ? 'presale-steps__item--arrived' : '']"
      >
        <view class="presale-steps__item-content">
          <view
            class="presale-steps__item__dot"
            :style="{ backgroundColor: themeColors['general'], borderColor: borderColor }"
          ></view>
          <text class="presale-steps__item__title">{{ item.phaseDesc }}</text>
          <text class="presale-steps__item__status">（{{ item.phasePayStatusDesc }}）</text>
          <van-icon
            v-if="item.phase === 1"
            class="icon"
            color="#1989fa"
            name="question-o"
            @click="onShowAgreement"
          />
        </view>
        <view class="presale-steps__item__price t-c-gray-darker">
          <!-- 支付优惠说明 -->
          <van-icon
            v-if="item.payUmpDiscountMoney > 0"
            class="presale-steps__item-info-icon"
            name="info-o"
            @click="onShowPayUmpDetail(item.phase)"
          />
          <text :style="{ minWidth: priceSpanWidth + 'px' }">
            {{ item.buyerRealPayStr }}
          </text>
        </view>
      </view>
    </view>

    <!-- 支付优惠详情弹窗 -->
    <van-popup
      :show="showPayUmpDetail"
      @close="onClosePayUmpDetail"
      custom-class="pay-ump-detail-dialog"
    >
      <view class="pay-ump-detail-dialog__title">价格说明</view>
      <view class="pay-ump-detail-dialog__main">
        <view class="pay-ump-detail-dialog__item">
          <text class="pay-ump-detail-dialog__left">
            {{ currentPhase.phase === 1 ? '定金' : '尾款' }}应付
          </text>
          <text class="pay-ump-detail-dialog__right">￥{{ currentPhase.currentPrice }}</text>
        </view>
        <view class="pay-ump-detail-dialog__item">
          <text class="pay-ump-detail-dialog__left">支付优惠</text>
          <text class="pay-ump-detail-dialog__right">
            -￥{{ currentPhase.payUmpDiscountMoney }}
          </text>
        </view>
      </view>
      <van-button
        round
        block
        type="danger"
        size="large"
        :color="themeColors['general']"
        custom-class="pay-ump-detail-dialog__btn"
        style="width: 100%"
        @click="onClosePayUmpDetail"
      >
        知道了
      </van-button>
    </van-popup>

    <!-- 定金不退协议 -->
    <van-action-sheet title="定金不退协议" :show="showAgreement" @close="onCloseAgreement">
      <view class="agreement">
        <view v-for="(block, index) in agreement" :key="index" class="agreement__paragraph">
          <view class="agreement__title">{{ block.title }}</view>
          <view v-for="(line, blockWordIndex) in block.words" :key="blockWordIndex">
            {{ line }}
          </view>
        </view>
      </view>
      <view class="presale-steps__bottom-wrapper">
        <van-button
          type="danger"
          size="large"
          :color="themeColors['general']"
          custom-class="presale-steps__bottom-btn"
          @click="onCloseAgreement"
        >
          我知道了
        </van-button>
      </view>
    </van-action-sheet>
  </view>
</template>

<script>
import ActionSheet from '@youzan/vant-tee/dist/action-sheet/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { money } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';
import { requestV2 } from '@youzan/tee-biz-request';

export default {
  components: {
    'van-action-sheet': ActionSheet,
    'van-button': Button,
    'van-popup': Popup,
    'van-icon': Icon,
  },

  data() {
    return {
      presaleSteps: [],
      showPresaleSteps: false,
      themeColors: {},
      agreement: [],
      showAgreement: false,
      showPayUmpDetail: false,
      currentPhase: {},
      borderColor: '',
    };
  },

  computed: {
    priceSpanWidth() {
      if (!this.presaleSteps || this.presaleSteps.length <= 0) {
        return 0;
      }
      const max = this.presaleSteps.reduce(
        (pre, cur) => (pre.buyerRealPay < cur.buyerRealPay ? cur : pre),
        this.presaleSteps[0]
      );
      return 12 + this.formatPrice(max.buyerRealPay).toString().length * 7;
    },
  },

  created() {
    mapData(this, ['showPresaleSteps', 'themeColors']);
    mapData(
      this,
      {
        presaleSteps: (presaleSteps) => {
          this.presaleSteps =
            presaleSteps &&
            presaleSteps.map((item) => {
              return {
                ...item,
                buyerRealPayStr: `¥${(item.buyerRealPay / 100).toFixed(2)}`,
              };
            });
        },
      },
      { isSetData: false }
    );
    const bdColorValue = this.ctx.lambdas.hexToRgb(this.themeColors.general || '#f44');
    this.borderColor = `rgba(${bdColorValue.join(',')}, 0.1)`;
    this.getDepositPreSaleAgreement();
  },

  methods: {
    formatPrice(v) {
      return (parseFloat(v) / 100).toFixed(2);
    },

    onShowAgreement() {
      this.showAgreement = true;
    },

    onCloseAgreement() {
      this.showAgreement = false;
    },

    onClosePayUmpDetail() {
      this.showPayUmpDetail = false;
    },

    onShowPayUmpDetail(phase = 1) {
      const currentPhase = this.presaleSteps[phase - 1];
      if (currentPhase) {
        this.currentPhase = {
          ...currentPhase,
          phase,
          currentPrice: money.toYuan(currentPhase.realPrice),
          payUmpDiscountMoney: money.toYuan(currentPhase.payUmpDiscountMoney),
        };
        this.showPayUmpDetail = true;
      }
    },

    getDepositPreSaleAgreement() {
      requestV2({
        path: '/wsctrade/order/static-config.json',
        method: 'POST',
        data: {
          keys: ['deposit_pre_sale_agreement'],
        },
      })
        .then((res) => {
          const agreement =
            (res?.deposit_pre_sale_agreement && JSON.parse(res.deposit_pre_sale_agreement)) || [];
          this.agreement = agreement;
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss">
.presale-steps {
  padding: 8px 16px;
  margin-top: 10px;
  background: #fff;
}

.presale-steps__item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 26px;
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.presale-steps__item:not(:first-of-type)::before {
  content: '';
  position: absolute;
  left: 5px;
  bottom: 24px;
  transform: translateX(-50%);
  width: 1px;
  height: 10px;
  background: #e5e5e5;
}

.presale-steps__item-content {
  display: flex;
  align-items: center;
}

.presale-steps__item__dot {
  margin-right: 12px;
  width: 6px;
  height: 6px;
  border: 2px solid #fff;
  border-radius: 100%;
  background: #e5e5e5;
}
.presale-steps__item__title {
  font-size: 14px;
  color: #646566;
}

.presale-steps__item__status {
  font-size: 14px;
  color: #323233;
}

.presale-steps__item-info-icon {
  vertical-align: middle;
  padding: 4px;
  position: relative;
  top: 1px;
}

.presale-steps__item-info-icon:active {
  color: #000;
}

.presale-steps__item__price text {
  display: inline-block;
  text-align: right;
  color: #232333;
}

.presale-steps__item--arrived .presale-steps__item__dot {
  background: #ff7272;
  border: 2px solid #ffd3d3;
}

.presale-steps__item--arrived::before {
  background: #ffd3d3 !important;
}

.pay-ump-detail-dialog {
  padding: 0 16px 7px 16px;
  display: flex;
  width: 84%;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: 12px;
}

.pay-ump-detail-dialog__title {
  padding: 11px;
  color: #323233;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  text-align: center;
}

.pay-ump-detail-dialog__main {
  width: 100%;
  padding: 14px 0;
}

.pay-ump-detail-dialog__item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  font-weight: 400;
}

.pay-ump-detail-dialog__btn {
  width: 100% !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 18px !important;
}

.agreement {
  max-height: 60vh;
  padding: 10px 30px 20px;
  overflow-y: auto;
  font-size: 12px;
  color: #666;
  white-space: pre-line;
}

.agreement__paragraph {
  margin-top: 10px;
}

.agreement__title {
  font-weight: bold;
}

.presale-steps__bottom-wrapper {
  margin: 0 16px 16px;
  border-radius: 999px;
}

.presale-steps__bottom-btn::after {
  border: none !important;
}

.presale-steps__bottom-btn::before {
  border: none !important;
}

.presale-steps__bottom-btn {
  border: none !important;
}

.t-c-gray-darker {
  color: #666;
}
</style>
