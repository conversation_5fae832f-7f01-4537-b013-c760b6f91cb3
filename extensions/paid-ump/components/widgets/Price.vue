<template>
  <view v-if="price === ''" class="font-calculate"></view>
  <view v-else class="value" :style="customStyle">
    <view class="prefix">{{ prefix }}￥</view>{{ integer
    }}<view class="decimal" v-if="decimal">.{{ decimal }}</view>
  </view>
</template>

<script>
export default {
  name: 'goods-price',
  props: {
    // 分为单位
    price: {
      type: String,
      default: '',
    },
    prefix: {
      type: String,
      default: '',
    },
    customStyle: {
      type: String,
      default: '',
    },
  },
  computed: {
    integer() {
      return this.price.split('.')?.[0] ?? '';
    },
    decimal() {
      return this.price.split('.')?.[1] ?? '';
    },
  },
};
</script>

<style lang="scss" scoped>
.value {
  font-size: 16px;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100px;
}

.decimal {
  font-size: 12px;
  display: inline-block;
}

.prefix {
  font-size: 12px;
  font-weight: normal;
  display: inline-block;
}
</style>
