import MainWidget from './Main.vue';
import GoodsItem from './widgets/GoodsItem.vue';
import BottomLinks from './widgets/BottomLinks.vue';
import getUniquePageKey from '@youzan/decorate-tee/src/common/utils/unique';
import TeeEvent from '@youzan/tee-event';

export default class Extension {
  ctx: any;

  static widgets = {
    Main: MainWidget,
    GoodsItem,
    BottomLinks,
  };

  constructor(options) {
    this.ctx = options.ctx;
  }

  onPageScroll(ev) {
    const eventKey = 'onPageScroll' + getUniquePageKey();
    TeeEvent.trigger(eventKey, ev);
  }
}
