<template>
  <award-container :custom-style="themeBackground5Style">
    <view class="coupon">
      <view class="goods">
        <view class="coupon-container">
          <view v-for="(item, index) in curCoupons" :key="index" class="coupon-item-pack">
            <view class="coupon-item-wrapper">
              <view class="coupon-item" :style="themeBorder30Style">
                <view class="price">
                  <font-calculate
                    :cur-font-size="30"
                    :min-font-size="12"
                    :item="item"
                    :color="themeColor"
                  />
                </view>
                <view class="limit" :style="themeBorder30Style">
                  <view
                    class="hole-s hole-left"
                    :style="[themeBackground5Style, themeBorder30Style]"
                  ></view>
                  <view
                    class="hole-s hole-right"
                    :style="[themeBorder30Style, themeBackground5Style]"
                  ></view>
                  {{ item.thresholdCopywriting }}
                </view>
              </view>
            </view>
            <view class="coupon-shadow" :style="[themeBackground5Style, themeBorder20Style]"></view>
          </view>
        </view>
      </view>
      <view class="preferential" :style="themeBorder15Style">
        <text class="coupon-pack-title t-ellipsis" :style="themeColorStyle">优惠券卡包</text>
        <text class="coupon-pack-total t-ellipsis">共{{ info.total }}张优惠券</text>
        <van-button
          custom-style="margin-top: 12px;"
          :color="themeColor"
          custom-class="button"
          round
          size="small"
          @click="clickBtn"
        >
          立即领取
        </van-button>
      </view>
    </view>
  </award-container>
</template>

<script>
import Button from '@youzan/vant-tee/dist/button';
import AwardContainer from '../Container';
import FontCalculate from '../../widgets/FontCalculate';
import args from '@youzan/utils/url/args';
import { RGBA2RGB } from '../../../utils';
import { PROMO_TYPE_ACTION } from '../../../constants';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

export default {
  name: 'coupon-pack-card',
  components: {
    'van-button': Button,
    'award-container': AwardContainer,
    'font-calculate': FontCalculate,
  },
  props: {
    info: Object,
    orderNo: String,
    themeColor: String,
  },
  computed: {
    themeBackground5Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.05)};`;
    },
    themeBackgroundStyle() {
      return `background: ${this.themeColor};`;
    },
    themeBorderStyle() {
      return `border-color: ${this.themeColor};`;
    },
    themeColorStyle() {
      return `color: ${RGBA2RGB(this.themeColor)};`;
    },
    themeBorder30Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.3)};`;
    },
    themeBorder15Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.15)};`;
    },
    themeBorder20Style() {
      return `border-color: ${RGBA2RGB(this.themeColor, 0.2)};`;
    },
    // 优惠券礼包
    curCoupons() {
      return this.info.coupons?.slice(0, 2);
    },
  },
  methods: {
    clickBtn() {
      const { detailUrl } = this.info;
      const action = PROMO_TYPE_ACTION.couponpackage;
      this.$emit('log-attach', true);

      action({
        orderNo: this.orderNo,
        detailUrl,
        ...mapKeysCase.toCamelCase(args.getAll(detailUrl)),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.coupon {
  display: flex;
  overflow: hidden;
  height: 120px;
  padding: 0;
  border-radius: 8px;
}

.title {
  color: #969799;
  font-size: 14px;
}

.goods {
  flex: 1;
  padding: 12px;
  font-size: 12px;
}

.coupon-container {
  display: flex;
  justify-content: space-around;

  .coupon-item {
    display: flex;
    flex-direction: column;
    background: #fff;
    border: 1px solid;
    border-radius: 4px;
    width: 96px;
    z-index: 1;

    .price {
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .limit {
      border-top: 1px dashed;
      color: #646566;
      height: 32px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
  }
}

.coupon-item-pack + .coupon-item-pack {
  margin-left: 8px;
}

.hole-s {
  width: 8px;
  height: 8px;
  position: absolute;
  border-radius: 50%;
  border: 1px solid;
}

.hole-left {
  left: -5px;
  top: -5px;
}

.hole-right {
  right: -5px;
  top: -5px;
}

.preferential {
  width: 106px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-left: dashed 1px;
  position: relative;

  .hole {
    width: 10px;
    height: 10px;
    position: absolute;
    background: #fff;
    border-radius: 50%;
  }

  .limit {
    color: #646566;
    font-size: 12px;
    line-height: 16px;
    margin-top: 4px;
    max-width: 86px;
  }

  .money {
    line-height: 22px;
  }

  .button {
    color: #fff;
    font-size: 14px;
    height: 24px;
    margin-top: 8px;
    padding: 0 12px;
    width: 82px;
    white-space: nowrap;
  }

  .count-down {
    font-size: 11px;
    line-height: 12px;
    margin-top: 4px;
    width: 94px;
    text-align: center;

    .block {
      display: inline-block;
      width: 15px;
    }
  }

  .received {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: 1px solid;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    opacity: 0.4;
    position: absolute;
    right: -12px;
    top: -12px;
    transform: rotate(-45deg);

    view {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border: 1px dashed;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.coupon-pack-title {
  font-size: 16px;
  font-weight: bold;
  line-height: 30px;
}

.coupon-pack-total {
  font-size: 12px;
  line-height: 16px;
  margin-top: 4px;
  color: #646566;
}

.coupon-item-wrapper {
  overflow: hidden;
  position: absolute;
}
.coupon-shadow {
  width: 88px;
  height: 6px;
  margin-left: 4px;
  border-radius: 4px;
  border: 1px solid;
  margin-top: 90px;
}
</style>
