# @wsc-tee-trade/detail-virtual-code

虚拟订单二维码 `纯展示型Extension`

![UI呈现](https://img.yzcdn.cn/public_files/0186b6249845062c6abc5d647310ba93.png)

## 调试方式

虚拟商品下单

## 存疑

- 这个 Extension 非常轻量级，但是会有实例化开销，是否能将纯展示的 widget 合并在一起，或者根据区域划分？

## Widget.Provide

| 名称                  | 说明 |
| --------------------- | ---- |
| VirtualCode `default` | ???  |

## Data.Consume

| 名称       | 类型      | 说明 |
| ---------- | --------- | ---- |
| showQrCode | _boolean_ | ???  |
| qrCode     | _QrCode_  | ???  |
