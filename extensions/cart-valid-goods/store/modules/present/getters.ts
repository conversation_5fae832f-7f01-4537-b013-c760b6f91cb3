export default {
  // 已挑选赠品键值对
  presentData() {
    const result: Record<string, unknown> = {};
    (this.computedGoodsGroupList || []).forEach((goodsGroup) => {
      const { groupActivityInfoList = [], groupActivityInfo = {} } = goodsGroup || {};

      let groupList = [];

      if (groupActivityInfoList?.length > 0) {
        groupList = groupActivityInfoList;
      } else if (groupActivityInfoList?.length === 0 && groupActivityInfo) {
        groupList = [groupActivityInfo];
      }
      groupList.forEach((groupActivityInfo) => {
        const {
          activityId,
          activityType,
          selectablePresents = [],
          selectablePresentNum,
        } = groupActivityInfo;
        // 赠品缓存信息
        const presentCookieInfo = this.pickPresentCookieData[activityId];
        const { skuIdList, selectablePresentNum: preSelectablePresentNum } =
          presentCookieInfo || {};
        let presentGoodsList = selectablePresents.filter((item) => item.isSelected);

        // 记录每个活动的可选赠品数量
        this.selectablePresentNumMap[activityId] = selectablePresentNum;

        function getGoods(goods) {
          // 处理商品数据
          return {
            ...goods,
            activityId,
            activityType,
            presentId: goods.id,
            goodsSkuInfoList: goods.goodsSkuInfoList.map((item) => ({
              ...item,
              isSelected: goods.skuId === item.skuId,
            })),
          };
        }

        // 预选择的数量等于赠品可选数量
        if (skuIdList && preSelectablePresentNum === selectablePresentNum) {
          // 获取所有可选的sku
          const presentsMap = selectablePresents.reduce((map, goods) => {
            // 所有可选赠品键值对
            const { skuId, goodsSkuInfoList = [] } = goods;
            // 如果是多sku的赠品，需要遍历goodsSkuInfoList
            if (goodsSkuInfoList.length) {
              goodsSkuInfoList.forEach((item) => {
                map[item.skuId] = {
                  ...goods,
                  sku: item.sku,
                  skuId: item.skuId,
                };
              });
            } else {
              map[skuId] = goods;
            }

            return map;
          }, {});

          // 缓存挑选商品是否还都在活动内
          const inPresents = skuIdList.every((skuId) => {
            return presentsMap[skuId];
          });

          if (inPresents) {
            result.IS_SELECT_PRESENT = true;
            presentGoodsList = skuIdList.map((skuId) => presentsMap[skuId]);
          }
        }
        result[activityId] = presentGoodsList.map(getGoods);

        // 没有缓存时 & 可选数量等于赠品列表数量,直接返回所有赠品列表
        if (!skuIdList && selectablePresents.length === selectablePresentNum) {
          result[activityId] = selectablePresents.map(getGoods);
        }
      });
    });

    return result;
  },
};
