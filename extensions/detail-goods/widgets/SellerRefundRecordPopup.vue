<template>
  <van-popup
    title="商家主动退款记录"
    @touchmove.stop
    safe-area-inset-bottom
    close-on-click-overlay
    round
    position="bottom"
    :show="show"
    @close="$emit('close')"
    custom-class="seller-refund-record-popup"
  >
    <view class="title">商家主动退款记录</view>
    <scroll-view scroll-y :scroll-x="false" class="body">
      <view class="record-list">
        <view v-for="item in records" :key="item.id" class="record">
          <view class="record-title"
            ><view class="text">商家主动退款</view
            ><view class="amount">¥ {{ item.refundAmt }}</view></view
          >
          <view class="field">
            <view class="label">退款说明：</view>
            <view class="content">{{ item.remark || '无' }}</view>
          </view>
          <view class="field">
            <view class="label">退款时间：</view>
            <view class="content">{{ item.time }}</view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="bottom">
      <view class="close-btn" @click="onClose">知道了</view>
    </view>
  </van-popup>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index.vue';

export default {
  components: {
    'van-popup': Popup,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    records: {
      type: Array,
      default: () => [],
    },
  },

  methods: {
    onClose() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
.seller-refund-record-popup {
  .title {
    font-weight: 500;
    height: 44px;
    line-height: 44px;
    box-sizing: border-box;
    width: 100%;
    text-align: center;
    background-color: #fff;
    font-size: 16px;
    color: #323233;
  }
  .body {
    box-sizing: border-box;
    height: 350px;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #f7f7f7;
    .record-list {
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      .record {
        width: 100%;
        background-color: #fff;
        border-radius: 8px;
        padding: 12px;
        box-sizing: border-box;
        margin-bottom: 10px;
        .record-title {
          font-weight: 400;
          color: #323233;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .field {
          font-size: 12px;
          color: #969799;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          margin-top: 4px;
          .label {
            width: 70px;
            white-space: nowrap;
          }
          .content {
            flex: 1;
            word-break: break-all;
          }
        }
      }
    }
  }
  .bottom {
    padding: 5px 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    .close-btn {
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #fff;
      background: linear-gradient(270deg, #ff6034 0%, #ee0a24 100%);
      border-radius: 20px;
      text-align: center;
    }
  }
}
</style>
