<template>
  <view>
    <view class="vc-info">
      <view class="vc-info__title">电子卡券码</view>
      <block v-if="voucherCardIsError">
        <image
          class="vc-info__error-img"
          src="https://img.yzcdn.cn/upload_files/2020/03/12/FoFlIZNte-bq-nCcHIiZHDdvdwNa.png"
          alt="电子卡券生成失败"
        />
        <view class="vc-info__error-text"> 券码生成失败，请稍后再试或前往订单详情查看 </view>
      </block>
      <view v-else-if="voucherCardIsLoading" class="vc-info__loading">
        <van-loading type="img" size="28" class="vc-info__loading-icon" />
      </view>
      <block v-else>
        <view class="vc-info__code-box">
          <view class="vc-info__qrcode-wrap">
            <image :src="voucherCard.qrcode" alt="二维码" class="vc-info__qrcode" />
          </view>
          <view class="vc-info__qrbar-wrap">
            <image :src="voucherCard.barcode" alt="二维码" class="vc-info__qrbar" />
          </view>
        </view>
        <view class="vc-code">统一核销码：{{ voucherCard.code }}</view>
        <!-- 增加提示 -->
        <text class="vc-info__tips">为了保证你的权益，请勿提前提供给商家</text>
      </block>
    </view>
  </view>
</template>

<script>
import Loading from '@youzan/vant-tee/loading/index';
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-loading': Loading,
  },
  data() {
    return {
      ...mapState(this, [
        'voucherCard',
        'orderNo',
        'themeCSS',
        'voucherCardIsLoading',
        'voucherCardIsError',
      ]),
    };
  },

  computed: {
    customPopupStyle() {
      return `border-top-left-radius: var(--theme-radius-dialog, 20px); border-top-right-radius: var(--theme-radius-dialog, 20px); text-align: center;${this.themeCSS}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.vc-info {
  border-radius: var(--theme-radius-card, 12px);
  background: #fff;
  width: calc(100vw - 24px);
  padding: 12px 0;
  margin: 0 12px 16px;
  text-align: center;

  &__error-img {
    display: block;
    width: 136tpx;
    height: 136tpx;
    margin: 16tpx auto;
  }

  &__error-text {
    font-weight: 400;
    font-size: 14tpx;
    line-height: 20tpx;
    text-align: center;
    color: #666;
  }

  &__title {
    font-weight: 400;
    color: #333;
    font-size: var(--eo-font-size-14, 14px);
    margin-bottom: 8px;
  }

  &__code-box {
    width: calc(100vw - 24px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__qrbar {
    display: block;
    width: 120px;
    height: 48px;
    transform: rotateZ(-90deg);
    &-wrap {
      margin-left: -16px;
    }
  }

  &__qrcode {
    position: relative;
    display: block;
    width: 120px;
    height: 120px;

    &-wrap {
      margin-left: 32px;
      padding-right: 16px;
      border-right: 1px dashed #e0e0e0;
    }
  }

  &__tips {
    display: block;
    font-family: PingFangSC-Regular, sans-serif;
    font-size: var(--eo-font-size-12, 12px);
    color: #999;
    font-weight: 400;
    margin: 8px auto 0;
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130tpx;
    height: 130tpx;
    margin: 8tpx auto;
    background: #eee;
    border-radius: 8tpx;
  }

  &__loading-icon {
    margin-left: 4px;
    width: 28px;
    height: 28px;
  }
}
.vc-code {
  margin: 8px auto;
  font-weight: 500;
  color: #333;
  font-size: var(--eo-font-size-16, 16px);
  word-break: break-all;
}
</style>
