<template>
  <van-cell
    v-if="showComponent"
    title="大家喜欢"
    center
    is-link
    to="index"
    title-class="welike-entry-title"
    value-class="welike-entry-value"
    custom-class="welike-entry"
    @click="navigateToWelikePage"
    :custom-style="customStyleStr"
  >
    <view :class="['avatar-container', showAnimation ? 'animation' : 'no-animation']">
      <image
        v-for="avatar in avatarList"
        :key="avatar.key"
        :src="avatar.url"
        :class="['avatar', avatar.class]"
        @animationend="animationEventCallback"
      />
    </view>

    <text>{{ userCount }} 人疯抢中</text>
  </van-cell>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { requestV2 } from '@youzan/tee-biz-request';
import throttle from '@youzan/weapp-utils/lib/throttle';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import { object, toKebabCase, url } from '@youzan/tee-util';
import { mapData } from '@youzan/ranta-helper-tee';

let subKdtId = '';
/* #ifdef web */
subKdtId = url.args.get('sub_kdt_id', window.location.search);
/* #endif */

export default {
  components: {
    'van-cell': Cell,
  },

  props: {
    sourcePage: String,
    orderId: String,
    customStyle: {
      type: Object,
      default: () => ({ marginTop: 10 }),
    },
  },

  data() {
    return {
      showComponent: false,
      // avatarList: [],

      // 用户数量和头像列表
      userCount: 0,
      avatars: [],

      // 动画效果第一个元素的位置
      animationIndex: 0,
    };
  },

  computed: {
    customStyleStr() {
      return Object.keys(this.customStyle)
        .map((key) => `${toKebabCase(key)}:${this.customStyle[key]}`)
        .join(';');
    },
    // 展示动画效果
    showAnimation() {
      return this.avatars.length > 3;
    },

    // 动画效果元素的范围
    animationRange() {
      const { avatars, animationIndex } = this;
      const { length } = avatars;
      const range = [animationIndex, animationIndex + 1, animationIndex + 2, animationIndex + 3];

      return range.map((item) => (item > length - 1 ? item - length : item));
    },

    // 元素列表
    avatarList() {
      const { avatars, showAnimation, animationRange } = this;
      if (!showAnimation) {
        return avatars.map((avatar, index) => ({
          key: avatar,
          url: avatar,
          class: `avatar-${index}`,
        }));
      }

      const list = avatars.map((avatar) => ({
        key: avatar,
        url: avatar,
        class: 'avatar__hide',
      }));
      animationRange.forEach((item, index) => {
        list[item].class = `avatar-${index}`;
      });

      return list;
    },
  },

  created() {
    /* #ifdef web */
    // 小红书环境不展示大家喜欢
    if (
      this.ctx.data?.miniprogram?.isXhsApp ||
      this.ctx.data?.miniprogram?.isKsApp ||
      this.ctx.data?.miniprogram?.isQQApp
    ) {
      return;
    }

    // 赞拼拼订单不展示大家喜欢
    const isFxZpp = object.get(window, '_global.env.isFxZpp', false);
    if (isFxZpp) {
      return;
    }
    /* #endif */

    // 批发订单详情不展示大家喜欢 包括小程序和h5
    mapData(this, {
      isWholesaleOrder: (val) => {
        this.isWholesaleOrder = val;
        if (val) {
          this.showComponent = false;
        }
      },
    });

    this.getWelikeInfo();
  },

  methods: {
    fetchWelikeInfo() {
      return requestV2({
        path: '/wsctrade/welike/entry-info.json',
        data: { sub_kdt_id: subKdtId, sourcePage: this.sourcePage },
      });
    },

    getWelikeInfo() {
      if (this.isWholesaleOrder) {
        return;
      }
      this.fetchWelikeInfo().then((res) => {
        const { showEntry = false, userCount = 0, avatars = [] } = res || {};
        if (!showEntry || userCount === 0 || avatars.length === 0) {
          return;
        }

        // 批发订单不展示组件
        this.showComponent = showEntry && !this.isWholesaleOrder;
        this.userCount = userCount;
        this.avatars = avatars;

        this.$emit('show');
        this.ctx.logger &&
          this.ctx.logger.log({
            et: 'view', // 事件类型
            ei: 'entry_view', // 事件标识
            en: '入口曝光', // 事件名称
            params: {
              order_id: this.orderId || '',
              source_page: this.sourcePage,
              component: 'welike_entry',
            }, // 事件参数
          });
      });
    },

    animationEventCallback: throttle(
      function () {
        this.updateAnimationIndex();
      },
      120,
      { leading: false }
    ),

    updateAnimationIndex() {
      const { avatars, animationIndex } = this;

      if (animationIndex >= avatars.length - 1) {
        this.animationIndex = 0;
      } else {
        this.animationIndex += 1;
      }
    },

    navigateToWelikePage() {
      const kdtId = subKdtId || this.ctx.data.kdtId;

      const path = '/wscump/welike';
      const weappPath = '/packages/ump/welike/index';
      const weappUrl = kdtId ? `${weappPath}?kdt_id=${kdtId}` : weappPath;
      const url = kdtId ? `${path}?kdt_id=${kdtId}` : path;

      this.ctx.logger &&
        this.ctx.logger.log({
          et: 'click', // 事件类型
          ei: 'entry_click', // 事件标识
          en: '入口点击', // 事件名称
          params: {
            source_page: 'order_detail',
            component: 'welike_entry',
          }, // 事件参数
        });
      /* #ifdef weapp */
      navigate({
        url: weappUrl,
        type: 'navigateTo',
      });
      /* #endif */
      /* #ifdef web */
      ZNB.navigate({
        url,
      });
      /* #endif */
    },
  },
};
</script>

<style lang="scss">
.welike-entry {
  &::after {
    border-bottom: none;
  }

  & &-title {
    flex: 0 1 auto;
  }

  & &-value {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #323233;
  }

  .avatar-container {
    position: relative;
    width: 70px;
    height: 30px;
    margin-right: 8px;

    .avatar {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 28px;
      height: 28px;
      border: 1px solid #fff;
      border-radius: 14px;
    }

    .avatar__hide {
      display: none;
    }
  }

  .animation {
    .avatar {
      &-0 {
        left: 0;
        animation: 1.2s element-a linear forwards;
        z-index: 1;
      }

      &-1 {
        animation: 1.2s element-b linear forwards;
        z-index: 2;
      }

      &-2 {
        animation: 1.2s element-c linear forwards;
        z-index: 3;
      }

      &-3 {
        animation: 1.2s element-d linear forwards;
        z-index: 4;
      }
    }
  }

  .no-animation {
    .avatar {
      &-0 {
        right: 0;
        z-index: 3;
      }

      &-1 {
        right: 20px;
        z-index: 2;
      }

      &-2 {
        right: 40px;
        z-index: 1;
      }
    }
  }
}

@keyframes element-a {
  0% {
    left: 0;
    width: 28px;
    height: 28px;
    opacity: 1;
  }
  100% {
    left: 0;
    width: 0;
    height: 0;
    opacity: 0;
  }
}

@keyframes element-b {
  0% {
    left: 20px;
  }
  100% {
    left: 0;
  }
}

@keyframes element-c {
  0% {
    left: 40px;
  }
  100% {
    left: 20px;
  }
}

@keyframes element-d {
  0%,
  20% {
    right: 0;
    width: 0;
    height: 0;
    opacity: 0;
  }
  100% {
    right: 0;
    width: 28px;
    height: 28px;
    opacity: 1;
  }
}
</style>
