<template>
  <view
    v-if="showPaidPromotion && !canUseTradeUmpV1 && !miniprogram.isTTApp"
    class="paid-promotion"
    @click="handleClick"
  >
    <image v-if="imgUrl" :src="imgUrl" class="paid-promotion__image" mode="aspectFit" />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { url } from '@youzan/tee-util';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import fullfillImage from '@youzan/utils/url/fullfillImage';

/* #ifdef weapp */
const PROMO_TYPE_ACTION = {
  tradeincard({ orderNo, type = 'promocard' }) {
    navigate({
      url: `/packages/user/coupon/detail/index?orderNo=${orderNo}&type=${type}&from=orderDetail`,
      type: 'navigateTo',
    });
  },

  promocode(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'promocode',
    });
  },

  couponpackage(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'couponpackage',
    });
  },

  seller() {
    navigate({
      url: '/packages/salesman/tutorial/index',
      type: 'navigateTo',
    });
  },

  wheel(options) {
    const detailUrl = encodeURIComponent(options.detailUrl);
    navigate({
      url: `/pages/common/webview-page/index?src=${detailUrl}`,
      type: 'navigateTo',
      success: () => {},
      error: () => {
        Toast('打开失败');
      },
    });
  },

  seniorseller() {
    PROMO_TYPE_ACTION.seller();
  },

  feature({ detailUrl }) {
    const match = detailUrl.match(/feature\/([^?/]+)/);
    navigate({
      url: `/packages/home/<USER>/index?alias=${match[1]}`,
      type: 'navigateTo',
    });
  },

  present({ detailUrl, present }) {
    const match = detailUrl.match(/goods\/([^?/]+)/);
    navigate({
      url: `/packages/goods/present/index?alias=${match[1]}&type=present&activityId=${present}`,
      type: 'navigateTo',
    });
  },
};
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const PROMO_TYPE_ACTION_KEYS = Object.keys(PROMO_TYPE_ACTION);
/* #endif */

export default {
  data() {
    return {
      orderNo: '',
      showPaidPromotion: false,
      paidPromotion: {},
      miniprogram: {},
      canUseTradeUmpV1: {},
    };
  },
  computed: {
    imgUrl() {
      return fullfillImage(this.paidPromotion.imgUrl, 'middle');
    },
  },
  created() {
    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    }).catch((e) => {
      console.log(e);
    });
    /* #endif */

    mapData(this, [
      'orderNo',
      'showPaidPromotion',
      'paidPromotion',
      'miniprogram',
      'canUseTradeUmpV1',
    ]);
  },
  methods: {
    handleClick() {
      const { orderNo } = this;
      const { detailUrl, promotionType } = this.paidPromotion || {};
      const { isAlipayApp, isQQApp } = this.miniprogram || {};

      /* #ifdef weapp */
      const action = PROMO_TYPE_ACTION[promotionType];
      action &&
        navigate({
          web: {
            type: 'native',
            native: {
              orderNo,
              detailUrl,
              ...mapKeysCase.toCamelCase(url.args.getAll(detailUrl)),
            },
          },
        });
      /* #endif */

      /* #ifdef web */
      // 本次支付宝 QQ 没有支付有礼营销活动
      if (isAlipayApp || isQQApp) {
        const url = `/packages/ump/coupon/detail/index?orderNo=${orderNo}&type=${promotionType}&from=payresult`;
        navigate({
          web: {
            type: 'znb',
            znb: {
              aliappUrl: url,
              qqUrl: url,
            },
          },
        });
      } else {
        if (!detailUrl) {
          return;
        }
        navigate({
          web: {
            type: 'safeLink',
            safeLink: {
              url: detailUrl,
            },
          },
        });
      }
      /* #endif */
    },
  },
};
</script>

<style lang="scss">
.paid-promotion {
  box-sizing: content-box;
  padding: 6px 12px 0 12px;
  background: #fff;

  &__image {
    max-width: 100%;
    border-radius: 4px;
  }
}
</style>
