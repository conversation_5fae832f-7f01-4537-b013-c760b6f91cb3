<template>
  <view>
    <text v-if="opt.queueStr">
      前面<text class="text-light">{{ opt.queueStr }}</text>制作
    </text>
    <text v-if="opt.queueStr && opt.waitTime">，</text>
    <text v-if="opt.waitTime">
      预计<text class="text-light">{{ opt.waitTime }}</text>分钟
    </text>
  </view>
</template>

<script>
export default {
  name: 'queue-node',
  props: {
    opt: Object,
  },
};
</script>

<style lang="scss" scoped>
.text-light {
  color: var(--general, '#a72d25');
}
</style>
