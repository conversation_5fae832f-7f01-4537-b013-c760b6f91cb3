<template>
  <view>
    <common-dialog />
    <shop-nav-weapp :force-show="isShowTabbar" />
  </view>
</template>

<script>
import Tee from '@youzan/tee';

const getIsShowTabbar = (tabbarList) => {
  return tabbarList.some((item) => item.pagePath === 'packages/trade/order/list/index');
};
export default {
  data() {
    return {
      isShowTabbar: false,
    };
  },

  created() {
    /* #ifdef weapp */
    /*
     * app.once('custom-tab-bar:nav:resolved') 的作用是为了处理当中台化订单列表页被
     * 配置成tab页的场景
     * 现在实际的pages/tab/(one|two|three)/index配的实际是
     * packages/trade/order/list/index 即中间页，wx.redirect到中台化订单列表之后
     * tabbar就会丢失，为了能让tabbar正确展示出来，这里加了这些逻辑
     *
     * TODO: 如果后续所有商家都能切到中台化订单列表之后（包括所有定制商家），也就是中间页和
     * !原生中台化能彻底下线的时候，那么这个逻辑也就不需要了，只需要把
     * !pages/tab/(one|two|three)/index.json 里面的 trade/order/list/index 改成
     * !trade/order-list/index 即可
     */
    const app = Tee.getApp();
    app.once('custom-tab-bar:nav:resolved', (tabbarList) => {
      this.isShowTabbar = getIsShowTabbar(tabbarList);
    });
    if ((app.globalData || {}).tabbarOriginList) {
      this.isShowTabbar = getIsShowTabbar(app.globalData.tabbarOriginList);
    }
    /* #endif */
  },
};
</script>
