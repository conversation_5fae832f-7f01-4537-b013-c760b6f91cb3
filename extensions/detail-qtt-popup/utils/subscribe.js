import { checkBlackRelation, createSubscribeAndSyncTakeVoucher, saveFocusMpPagePath } from '../api';
// import Toast from '@youzan/vant-tee/dist/toast/toast';
import ZNB from '@youzan/znb';

ZNB.init({
  kdtId: _global.kdt_id,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
}).catch(() => {});

// 跳转公众号引导页前时间间隔
// const duration = 1000;

// 关注公众号
export const followMp = () => {
  ZNB.navigate({
    weappUrl: `/pages/web-view/index?src=${encodeURIComponent(
      // eslint-disable-next-line
      'https://mp.weixin.qq.com/s?__biz=Mzg3Nzc0MTE0MQ==&mid=2247483883&idx=1&sn=dd53a2b275307db200c663beb4f7d441&chksm=cf1f14d1f8689dc7c2288b63a1f5ba57144a5acd97f9fb7231004d0b177beacf0dd6b64f4a77#rd'
    )}`,
  });
};

/*
 * 分享、订阅、一键帮卖、编辑后帮卖、邀请赚操作时检查是否已被拉黑
 * @param {number} founderPartyId 团长ID
 * @param {string} errMsg 拉黑后的Toast提示语
 */
export const checkHasBlackRelation = async (founderPartyId) => {
  try {
    const res = await checkBlackRelation({
      founderPartyId,
    });
    if (res) {
      // Toast(errMsg);

      return true;
    }
  } catch (error) {
    // Toast(error?.msg || '操作失败');

    return true;
  }
};
/**
 * 创建订阅关系同步领取订阅有礼优惠券，需要关注发券结果，主要用于首页、团购详情页、团长主页
 *
 *  @param {number} subscribedId - 要订阅团长id
 *  @param {function} subscribeSuccessCallback  - 订阅成功回调
 *  @param {function} takeVoucherSuccessCallback  - 发券成功回调
 */
export const createSubscribeWithSyncTakeVoucher = async (
  subscribedId,
  subscribeSuccessCallback,
  takeVoucherSuccessCallback
) => {
  const isBlacklist = await checkHasBlackRelation(subscribedId, '无法订阅该团长，有疑问请咨询团长');

  // 存在黑名单关系无法订阅
  if (isBlacklist) {
    return;
  }

  return createSubscribeAndSyncTakeVoucher({ subscribedId })
    .then((res) => {
      const { code, voucher = {} } = res;
      if (code === 200) {
        // 订阅、发券成功
        subscribeSuccessCallback && subscribeSuccessCallback();
        takeVoucherSuccessCallback && takeVoucherSuccessCallback(voucher);
      } else if (code === 230 || code === 220) {
        // 订阅成功，发券失败
        subscribeSuccessCallback && subscribeSuccessCallback();
      } else if (code === 101 || code === 300) {
        // 订阅失败
        // Toast(message || '订阅失败');
      } else if (code === 102) {
        // // 未关注公众号
        // Toast(message);
        // // 跳转关注公众号引导页
        // setTimeout(() => {
        //   followMp();
        // }, duration);
      }
    })
    .catch((rej) => {
      console.log(rej);
    });
};

/**
 * 关注群团团公众号，
 * 关注前调用记录接口存入页面路径，方便后端给用户推送页面卡片
 */
export const saveFollowMpPagePath = (path) => {
  // 跳转关注公众号引导页
  setTimeout(() => {
    followMp();
  }, 500);
  return saveFocusMpPagePath({
    pagePath: path,
  });
};
