/*
 * env:
 * isWeixin
 * isYouzanmars: false
 * isXmlyApp: false
 * isYouzanke: false
 * isThirdApp: false
 * isKuaiShou: false
 * isDouYinApp
 * isYouzanwxd
 *
 * miniprogram:
 * isAliApp: false
 * isAlipayApp: false
 * isMiniProgram: false
 * isQQApp: false
 * isSwanApp: false
 * isTTApp: false
 * isWeapp: false
 */

let env = {};
let miniprogram = {};
/* #ifdef web */
// 服务于H5场景
env = window?._global?.env ?? {};
miniprogram = window?._global?.miniprogram ?? {};
const uaStr = navigator?.userAgent;
const isWeibo = /(weibo)\//i.test(uaStr);
const isZhihu = /(osee2unifiedRelease|zhihu)/i.test(uaStr);
/* #endif */

/* #ifdef weapp */
// eslint-disable-next-line no-redeclare
env = { isWeixin: true };
// eslint-disable-next-line no-redeclare
miniprogram = { isWeapp: true };
/* #endif */

export default {
  ...env,
  ...miniprogram,
  /* #ifdef web */
  isWeibo,
  isZhihu,
  /* #endif */
};
