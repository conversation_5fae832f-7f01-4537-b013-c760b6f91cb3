<template>
  <view class="skeleton-wrapper">
    <view class="skeleton" :class="animationClass" />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      animationClass: '',
      isAuthProtocol: undefined,
      dataLoaded: false,
    };
  },

  created() {
    mapData(this, {
      dataLoaded: (dataLoaded) => {
        if (dataLoaded) {
          this.hideSkeleton();
        }
      },
      isAuthProtocol: () => {
        this.hideSkeleton();
      },
    });
  },

  methods: {
    hideSkeleton() {
      if (this.animationClass === '' && this.dataLoaded && this.isAuthProtocol !== undefined) {
        this.animationClass = 'fade-out';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.skeleton-wrapper {
  position: relative;
}

.skeleton {
  position: fixed;
  width: 100%;
  min-height: 100vh;
  left: 0;
  right: 0;
  top: 0;
  background-color: #f7f8fa;
  background-image: url('https://img01.yzcdn.cn/public_files/2020/12/24/4b0b7a5046e76f5b024531324e02fd92.png');
  background-size: 100% auto;
  z-index: 50;
}

.fade-out {
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.1s;
}
</style>
