<template>
  <view>
    <!-- 如果不显示步进器，只显示数量 -->
    <view v-if="!good.showStepper" :class="[good.isECard ? 'num-padding' : '']"> x{{ num }} </view>
    <!-- 如果显示步进器 -->
    <view v-else class="goods-num-wrapper">
      <!-- 抖音核销小程序零售场景直接 return，不能主动调整商品数量 -->
      <view v-if="isTTVerifyRetailScene" style="text-align: right">x {{ num }}</view>
      <!--电子卡券类型或者是订单里只有一件商品 商品数量可以修改-->
      <stepper
        v-else
        :value="num"
        input-width="32px"
        button-size="24px"
        integer
        :min="startSaleNum"
        :max="goodMax"
        :long-press="false"
        :disabled="showToast"
        :disable-minus="stepperDisableMinus"
        input-class="new-goods-list-card__stepper"
        @overlimit="overLimitAction"
        @plus="onPlusMinus('plus')"
        @minus="onPlusMinus('minus')"
        @blur="onBlur"
        ref="stepper"
      />
      <van-loading v-if="showToast" size="16" class="goods-loading" />
    </view>
    <!-- 每期配送数量 -->
    <view v-if="good.isPeriodBuy" class="period-buy">每期配送数量</view>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Loading from '@youzan/vant-tee/dist/loading/index';
import Stepper from '@youzan/wsc-tee-trade-common/components/Stepper';
import debounce from '@youzan/weapp-utils/lib/debounce';
import { logGoodStepperPlusMinus, logGoodStepperChange, logGoodStepperShow } from '../log';
import { calcLimitedText } from '../utils';

export default {
  components: {
    stepper: Stepper,
    'van-loading': Loading,
  },
  props: {
    // 商品数量
    num: {
      type: Number,
      default: 1,
    },
    // 起售数量
    startSaleNum: {
      type: Number,
      default: 1,
    },
    // 商品对象
    good: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      showToast: false,
      stepperDisableMinus: this.getStepperDisableMinus(),
      isTTVerifyRetailScene: false,
    };
  },

  computed: {
    goodsId() {
      return this.good.goodsId;
    },
    skuId() {
      return this.good.skuId;
    },
    goodMax() {
      return this.good.goodMax;
    },
  },

  watch: {
    num(newVal) {
      this._latestNum = newVal;
      this.stepperDisableMinus = this.getStepperDisableMinus();
    },
    startSaleNum() {
      this.stepperDisableMinus = this.getStepperDisableMinus();
    },
  },

  created() {
    /* #ifdef web */
    const { retailOrderScene, ttOrderScene } = this.$getPageQuery ? this.$getPageQuery() : {};
    const isTTApp =
      window && window._global && window._global.miniprogram && window._global.miniprogram.isTTApp;
    this.isTTVerifyRetailScene =
      retailOrderScene === '24hshelf' && ttOrderScene === 'verify' && isTTApp;
    /* #endif */
    this.changeNumDebounced = debounce(this.changeData, 300, false);

    // 初始化最新数量值
    this._latestNum = this.num;
  },

  mounted() {
    logGoodStepperShow(undefined);
  },

  methods: {
    onPlusMinus(action) {
      // 使用本地变量跟踪最新的数量值
      if (!this._latestNum) {
        this._latestNum = this.num;
      }

      if (action === 'plus') {
        this._latestNum += 1;
      } else {
        this._latestNum -= 1;
      }

      // 使用最新的数量值
      this.onChange(this._latestNum);
      logGoodStepperPlusMinus(undefined, { action });
    },

    onBlur(e) {
      const num = Number(e.value);
      this._latestNum = num; // 更新最新数量值
      this.onChange(num);
      logGoodStepperChange(undefined, { value: e.value });
    },

    onChange(num) {
      const { goodsId, skuId } = this;
      this.changeNumDebounced(goodsId, skuId, num);
    },

    changeData(goodsId, skuId, num) {
      this.showToast = true;
      this.$emit('change', {
        loading: false, // 不使用统一的loading动画，由组件内自行处理
        goodsId,
        skuId,
        num,
        successCB: () => {
          this.showToast = false;
        },
        errorCB: () => {
          this.showToast = false;
          this.$refs.stepper.currentValue = String(this.num);
        },
      });
    },

    overLimitAction(detail) {
      const currentNum = this._latestNum || this.num;
      const { stepperDisableMinus } = this;

      if (currentNum === 1 && detail === 'minus') {
        return Toast('最少购买1件');
      }

      // 不能低于起售
      if (detail === 'minus' && stepperDisableMinus) {
        return Toast(`该商品${this.startSaleNum}件起售`);
      }

      //  超出库存&&超出限购
      if (currentNum >= this.goodMax && detail === 'plus') {
        return Toast(calcLimitedText(this.good));
      }
    },

    getStepperDisableMinus() {
      // 等于起售数量时，禁用减少按钮
      const currentNum = this._latestNum || this.num;
      return currentNum <= this.startSaleNum || currentNum <= 1;
    },
  },
};
</script>

<style lang="scss">
@import '@youzan/wsc-tee-trade-common/styles/common';
.goods-num-wrapper {
  width: 86px;
  height: 26px;
  position: relative;
}

.new-goods-list-card__stepper {
  padding: 0 !important;
}

.goods-loading {
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: $text-color;
  z-index: 100;
}

.num-padding {
  padding-top: 4px;
}

.period-buy {
  text-align: right;
}
</style>
