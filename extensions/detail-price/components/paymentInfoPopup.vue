<template>
  <van-popup
    round
    closeable
    :show="show"
    class="popup"
    position="bottom"
    @close="closePayTypePopup"
    safe-area-inset-bottom
  >
    <view class="popup-container">
      <view class="popup-container-title">查看支付信息</view>
      <view class="popup-container-content">
        <view class="content-voucher" v-if="isShowVoucher">
          <text>凭证：</text>
          <view class="content-voucher-gird">
            <view
              class="content-voucher-gird-item"
              v-for="(img, index) in voucherUrlList || []"
              :key="img"
              @click="ImgPreview(index)"
            >
              <image :src="img" mode="aspectFit" />
            </view>
          </view>
        </view>
        <view class="content-remark" v-if="isShowRemark">
          <text class="content-subtitle">备注：</text>
          <text>{{ paymentInfo.voucherDesc }}</text>
        </view>
      </view>

      <view class="popup-container-operate">
        <view class="popup-container-operate-confirm" @click="closePayTypePopup">我知道了</view>
      </view>
    </view>
  </van-popup>
</template>

<script>
import { Popup, Button } from '@vant/tee';
import { previewImage } from '@youzan/tee-api';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  components: {
    'van-popup': Popup,
    'van-button': Button,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    paymentInfo: Object,
  },

  computed: {
    isShowVoucher() {
      return this.paymentInfo?.voucherUrlList?.length;
    },
    isShowRemark() {
      return ![null, undefined, ''].includes(this?.paymentInfo?.voucherDesc);
    },
    voucherUrlList() {
      return (this.paymentInfo.voucherUrlList || []).map((item) => fullfillImage(item, 'middle'));
    },
  },

  methods: {
    ImgPreview(index) {
      previewImage({ current: index, urls: this.voucherUrlList || [] });
    },

    closePayTypePopup() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
.popup {
  color: #323233;

  &-container {
    min-height: 406tpx;
    display: flex;
    flex-direction: column;

    &-title {
      width: 100vw;
      height: 44tpx;
      font-weight: 500;
      font-size: 16px;
      line-height: 44tpx;
      text-align: center;
      margin-bottom: 16tpx;
    }

    &-content {
      display: flex;
      padding: 0 16tpx;
      flex-direction: column;
      flex: 1;

      .content-voucher {
        line-height: 16px;
        display: flex;

        &-gird {
          &-item {
            width: 80tpx;
            height: 80tpx;
            object-fit: contain;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .content-remark {
        margin-top: 8tpx;
      }
    }

    &-operate {
      display: flex;
      padding: 5tpx 16tpx;

      &-confirm {
        width: 100%;
        height: 40tpx;
        color: #fff;
        line-height: 40px;
        text-align: center;
        border-radius: 20px;
        background: linear-gradient(270deg, #ee0a24 0%, #ff6034 100%);
      }
    }
  }
}
</style>
