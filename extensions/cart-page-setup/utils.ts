import { cdnImage } from '@youzan/tee-biz-util';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import getGoodsPropertiesStr from '@youzan/wsc-tee-trade-common/lib/order-utils/goods/getGoodsPropertiesStr';
import hexToRgba from '@youzan/utils/string/hexToRgba';
import type {
  TradeCartGoods,
  TradeCartGoodsGroup,
  CartActivityInfo,
} from '@youzan-cloud/cloud-biz-types';
import { getSkuStr, formatSku } from './sku';
import type { ThemeColors } from './types';

export { getSkuStr } from './sku';

// 获取商品sku 和属性信息
export function getGoodsSkuProperty(goods) {
  const skuStr = getSkuStr(goods.sku);
  const propertiesStr = getGoodsPropertiesStr(goods.properties);

  return [skuStr, propertiesStr].filter((item) => !!item).join('，');
}

// 获取商品 unique
function getUnique(goods) {
  const goodsPropertiesIds = goods.propertyIds || [];
  return `${goods.kdtId}-${goods.goodsId}-${goods.skuId}-${
    goods.activityId || 0
  }-${goodsPropertiesIds.join('-')}`;
}

function getTariffPriceText({ tariffRule, tariffPrice, num }) {
  if (tariffRule === 0) {
    return `预计 ￥ ${((+tariffPrice * num) / 100).toFixed(2)}`;
  }
  if (tariffRule === 1) {
    return '商品已含税';
  }

  return '';
}

export function cartParser(rawData) {
  const unAvailableGoodsList = [];

  const rawDataCamel = mapKeysCase.toCamelCase(rawData);

  const allGoodsIds = [];
  const shopList = (rawDataCamel || []).map((item) => {
    const {
      unavailableItems,
      activities,
      shopName,
      kdtId,
      selectedPreferencePrice = 0,
      goodsGroupList = [],
      isShowEstimatedPrice,
      isNewHopeShop = false,
      ...rest
    } = item;

    // 购物车商品图片填充格式改为aspectFill
    const goodsImgMode = 'aspectFill';
    const goodsGroupListFormated = goodsGroupList.map((goodsGroup = {} as any) => {
      const goodsList = (goodsGroup?.goodsList || []).map((item) => {
        const { activityId, goodsId, comboDetail } = item;
        if (allGoodsIds.indexOf(goodsId) === -1) {
          allGoodsIds.push(goodsId);
        }
        return {
          // 商品套餐的数据要加上
          comboDetail,
          cartId: item.cartId,
          kdtId: item.kdtId,
          channelId: item.channelId,
          canyinId: item.canyinId,
          goodsId,
          skuId: item.skuId,
          storeId: item.storeId,
          title: item.title,
          num: item.num,
          stock: item.stockNum,
          activityStartTime: item.activityStartTime, // 活动开始时间
          activityEndTime: item.activityEndTime, // 活动结束时间
          isShowStockNum: item.isShowStockNum, // 是否显示剩余数量
          isShowStockShort: item.isShowStockShort, // 是否显示库存紧张
          limitNum: item.limitNum || 0,
          cutPrice: item.cutPrice, // 比加入时降价
          estimatedPrice: typeof item.estimatedPrice === 'number' ? item.estimatedPrice : '',
          price: item.payPrice,
          payPrice: item.payPrice,
          originPrice: item.pointsPrice ? 0 : item.originPrice,
          sku: getGoodsSkuProperty(item),
          skuData: formatSku(item.sku),
          propertyIds: item.propertyIds,
          checked: !!item.selectState,
          maxNum: +item.limitNum ? Math.min(item.limitNum, item.stockNum) : item.stockNum,
          attachmentUrl: item.attachmentUrl,
          imgUrl: cdnImage(item.attachmentUrl, '!300x300.jpg'),
          unique: getUnique(item),
          extraAttribute: item.extraAttribute || '{}',
          messages: item.messages || '{}',
          alias: item.alias,
          logisticsTypeList: item.logisticsTypeList,
          goodsType: item.goodsType,
          activityId,
          weight: item.weight,
          activityType: item.activityType,
          activityAlias: item.activityAlias || '',
          activityTag: item.activityTag || '',
          activityTypeStr: item.activityTypeStr || '',
          isInstallment: item.isInstallment || false,
          settlementRule: item.settlementRule,
          tariffPrice: item.tariffPrice,
          tariffPriceText: getTariffPriceText(item),
          tariffRule: item.tariffRule,
          properties: item.properties || [],
          createdTime: item.createdTime,
          updatedTime: item.updatedTime,
          deliverTime: item.deliverTime,
          startSaleNum: item.startSaleNum || 0,
          revive: item.revive || false, // 是否是待复活商品
          isSevenDayUnconditionalReturn: item.isSevenDayUnconditionalReturn || false, // 七天无理由退货
          yzGuarantee: item.yzGuarantee || false, // 有赞放心购
          hideGuarantee: item.hideGuarantee || false, // 黑金隐藏有赞放心购
          startSoldTime: item.startSoldTime || '', // 开售时间
          disableSelectMsg: item.disableSelectMsg || '',
          bizExtension: item.bizExtension,
          birthdayRelation: item.birthdayRelation || {},
          quotaCycle: item.quotaCycle,
          imgMode: goodsImgMode,
          pointsPrice: item.pointsPrice,
          pointsOriginPrice: item.pointsOriginPrice,
        };
      });

      return {
        ...goodsGroup,
        goodsList,
      };
    });

    (unavailableItems || []).forEach((item) => {
      const imgUrl = item.attachmentUrl || cdnImage('v2/image/wap/no-pic-v2.png');
      const activityId = item.activityId || 0;
      unAvailableGoodsList.push({
        cartId: item.cartId,
        kdtId: item.kdtId,
        channelId: item.channelId,
        goodsId: item.goodsId,
        goodsType: item.goodsType,
        skuId: item.skuId,
        storeId: item.storeId,
        productSkuId: item.skuId,
        title: item.title || '',
        messages: item.messages || '{}',
        price: item.payPrice,
        payPrice: item.payPrice,
        originPrice: item.originPrice,
        sku: getSkuStr(item.sku),
        num: item.num,
        errorMsg: item.errorMsg,
        imgUrl: cdnImage(imgUrl, '!300x300.jpg'),
        unique: getUnique(item),
        alias: item.alias,
        activityId,
        activityType: item.activityType,
        activityTag: item.activityTag === '加价购' ? '换购' : item.activityTag || '',
        imgMode: goodsImgMode,
      });
    });
    return {
      kdtId,
      shopName,
      isNewHopeShop, // 是否是新希望店铺
      goodsGroupList: goodsGroupListFormated,
      activities,
      isShowEstimatedPrice,
      selectedPreferencePrice, // 活动优惠金额
      ...rest,
    };
  });

  return {
    shopList,
    unAvailableGoodsList,
    allGoodsIds,
  };
}
const goodsTypeEnum = {
  /** 普通类型商品 goodsType = 0 */
  NORMAL: [0, 'normal'],
  /** 周期购 goodsType = 24 */
  PERIOD_BUY: [24, 'periodBuy'],
  /** 知识付费商品 goodsType = 31 */
  KNOWLEDGE: [31, 'knowledge'],
  /** 酒店商品 goodsType = 35 */
  HOTEL: [35, 'hotel'],
  /** 普通虚拟商品 goodsType = 182 */
  VIRTUAL: [182, 'virtual'],
  /** 电子卡券商品 goodsType = 183 */
  ECARD: [183, 'ecard'],
  /** 分销商品 goodsType = 10 */
  FENXIAO: [10, 'fenxiao'],
  /** 拍卖商品 goodsType = 1 */
  AUCTION: [1, 'auction'],
  /** 会员卡商品 goodsType = 20 */
  MEMBER_CARD: [20, 'memberCard'],
  /** 礼品卡商品 goodsType = 21 */
  GIFT_CARD: [21, 'giftCard'],
  /* 餐饮商品 goodsType = 5 */
  FOOD: [5, 'food'],
  /* 次卡商品 goodsType = 22 */
  TIME_CARD: [22, 'timeCard'],
  /* 有赞会议商品 goodsType = 23 */
  MEETINGS: [23, 'meetings'],
  /* 收银台商品 goodsType = 30 */
  CASHIER_VIRTUAL: [30, 'cashierVirtual'],
  /* 酒店套餐商品 goodsType = 185 */
  HOTEL_PACKAGE: [185, 'hotelPackage'],
  /* 普通服务类商品 goodsType = 40 */
  SERVICE: [40, 'service'],
  /* 卡项商品 goodsType = 71 */
  CARD_ITEM: [71, 'cardItem'],
  /* 混合类型 goodsType = 184 */
  MIXED: [184, 'mixed'],
  /* 外部会员卡商品 goodsType = 201 */
  OUT_MEMBER_CARD: [201, 'outMemberCard'],
  /* 外部直接收款商品 goodsType = 202 */
  OUT_CASH: [202, 'outCash'],
  /* 外部普通商品 goodsType = 203 */
  OUT_COMMON: [203, 'outCommon'],
  /* 外部服务商品 goodsType = 204 */
  OUT_SERVICE: [204, 'outService'],
  /* mock不存在商品 goodsType = 205 */
  MOCK: [205, 'mock'],
  /* 小程序二维码 goodsType = 206 */
  WX_QRCODE: [206, 'wxQrcode'],
  /* 积分充值商品 goodsType = 207 */
  POINT_PURCHASE: [207, 'pointPurchase'],
  /* 付费优惠券商品 goodsType = 208 */
  PAY_COUPONS: [208, 'payCoupons'],
  /* 商品化商品类型 goodsType = 80 */
  COMMERCIAL_SALE_ITEM: [80, 'commercialSaleItem'],
};
export const getGoodsTypeStr = (goodsType: number) =>
  Object.values(goodsTypeEnum).find((item) => item[0] === goodsType)?.[1];
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  getThemeColors({ themeColors = {} as Record<string, any> }): ThemeColors {
    return {
      themeMainBgColor: themeColors['main-bg'],
      themeMainBgAlpha10Color: hexToRgba(themeColors.general, 0.1),
    };
  },
  getGoodsGroupList({ shopCart }): TradeCartGoodsGroup[] {
    return (shopCart?.goodsGroupList || []).map((item) => {
      const goodsList: TradeCartGoods[] = item.goodsList.map((item) => {
        return {
          id: item.goodsId,
          imgUrl: item.imgUrl,
          type: getGoodsTypeStr(item.goodsType),
          payPrice: item.payPrice,
          originPrice: item.originPrice,
          title: item.title,
          alias: item.alias,
          price: item.price,
          weight: item.weight || 0,
          cartId: item.cartId,
          activityId: item.activityId,
          activityType: item.activityType,
          num: item.num,
          storeId: item.storeId,
          skuId: item.skuId,
          sku: item.sku,
          messages: item.messages,
          isSelected: item.checked,
        };
      });
      const { groupActivityInfo = null } = item;
      const activityInfo: CartActivityInfo | null = groupActivityInfo
        ? {
            id: groupActivityInfo.activityId,
            url: groupActivityInfo.activityUrl,
            conditionPrice: groupActivityInfo.conditionPrice || 0,
            desc: groupActivityInfo.activityDesc,
            alias: groupActivityInfo.activityAlias,
            duration: groupActivityInfo.activityDuration,
            meet: groupActivityInfo.meet,
            type: groupActivityInfo.activityType,
            tags: groupActivityInfo.activityTags,
          }
        : null;
      return {
        goodsList,
        activityInfo,
      };
    });
  },
};

// 格式化营销返回活动
export const formatPromotionInfoList = (promotionInfo) => {
  const { activityId, activityType, promotionTag, sendBenefitInfo = {} } = promotionInfo;
  const { sendPointsGifts = [], sendCouponsGifts = [], sendPresentInfo = {} } = sendBenefitInfo;
  const newCouponList = sendCouponsGifts.map((item) => {
    const { couponTemplate = {} } = item;
    return {
      ...couponTemplate,
      extraInfo: couponTemplate.extra,
      id: item.couponTemplateId,
      num: item.num,
      // 购物车指定营销活动处要求优惠券不展示描述
      description: '',
    };
  });
  const umpSendPromotionInfo = {
    activityId,
    activityType,
    coupons: newCouponList.filter((item) => item.activityTypeGroup === 1),
    couponsCode: newCouponList.filter((item) => item.activityTypeGroup === 2),
    score: sendPointsGifts[0]?.num,
    presents: sendPresentInfo.allPresents,
    canChoosePresentNum: sendPresentInfo.choosePresentNum,
  };
  return {
    activityId,
    includeActivityType: activityType,
    promotionTag,
    umpSendPromotionInfo,
  };
};
