import { buildUrl } from '@youzan/tee-biz-util';
import { url, object } from '@youzan/tee-util';
import Config from './config';

// 赠送时长
function getGivingDuration(givingDuration, currnetSkuId) {
  const giveawayTypeEnum = ['赠送$1天', '赠送$1课时'];
  if (Array.isArray(givingDuration) && givingDuration.length) {
    return givingDuration
      .filter(({ skuId }) => skuId === currnetSkuId)
      .map((item) => {
        const { giveawayType, number } = item;
        const des = giveawayTypeEnum[giveawayType - 1];
        return des.replace('$1', giveawayType === 2 ? Number(number / 100).toFixed(2) : number);
      })
      .join(' ');
  }
  return '';
}

function formatHotelDate(goodsDate = '') {
  const dateArr = goodsDate.split('-');
  return dateArr.length === 3 ? `${dateArr[1]}月${dateArr[2]}日` : '';
}

function getGoodsUrl(item, order, orderBizExtra) {
  const { isUsingNewSnapshot = false } = orderBizExtra;
  const { goodsInfo = {} } = item;
  let goodsUrl = buildUrl(`/showcase/goods?alias=${goodsInfo.alias}`, 'wap', order.kdtId);

  const isCourseGoods = goodsInfo.courseGoodsMark && goodsInfo.courseGoodsMark !== 'null';
  // 课程商品链接
  if (isCourseGoods) {
    goodsUrl = buildUrl(
      `/wscvis/edu/prod-detail?alias=${goodsInfo.alias}&kdt_id=${order.kdtId}`,
      'h5',
      order.kdtId
    );
  } else if (item.snapShot && !isUsingNewSnapshot) {
    // 商品快照
    goodsUrl = buildUrl(
      `/showcase/goodsSnapRedirect/urlByOrder?order_no=${order.orderNo}&alias=${goodsInfo.alias}&goods_id=${item.goodsId}&snap_key=${item.snapShot}`,
      'wap',
      order.kdtId
    );
  }

  // 批发商品链接
  if (order.isWholesaleOrder) {
    goodsUrl = buildUrl(`/wscgoods/wholesale_detail/${goodsInfo.alias}`, 'h5', order.kdtId);
  }

  let disableGoodsUrl = false;
  // 收银台订单，无商品页跳转
  disableGoodsUrl = disableGoodsUrl || order.orderType === 6;
  // 佣金订单，无商品页跳转
  disableGoodsUrl = disableGoodsUrl || order.buyWay === 21;
  // 知识付费订单屏蔽跳转
  disableGoodsUrl = disableGoodsUrl || order.orderType === 75;
  if (disableGoodsUrl) {
    goodsUrl = 'javascript:;';
  }
  return goodsUrl;
}

function findGoodsPackages(itemId, goodsPackages) {
  let pack;
  if (goodsPackages && goodsPackages.length > 1) {
    for (let i = 0; i < goodsPackages.length; i++) {
      if (goodsPackages[i].itemId === itemId) {
        pack = goodsPackages[i];
        break;
      }
    }
  }
  return pack;
}

function findPeriodDetail(itemId, periodDetail) {
  let period;
  if (periodDetail && periodDetail.length > 1) {
    for (let i = 0; i < periodDetail.length; i++) {
      if (periodDetail[i].itemId === itemId) {
        period = periodDetail[i];
        break;
      }
    }
  }
  return period;
}

/**
 * 周期购订单商品信息处理
 */
function formatPeriodBuyItem(item, goodsPackages, periodDetail) {
  return {
    packageData: findGoodsPackages(item.itemId, goodsPackages),
    periodDetail: findPeriodDetail(item.itemId, periodDetail),
  };
}

/**
 * 处理商品信息的退款逻辑
 */
function formatItemRefund(item, order, isKosTokenVerified) {
  const safeRefund = item.safeRefund || {};

  if (Object.keys(safeRefund).length > 0) {
    const itemIdParams = {
      order_no: order.orderNo,
      item_id: item.itemId,
      kdt_id: order.kdtId,
    };

    let safeRefundPathname = null;
    if (safeRefund.buttonType === 2) {
      safeRefundPathname = '/trade/refund/process';
    } else if (safeRefund.buttonType === 3) {
      safeRefundPathname = '/trade/safe/notSupport';
    } else if (safeRefund.buttonType === 4) {
      safeRefundPathname = '/trade/safe/apply';
    } else {
      safeRefundPathname = '/trade/safe/info';
    }
    const safeRefundButtonUrl = buildUrl(
      url.args.add(safeRefundPathname, itemIdParams),
      'trade_youzan',
      order.kdtId
    );

    return {
      refundText: safeRefund.buttonText,
      refundUrl: safeRefundButtonUrl,
      showRefund: !!(safeRefund.buttonType && !isKosTokenVerified),
    };
  }
  return null;
}

/**
 * 格式化商品信息
 * @param {*} itemInfos
 * @param {*} order
 * @param {*} orderBizExtra
 * @param {*} goodsPackages
 * @param {*} periodDetail
 * @param {*} givingDuration
 * @param {*} isMultiPeriodBuy
 * @param {*} isKosTokenVerified
 */
export function parseItemInfo(
  itemInfos,
  order,
  orderBizExtra,
  goodsPackages = [],
  periodDetail = [],
  givingDuration,
  isMultiPeriodBuy,
  isKosTokenVerified
) {
  const items = itemInfos.map((item, index) => {
    const itemInner = {};
    const isPaidContentOrder = item.goodsType === 31;
    const isSamePrice = item.unitPrice === item.originUnitPrice;
    const controlButton = item.controlButton || {};
    const extra = item.extra || {};
    const usedPro = extra.USED_PRO || {};
    /* const isMarsVipPrice =
      extra.BIZ_ITEM_ATTRIBUTE && // 精选商品会员专享价
      extra.BIZ_ITEM_ATTRIBUTE.USE_SPOTLIGHT_SELF_SHOP_VIP &&
      extra.BIZ_ITEM_ATTRIBUTE.USE_SPOTLIGHT_SELF_SHOP_VIP === '1'; */
    const isNewHotel =
      extra.BIZ_ITEM_ATTRIBUTE &&
      extra.BIZ_ITEM_ATTRIBUTE.NEW_HOTEL_GOOD &&
      extra.BIZ_ITEM_ATTRIBUTE.NEW_HOTEL_GOOD === '1';

    itemInner.id = item.itemId + '' + index; // 满减送商品时会出现两个同样商品情况
    itemInner.itemId = item.itemId;
    itemInner.goodsType = item.goodsType;
    itemInner.weight = extra.WEIGHT;
    itemInner.goodsId = item.goodsId;
    itemInner.goodsInfo = item.goodsInfo;
    itemInner.title = item.goodsInfo.shortTitle || item.goodsInfo.title || '';
    itemInner.goodsUrl = getGoodsUrl(item, order, orderBizExtra); // 商品链接
    itemInner.imgUrl = item.goodsInfo.imgUrl || '';
    itemInner.payPrice = item.unitPrice;
    // 商品原价（赠品，秒杀，限时折扣，降价拍，会员折扣，拼团等）
    itemInner.originPrice =
      ((controlButton.isPresent ||
        controlButton.isSeckill ||
        controlButton.isTimelimitedDiscount ||
        controlButton.isAuction ||
        controlButton.isCustomerDiscount ||
        (isPaidContentOrder && !isSamePrice)) &&
        item.originUnitPrice) ||
      null;
    itemInner.pointsPrice = item.goodsInfo.pointsPrice;
    itemInner.num = item.num || 0;
    itemInner.sku = item.sku;
    itemInner.skuId = item.skuId;
    itemInner.message = item.buyerMemo || {}; // 商品留言
    itemInner.isShipped = !!item.isShipped;

    itemInner.tags = [];
    controlButton.isPreSale && itemInner.tags.push(Config.tag.presale); // 预售商品
    orderBizExtra.isSelfFetch && itemInner.tags.push(Config.tag.selfFetch); // 自提
    controlButton.isPeriodBuy && itemInner.tags.push(Config.tag.periodBuy); // 周期购
    order.activityType === 20 && itemInner.tags.push(Config.tag.fCode); // F码专享
    order.activityType === 21 && itemInner.tags.push(Config.tag.bargain); // 砍价
    const gdTag = getGivingDuration(givingDuration, item.skuId);
    gdTag && itemInner.tags.push({ text: gdTag, type: 'danger', plain: true }); // 赠送时长
    item.isUseFissionUmp && itemInner.tags.push(Config.tag.inSourcing); // 内购价
    controlButton.isSeckill && itemInner.tags.push(Config.tag.seckill); // 秒杀
    controlButton.isPresent && itemInner.tags.push(Config.tag.present); // 赠品
    controlButton.isTimelimitedDiscount && itemInner.tags.push(Config.tag.timelimitedDiscount); // 限时折扣
    controlButton.isAuction && itemInner.tags.push(Config.tag.auction); // 降价拍
    controlButton.isCustomerDiscount && itemInner.tags.push(Config.tag.customerDiscount); // 会员折扣
    controlButton.isCrossBorder && itemInner.tags.push(Config.tag.crossBorder); // 海淘
    controlButton.isUseGoodsExchangeCoupon && itemInner.tags.push(Config.tag.exchangeCoupon); // 兑换券
    +usedPro.activityType === 24 && itemInner.tags.push(Config.tag.plusBuy); // 加价购

    // 活动预定 & 活动展会
    itemInner.isReserves = [14100, 14200].includes(+usedPro.activityType);

    itemInner.deliveryTime =
      (item.controlExtra && item.controlExtra.preSale && item.controlExtra.preSale.preSaleDate) ||
      ''; // 发货时间，只有预售显示
    itemInner.showDeliveryTime = itemInner.deliveryTime && controlButton.isPreSale; // 显示发货时间
    itemInner.bookDate = item.goodsInfo.goodsDate; // 酒店订单 入住日期等
    itemInner.goodsDate = formatHotelDate(item.goodsInfo.goodsDate || ''); // 酒店订单 价格明细
    itemInner.showPostpone = order.isShowPostponeShip; // 是否显示顺延配送按钮
    itemInner.postponeDeliveryUrl = order.isShowPostponeShip ? item.postponeDeliveryUrl : '';
    itemInner.cancelPostponeDeliveryUrl = order.isShowPostponeShip
      ? item.cancelPostponeDeliveryUrl
      : '';
    itemInner.tariffPay = item.tariffPay;
    // null表示不是海淘订单，true表示含税，false表示不含税
    itemInner.tariffTag = item.tariffTag;
    itemInner.refundOrderItem = item.refundOrderItem || {}; // 差价信息
    itemInner.processInfo = item.processInfo; // 制作工序

    // 新酒店商品信息
    itemInner.isNewHotel = isNewHotel;
    // {"hotelName":"酒店名称","roomTypeName":"房型名称","saleProjectName":"销售方案名称"}
    itemInner.newHotelInfo = orderBizExtra.hotelVO || {};
    itemInner.properties = extra.GOODS_PROPERTY || []; // 商品属性

    /* #ifdef web */
    // 先囤后约订单
    itemInner.hasReservedOrders = item.hasReservedOrders;
    /* #endif */

    // 多周期购物流与配送处理
    if (isMultiPeriodBuy) {
      Object.assign(itemInner, formatPeriodBuyItem(item, goodsPackages, periodDetail));
    }
    // 处理退款信息
    Object.assign(itemInner, formatItemRefund(item, order, isKosTokenVerified));
    return itemInner;
  });

  return items;
}

/**
 * 格式化先囤后约券商品参数信息
 */
export function formatStockCouponGoods(goods, kdtId, forOrderExtra) {
  try {
    const {
      couponGoodsRelatedInfo: { goodsId, relatedFirstCouponId: couponId, goodsAbnormalInfo, skuId },
      goodsInfo: { alias: goodsAlias },
      itemId,
      num,
      outItemSnapKey,
      extra,
    } = goods[0];
    const orderTag = object.get(forOrderExtra, 'ATTR_RETAIL_STORE_THEN_RESERVE_TAG', '');
    const xhsRelatedAlias = object.get(extra, 'ATTR_RETAIL_COUPON_GOODS_RELATED_GOODS.alias', '');
    const xhsGoodsAlias = orderTag === '0' && xhsRelatedAlias ? xhsRelatedAlias : goodsAlias;
    const hasStockCouponGoods = goodsId && skuId;
    if (!hasStockCouponGoods) return null;
    return {
      canReserveNum: goods.reduce((pre, cur) => {
        pre += cur.canReserveNum || 0;
        return pre;
      }, 0),
      couponId,
      goodsId,
      goodsAlias,
      skuId,
      hasError: !!goodsAbnormalInfo,
      errorMsg: goodsAbnormalInfo,
      kdtId,
      itemId,
      num,
      outItemSnapKey,
      xhsGoodsAlias,
    };
  } catch {
    return null;
  }
}
