<template>
  <view
    :class="['t-coupon', disabled ? 't-coupon--disabled' : '', noClick ? 't-coupon--noClick' : '']"
    v-if="couponInfo"
  >
    <coupon-card
      :coupon="couponInfo"
      :is-super="couponInfo.isVip"
      :disabled="disabled"
      :vip-tag-text="vipTagText"
      @coupon-click="onClick"
      :custom-style="customStyle"
    >
      <view v-if="coupon.id < 0" class="t-coupon__virtual-tag" :style="vts" slot="coupon-tag">
        {{ coupon.extraInfo.CARRIER_TPL_NAME }}
      </view>
      <view
        v-if="couponLabel.hasLabel"
        class="t-coupon__label"
        :style="couponLabelStyle"
        slot="coupon-tag"
        >{{ couponLabel.label }}</view
      >
      <van-checkbox
        v-if="!disabled && canCheck"
        :size="18"
        :value="chosen"
        class="t-coupon__corner"
        :checked-color="checkedColor"
        slot="coupon-action"
      />
      <view slot="coupon-valid" v-if="couponInfo.valid">
        <view class="t-coupon__valid">
          {{ couponInfo.valid }}
        </view>
      </view>
      <view
        class="t-coupon__corner--tag"
        v-if="cornerTag"
        :style="overlayingTagStyle"
        slot="coupon-label"
        >{{ cornerTag }}</view
      >
      <view class="t-coupon__right-num" v-if="needNum" slot="coupon-label">x{{ coupon.num }}</view>
      <view v-if="showAdditional" slot="coupon-additional" class="t-coupon__additional">
        <view :key="item" v-for="item in additional" class="t-coupon__description">
          {{ item }}
        </view>
      </view>
      <view slot="coupon-title" v-if="couponInfo.singleTitle">
        <view class="t-coupon__title">
          {{ couponInfo.title }}
        </view>
      </view>
    </coupon-card>
  </view>
</template>
<script>
import hexToRgba from '@youzan/utils/string/hexToRgba';
import Checkbox from '@youzan/vant-tee/dist/checkbox/index';
import CouponCard from '@youzan/wsc-tee-goods-common/components/promotion-block/promotion-pop/components/CouponCard.vue';

import { formatDate, COUPON_TYPE, GUIDE_TYPE } from './utils';

export default {
  components: {
    'van-checkbox': Checkbox,
    'coupon-card': CouponCard,
  },
  props: {
    coupon: Object,
    chosenCoupons: Array,
    disabled: Boolean,
    currency: {
      type: String,
      default: '¥',
    },
    color: String,
    diyColor: String,
    priceColor: String,
    canCheck: {
      type: Boolean,
      default: true,
    },
    needNum: {
      type: Boolean,
      default: false,
    },
    noClick: {
      type: Boolean,
      default: false,
    },
    vipTagText: String,
    couponOverlyingSuperposedTotalNum: Number,
  },

  computed: {
    couponInfo() {
      const { valueDesc, unitDesc, condition, name, extraInfo = {} } = this.coupon;
      return {
        ...this.coupon,
        headValue: valueDesc || '',
        unit: unitDesc,
        condition,
        title: name,
        valid: this.validPeriod,
        isVip: [GUIDE_TYPE.FREE, GUIDE_TYPE.PAID].includes(
          +extraInfo.optimalApplicableLimitIdentity
        ),
      };
    },
    chosen() {
      return !!this.chosenCoupons?.find((coupon) => coupon.id === this.coupon.id);
    },

    customStyle() {
      if (!this.couponInfo.isVip) {
        return `background-color: unset;${this.diyColor ? ` color: ${this.diyColor}` : ''}`;
      }
    },
    canChosenCouponsOverlying() {
      return this.chosenCoupons.some((coupon) => coupon.canOverlying);
    },
    cornerTag() {
      const { length: chosenCount } = this.chosenCoupons;
      return this.canChosenCouponsOverlying &&
        chosenCount < (this.couponOverlyingSuperposedTotalNum || 3) &&
        !!this.coupon.canOverlying &&
        !this.chosen
        ? '可叠加'
        : '';
    },
    extraDescription() {
      return this.canChosenCouponsOverlying && !this.coupon.canOverlying && !this.chosen
        ? '此券暂不可和已勾选的券叠加'
        : '';
    },
    validPeriod() {
      const { coupon } = this;
      if (!coupon) {
        return '';
      }
      const validPeriodCopywriting = coupon.extraInfo?.validPeriodCopywriting;

      if (validPeriodCopywriting) return validPeriodCopywriting;
      if (!coupon.startAt && !coupon.endAt) return '';

      return formatDate(coupon.startAt) + ' - ' + formatDate(coupon.endAt);
    },
    couponLabel() {
      const { extraInfo = {} } = this.coupon || {};
      const { externalPlatformType = '' } = extraInfo;
      const label = COUPON_TYPE[externalPlatformType];

      return {
        hasLabel: externalPlatformType && label,
        label,
      };
    },
    overlayingTagStyle() {
      return `color: ${this.color}; border-color: ${this.color}`;
    },
    couponLabelStyle() {
      return `color: ${this.priceColor}; background-color: ${this.priceColor}38`;
    },
    vts() {
      return `color: ${this.color}; background-color: ${hexToRgba(this.color, 0.1)}`;
    },

    additional() {
      if (this.disabled) {
        if (this.coupon.reason) {
          return [this.coupon.reason];
        }
        return [];
      }
      const descList = [];
      if (this.coupon.description) {
        descList.push(this.coupon.description);
      }
      if (this.extraDescription) {
        descList.push(this.extraDescription);
      }
      return descList;
    },

    showAdditional() {
      return this.additional.length > 0;
    },

    checkedColor() {
      return this.couponInfo.isVip ? '#674531' : this.color;
    },
  },
  methods: {
    onClick() {
      this.$emit('onChange', this.coupon.id);
    },
  },
};
</script>
<style lang="scss" scoped>
.t-coupon {
  margin: 0 12px 12px;
  overflow: hidden;
  position: relative;
  width: calc(100% - 24px);
  border-radius: var(--theme-radius-card, 8px);
  background-color: #fff;

  &:active {
    background-color: #f2f3f5;
  }

  &--noClick,
  &--disabled {
    &:active {
      background-color: #fff;
    }
  }

  &__label {
    position: absolute;
    left: 0;
    top: 0;
    padding: 1px 8px;
    border-radius: var(--theme-radius-tag, 4px) 0 var(--theme-radius-tag, 4px) 0;
    font-size: 10px;
    line-height: 14px;
  }

  &__valid {
    color: #969799;
  }

  &__title {
    font-size: 14px;
    color: #323233;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 限制显示2行，超出部分省略号 */
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; /* 可选，防止长单词溢出 */
  }

  &__corner {
    position: absolute;
    top: 0;
    right: 20px;
    bottom: 0;
    display: flex;
    align-items: center;
  }

  &__description {
    padding: 8px 12px;
    font-size: 12px;
    color: #323233;
  }

  &__corner--tag {
    position: absolute;
    top: 8px;
    right: -16px;
    width: 64px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    transform: rotate(45deg);
    text-align: center;
    font-size: 12px;
    opacity: 0.5;
  }

  &__right-num {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 5px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    background-color: var(--ump-tag-bg, rgba(238, 10, 36, 0.1));
    border-top-right-radius: var(--theme-radius-tag, 4px);
    border-bottom-left-radius: var(--theme-radius-tag, 4px);
    color: var(--ump-icon, #ee0a24);
  }

  &__virtual-tag {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    height: 16px;
    padding: 0 8px;
    font-size: 10px;
    line-height: 16px;
    border-radius: 8px 0;
  }
  &__additional {
    border-top: 1px dashed #ebedf0;
  }
}
</style>
