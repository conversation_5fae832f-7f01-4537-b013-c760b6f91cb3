<template>
  <view class="goods-list" :style="themeCSS">
    <!-- 支付优化复购券 -->
    <view
      v-if="!isFxZpp && canUseTradeUmpV1 && hasBuyAgainCoupon && !isKsApp"
      class="buy-again-coupon-opt"
      @click="goToBuyAgainCoupon"
    >
      <van-icon
        name="coupon"
        color="var(--ump-icon, #323233)"
        size="18"
        style="margin-right: 8px; display: flex; align-items: center"
      />
      <view class="main-text">
        领取<view class="coupon-desc">{{ buyAgainCouponPriceTextNew }}</view
        >{{ buyAgainCouponActionDescNew }}
      </view>
      <view class="coupon-btn">{{ buyAgainCouponActionTextNew }} </view>
    </view>
    <!--酒店商品-->
    <view v-if="isHotel">
      <cap-order-goods-hotel-card
        :item-start="itemList[0]"
        :item-end="itemList[itemList.length - 1]"
        :hotel-recipients="hotelRecipients"
        :buyer-phone="buyerPhone"
        :book-days="itemList.length"
        @goodsAction="doGoodsAction"
      />
    </view>
    <!--普通商品-->
    <view v-else>
      <view v-for="(item, index) in formatedGoods" :key="index" class="goods">
        <van-card
          :num="item.isInvalid || isWscSingleStore ? '' : item.num"
          link-type="navigate"
          :tag="item.imgTag.text || ''"
          :price="isWscSingleStore ? '' : item.price"
          :points-price="item.pointsPrice"
          :points-name="pointsName"
          :origin-price="isWscSingleStore ? '' : item.originPrice"
          :style="themeStyle"
          custom-class="goods-thumb"
        >
          <view slot="thumb" class="cart__img">
            <image
              v-if="item.thumb"
              :src="item.thumb"
              mode="aspectFit"
              class="t-card__img"
              @load="onGoodsImgLoad(index)"
              @click="onThumbClick(item)"
            />
            <view class="goods-invalid" v-if="item.isInvalid">
              <view class="goods-invalid-inner">
                <view>失效</view>
                <view>赠品</view>
              </view>
            </view>
          </view>
          <view slot="title" class="card__title" @click="onThumbClick(item)">
            <image
              v-if="item.isPrescriptionDrugGoods"
              class="card__title__drug-tag"
              :src="activityTag"
            />
            <!-- eslint-disable-next-line @youzan-open/tee/require-child-tag -->
            <text
              v-if="!isWscSingleStore"
              :class="['card__title__goods-name', item.isInvalid ? 'text-gray' : '']"
              :vhtml="item.title"
            />
            <!-- 微商城单店展示特殊处理 -->
            <view
              v-if="isWscSingleStore"
              class="wsc-single"
              :style="item.isPrescriptionDrugGood ? 'width: 80%' : 'width: 100%'"
            >
              <text :class="['wsc-single__title', item.isInvalid ? 'text-gray' : '']">{{
                item.title
              }}</text>
              <view class="wsc-single__price-container">
                <view
                  v-if="item.realPriceObj.pointsPrice"
                  class="wsc-single__real-price"
                  :style="priceStyle"
                >
                  <text>实付</text>
                  <text class="wsc-single__real-price--big">{{
                    item.realPriceObj.pointsPrice
                  }}</text>
                  <text>{{ item.realPriceObj.pointsName }}</text>
                  <text v-if="item.realPriceObj.realPay">+</text>
                  <text v-if="item.realPriceObj.realPay" class="wsc-single__real-price--big">{{
                    item.realPriceObj.integer || 0
                  }}</text>
                  <text v-if="item.realPriceObj.decimals">.{{ item.realPriceObj.decimals }}</text>
                  <text v-if="item.realPriceObj.realPay">元</text>
                </view>
                <view v-else class="wsc-single__real-price" :style="priceStyle">
                  <text>实付￥</text>
                  <text class="wsc-single__real-price--big">{{
                    item.realPriceObj.integer || 0
                  }}</text>
                  <text v-if="item.realPriceObj.decimals">.{{ item.realPriceObj.decimals }}</text>
                </view>
                <view class="wsc-single__origin-price">
                  <text>￥</text>
                  <text class="wsc-single__origin-price--big">{{
                    item.originPriceObj.integer
                  }}</text>
                  <text v-if="item.originPriceObj.decimals"
                    >.{{ item.originPriceObj.decimals }}</text
                  >
                </view>
                <view class="wsc-single__goods-num">x {{ item.num }}</view>
              </view>
            </view>
          </view>
          <view slot="desc" class="card__desc">
            <view v-if="item.desc" class="card__desc-item"> {{ item.desc }} </view>
            <view v-if="item.formatComboDetail">
              <text-collapse :content="item.formatComboDetail" :rows="3" :alias="item.alias" />
            </view>
            <view class="goods-card__tips">
              <view class="goods-card__tips-item" v-for="tip in item.goodsTips" :key="tip">
                {{ tip }}
              </view>
            </view>
          </view>

          <view slot="tags">
            <view class="card__tags">
              <van-tag
                v-for="(tag, tagIndex) in item.tags"
                :key="tagIndex"
                round
                color="var(--ump-tag-bg, #f2f2ff)"
                text-color="var(--ump-tag-text, #323233)"
                :plain="tag.plain"
                class="card__tag"
              >
                {{ tag.text }}
              </van-tag>
            </view>
          </view>
          <view slot="bottom" v-if="item.isInvalid">
            {{ item.bizExtra.followRefundPresentText }}
          </view>
          <view slot="bottom" v-else>
            <view class="card__tax" v-if="item.taxTips">
              {{ item.taxTips }}
            </view>

            <goods-progress :process-info="item.processInfo" />
            <view v-if="item.isShipped" class="card__title__shipped-tag"> 已发货 </view>
            <view
              v-if="item.refundAmt > 0"
              class="card__seller-refund-record-tips"
              @click="toggleSellerRefundRecordPopup(true, item)"
            >
              <view class="refund-text">商家主动退款：￥ {{ item.refundAmt }}</view>
              <van-icon custom-class="icon" name="arrow" />
            </view>
          </view>

          <view slot="footer" class="card__btn-cell" v-if="!item.isInvalid">
            <view
              class="text-gray refund-tips"
              v-if="item.bizExtra && item.bizExtra.followRefundPresentText"
              >{{ item.bizExtra.followRefundPresentText }}</view
            >
            <view
              v-if="
                !isTTApp &&
                index === 0 &&
                hasBuyAgainCoupon &&
                buyAgainCouponAvailable &&
                !canUseTradeUmpV1
              "
              class="buy-again-coupon"
              @click="goToBuyAgainCoupon"
            >
              <van-icon
                name="coupon"
                color="#EE0A24"
                size="18"
                style="margin-right: 8px; display: flex; align-items: center"
              />
              <view class="main-text">
                领取<text style="color: #ee0a24">{{ buyAgainCouponPriceText }}优惠券</text
                >，再买一单
              </view>
              <view>{{ buyAgainCouponActionText }} </view>
              <van-icon
                name="arrow"
                color="#969799"
                size="14"
                style="display: flex; align-items: center"
              />
            </view>
            <van-button
              v-for="(btn, btnIndex) in item.btns"
              :key="btnIndex"
              :round="btn.round"
              :plain="btn.plain"
              size="small"
              :id="'goods-btn-' + btn.value"
              class="card__btn"
              @click="handleBtnClick(btn.value, item.itemId)"
            >
              {{ btn.text }}
            </van-button>
            <text class="feedback-tips" v-if="item.feedbackTips">{{ item.feedbackTips }}</text>
          </view>
        </van-card>

        <!-- 商品留言弹窗 -->
        <!-- ! 当定制移除goods-info时，this.yz.handleGoodsAction({ type: 'message' }) 会不生效 -->
        <!-- ! 这是已知问题，修复方案是：将 <message-popup /> 独立，用provide widget形式插入到其他必定不会被移除的ext中 -->
        <!-- ! 推荐在 detail-page-setup中增加一个 consume widget:popup 然后插入其中 -->
        <!-- ! 或者通过新增一个 ext:detail-popup 专门插入slot(需要考虑体积问题) -->
        <message-popup
          :message="item.message"
          :show="showMessagePopupItemId === item.itemId"
          :theme-colors="themeColors"
          @close="toggleMessagePopup(item.itemId)"
        />

        <diff-price
          v-if="item.refundOrderItem && item.refundOrderItem.refundFee"
          :refund-order-item="item.refundOrderItem"
        />
      </view>
    </view>
    <!--教育商品-->
    <face-to-face-course-info v-if="order.faceToFaceCourseInfo" :order="order" />
    <!-- 加购弹窗 -->
    <view :class="[showAddToCartSuccess ? 'show-popup' : 'hide-popup']">
      <!-- 这里套一层是希望能在 cart-widget 中的请求回调里判断是否展示，所以要保持组件内先请求 -->
      <van-action-sheet class="add-to-cart-popup" :show="true">
        <view :class="['popup-header', addCartResultTitle === '加购失败' ? 'gray' : '']">
          <van-icon
            :name="addCartResultTitle === '加购失败' ? 'clear' : 'checked'"
            :color="addCartResultTitle === '加购失败' ? '#C8C9CC' : 'var(--icon, #323233)'"
            size="18"
            class="add-to-cart-success"
          />
          {{ addCartResultTitle }}
          <van-icon
            name="cross"
            color="#C8C9CC"
            size="22"
            class="add-to-cart-close"
            @click="closeActionSheet"
          />
        </view>
        <!-- 推荐商品  -->
        <cart-widget />
        <view class="go-to-cart-btn">
          <van-button size="large" @click="redirectToCart" round> 去购物车结算 </van-button>
        </view>

        <!-- 高度站位 -->
        <view style="height: 34px"></view>
      </van-action-sheet>
    </view>
    <!-- 商家主动退款记录 -->
    <seller-refund-record-popup
      :show="sellerRefundRecordPopupConfig.isShow"
      :records="sellerRefundRecordPopupConfig.records"
      @close="toggleSellerRefundRecordPopup(false)"
    />
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Card from '@youzan-open/vant-tee/dist/card/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import ActionSheet from '@youzan/vant-tee/dist/action-sheet/index.vue';
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Button from '@youzan/vant-tee/dist/button/index';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { object } from '@youzan/tee-util';
import CountDown from '@youzan/weapp-utils/lib/countdown';
import TextCollapse from '@youzan/wsc-tee-trade-common/components/TextCollapse';
import { cdnImage, errorToast } from '@youzan/tee-biz-util';
import { checkWscSingleStore } from '@youzan/utils-shop';
import { mapActions, mapState } from '@ranta/store';

import GoodsProgress from './components/GoodsProgress';
import MessagePopup from './components/MessagePopup';
import DiffPrice from './components/DiffPrice';
import FaceToFaceCourse from './components/FaceToFaceCourse';
import HotelCard from './components/HotelCard';

import { formatGoods } from './format';
import { BTN } from './contants';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { requestV2 } from '@youzan/tee-biz-request';

import debounce from '@youzan/weapp-utils/lib/debounce';
/* #ifdef weapp */
// eslint-disable-next-line import/no-duplicates,@youzan-open/tee/no-define-in-branch
import navigate from '@youzan/tee-biz-navigate';
/* #endif */
/* #ifdef web */
// eslint-disable-next-line import/no-duplicates,no-redeclare
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
/* #endif */

const PLATFORM_TAG_MAP = {
  haitao: cdnImage('public_files/3a774609c08dc284f27ba5a64be85fa6.png'),
  periodBuy: cdnImage('public_files/61954b0fdd8319a9c5722f16ca2e31de.png'),
  memberPrice: cdnImage('public_files/70bf2253b3860184cb8adacbe831e946.png'),
  drug: cdnImage('path/to/cdn/dir/isDrugTag_3x.png'),
};

let isTTApp = false;
/* #ifdef web */
({ isTTApp } = window._global?.miniprogram || {});
/* #endif */

export default {
  components: {
    'van-card': Card,
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-action-sheet': ActionSheet,
    'van-icon': Icon,
    'van-tag': Tag,
    'van-button': Button,
    'goods-progress': GoodsProgress,
    'message-popup': MessagePopup,
    'diff-price': DiffPrice,
    'cap-order-goods-hotel-card': HotelCard,
    'face-to-face-course-info': FaceToFaceCourse,
    'text-collapse': TextCollapse,
  },

  data() {
    return {
      isTTApp,
      themeColors: {},
      themeCSS: {},
      goods: [],
      addressInfo: {},
      activityType: 0,
      isShowPostponeShip: false,
      pointsName: '',
      order: {},
      env: {},
      orderBizExtra: {},
      ump: {},
      marketingInfo: {},
      buyAgainCouponAvailable: false,
      buyAgainCouponActionText: '立即领取',
      countDownData: {},
      canUseTradeUmpV1: false,
      updateCouponInfo: null,
      sellerRefundRecordPopupConfig: {
        isShow: false,
        records: [],
      },
      formatedGoods: [],
      shopInfo: {},
      ...mapState(this, [
        'itemList',
        'orderNo',
        'kdtId',
        'sourceInfo',
        'contact',
        'isFxZpp',
        'miniprogram',
        'paymentInfo',
        'isHotel',
        'isTradeComponent3',
        'showRecommend',
        'addCartResultTitle',
        'showAddToCartSuccess',
        'showMessagePopupItemId',
        'orderExtra',
      ]),
    };
  },

  computed: {
    isWscSingleStore() {
      return checkWscSingleStore(this.shopInfo);
    },
    generalColor() {
      return this.themeColors.general || '#EE0A24';
    },
    activityTag() {
      // 如果是处方药就增加处方药的标
      return PLATFORM_TAG_MAP.drug;
    },
    priceStyle() {
      return `color: var(--icon, #323233)`;
    },
    themeStyle() {
      return `--card-price-color: ${this.themeColors.general};`;
    },
    // 复购券内容
    repurchaseCoupon() {
      return this.updateCouponInfo || this.marketingInfo?.repurchaseCoupon || {};
    },
    // 复购券标题内容
    buyAgainCouponPriceTextNew() {
      return this.repurchaseCoupon.desc || '';
    },
    // 复购券是否已经领取
    buyAgainCouponReceived() {
      const { isReceived = 0 } = this.repurchaseCoupon;
      return isReceived;
    },
    // 复购券按钮文案
    buyAgainCouponActionTextNew() {
      const isReceived = this.buyAgainCouponReceived;
      return isReceived ? '立即使用' : '立即领取';
    },
    // 复购券详细文案内容
    buyAgainCouponActionDescNew() {
      const isReceived = this.buyAgainCouponReceived;
      if (!isReceived) return '，下单立享优惠';
      const { day = 999, hour = 0, minute = 0, second = 0 } = this.countDownData;
      // 超过48小时不展示倒计时
      if (day && day > 1 && day !== 999) {
        this.countDown?.stop && this.countDown.stop();
        return '';
      }
      const countDownDesc = `${+day ? `${day}天 ` : ''}${hour}:${minute}:${second}后失效`;
      return countDownDesc;
    },
    /* 复购券金额 */
    buyAgainCouponPriceText() {
      const valueWriting = this.ump?.goodsAvlActivity?.valueCopywriting;
      const unitWriting = this.ump?.goodsAvlActivity?.unitCopywriting;
      return valueWriting + unitWriting;
    },
    hasBuyAgainCoupon() {
      let couponInfo = this.ump?.goodsAvlActivity || {};
      if (this.canUseTradeUmpV1) {
        couponInfo = this.marketingInfo?.repurchaseCoupon || {};
      }
      const showCoupon = couponInfo.alias;
      if (showCoupon) {
        this.ctx.logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '复购券曝光', // 事件名称
          params: {
            component: 'buyAgainCoupon',
            isCoupon: true,
            alias: couponInfo.alias,
            item_type: 'coupon',
            item_id: couponInfo.id,
          }, // 事件参数
        });
      }
      return !!showCoupon;
    },
    buyerPhone() {
      return this.addressInfo.receiverTel || '';
    },

    isSupportMarsVip() {
      return this.env.isSupportMarsVip;
    },

    isYouzanmars() {
      return this.env.isYouzanmars;
    },

    // 线下报名课程
    courses() {
      return object.get(this.order, 'faceToFaceCourseInfo.courses', []);
    },

    // 是否为CRM线下门店订单
    isCrmOfflineOrder() {
      return this.order?.channelType === 100;
    },

    // 酒店入住人信息
    hotelRecipients() {
      return (this.addressInfo.addressExtra && this.addressInfo.addressExtra.recipients) || [];
    },

    // 快手小程序环境订单
    isKsApp() {
      let result = false;
      /* #ifdef web */
      result = window?._global?.miniprogram?.isKsApp;
      /* #endif */
      return result;
    },
  },

  watch: {
    buyAgainCouponReceived(val) {
      val && this.countDownStart();
    },
  },

  created() {
    // 监听推荐商品请求的回调，当推荐商品存在时，展示加购弹窗和推荐列表 title
    mapEvent(this, {
      loadSuccess: (value) => {
        this.store.SET_SHOW_RECOMMEND(value && !!value.length);
      },
    });
    /* #ifdef web */
    ZNB.init({
      kdtId: window._global.kdtId,
    });
    /* #endif */
    mapData(this, [
      'themeColors',
      'themeCSS',
      'addressInfo',
      'ump',
      'marketingInfo',
      'buyAgainCouponAvailable',
      'canUseTradeUmpV1',
      'shopInfo',
      'stockCouponGoods',
      'env',
    ]);

    mapActions(this, ['toggleMessagePopup', 'handleGoodsAction', 'closeActionSheet', 'refund']);

    Tee.setGlobal('detail-goods-widget', this);

    mapData(
      this,
      [
        'goods',
        'activityType',
        'isShowPostponeShip',
        'pointsName',
        'order',
        'orderBizExtra',
        'itemList',
      ],
      {
        callback: () => {
          this.updateFormatedGoods();
        },
      }
    );
  },

  destroyed() {
    this.countDown?.stop && this.countDown.stop();
  },

  methods: {
    onGoodsImgLoad(index) {
      if (index === 0) {
        const { mark } = this.ctx.hummer || {};
        if (mark && mark.log) {
          mark.log({
            tag: 'order-detail',
            scene: ['route'],
          });
        }
      }
    },
    // 分发商品按钮点击事件
    handleBtnClick(type, itemId) {
      this.handleGoodsAction({ type, itemId });
    },

    toggleSellerRefundRecordPopup(isShow = false, goods = null) {
      this.sellerRefundRecordPopupConfig = {
        isShow,
        records: goods?.refundRecord,
      };
      if (isShow) {
        this.ctx.logger &&
          this.ctx.logger.log({
            et: 'click', // 事件类型
            ei: 'merchant_voluntary_refund_click', // 事件标识
            en: '商家主动退款按钮点击', // 事件名称
            pt: 'orderdetail', // 页面类型
            params: {}, // 事件参数
          });
      }
    },

    async goToBuyAgainCoupon() {
      let alias = this.ump?.goodsAvlActivity?.alias;
      let activityId = this.ump?.goodsAvlActivity?.id;
      let couponId = 0;
      if (this.canUseTradeUmpV1) {
        const repurchaseCoupon = this.marketingInfo?.repurchaseCoupon || {};
        alias = repurchaseCoupon.alias;
        activityId = repurchaseCoupon.id;
        couponId = repurchaseCoupon.couponId;
      }
      this.ctx.logger.log({
        et: 'click', // 事件类型
        ei: 'component_click', // 事件标识
        en: '复购券点击', // 事件名称
        params: {
          component: 'buyAgainCoupon',
          isCoupon: true,
          alias,
          item_type: 'coupon',
          item_id: activityId,
        }, // 事件参数
      });
      const hasFetchCoupon =
        this.buyAgainCouponActionText === '立即使用' || this.buyAgainCouponReceived;
      if (hasFetchCoupon) {
        // 新版本中使用统一优惠券使用接口获取跳转链接
        if (this.canUseTradeUmpV1) {
          return this.getCouponRedirectPath({
            couponId: couponId || this.asyncBuyAgainCouponInfo?.couponId,
            groupType: 'card',
          });
        }
        const url = `/wscump/coupon/goodslist?kdt_id=${this.kdtId}&alias=${alias}&type=card&id=${
          this.asyncBuyAgainCouponInfo?.couponId || couponId
        }`;
        /* #ifdef weapp */
        navigate({
          url: `pages/common/webview-page/index?src=${encodeURIComponent(url)}`,
          type: 'navigateTo',
        });
        /* #endif */
        /* #ifdef web */
        Tee.navigate({
          url,
        });
        /* #endif */
      } else {
        try {
          const res = await requestV2({
            path: '/wsctrade/order/detail/getVoucher.json',
            method: 'POST',
            contentType: 'application/json',
            errorMsg: '领取失败',
            withCredentials: true,
            options: {
              rawResponse: true,
            },
            data: {
              alias,
              /** 业务名称 */
              bizName: 'order_detail',
              /** 来源, 可以与业务名称一致 */
              source: 'order_detail',
              /** 无约定，保证唯一即可, 每次请求不同 */
              // requestId: 'order_detail_fission_coupon',
            },
          });
          Toast('领取成功');
          if (this.canUseTradeUmpV1) {
            const { buyAgainCouponPriceTextNew } = this;
            this.updateCouponInfo = {
              ...res.data,
              isReceived: 1,
              endTime: res.data?.validEndTime,
              desc: buyAgainCouponPriceTextNew,
            };
          }
          this.buyAgainCouponActionText = '立即使用';
          this.buyAgainCouponReceived = true;
          this.asyncBuyAgainCouponInfo = res.data;
        } catch (e) {
          errorToast(e, { message: '领取失败' });
          this.hasBuyAgainCoupon = false;
        }
      }
    },

    getCouponRedirectPath(data) {
      return requestV2({
        path: '/wscump/coupon/coupon_use_redirect.json',
        data,
        withCredentials: true,
      })
        .then((res) => {
          /* #ifdef weapp */
          const isHome = res.isSwitchTab;
          Tee.navigate({
            url: res.weappUrl,
            type: isHome ? 'reLaunch' : 'navigateTo',
          });
          /* #else */
          Tee.navigate({ url: res.h5Url });
          /* #endif */
        })
        .catch((err) => {
          errorToast(err, { message: '跳转异常，请稍后重试' });
        });
    },

    onThumbClick(value) {
      /* #ifdef web */

      // 赞拼拼订单暂不支持跳转商品详情 包括小程序和h5
      // 抖音小程序订单不支持跳转详情
      // 门店订单点击不跳转
      if (this.isFxZpp || this.miniprogram?.isTTApp || window._global?.isOfflineOrder) {
        return;
      }

      const { id } = value;
      const item = this.itemList.find((item) => item.id === id) || {};

      const { goodsUrl } = item;
      const realGoodsUrl = this.ctx.process.invoke('handleUrlWithShopAutoEnter', goodsUrl)[0];

      // 线下报名课程、CRM门店订单。点击不跳转
      if (this.courses.length > 0 || this.isCrmOfflineOrder) return;

      if (this.isSupportMarsVip || this.isYouzanmars) {
        navigate({
          // from Web,
          web: {
            type: 'native', // 'znb' | 'safeLink' | 'jumpLink' | 'native', web端的跳转方式
            native: {
              url: realGoodsUrl,
              page: 'web',
            },
          },
        });
        // action.gotoWebview({
        //   url: item.goodsUrl,
        //   page: 'web',
        // });
      } else {
        const { alias } = item.goodsInfo;
        ZNB.getEnv().then((env) => {
          const isAliWebview = env.platform === 'aliapp' || env.platform === 'qqapp';
          const isXhsWebview = env.platform === 'xhsapp';
          const isKsWebview = env.platform === 'ksapp';

          if (isXhsWebview) {
            const jumpAlias = object.get(this.stockCouponGoods, 'xhsGoodsAlias', alias);

            navigate({
              web: {
                type: 'znb',
                znb: {
                  xhsUrl: `/packages/goods/detail/index?alias=${jumpAlias}`,
                },
              },
            });
          } else if (isKsWebview) {
            navigate({
              web: {
                type: 'znb',
                znb: {
                  ksUrl: `/packages/goods/detail/index?alias=${alias}`,
                },
              },
            });
          } else if (isAliWebview) {
            navigate({
              // from Web,
              web: {
                type: 'jumpLink', // 'znb' | 'safeLink' | 'jumpLink' | 'native', web端的跳转方式
                jumpLink: {
                  url: realGoodsUrl,
                },
              },
            });
            // jumpLink(item.goodsUrl);
          } else {
            // 改造，通过safelink跳转，带上bizEnv参数
            navigate({
              // from Web,
              web: {
                type: 'safeLink', // 'znb' | 'safeLink' | 'jumpLink' | 'native', web端的跳转方式
                safeLink: {
                  url: realGoodsUrl,
                },
              },
            });
          }
        });
      }
      /* #endif */
      /* #ifdef weapp */
      const { itemId, url } = value;
      const detail = this.itemList.find((i) => i.itemId === itemId) || {};
      const { isOfflineOrder = false } = this.sourceInfo || {};
      // 线下报名课程、CRM门店订单。点击不跳转
      // 门店订单点击不跳转
      if (this.isCrmOfflineOrder || isOfflineOrder) return;

      Tee.navigate({
        url: this.ctx.process.invoke('handleUrlWithShopAutoEnter', url || detail.goodsUrl)[0],
      });
      /* #endif */
    },

    redirectToCart() {
      let url = '';
      /* #ifdef weapp */
      url = 'packages/goods/cart/index';
      /* #endif */
      /* #ifdef web */
      url = `/wsctrade/cart?kdt_id=${this.kdtId}`;
      /* #endif */
      this.closeActionSheet();
      Tee.navigate({
        url,
      });
    },
    doGoodsAction({ type, id }) {
      this.handleGoodsAction({ type, itemId: id });
    },
    handleTitleClick(item) {
      /* #ifdef web */
      const { goodsUrl } = item;
      goodsUrl &&
        Tee.navigate({
          url: goodsUrl,
          type: 'redirectTo',
        });
      /* #endif */

      /* #ifdef weapp */
      const { url } = item;
      url && Tee.navigate({ url });
      /* #endif */
    },

    /**
     * 判断订单是否由抖音券支付
     *
     * @returns {boolean}
     */
    isPaidByDouyinCoupon() {
      const { assetPayInfos = [] } = this.paymentInfo;

      return assetPayInfos.map((info) => info.payWay).includes('DY_COUPON_PAY');
    },

    countDownStart() {
      const nowTime = Date.now();
      const endTime = this.marketingInfo?.repurchaseCoupon?.endTime || 0;
      this.countDown = new CountDown(endTime > nowTime ? endTime - nowTime : 0, {
        onChange: (timeData, strData) => {
          this.countDownData = strData;
        },
      });
    },

    updateFormatedGoods: debounce(async function () {
      const {
        goods,
        activityType,
        isShowPostponeShip,
        pointsName,
        order,
        orderBizExtra,
        itemList,
        env,
        stockCouponGoods,
        orderExtra,
      } = this;
      let data = formatGoods(
        goods,
        activityType,
        isShowPostponeShip,
        pointsName,
        order,
        orderBizExtra,
        env,
        stockCouponGoods,
        orderExtra
      );
      const { isReserves } = itemList[0] || {};
      data = data.map((item) => {
        const cur = itemList.find((v) => v.itemId === item.itemId);
        item.btns = item.btns.filter((btn) => cur.showRefund || btn.value !== BTN.refund.value);
        // 活动预定相关隐藏加入购物车
        item.btns = item.btns.filter((btn) => {
          if (!isReserves) return true;

          return btn.value !== BTN.addToCart.value;
        });
        if (!!item.refundAmt) {
          this.ctx.logger &&
            this.ctx.logger.log({
              et: 'view', // 事件类型
              ei: 'merchant_voluntary_refund_view', // 事件标识
              en: '商家主动退款按钮曝光', // 事件名称
              pt: 'orderdetail', // 页面类型
              params: {}, // 事件参数
            });
        }

        return item;
      });

      try {
        const { goodsList = [] } =
          (await this.ctx.cloud.invoke('beforeDisableGoodsBtns', {
            goodsList: data.map((item) => {
              const { itemId, btns } = item;
              return {
                itemId,
                renderBtns: btns.map(({ value }) => value),
              };
            }),
          })) || {};
        data = data.map((item) => {
          const curGoods = goodsList?.find((_) => _.itemId === item.itemId);
          if (curGoods?.disableBtns) {
            item.btns = item.btns.filter((_) => !(curGoods.disableBtns || []).includes(_.value));
          }
          return item;
        });
      } catch (e) {
        console.warn(e);
      }

      this.formatedGoods = data;
      this.ctx.data.formatedGoods = data;
    }, 300),
  },
};
</script>

<style lang="scss">
@import '@youzan/tee-biz-common-style/css/mixins/index';

.wsc-single {
  display: flex;
  justify-content: space-between;

  &__title {
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__price-container {
    position: relative;
    flex-shrink: 0;
  }

  &__real-price {
    font-size: 12px;
    font-weight: bold;
    text-align: right;
    &--big {
      font-size: 16px;
    }
  }

  &__origin-price {
    position: absolute;
    top: 20px;
    right: 0;
    font-size: 12px;
    font-weight: bold;
    color: #969799;
    white-space: nowrap;

    &--big {
      font-size: 16px;
    }
  }

  &__goods-num {
    position: absolute;
    top: 40px;
    right: 0;
  }
}

.hide-popup {
  visibility: hidden;
}
.show-popup {
  visibility: visible;
}
.go-to-cart-btn {
  margin: 0 16px;
  .t-button {
    color: var(--main-text, #fff);
    background-color: var(--main-bg, #323233);
    border-color: var(--main-bg, #323233);
  }
}

.goods {
  background-color: #fafafa;
}

.goods + .goods {
  margin-top: 10px;
}

.card__row {
  display: flex;
  font-size: 14px;
  line-height: 20px;
}

.card__title {
  line-height: 20px;
  display: flex;
  align-items: center;
  color: #323233;
  font-size: 14px;
  img {
    float: left;
    padding-top: 4px;
    height: 14px;
    padding-right: 4px;
  }
  &__drug-tag {
    width: 38px;
    height: 14px;
    margin-right: 5px;
  }
}
.card__desc {
  font-size: 12px;
  color: #969799;

  &-item {
    width: 80%;
  }
}

.card__title__goods-name {
  word-break: break-all;
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card__title__shipped-tag {
  color: #faab0c;
  float: right;
}

.card__seller-refund-record-tips {
  width: 100%;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .refund-text {
    flex: 1;
  }
  .icon {
    color: #646566;
  }
}

.goods-card__presale-date {
  font-size: 12px;
  color: #ed6a0c;
  margin-top: 5px;
}

.card__btn {
  display: inline-block;
  margin: 16px 0 0;
  color: #323233;
  border-color: #c8c9cc;
  &:not(:last-child) {
    margin-right: 5px;
  }
}

.feedback-tips {
  float: right;
  font-size: 12px;
  line-height: 17px;
  color: #323233;
}

.card__tags {
  margin-bottom: 10px;
  margin-top: 5px;
}

.card__tag {
  margin-top: 6px;
}

.card__tag:not(:last-child) {
  margin-right: 6px;
}

.card__extra {
  background-color: #fafafa !important;
}

.card__tax {
  font-size: 10px;
  color: '#323233';
}

.cart__img {
  width: 88px;
  height: 88px;
}

.goods-thumb {
  background-color: #fff !important;
  .t-card__origin-price {
    color: #969799;
  }
  .t-card__num {
    color: #323233;
  }
}

.t-c-gray-darker {
  color: #666;
}

.add-to-cart-popup {
  overflow: hidden;
  position: relative;

  .t-action-sheet__content {
    padding: 0 6px;
  }

  .popup-header {
    margin: 5px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #323233;
    font-family: PingFangSC-Medium, sans-serif;
    letter-spacing: 0;
    height: 44px;

    &.gray {
      color: #c8c9cc;
    }
  }

  .go-to-check-out {
    padding: 5px 10px;
    margin-bottom: 8px;
    margin-top: 16px;

    .t-button {
      font-weight: bold;
      height: 40px;
      font-size: 14px;
    }
  }
}

.add-to-cart-success {
  margin-right: 4px;
}

.add-to-cart-close {
  position: absolute !important;
  right: 16px;
  top: 16px;
}

.buy-again-coupon {
  margin: 12px 0 0;
  line-height: 16px;
  text-align: left;
  display: flex;
  align-items: center;

  .main-text {
    display: flex;
    align-items: center;
    flex-grow: 1;
  }
}

.buy-again-coupon-opt {
  margin: 0 16px;
  height: 44px;
  display: flex;
  align-items: center;
  font-size: 12px;

  .main-text {
    display: flex;
    flex: 1;
    color: #323233;

    .coupon-desc {
      font-weight: bolder;
      margin: 0 4px;
    }
  }

  .coupon-btn {
    width: 50px;
    text-align: right;
    color: var(--ump-icon, #323233);
  }
}
.goods-card__tips {
  margin-left: -8px;
  display: flex;
}
.goods-card__tips-item {
  position: relative;
  padding: 0 8px;
  font-size: 12px;
  line-height: 16px;
  color: #ed6a0c;
  margin: 5px 0;
  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 1px;
    display: block;
    width: 1px;
    height: 14px;
    background: #ebedf0;
  }
  &:last-child::after {
    display: none;
  }
}

.goods-invalid {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  .goods-invalid-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
  }
}
.text-gray {
  color: #999;
}
.refund-tips {
  /* #ifdef web */
  position: relative;
  top: 8px;
  /* #endif */
  margin-right: 4px;
  display: inline-block;
}
</style>
