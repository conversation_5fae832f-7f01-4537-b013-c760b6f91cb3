import { requestV2 } from '@youzan/tee-biz-request';

const QUERY_AVL_COUPON_URL = '/wscump/coupon/cart/get-avl-coupon.json';
const SEND_COUPON_URL = '/wscump/coupon/cart/send-coupon.json';

const fetch = (requestConfig) => requestV2({ ...requestConfig, config: {} });

// 获取优惠卷列表
function getAvlCouponList(data) {
  return fetch({
    path: QUERY_AVL_COUPON_URL,
    method: 'GET',
    data,
  });
}

function sendCoupon(data) {
  return fetch({
    path: SEND_COUPON_URL,
    method: 'GET',
    data,
  });
}

function getGuideInfo(data) {
  return fetch({
    path: '/wscump/guide-member/get-guide-info.json',
    method: 'GET',
    data,
  });
}

export { getAvlCouponList, sendCoupon, getGuideInfo };
