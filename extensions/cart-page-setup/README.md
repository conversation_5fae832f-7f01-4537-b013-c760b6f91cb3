# wsc-tee-trade/cart-page-setup

page-setup

## 调试方式

## 存疑

## Widget.Provide

| 名称               | 说明   |
| ------------------ | ------ |
| Skeleton `default` | 骨架屏 |

## Process.Invoke

| 名称            | 说明               |
| --------------- | ------------------ |
| invoke-protocol | 获取个保法授权实例 |

## Process.Define

| 名称         | 说明           |
| ------------ | -------------- |
| authProtocol | 调用个保法授权 |

## Event.Listen

| 名称                | 说明                   |
| ------------------- | ---------------------- |
| updateCartGoodsList | 更新购物车商品列表数据 |

## Event.Emit

| 名称                   | 说明 |
| ---------------------- | ---- |
| ORDER_KEEP:open        | ???  |
| cartGoodsListDidUpdate | ???  |

## Data.Provide

| 名称               | 类型             | 默认值        | 说明                             |
| ------------------ | ---------------- | ------------- | -------------------------------- |
| kdtId              | _number_         | ???           | 店铺 ID                          |
| isDrugShop         | _boolean_        | false         | 是否是医药店铺                   |
| channelId          | _number_         | 0             | ??? 渠道 ID                      |
| shopCart           | _ShopCart_       | ???           | ???                              |
| unavailableItems   | _ICartGoodsVO[]_ | []            | ???                              |
| shopTitle          | _string_         | ''            | ???                              |
| emptyCartPath      | _string_         | ''            | ???                              |
| showShoppingCircle | _number_         | 0             | ???                              |
| title              | _string_         | 自定义标题    | ??? 从 recommend-goods 反向写入? |
| pageSize           | _number_         | 10            | ??? 推荐商品分页显示数量         |
| requestExtraParams | _string_         | 'note,coupon' | ???                              |
| bizName            | _string_         | 'cart'        | ???                              |
| bookKey            | _string_         | 0             | 下单挽留:bookKey                 |
| displayData        | _Object_         | {}            | 下单挽留:显示数据                |
| orderData          | _Object_         | {}            | 下单挽留:订单数据                |
| canSelectPresent   | _boolean_        | false         | 是否展示满减送入口               |
| dataLoaded         | _boolean_        | false         | 核心数据是否加载完成             |
| isAuthProtocol     | _boolean_        | false         | 是否显示个保法授权弹窗           |
