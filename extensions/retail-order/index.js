/* eslint-disable no-undef */
import debounce from '@youzan/weapp-utils/lib/debounce';
import get from '@youzan/weapp-utils/lib/get';
import { checkRetailShop } from '@youzan/utils-shop';
import cloneDeep from '@youzan/utils/object/clone-deep';
import Tee from '@youzan/tee';
import { EXPRESS_TYPE, EXPRESS_VALUE } from './contants';
import { getWarehouseBySupplyMode } from './api';
import SelfFetchShopSecondConfirmDialog from './widgets/SelfFetchShopSecondConfirmDialog.vue';
import * as subscribeController from './controllers/subscribe';
import * as pickUpWayController from './controllers/pickUpWay';
import * as addressController from './controllers/address';
import * as timeController from './controllers/time';
import * as waitTimeController from './controllers/waitingTime';
import * as contactController from './controllers/contact';
import * as dineController from './controllers/dine';
import * as tradeController from './controllers/trade';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';

const dataFns = [pickUpWayController.initData, waitTimeController.initData];
const hookFns = [addressController.initHooks];
const processFns = [
  pickUpWayController.initProcesses,
  contactController.initProcesses,
  tradeController.initProcesses,
];

export default class RetailOrder {
  static widgets = {
    PickUpWay: pickUpWayController.widgets.PickUpWay,
    PickUpWayCard: pickUpWayController.widgets.PickUpWayCard,
    WaitingProcess: waitTimeController.widgets.WaitingProcess,
    GetPhoneButton: contactController.widgets.GetPhoneButton,
    SelfFetchShopSecondConfirmDialog,
    Dine: dineController.widgets.Dine,
  };

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.env.getQueryAsync().then((query) => {
      this.initData(query);
      // 24小时货架场景下需要加载
      if (!(this.data.isRetailShelfPage || this.data.isRetailOrderScene)) return;
      this.initShelfHooks();
      this.initShelfProcesses();

      // trade-buy-core初始化完成后再开始dataWatch，防止trade-buy-core中初始化的data赋值触发此watch
      mapData(
        this,
        {
          coreReady(val) {
            // TODO: coreReady 有一定几率不触发，导致 watch 不生效，后续要排查下
            if (val) {
              this.initShelfWatchers();
            }
          },
        },
        { isSetData: false }
      );
    });
    // h5和小程序下下判断是否是零售店铺
    if (checkRetailShop(this.ctx.data?.shop ?? {}) || this.data.isRetailShelfPage) {
      /* #ifdef weapp */
      this.data.isRetailWeappScene = true; // 是零售小程序场景
      /* #endif */

      // 零售店铺场景需要加载，包括24小时货架、网店小程序、h5
      this.initH5AndWeappHooks();
      // trade-buy-core初始化完成后再开始dataWatch，防止trade-buy-core中初始化的data赋值触发此watch
      mapData(
        this,
        {
          coreReady(val) {
            if (val) {
              this.initH5AndWeappWatchers();
            }
          },
          orderSkuShow(val) {
            if (val) {
              this.initData({});
            }
          },
        },
        { isSetData: false }
      );
    } else {
      this.process.define('callHookAfterFetchShow', (params = {}) => Promise.resolve(params));
      this.process.define('callHookBeforeFetchShow', (params) => Promise.resolve(params));
    }

    mapEvent(this, {
      'skuOrder:reset': () => {
        this.currentExpressType = '';
        this.selfFetchShopId = '';
        this.currentAddressId = '';
      },
    });
  }

  get data() {
    return this.ctx.data;
  }

  get process() {
    return this.ctx.process;
  }

  initData(query) {
    const { retailOrderScene = '', jointId = '', warehouseId = '' } = query;
    const ctxData = this.data;
    // weapp 下判断是否是24小时货架场景
    ctxData.isRetailOrderScene = ctxData.isRetailShelfPage || retailOrderScene === '24hshelf';
    // 添加场景来源
    ctxData.retailOrderScene = ctxData.isRetailShelfPage ? '24hshelf' : retailOrderScene;
    ctxData.jointId = jointId;
    ctxData.warehouseId = warehouseId;
    // 店铺是否为供货模式
    ctxData.isSupplyMode = !!warehouseId;
    // 订单商品总数
    ctxData.goodsNum = 0;
    ctxData.goodsInfoList = [];
    ctxData.showSubscribeGuide = false;

    ctxData.isShowRetailDeliveryAddress = '';

    dataFns.forEach((fn) => {
      if (typeof fn !== 'function') return;
      fn.call(this, this.ctx);
    });
  }

  initShelfProcesses() {
    processFns.forEach((fn) => {
      if (typeof fn !== 'function') return;
      fn.call(this, this.ctx);
    });
  }

  initShelfWatchers() {
    if (this._initShelfWatchers) return;
    this._initShelfWatchers = true;

    this.ctx.watch('prepareData', (data) => {
      pickUpWayController.watchPrepareData.call(this, this.ctx, data);
      contactController.watchPrepareData(this.ctx, data);
    });

    this.ctx.watch('state', (val) => {
      const selfFetchShopId = val.selfFetch?.shop?.kdtId;
      if (selfFetchShopId && selfFetchShopId !== this.selfFetchShopId) {
        this.selfFetchShopId = selfFetchShopId;
        this.initWaitingProgress(this.ctx, { selfFetchShopId });
      }
    });

    this.ctx.watch('currentAddress', (val) => {
      if (this.data.expressType === EXPRESS_TYPE.SELF_FETCH) return;
      const { id, lat, lon } = val || {};
      if (this.data.prepareLoading) return;
      if (id !== this.currentAddressId && lat && lon) {
        this.currentAddressId = id;
        getWarehouseBySupplyMode({
          lat,
          lon,
        }).then((data) => {
          this.data.isSupplyMode = !!data?.warehouse;
          const currentWarehoseId = String(data?.warehouse?.warehouseId || '');
          // 监听地址变化，warehoseId 发生改变时校验配送地址范围
          if (currentWarehoseId !== this.data.warehouseId) {
            const { postage } = this.ctx.data;
            this.debounceSetRetailAddress(postage);
          }
          const needFetch =
            currentWarehoseId &&
            currentWarehoseId !== this.data.warehouseId &&
            this.data.expressType === EXPRESS_TYPE.EXPRESS;
          this.data.warehouseId = currentWarehoseId;
          needFetch && this.ctx.process.invoke('fetchShow');
          this.initWaitingProgress(this.ctx);
        });
      }
    });

    this.ctx.watch('goods', (val) => {
      // TODO: 计算订单商品总数逻辑拷贝于 src/ext-tee-wsc-trade/extensions/trade-buy-goods/SubTotal.vue：count()
      // 过滤掉赠品和换购商品
      const filteredGoods =
        val.list?.filter((item) => {
          if (item.activityType === 24) {
            return true;
          }
          return !item.fromTmpAdded && !item.present;
        }) || [];
      const count = filteredGoods.reduce((prev, item) => prev + item.num, 0) || 0;
      if (count !== this.data.goodsNum) {
        this.data.goodsNum = count;
        this.data.goodsInfoList = filteredGoods.map(({ goodsId, skuId, num, combo }) => {
          if (combo) {
            const cloneCombo = cloneDeep(combo);
            const { comboType, subComboList } = cloneCombo;
            // TODO: 聚合相同id数组
            const comboDesc = subComboList?.map(({ goodsId, skuId, num, groupId }) => {
              return { subComboList: [{ goodsId, skuId, num }], id: groupId };
            });
            return {
              goodsId,
              skuId,
              num,
              comboType,
              combo: comboDesc,
              isCombo: true,
            };
          }

          return { goodsId, skuId, num };
        });
        this.initWaitingProgress(this.ctx);
      }
    });
  }

  initH5AndWeappWatchers() {
    if (this._initH5AndWeappWatchers) return;
    this._initH5AndWeappWatchers = true;
    this.ctx.watch('postage', (val) => {
      if (!val) return;
      const { currentExpressType } = val;
      if (currentExpressType !== this.currentExpressType) {
        const now = this.currentExpressType;
        const next = currentExpressType;
        this.currentExpressType = currentExpressType;
        // 订阅消息用到了currentExpressType
        this.ctx.data.currentExpressType = currentExpressType;
        this.initWaitingProgress(this.ctx, { currentExpressType });
        // 配送方式改变时需要重新校验地址是否超出配送范围
        this.debounceSetRetailAddress(val);

        if ([now, next].every((v) => v === 0 || Boolean(v))) {
          // 在切换配送方式后，清除配送时间脏数据。否则切回同城配送时，参数中 **不会** 传配
          // 送时间但是页面上还遗留上次的选择，导致特殊时段场景下，显示时间和实际价格不匹配
          // 以下操作是为了保证任何无法传时间的场景下（初次进入同城配送，配送方式切回同城），不会遗留上次选择，而是前后端都走默认选择逻辑，那么后端将根据最近时间点/段去计算配送费
          // 这段备注整不明白，但是这段代码会导致配送时间来回跳，且影响后台配送的配置，我先删掉了，有问题再说吧
          // this.resetDeliveryTime();
        }
      }
    });

    this.ctx.watch('address', (address) => {
      if (!address) return;
      if (!this.addressId) {
        this.addressId = address.id;
      } else if (this.addressId !== address.id) {
        // 在切换配送地址后，清除配送时间脏数据。否则切换地址时，参数中的时间配置和现显示的不
        // 是同一个，导致特殊时段场景下，显示配送时间和实际计算的配送价格不匹配
        // 以下操作是为了保证任何无法传时间的场景下（初次进入同城配送，配送方式切回同城），不会遗留上次选择，而是前后端都走默认选择逻辑，那么后端将根据最近时间点/段去计算配送费
        this.resetDeliveryTime();
        this.addressId = address.id;
      }
    });

    this.ctx.watch('state', (val) => {
      // 零售线上商城地址样式
      this.data.isShowRetailDeliveryAddress = !!val.display.localDeliveryPosition;

      // 同城配送特殊时段配送费能力
      this.data.attrRefreshTimeBucket = val.extra.ATTR_REFRESH_TIME_BUCKET === '1';
      this.data.attrDispatchWarehouseIds = val.extra.ATTR_DISPATCHER_WAREHOUSE_IDS;
    });

    this.ctx.watch('prepareData', (data) => {
      addressController.watchPrepareData(this.ctx, data);
      this.setReverseData(data);
    });
  }

  initShelfHooks() {
    hookFns.forEach((fn) => {
      if (typeof fn !== 'function') return;
      fn.call(this, this.ctx);
    });

    this.process.define('callHookBeforeFetchShowByBookKey', (params) => {
      const { jointId } = this.data;
      if (this.data.isRetailOrderScene && jointId) {
        return Promise.resolve({
          ...params,
          usePointDeduction: false,
        });
      }
      return Promise.resolve(params);
    });

    this.process.define('callHookBeforeFetchShow', (params = {}) =>
      Promise.resolve(this.setRetailParams(params))
    );

    this.process.define('callHookBeforeCreateOrder', (params = {}) =>
      Promise.resolve(this.setRetailParams(params))
    );

    this.process.define('callHookBeforeAsyncCreateOrderBookKey', (params = {}) =>
      Promise.resolve(this.setRetailParams(params))
    );
  }

  // h5 和 weapp 场景下都需要加载的 hooks
  initH5AndWeappHooks() {
    this.process.define('callHookBeforeGetDefaultSelfFetch', (params = {}) => {
      // 首次进入时获取默认自提点
      params.firstOneFill = true;
      return params;
    });
    this.process.define('callHookAfterFetchShow', (params = {}) => {
      const deliveryConfig = this.getDeliveryConfig();
      // 区分自提和外送
      return Promise.resolve(
        params.postage.currentExpressType === EXPRESS_VALUE.SELF_FETCH
          ? timeController.setRetailGetSelfFetchTime(this.ctx, params, deliveryConfig)
          : timeController.setRetailGetDeliveryTime(this.ctx, params, deliveryConfig)
      );
    });

    this.process.define('callHookBeforeFetchShow', (params) =>
      this.setRetailSpecialPeriodParams(params)
    );

    this.process.define('callHookBeforeCreateOrder', (params) =>
      this.setRetailSpecialPeriodParams(params)
    );

    subscribeController.initHooks.call(this, this.ctx);
  }

  setRetailSpecialPeriodParams(from = {}) {
    const { extensions } = from;
    const { attrDispatchWarehouseIds, expressType } = this.data;

    if (expressType === EXPRESS_TYPE.EXPRESS) {
      // 硬编码，字符串 "1" 代表当前下单是新版 C 端，需验证是否支持特殊时段费计算
      extensions.ATTR_SUPPORT_TIMESPAN_DELIVERY_FEE = '1';
      if (attrDispatchWarehouseIds) {
        // 用于同城配送，固定初始仓，计算特殊时段费用
        extensions.ATTR_DISPATCHER_WAREHOUSE_IDS = attrDispatchWarehouseIds;
      }
    }
    return from;
  }

  setRetailParams(originParams) {
    if (this.data.isRetailOrderScene) {
      const {
        warehouseId,
        jointId,
        expressType,
        state: { contact },
      } = this.data;
      const { delivery = {}, extensions = {} } = originParams;
      // delivery
      if (expressType === EXPRESS_TYPE.SELF_FETCH) {
        this.ctx.logger.log({
          et: 'click',
          ei: 'switch_pickup_way',
          en: '自提取货方式',
          params: {
            label: this.data.retailPickUpWayName,
            value: this.data.retailPickUpWayValue,
          },
        });
        delivery.pickUpWay = this.data.retailPickUpWayValue;
        const { showName, showPhone, requirePhone } = this.data.retailSelfFetchContact || {};
        const { reset, telephone, userName } = contact;
        /* #ifdef weapp */
        let appointmentInfo = {};
        // 微信点单宝才走这段逻辑，抖音h5不走
        // 只显示手机号但不是必填，没有触发手机号的getRetailPhone和onChangeRetailPhone事件，或者，手机号联系人同时不显示
        if ((!showName && showPhone && !requirePhone && !reset) || (!showName && !showPhone)) {
          appointmentInfo = {
            appointmentPerson: '',
            appointmentTel: '',
          };
        } else if (showName && !showPhone) {
          appointmentInfo = {
            appointmentPerson: userName,
            appointmentTel: '',
          };
        } else if (!showName && showPhone) {
          appointmentInfo = {
            appointmentPerson: '',
            appointmentTel: telephone,
          };
        }
        delivery.selfFetch = {
          ...delivery.selfFetch,
          ...appointmentInfo,
        };
        /* #endif */
      } else if (warehouseId && delivery.expressTypeChoice === EXPRESS_VALUE.CITY_EXPRESS) {
        delivery.dispatcherWarehouseId = warehouseId;
      }

      // extensions
      let bizOrderAttribute;
      if (jointId) {
        try {
          bizOrderAttribute = JSON.parse(extensions.BIZ_ORDER_ATTRIBUTE || '{}');
        } catch (e) {
          bizOrderAttribute = {};
        }
        bizOrderAttribute = {
          ...bizOrderAttribute,
          JOINT_ORDER: '1',
          JOINT_ID: '' + jointId,
        };
        extensions.BIZ_ORDER_ATTRIBUTE = JSON.stringify(bizOrderAttribute);
      }

      // 快送场景进入 增加拓展字段
      const { scene } = Tee.getAppOptions();
      if (+scene === 1242) {
        extensions.ATTR_WX_STORE_KUAISONG = '1';
      }

      return {
        ...originParams,
        delivery,
        extensions,
      };
    }
    return originParams;
  }

  setRetailAddress(postage) {
    if (postage.currentExpressType === EXPRESS_VALUE.SELF_FETCH) return;
    const { isRetailOrderScene, address = {} } = this.ctx.data;
    if (isRetailOrderScene && postage) {
      let { warehouseId } = this.data;
      // 铺货模式下，校验地址时传递 warehouseId 为当前网店 id
      if (!this.data.isSupplyMode) {
        warehouseId = this.ctx.data.kdtId;
      }
      addressController
        .formatRetailAddress({
          currentExpressType: postage.currentExpressType,
          warehouseId,
          address,
        })
        .then((address) => {
          this.ctx.process.invoke('mutateState', (state) => {
            state.address = address;
            return ['address'];
          });
        });
    }
  }

  // 活动预定相关数据
  setReverseData(data) {
    let reserves = {};
    try {
      const reservesEnrollment = get(
        data,
        'orderCreation.extensions.EXHIBITIONRESERVE_ENROLLMENT',
        '{}'
      );
      reserves = JSON.parse(reservesEnrollment);
    } catch (error) {}
    this.data.reserves = reserves;
  }

  // 获取是否开启默认时间切片设置
  getDeliveryConfig() {
    try {
      let deliveryConfig;
      // 配送设置prepare返回不准确，所以优先用confirm返回的
      /* #ifdef weapp */
      const configData = this.data.confirmData || this.data.prepareData || {};
      deliveryConfig = configData.deliveryTimeBucket?.deliveryConfig || {};
      /* #endif */
      /* #ifdef web */
      const confirmData = this.data.confirmData || window._global.prepare;
      deliveryConfig = confirmData.deliveryTimeBucket.deliveryConfig || {};
      /* #endif */
      return deliveryConfig;
    } catch {
      return {};
    }
  }

  resetDeliveryTime() {
    const { startTime, endTime } = this.data.state.delivery;
    if ([startTime, endTime].some(Boolean)) {
      this.ctx.process.invoke('mutateState', (state) => {
        state.delivery = {
          ...state.delivery,
          text: '',
          textWithWeekday: '',
          startTime: '',
          endTime: '',
        };
      });
      // TODO: 这里的 return ['delivery']; 写错了应该放在 mutateState 的回调里面，后续要调整掉
      return ['delivery'];
    }
  }

  initWaitingProgress = waitTimeController.initWaitingProgress;

  // 初始化时配送方式切换可能会导致多次调用，做防抖处理
  debounceSetRetailAddress = debounce(this.setRetailAddress, 500);
}
