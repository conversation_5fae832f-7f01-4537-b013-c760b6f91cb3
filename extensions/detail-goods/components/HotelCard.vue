<template>
  <view class="goods-hotel-card">
    <view class="goods-hotel-card__book-room">
      <van-cell class="goods-hotel-card__room">
        <view slot="title">
          <view @click="openUrl" class="goods-hotel-card__room-title">
            <view class="goods-hotel-card__room-title--p">{{ title }}</view>
          </view>
          <view class="goods-hotel-card__value">
            {{ desc }}
          </view>
        </view>
      </van-cell>
      <!-- 房间预定信息 -->
      <van-cell class="goods-hotel-card__book">
        <view slot="title">
          <view class="goods-hotel-card__value">入住： {{ startDate }}</view>
          <view class="goods-hotel-card__value">离店： {{ endDate }}，共{{ bookDays }}晚</view>
        </view>
      </van-cell>
      <view class="goods-hotel-card__action-container">
        <van-button
          v-if="hasMessage"
          class="goods-hotel-card__message-button"
          @click="onClickMessageButton"
        >
          查看留言
        </van-button>
        <van-button
          v-if="itemStart.showReview"
          class="goods-hotel-card__review-button"
          @click="onClickButtonAction('review')"
        >
          {{ itemStart.reviewText }}
        </van-button>
        <van-button
          v-if="itemStart.showRefund"
          class="goods-hotel-card__refund-button goods-hotel-card__button_red"
          @click="onClickButtonAction('refund')"
        >
          {{ itemStart.refundText }}
        </van-button>
      </view>
    </view>
    <view>
      <van-cell>
        <view slot="title" class="">
          房间数：
          <text class="goods-hotel-card__value">{{ roomNum }}</text>
        </view>
      </van-cell>
      <!-- 酒店房间入住人和联系人电话 -->
      <van-cell v-for="(recipient, key) in hotelRecipients" :key="key">
        <view slot="title" class="">
          入住人{{ key + 1 }}：
          <text class="goods-hotel-card__value">{{ recipient }}</text>
        </view>
      </van-cell>
      <van-cell>
        <view slot="title" class="">
          手机号码：
          <text class="goods-hotel-card__value">{{ buyerPhone }}</text>
        </view>
      </van-cell>
    </view>
    <goods-message-list :show="showMessage" :messages="itemStart.message" @close="onCloseMessage" />
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import Tee from '@youzan/tee';
/* #ifdef weapp */
import navigate from '@youzan/tee-biz-navigate';
/* #endif */

import GoodsMessageList from './GoodsMessageList';
import formatDate from '@youzan/utils/date/formatDate';
import travel from '@youzan/utils/date/travel';
import parseDate from '@youzan/utils/date/parseDate';

export default {
  name: 'cap-order-goods-hotel-card',

  components: {
    'goods-message-list': GoodsMessageList,
    'van-cell': Cell,
    'van-button': Button,
  },

  props: {
    itemStart: {
      type: Object,
      default: () => ({}),
    },
    itemEnd: {
      type: Object,
      default: () => ({}),
    },
    bookDays: {
      type: Number,
      default: 1,
    },
    hotelRecipients: {
      type: Array,
      default() {
        return [];
      },
    },
    buyerPhone: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      showMessage: false,
    };
  },

  computed: {
    isNewHotelOrder() {
      return this.itemStart.isNewHotel;
    },
    newHotelInfo() {
      return this.itemStart.newHotelInfo || {};
    },
    oldTitle() {
      return this.itemStart.title || '';
    },
    newTitle() {
      return this.newHotelInfo.hotelName;
    },
    title() {
      return this.isNewHotelOrder ? this.newTitle : this.oldTitle;
    },
    oldDesc() {
      return this.itemStart.sku
        ? this.itemStart.sku
            .filter((item) => item.v)
            .map((item) => item.v)
            .join(', ')
        : '';
    },
    newDesc() {
      return `${this.newHotelInfo.roomTypeName} ${this.newHotelInfo.saleProjectName}`;
    },
    desc() {
      return this.isNewHotelOrder ? this.newDesc : this.oldDesc;
    },
    oldGoodsUrl() {
      return this.itemStart.goodsUrl || 'javascript:;';
    },
    goodsUrl() {
      return this.isNewHotelOrder
        ? // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
          `https://h5.youzan.com/wscindustry/hotel/detail?kdtId=${this.newHotelInfo.kdtId}&id=${this.newHotelInfo.hotelId}`
        : this.oldGoodsUrl;
    },
    startDate() {
      const goodsDate = this.itemStart.bookDate || '';
      const dateArr = goodsDate.split('-');
      return dateArr.length === 3 ? `${dateArr[1]}月${dateArr[2]}日` : '';
    },
    endDate() {
      const goodsDateStr = this.itemEnd.bookDate || '';
      if (goodsDateStr.length > 6) {
        const goodsDate = parseDate(goodsDateStr, 'YYYY-MM-DD');
        return formatDate(travel(1, goodsDate, 'day'), 'MM月DD日');
      }
      return '';
    },
    roomNum() {
      return this.itemStart.num;
    },
    hasMessage() {
      return this.itemStart.message && Object.keys(this.itemStart.message).length;
    },
  },

  methods: {
    onCloseMessage() {
      this.showMessage = false;
    },
    openUrl() {
      /* #ifdef weapp */
      navigate({
        url: `pages/common/webview-page/index?src=${encodeURIComponent(this.goodsUrl)}`,
        type: 'navigateTo',
      });
      /* #endif */
      /* #ifdef web */
      Tee.navigate({
        url: this.goodsUrl,
      });
      /* #endif */
    },
    onClickMessageButton() {
      this.showMessage = true;
    },

    onClickButtonAction(type) {
      this.$emit('goodsAction', { type, id: this.itemStart.id, message: this.itemStart.message });
    },
  },
};
</script>

<style lang="scss">
$gray-darker: #646566;

.goods-hotel-card {
  &__book-room {
    line-height: 1.5;
  }

  &__room {
    &-title {
      &--p {
        margin-bottom: 8px;
      }
    }
  }

  &__row {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  &__value {
    color: $gray-darker !important;
  }

  &__message-button,
  &__review-button,
  &__refund-button {
    height: 24px;
    padding: 0 5px;
    font-size: 10px;
    line-height: 22px;
  }

  &__action-container {
    margin: 10px 0;
    padding: 0 16px;

    .t-button + .t-button {
      margin-left: 4px;
    }
  }

  &__button_red {
    color: #ed5050;
    border-color: #ed5050;
    background-color: #fff;
  }
}
</style>
