<template>
  <view class="mask-background" :style="customStyle">
    <view @click="jumpOut" class="mask-background__desc"
      >查看更多优惠 <van-icon name="arrow-down" style="margin-left: 4px"
    /></view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon';

export default {
  components: {
    'van-icon': Icon,
  },
  props: {
    customStyle: {
      type: String,
      default: '',
    },
  },

  methods: {
    jumpOut() {
      this.$emit('jump');
    },
  },
};
</script>

<style lang="scss">
.desc {
  font-size: 14px;
  color: #576b95;
  text-align: center;
  padding-bottom: 8px;
}
.mask-background {
  z-index: 100;
  height: 88px;
  width: 100%;
  position: absolute;
  top: 0;
  background-image: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 2%,
    rgba(255, 255, 255, 0.8) 29%,
    #fff 72%
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;

  &__desc {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #646566;
    padding-bottom: 8px;
    text-align: center;
    line-height: 16px;
  }
}
</style>
