<template>
  <view class="goods-info-price__detail">
    <van-cell
      class="goods-info-price__detail__total"
      title="总计"
      title-class="goods-info-price__detail__total--title"
      :value="totalPrice"
    />
    <van-cell
      class="goods-info-price__detail__item-group"
      :style="{
        color: mainColor + '!important',
      }"
    >
      <view v-for="(item, key) in itemList" :key="key" class="goods-info-price__detail--value">
        <view class="goods-info-price__detail__item">
          {{ item.goodsDate }}
        </view>
        <view class="goods-info-price__detail__item goods-info-price__detail__item--right">
          {{ getPayPrice(item) }} <i>{{ getNum(item.num) }}</i>
        </view>
      </view>
    </van-cell>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import { formatTotalPrice } from '@youzan/wsc-tee-trade-common/lib/order-utils/format';

export default {
  name: 'price-detail',

  components: {
    'van-cell': Cell,
  },

  props: {
    price: Number,
    points: Number,
    pointsName: String,
    itemList: {
      type: Array,
      default: () => [],
    },
    mainColor: String,
  },

  computed: {
    totalPrice() {
      return formatTotalPrice(this.price, this.points, this.pointsName);
    },
  },

  methods: {
    getPayPrice(item) {
      return formatTotalPrice(item.payPrice, item.pointsPrice, this.pointsName);
    },

    getNum(num) {
      return 'x' + num;
    },
  },
};
</script>

<style lang="scss">
.goods-info-price__detail {
  &::after {
    border-width: 0;
  }

  &__total {
    .t-cell__value {
      color: #f44;
      text-align: right;
    }
    &--title {
      flex: 0;
      white-space: nowrap;
    }
  }

  &--value {
    display: flex;
    justify-content: space-between;
  }

  &__item-group {
    line-height: 36px;
  }

  &__item {
    display: inline-block;
    i {
      font-style: normal;
      padding-left: 5px;
    }

    &--right {
      float: right;
    }
  }
}
</style>
