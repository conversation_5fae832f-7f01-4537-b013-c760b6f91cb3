<template>
  <van-popup
    :show="showPopup"
    position="bottom"
    round
    :custom-style="customPopupStyle"
    @touchmove.stop
    @click-overlay="closePopup"
  >
    <view class="popup-content">
      <view class="popup-title">
        <text>套餐详情</text>
        <view class="close-icon" @click="closePopup">
          <van-icon name="cross" size="18" />
        </view>
      </view>
      <view class="popup-body">
        <scroll-view scroll-y class="wrapper" ref="wrapper" :scroll-top="scrollTop">
          <view v-for="group in comboDetail" :key="group.id">
            <view class="item" v-for="(item, index) in group.subComboList" :key="index">
              <image class="img" :src="item.thumbUrl" alt="" />
              <view class="right">
                <view class="title">
                  {{ item.title }}
                </view>
                <!-- sku信息 -->
                <view class="sku" v-if="item.skuDescArr || item.propDescArr">
                  <text v-for="(skuItem, i) in item.skuDescArr" :key="i">
                    <text>{{ skuItem.sn }}</text>
                    <text class="theme-color">
                      {{ skuItem.sp }}
                    </text>
                    <text v-if="item.propDescArr.length || i !== item.skuDescArr.length - 1"
                      >;</text
                    >
                  </text>
                  <text v-for="(propItem, i) in item.propDescArr" :key="i">
                    <text>{{ propItem.pn }}</text>
                    <text class="theme-color">
                      {{ propItem.pp }}
                    </text>
                    <text v-if="i !== item.propDescArr.length - 1">;</text>
                  </text>
                </view>
                <view class="bottom">
                  <price class="cart-goods__price theme-color" :price="item.price" />
                  <text class="number">x{{ item.num }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </van-popup>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */

export default {
  components: {
    'van-icon': Icon,
    'van-popup': Popup,
  },
  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */
  data() {
    return {
      comboDetail: [],
      showPopup: false,
      scrollTop: 0,
    };
  },
  computed: {
    customPopupStyle() {
      let bottomHeight = 0;
      /* #ifdef weapp */
      bottomHeight = this.popupBottom;
      if (this.safeBottom) {
        bottomHeight = this.noSafeBottom;
      }
      /* #endif */
      return `bottom: ${bottomHeight}px`;
    },
  },
  methods: {
    openPopup(data) {
      if (data?.groupList?.length) {
        this.comboDetail = data.groupList;
        this.showPopup = true;
        setTimeout(() => {
          this.scrollTop = this.scrollTop === 0 ? 0.01 : 0;
        }, 100);
      }
    },
    closePopup() {
      this.showPopup = false;
    },
  },
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  display: none;
}

.popup-content {
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 487px;
  .popup-title {
    display: flex;
    width: 100%;
    height: 44px;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #323233;
    background-color: #fff;
    .close-icon {
      position: absolute;
      top: 13px;
      right: 12px;
    }
  }

  .popup-body {
    padding: 12px;
    flex: 1;
    overflow: hidden;
    .wrapper {
      height: 100%;
      .item {
        width: 100%;
        height: 121px;
        display: flex;
        padding: 12px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
        &-last-of-type {
          margin-bottom: 0;
        }
        .img {
          width: 96px;
          height: 96px;
          border-radius: 8px;
          flex-shrink: 0;
        }
        .right {
          margin-left: 8px;
          display: flex;
          flex-direction: column;
          // justify-content: space-between;
          position: relative;
          flex: 1;
          .title {
            font-size: 14px;
            line-height: 20px;
            color: #323233;
            // height: 40px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .sku {
            margin-top: 8px;
            font-size: 12px;
            color: #969799;
            height: 18px;
          }
          .bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-top: 10px;
            .cart-goods__price {
              padding-top: 4px;
            }
            .number {
              font-size: 12px;
              color: #969799;
            }
          }
          .theme-color {
            color: var(--general);
          }
        }
      }
    }
  }
}
</style>
