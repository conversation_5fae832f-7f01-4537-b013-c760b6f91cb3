<template>
  <concat-button custom-class="ump-concat-button" :h5-link="paidPromotion.detailUrl">
    <image :src="paidPromotion.imgUrl" mode="aspectFit" class="poster-image" />
  </concat-button>
</template>

<script>
import ConcatButton from './widgets/ConcatButton';

export default {
  components: {
    'concat-button': ConcatButton,
  },
  props: {
    paidPromotion: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" scoped>
.poster-image {
  width: calc(100vw - 24px);
  height: 88px;
  border-radius: 4px;
}

.ump-concat-button {
  display: block !important;
  border-radius: 4px !important;
  margin: 0 !important;
  padding: 0 !important;
  width: calc(100vw - 24px) !important;
  height: 88px !important;
  border: none !important;
}
</style>
