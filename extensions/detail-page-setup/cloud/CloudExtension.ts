import type ExtensionMetadata from '../extension';
import type { ModuleContext } from '@youzan/ranta-tee';
import type { CloudCtx } from '@youzan-cloud/ranta-cloud-util';
import type { OrderInfoData } from '@youzan-cloud/cloud-biz-types';
import { type Instance, cloud, bridge, cloudEvent } from '@youzan/ranta-helper';
import { cloudData, isSameObject } from './utils';
import { setClipboardData, setNavigationBarTitle } from '@youzan/tee-api';
import { mapData } from '@youzan/ranta-helper-tee';
import { IPaymentInfo, SetBottomBtnsDisplay } from './types';

type EnjoyBuyOrderInfo = Record<string, any>;

type ModuleContextCtx<C extends Object> = ModuleContext.Ctx<ExtensionMetadata> & CloudCtx<C>;
type ModuleContextOptions<C extends Object> = ModuleContext.Options<ExtensionMetadata> & {
  ctx: ModuleContextCtx<C>;
};

class SuperExtension<C> {
  ctx: ModuleContextCtx<C>;

  constructor(options: ModuleContextOptions<C>) {
    this.ctx = options.ctx;
  }
}

class CloudExtension<C> extends SuperExtension<C> {
  constructor(options: ModuleContextOptions<C>) {
    super(options);

    this.initCloudData();
  }

  // 初始化开放数据
  private initCloudData() {
    mapData(this as unknown as Instance, ['orderExtra'], {
      callback: () => {
        const { orderExtra } = this.ctx.data;
        const newOpenData = {
          enjoyBuyOrderInfo: cloudData.getEnjoyBuyOrderInfo({ orderExtra }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
    mapData(this as unknown as Instance, ['order'], {
      callback: () => {
        const { order } = this.ctx.data;
        const newOpenData = {
          orderInfo: cloudData.getOrderInfo({ order }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
    mapData(this as unknown as Instance, ['payment'], {
      callback: () => {
        const { payment } = this.ctx.data;
        const newOpenData = {
          paymentInfoV1: cloudData.getPaymentInfo({ payment }),
          preSalePhaseInfo: cloudData.getPreSalePhaseInfo({ payment }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }

  @cloud('orderInfo', 'data')
  orderInfo: OrderInfoData;

  /** **********************************************************************************************
   * ↓↓↓↓↓ Open1.0 Start 以下都是开放1.0时期的产物目前仅做维护，如无必要请勿调整 ↓↓↓↓↓
   ********************************************************************************************** */

  @bridge('preSalePhaseInfo', 'data')
  preSalePhaseInfo: Record<string, any>;

  @bridge('enjoyBuyOrderInfo', 'data')
  enjoyBuyOrderInfo: EnjoyBuyOrderInfo;

  @bridge('paymentInfo', 'data')
  paymentInfoV1: IPaymentInfo;

  @bridge('handleShowBatchRefund', 'process')
  handleShowBatchRefund(isShow: boolean) {
    this.ctx.process.invoke('setBottomBtnsDisplay', {
      batchRefund: isShow,
    });
  }

  @bridge('copyOrderNo', 'process')
  copyOrderNo() {
    const orderNo = this.ctx.data.order.orderNo || '';
    setClipboardData(orderNo);
  }

  @bridge('copyToClipboard', 'process')
  copyToClipboard(content: string) {
    setClipboardData(content);
  }

  @bridge('updatePageTitle', 'process')
  updatePageTitle(title: string) {
    setNavigationBarTitle(title);
  }

  @bridge('updateConfirmReceiveText', 'process')
  updateConfirmReceiveText(content: string) {
    this.ctx.process.invoke('setReceivingContent', content);
  }

  @bridge('setRefundBtnsDisplay', 'process')
  setRefundBtnsDisplay(goodsId: number) {
    const { ctx } = this;
    const newItemList = ctx.data?.itemList?.map((v) => {
      if (v.goodsId === goodsId) {
        return {
          ...v,
          showRefund: false,
        };
      }
      return v;
    });
    ctx.process.invoke('setItemList', newItemList);
  }

  @bridge('showMessage', 'process')
  showMessage(isShow: boolean) {
    this.ctx.process.invoke('setShowMessage', isShow);
  }

  @bridge('setBottomBtnsDisplay', 'process')
  setBottomBtnsDisplay(payload: SetBottomBtnsDisplay) {
    const newBottomBtnsDisplay = payload.type.reduce(
      (prev, key) => {
        return {
          [key]: false,
          ...prev,
        };
      },
      {
        confirmHotel: false,
      }
    );
    if ((payload.confirmHotel ?? '') !== '' && !payload.confirmHotel) {
      newBottomBtnsDisplay.confirmHotel = false;
    }

    this.ctx.process.invoke('setBottomBtnsDisplay', newBottomBtnsDisplay);
  }

  @bridge('onOrderLoadedEvent', 'event')
  onOrderLoadedEvent = cloudEvent<() => void>();

  /* #ifdef weapp */
  @bridge('updateComponent', 'process')
  updateComponent(blockName: 'OrderAddress' | 'OrderStatus', display: boolean) {
    this.ctx.process.invoke('toggleBlockDisplay', { blockName, display });
  }
  /* #endif */
}

export default CloudExtension;
