export function padZero(num) {
  num = '00' + num;
  return num.slice(-2);
}

export function formatDate(timeStamp) {
  const date = new Date(timeStamp * 1000);
  return date.getFullYear() + '.' + padZero(date.getMonth() + 1) + '.' + pad<PERSON>ero(date.getDate());
}

function isObj(x) {
  const type = typeof x;
  return x !== null && (type === 'object' || type === 'function');
}

export function isEmptyObject(obj) {
  return isObj(obj) && !Object.keys(obj).length;
}

export const COUPON_TYPE = {
  10: '抖音券',
  12: '美团券',
};

export const GUIDE_TYPE = {
  FREE: 1,
  PAID: 2,
  CARD: 3,
};
