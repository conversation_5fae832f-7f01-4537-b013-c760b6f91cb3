# @wsc-tee-trade/cart-coupon-bar

购物车领券

![UI呈现](https://img.yzcdn.cn/public_files/866051d28647fe1c3a842b20ae14bd22.png) ![优惠券弹窗](https://img.yzcdn.cn/public_files/ca14445f08784bcb7f492504abc30e56.png)

## 调试方式

打开购物车即可看到

## 存疑

- showCouponList 自己监听自己？

## Widget.Provide

| 名称                | 说明       |
| ------------------- | ---------- |
| CouponBar `default` | 领券状态栏 |
| CouponListPopup     | 优惠券弹窗 |

## Widget.Consume

| 名称          | 说明         |
| ------------- | ------------ |
| UserAuthorize | 用户授权组件 |

## Data.Provide

| 名称       | 类型                     | 默认值 | 说明           |
| ---------- | ------------------------ | ------ | -------------- |
| couponList | _IGoodsAvlActivityDTO[]_ | []     | 有效优惠券列表 |

## Event.Emit

| 名称           | 说明                     |
| -------------- | ------------------------ |
| showCouponList | 触发 显示优惠券弹窗 事件 |

## Event.Listen

| 名称           | 说明                |
| -------------- | ------------------- |
| showCouponList | 显示优惠券弹窗 事件 |

## Lambda.Provide

| 名称 | 说明 |
| ---- | ---- |

## Lambda.Consume

| 名称     | 说明       |
| -------- | ---------- |
| hexToRgb | hex 转 rgb |

## Data.Consume

| 名称 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| kdtId | _number_ | 当前 kdtId | 内部使用 `app.getKdtId()` 获取，实际并没有使用 |
| shopCart | _ICartListVO_ | {} | 购物车商品详情 |
| themeCSS | _string_ | '' | 主题色 |
| themeColors | _ThemeColors_ | {} | 主题色 |
| couponList | _IGoodsAvlActivityDTO[]_ | [] | 有效优惠券列表 |
