<template>
  <view class="diff-price">
    <van-icon custom-class="diff-price__icon" name="info-o" size="12px" color="#ed6a0c" />
    <view slot="title" class="diff-price__title">
      因商品存在重量误差，已退款 ￥{{ totalPrice }}
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

export default {
  components: {
    'van-icon': Icon,
  },

  props: {
    refundOrderItem: Object,
  },

  computed: {
    totalPrice() {
      const { refundOrderItem } = this;
      if (refundOrderItem && refundOrderItem.refundFee) {
        return (refundOrderItem.refundFee / 100).toFixed(2);
      }
      return '';
    },
  },
};
</script>

<style lang="scss">
.diff-price {
  padding: 8px 0 8px 15px;
  background: #fff;
}

.diff-price__title {
  display: inline;
  font-size: 12px;
  margin-left: 4px;
  color: #ed6a0c;
  vertical-align: middle;
}

.diff-price__icon {
  vertical-align: middle;
  line-height: normal;
}
</style>
