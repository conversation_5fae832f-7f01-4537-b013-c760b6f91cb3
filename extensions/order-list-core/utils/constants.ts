import Tee from '@youzan/tee';
import args from '@youzan/utils/url/args';
import get from '@youzan/utils/object/get';
import { cdnImage } from '@youzan/tee-biz-util';
import mapKeysToCameLCase from '@youzan/utils/string/mapKeysToCamelCase';
import api from './api';
import { BeforeCancelOrderPopupToggleReturnConfig } from '../extension';

export const getPointsName = () => {
  return new Promise((resolve) => {
    /* #ifdef web */
    resolve(get(window, '_global.pointsName') || '积分');
    /* #endif */
    /* #ifdef weapp */
    const app = Tee.getApp();
    // @ts-ignore
    app.getPointsName().then(({ pointsName }) => {
      resolve(pointsName || '积分');
    });
    /* #endif */
  });
};

export const DEFAULT_POINTS_NAME = '积分';

// 页面标题配置
export const TITLE_CONF = {
  all: '所有订单',
  topay: '待付款的订单',
  totuan: '待接单的订单',
  tosend: '待发货的订单',
  send: '已发货的订单',
  sign: '已完成的订单',
  toevaluate: '待评价的订单',
};

// CRM 线下订单展示枚举
export const CRM_OFFLINE_TYPE = {
  NONE: 0, // 不展示
  ONLINE: 1, // 线上订单
  OFFLINE: 2, // 线下订单
};

// 页面类型
export const QUERY_PAGE_TYPE = {
  SINGLE: 'single',
  ALL: 'all',
};

export const CPS_CONFIG_KEY = 'cps_goods_recommend_order_list';

let isIvr = false;
/* #ifdef web */
isIvr = args.get('from', location.href) === 'ivr';
/* #endif */

let miniprogram = {};
/* #ifdef web */
miniprogram = get(window, '_global.miniprogram', {});
/* #endif */
export const isAlipayApp = get(miniprogram, 'isAlipayApp', false);
export const isQQApp = get(miniprogram, 'isQQApp', false);
export const isXhsApp = get(miniprogram, 'isXhsApp', false);
export const isKsApp = get(miniprogram, 'isKsApp', false);

export const getDirectBuyAgainBtnConfig = async () => {
  /* #ifdef web */
  return get(window, '_global.directBuyAgainBtnConfig', {});
  /* #endif */
  /* #ifdef weapp */
  let directBuyAgainBtnConfig = {};
  try {
    directBuyAgainBtnConfig = await api.fetchDirectBuyAgainBtnConfig();
  } catch (e) {
    console.error(e);
  }
  return directBuyAgainBtnConfig;
  /* #endif */
};

let openAppConfig = {};
/* #ifdef web */
openAppConfig = mapKeysToCameLCase(get(window, '_global.open_app_config', {}));
/* #endif */
export const PAGE_SIZE = 10;
export const SHARE_OPTIONS = [
  { key: 'wechat', name: '微信', icon: 'wechat' },
  {
    key: 'toutiao',
    name: '抖音',
    icon: cdnImage('public_files/2020/06/08/4998cb9fdc26c1b8ea0fdafddb8e6d94.png'),
  },
  { key: 'copylink', name: '复制链接', icon: 'link' },
  { key: 'haibao', name: '分享海报', icon: 'poster' },
];
export const DEFAULT_ACTIVITY_TAB = {
  type: 'all',
  hasNext: true,
  page: 0,
  pageSize: PAGE_SIZE,
};
export const COUPON_CODE_MAP = {
  161201035: '优惠券活动已失效，无法再领取',
  161201033: '优惠券库存不足，无法再领取',
  161201406: '领取规则调整，该券不可再领',
  161201050: '当前领取人数太多，请稍后再试',
};
export const CHECK_ORDER_DELAY_RECEIVE_STATUS_MAP = {
  TIME_NOT_ENOUGH: 102580009,
  ALREADY_EXTENDED: 102580012,
};
export const ACTIVITY_TYPE = {
  blindBoxBuy: 401, // 盲盒购买订单
  blindBoxVerification: 402, // 盲盒核销订单
};
export const ORDER_STATUS = {
  TOPAY: 'topay',
  SEND: 'send',
  SIGN: 'sign',
  TOSEND: 'tosend',
};

export const DEFAULT_CANCEL_ORDER_POPUP_CONFIG: BeforeCancelOrderPopupToggleReturnConfig = {
  autoAddCart: {
    /* 初始勾选状态，默认勾选 */
    isChecked: true,
    /* 是否禁用，默认允许 */
    isDisabled: false,
    /* 是否展示，默认展示 */
    isShow: true,
  },
};

export const DEFAULT_TAB_LIST = [
  { type: 'all', text: '全部', pageId: 'wsc' },
  {
    type: 'topay',
    text: '待付款',
    pageId: 'wsc',
  },
  {
    type: 'tosend',
    text: '待发货',
    pageId: '1',
  },
  {
    type: 'send',
    text: '待收货',
    pageId: '1',
  },
  {
    type: 'toevaluate',
    text: '待评价',
    pageId: '1',
  },
];

export const CRM_OFFLINE_TEXT = {
  [CRM_OFFLINE_TYPE.NONE]: '',
  [CRM_OFFLINE_TYPE.ONLINE]: '网店订单',
  [CRM_OFFLINE_TYPE.OFFLINE]: '门店订单',
};

export { isIvr, openAppConfig };

// 页面场景 order-list-core以properties方式传入，表示当前是什么场景的页面
export const PAGE_SCENE = {
  RETAIL: 'retail-order-list',
};
