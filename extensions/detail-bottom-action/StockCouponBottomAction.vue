<template>
  <view>
    <view class="stock-coupon-bottom-action-placeholder" />
    <view v-if="show" :class="['stock-coupon-bottom-action', deviceType]">
      <block v-if="useNewReserve">
        <address-list :show="showAddressList" @close="handleClose" />
        <van-popup
          close-on-click-overlay
          position="center"
          :show="isShowToDYPopup"
          @close="isShowToDYPopup = false"
        >
          <image
            style="width: 70vw"
            src="https://img01.yzcdn.cn/upload_files/2024/06/26/FvguiR9TJz7sBj6x2dQPSFfGiIWG.jpeg"
            @click="isShowToDYPopup = false"
          />
        </van-popup>
        <view class="stock-coupon-btns-wrapper">
          <!-- ! 不在抖音或小红书 -->
          <view class="stock-coupon-btns-tips" v-if="!(isTTApp || isXhsApp)">
            <text>请前往{{ platformName }}进行预约 </text>
            <text style="color: #576795; margin-left: 6px" @click="isShowToDYPopup = true"
              >查看使用路径</text
            >
          </view>
          <block v-else-if="current">
            <view class="coupon-title">
              <text class="coupon-title__status">{{ current.status }}</text>
              <text class="coupon-title__time"
                >{{ current.prefix }} <text style="color: var(--main-bg)"> {{ current.time }} </text
                >{{ current.suffix }}</text
              >
            </view>
            <view class="coupon-address">
              <image
                class="coupon-address__icon"
                src="https://img01.yzcdn.cn/upload_files/2024/06/25/Fjq6Iy4D6Hx6FiJbQ3rlaOKCFYDH.png"
              />
              <text class="coupon-address__text">{{ current.detail }}</text>
              <text class="coupon-address__switch" @click="handleOpen"
                >{{ current.switchText }} ></text
              >
            </view>
            <view class="coupon-btns" v-if="current.disabled">
              <van-button round class="stock-coupon-btn small" @click="handleRefund"
                >申请退款</van-button
              >
              <van-button
                disabled
                round
                class="stock-coupon-btn large"
                :loading="loading"
                @click="onClick"
                >{{ mainBtnText }}</van-button
              >
            </view>
            <van-button
              v-else
              class="stock-coupon-btn large"
              round
              size="large"
              :loading="loading"
              @click="onClick"
              >{{ mainBtnText }}</van-button
            >
          </block>
        </view>
      </block>
      <view v-else class="stock-coupon-btns-wrapper">
        <van-button class="stock-coupon-btn colorful" round size="large" @click="onClick">
          预约
        </van-button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Button from '@youzan/vant-tee/dist/button/index';
import Popup from '@youzan/vant-tee/dist/popup/index';
import args from '@youzan/weapp-utils/lib/args';
import api from './api';
import object from '@youzan/utils/object';

const { kdtId, useNewReserve, miniprogram } = window._global;
const { isTTApp, isXhsApp } = miniprogram || {};

export default {
  components: {
    'van-button': Button,
    'van-popup': Popup,
  },
  props: {
    goods: Object,
  },
  data() {
    return {
      isTTApp,
      isXhsApp,
      useNewReserve,
      show: false,
      isShowToDYPopup: false,
      showAddressList: false,
      address: null,
      currentItem: null,
      loading: false,
      order: {},
    };
  },
  computed: {
    mainBtnText() {
      return this.isXhsApp ? '立即兑换' : '预约配送';
    },
    platformName() {
      const {
        originSource: { platform },
      } = object.get(window, '_global.sourceInfo', {});
      return platform === 'dy_mini_program' ? '抖音' : '小红书';
    },
    deviceType() {
      let isIphoneX = false;
      isIphoneX = /iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812;
      return isIphoneX ? 'iPhone-X' : '';
    },
    current() {
      const { address, currentItem } = this;

      if (!address) {
        return {
          status: '请先填写配送地址',
          switchText: '新增地址',
          detail: '配送至配送地址',
        };
      }

      const result = {
        switchText: '切换地址',
      };

      const { city, county, location } = address;
      result.detail = `配送至${city}${county}${location}`;

      const { goodsId, arriveTimeDesc } = currentItem || {};
      result.status = `当前位置${goodsId ? '可配送' : '不可送'}`;
      // 小红书不禁用
      if (!this.isXhsApp) {
        result.disabled = !goodsId;
      }

      if (arriveTimeDesc) {
        const match = arriveTimeDesc.match(/\d+:\d+|\d+月\d+日/);
        if (match) {
          const time = match[0];
          const [prefix, suffix] = arriveTimeDesc.split(time);

          result.time = time;
          result.prefix = '，' + prefix;
          result.suffix = suffix;
        } else {
          result.prefix = arriveTimeDesc;
        }
      }

      return result;
    },
  },
  watch: {
    goods: {
      handler(val) {
        if (val) {
          if (val.hasError) {
            this.show = false;
            if (!this._hasToast) {
              Toast(val.errorMsg);
              this._hasToast = true;
            }
          } else {
            this.show = true;
          }
        }
      },
      immediate: true,
    },
    address(val) {
      val && this.localSale(val);
    },
  },
  beforeCreate() {
    this._hasToast = false;
  },
  created() {
    mapData(this, ['order']);
    this.ctx.process.define('selectAddress', (id) => {
      const { address } = this.ctx.data;
      this.handleSelectAddress(id, address);
    });

    this.ctx.lambdas.onEvent('address-save', (newAddress) => {
      api.getAddress().then((address) => {
        this.handleSelectAddress(newAddress.id, address);
      });
    });
    this.ctx.lambdas.onEvent('address-delete', (id) => {
      if (id === this.address.id) {
        this.initAddressList();
      } else {
        const { address = {} } = this.ctx.data;
        const { list = [] } = address;
        const index = list.findIndex((item) => item.id === id);

        list.splice(index, 1);

        this.ctx.data.address = { ...address, list };
      }
    });
  },
  beforeDestroy() {
    this.ctx.lambdas.offEvent?.('address-save');
    this.ctx.lambdas.offEvent?.('address-delete');
  },
  mounted() {
    ZNB.init({ kdtId })
      .catch(() => {})
      .finally(this.initAddressList);
  },
  methods: {
    handleClose() {
      this.showAddressList = false;
    },
    handleOpen() {
      this.showAddressList = true;
    },
    handleRefund() {
      const { itemId } = this.goods;
      this.ctx.process.invoke('handleGoodsAction', { type: 'refund', itemId });
    },
    handleSelectAddress(id, address) {
      const current = address.list.find((item) => item.id === id);

      this.address = current;
      this.ctx.data.address = { ...address, id, current };
    },
    initAddressList() {
      useNewReserve &&
        api.getAddress().then((address) => {
          this.address = address.current;
          this.ctx.data.address = address;
        });
    },
    async localSale(val) {
      this.loading = true;
      const { num, skuId, goodsId, outItemSnapKey } = this.goods;
      const params = {
        kdtId,
        buyerAddress: val.addressDetail,
        buyerCountyName: val.county,
        buyerCityName: val.city,
        buyerProvinceName: val.province,
        buyerLat: val.lat,
        buyerLng: val.lon,
        buyerId: val.userId,
        buyerCountyId: val.areaCode,
        hasAddress: true, // 这个要不要传
        name: val.userName,
        phone: val.tel,
        needCheckSellerKdt: true, // 过滤出适用店铺
        itemInfoParamList: [
          {
            num,
            itemId: goodsId,
            skuId,
            outItemSnapKey, // 过滤出适用店铺
          },
        ],
        couponId: _global?.itemInfo?.[0]?.goodsInfo?.payCouponId || 0,
        relatedChannel: this.isTTApp ? 'douyin' : 'xhs',
      };

      const result = await api.localSale(params).finally(() => {
        this.loading = false;
      });
      this.currentItem = result || {};
    },
    onClick() {
      if (!this.address) {
        return Toast('请先填写配送地址');
      }
      const { bizType, orderNo } = this.order;
      const { couponId, skuId, goodsAlias, xhsGoodsAlias, canReserveNum } = this.goods;
      let { goodsId, kdtId } = this.goods;
      let addressId;
      if (useNewReserve && !object.isEmpty(this.currentItem)) {
        ({ goodsId, kdtId } = this.currentItem);
        addressId = this.address?.id;
      }

      if (this.isXhsApp) {
        const url = args.add('/packages/goods/detail/index', {
          alias: xhsGoodsAlias,
          sku: 1,
          forceKdtId: kdtId,
        });
        navigate({
          web: {
            type: 'znb',
            znb: {
              xhsUrl: url,
            },
          },
        });
      } else {
        const query = {
          scene: 2, // 后约路径下单场景
          shopAutoEnter: 2, // 强制进店
          kdtId, // 指定进入kdtId
          couponId, // 券id
          skuId, // 商品skuId
          goodsId, // 商品id
          goodsAlias, // 分享需要用
          mode: 1, // 默认外送进入
          addressId,
          isDouYinOrder: ~~(+bizType === 7), // 如果是抖音融合三方订单，bizType = 7
          canReserveNum,
          storeOrderNo: orderNo,
        };
        console.log('🔥 query ===>', query, '<=== 🔥');
        // 之前可以直接用 ttUrl 但 20240701 这天突然不行了 后面有空了看下原因
        const url = args.add('/packages/retail-shelf/shelf/index', query);
        ZNB.navigate({
          ttUrl: url,
          url,
          type: 'reLaunch',
        });
      }
    },
  },
};
</script>

<style lang="scss">
$iphone-x-bottom: 34px;

@mixin iphone-x-after($iphone-x-bottom) {
  content: ' ';
  height: $iphone-x-bottom;
  background: #fff;
  width: 100%;
  display: block;
}
.stock-coupon-bottom-action-placeholder {
  height: 100px;
}
.stock-coupon-bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  background: #fff;

  .stock-coupon-btns-wrapper {
    padding: 20px;
    overflow: hidden;
  }

  .stock-coupon-btns-tips {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFang SC, sans-serif;
    font-size: 14px;
    line-height: 18px;
  }

  &.iPhone-X::after {
    @include iphone-x-after($iphone-x-bottom);
  }
}

.coupon-btns {
  display: flex;
}

.stock-coupon-btn {
  border-color: var(--main-bg);
  height: 39px !important;

  &.small {
    width: 103px;
    margin-right: 21px;
    color: var(--main-bg);
  }

  &.large {
    flex-grow: 1;
    color: var(--main-text);
    background-color: var(--main-bg);
  }

  &.colorful {
    color: var(--main-text);
    background-color: var(--main-bg);
  }
}

.coupon-address {
  display: flex;
  align-items: flex-start;
  margin: 18px 0 13px 0;

  &__icon {
    margin-top: 3px;
    width: 12px;
    height: 12px;
  }

  &__text {
    font-family: PingFang SC, sans-serif;
    font-size: 12px;
    line-height: 18px;
    text-align: left;
    margin: 0 6px;
  }

  &__switch {
    flex-shrink: 0;
    font-family: PingFang HK, sans-serif;
    font-size: 12px;
    line-height: 18px;
    margin-left: auto;
    color: var(--main-bg);
  }
}

.coupon-title {
  font-family: PingFang SC, sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  text-align: left;
}
</style>
