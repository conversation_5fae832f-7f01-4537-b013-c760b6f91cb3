<template>
  <view v-if="show" ref="poster" class="order-share__poster" @click.stop="close">
    <view v-if="visible" class="van-overlay" />
    <view class="order-share__container">
      <view class="order-share__img-container">
        <image v-if="imgLoad" :src="posterUrl" class="order-share__img" @click.stop />
        <image
          v-else
          class="order-share__img order-share__img__placeholder"
          src="https://b.yzcdn.cn/public_files/3e22c11fb1e0e5e4927a017fec1b565b.png"
        />
      </view>
      <view class="order-share__close-container" @click="close">
        <view class="order-share__img-close" />
      </view>
    </view>
    <view class="order-share__word">长按图片保存至相册</view>
  </view>
</template>

<script>
/* *****************************************************
 * 分享能力，目前仅支持web端，如果后续支持weapp之后请将此注释移除
 * 另外，请注意需要移除index.ts中的widget多端编译条件
 ***************************************************** */
// Dependencies
import api from '../utils/api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { errorToast } from '@youzan/tee-biz-util';

export default {
  props: {
    visible: Boolean,
    shareUrl: String,
    orderNo: String,
  },

  data() {
    return {
      show: false,
      posterUrl: '',
      imgLoad: false,
    };
  },

  watch: {
    visible(val) {
      if (val) {
        this.mountNode();
      } else if (this.show) this.$emit('close');
    },
  },

  mounted() {
    if (this.visible) {
      this.mountNode();
    }
  },

  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },

  methods: {
    // 获取分享海报
    doGetPoster(toast, needRetry) {
      this.imgLoad = false;
      const { orderNo, shareUrl } = this;
      api
        .getPoster({ orderNo, shareUrl })
        .then((res) => {
          const img = new Image();
          img.src = res;
          img.onload = () => {
            toast.clear();
            this.posterUrl = res;
            this.imgLoad = true;
          };
        })
        .catch((err) => {
          if (needRetry) {
            setTimeout(() => {
              this.doGetPoster(toast, false);
            }, 1000);
          } else {
            toast.clear();
            this.close();
            errorToast(err, { message: '生成海报失败，请稍后再试' });
          }
        });
    },

    close() {
      this.show = false;
      this.posterUrl = '';
      this.$emit('close');
    },

    mountNode() {
      this.$emit('open');
      document.body.appendChild(this.$el);
      if (!this.posterUrl) {
        const toastLoading = Toast.loading({
          message: '正在生成',
          forbidClick: true,
          zIndex: 2001,
        });
        this.doGetPoster(toastLoading, true);
      }
      this.show = true;
    },
  },
};
</script>

<style lang="scss">
.order-share {
  &__poster {
    z-index: 2000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent;
    overflow-x: hidden;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &__container {
    position: relative;
    border-radius: 5px;
    width: 64vw;
  }

  &__img-container {
    position: relative;
    overflow-y: scroll;
    overflow-x: hidden;
    max-height: 76vh;
    width: 64vw;
    z-index: 1000;
    border-radius: 5px;
  }

  &__img {
    width: 100%;
    border-radius: 5px;

    &__placeholder {
      z-index: -99;
    }
  }

  &__word {
    font-size: 14px;
    color: #fff;
    z-index: 300;
    margin-top: 24px;
    text-align: center;
    position: relative;
  }

  &__close-container {
    position: absolute;
    right: -21px;
    top: -51px;
    z-index: 3000;
    height: 60px;
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__img-close {
    height: 24px;
    z-index: 300;
    width: 24px;
    border-radius: 50%;
    border: 1px solid #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &__img-close::after,
  &__img-close::before {
    content: '';
    width: 13px;
    height: 1px;
    background: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
  }

  &__img-close::after {
    transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
  }

  &__img-close::before {
    transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
  }

  @media only screen and (min-width: 375px) {
    &__img-word {
      font-size: 14px;
    }
  }
}
</style>
