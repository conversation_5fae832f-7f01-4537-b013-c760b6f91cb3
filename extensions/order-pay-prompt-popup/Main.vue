<template>
  <van-popup
    :close-on-click-overlay="false"
    custom-class="order-pay-prompt"
    :show="innerShow"
    v-if="initialShow"
  >
    <view class="order-pay-prompt__title">{{ promptData.title }}</view>
    <order-pay-prompt-expire-tip
      :theme-colors="formatThemeColors"
      :desc="promptData.descTemplate"
      @close="onClose"
    />
    <order-pay-prompt-evaluate
      :theme-colors="formatThemeColors"
      :evaluate="promptData.evaluateList"
    />
    <order-pay-prompt-goods
      class="order-pay-prompt__goods"
      @click="toPay"
      :prompt="promptData"
      :goods="promptData.goods"
    />
    <van-button
      custom-class="order-pay-prompt__button"
      style="width: 100%"
      custom-style="width: 100%;"
      :color="formatThemeColors['main-bg']"
      @click="toPay"
      round
      block
      >立即支付</van-button
    >
    <view
      class="order-pay-prompt__button order-pay-prompt__close-button"
      style="width: 100%"
      @click="onClose"
      round
      block
      >稍后再说</view
    >
  </van-popup>
</template>

<script>
import { url } from '@youzan/tee-util';
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import { setStorage, getStorage } from '@youzan/tee-api';

import api from './api';

import Popup from '@youzan/vant-tee/dist/popup/index';
import Button from '@youzan/vant-tee/dist/button/index';
import OrderPayPromptExpireTip from './components/OrderPayPromptExpireTip.vue';
import OrderPayPromptEvaluate from './components/OrderPayPromptEvaluate.vue';
import OrderPayPromptGoods from './components/OrderPayPromptGoods.vue';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';

/* #ifdef weapp */
import { PAGE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
/* #endif */

const { args } = url;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const SOURCE_MAP = {
  1: 'SHOP_HOME', // 店铺首页
  2: 'ITEM_DETAIL', // 商品详情
  4: 'MICRO', // 微页面
  5: 'LOGISTICS_DETAIL', // 物流详情页
};

const POP_NAME = 'ORDER_PAY_PROMPT';

export default {
  components: {
    'van-popup': Popup,
    'van-button': Button,
    'order-pay-prompt-expire-tip': OrderPayPromptExpireTip,
    'order-pay-prompt-evaluate': OrderPayPromptEvaluate,
    'order-pay-prompt-goods': OrderPayPromptGoods,
  },

  data() {
    return {
      kdtId: 0,
      prompt: {},
      needRequest: true,
      source: '',
      themeColors: {},
      innerShow: false,
      remotePrompt: {},
      initialShow: false,
    };
  },

  computed: {
    formatThemeColors() {
      const mainBg = this.themeColors['main-bg'] || '';
      const viceBg = this.themeColors['vice-bg'] || '';
      return {
        ...this.themeColors,
        'main-bg':
          typeof mainBg === 'string'
            ? mainBg
            : `linear-gradient(to right, ${mainBg.start}, ${mainBg.end})`,
        'vice-bg':
          typeof viceBg === 'string'
            ? viceBg
            : `linear-gradient(to right, ${viceBg.start}, ${viceBg.end})`,
      };
    },

    promptData() {
      if (this.needRequest) {
        return this.remotePrompt;
      }

      return this.prompt;
    },
  },

  watch: {
    innerShow(val) {
      if (val && !this.initialShow) {
        this.initialShow = true;
      }
    },
  },

  async created() {
    /* #ifdef weapp */
    const popup = await getStorage('order-pay-prompt-popup');
    /* #endif */
    mapData(this, ['kdtId', 'needRequest', 'source', 'themeColors', 'prompt']);
    mapEvent(this, {
      open: () => {
        /* #ifdef weapp */
        if (popup?.data) {
          this.ctx.event.emit('stateChange', {
            name: POP_NAME,
            state: 'CLOSE',
          });
          return;
        }
        /* #endif */

        this.showPopup();
      },
      close: () => {
        this.onClose();
      },
    });

    this.ctx.event.emit('stateChange', {
      name: POP_NAME,
      state: 'READY',
      /* #ifdef weapp */
      skip: popup || false,
      /* #endif */
    });
  },

  methods: {
    log(type) {
      const params = {
        banner_id: this.getBannerId(),
        order_no: this.promptData.orderNo,
        popup_type: this.promptData.descTemplate?.popupType,
        component: 'pending_payment_popup',
      };
      const EVENT_MAP = {
        view: {
          et: 'view',
          ei: 'component_view',
          en: '组件曝光',
          params,
        },
        topay: {
          et: 'click',
          ei: 'pay_click',
          en: '去支付点击',
          params,
        },
        cancel: {
          et: 'click',
          ei: 'temporarily_abandon_click',
          en: '点击暂时放弃',
          params,
        },
      };

      this.ctx.logger.log(EVENT_MAP[type]);
    },

    getBannerId(index = 0) {
      const pageRandomNumber = makeRandomString(8);
      const loggerSpm = this.ctx.logger.getSpm();
      return `${loggerSpm}~pending_payment_popup~${index}~${pageRandomNumber}`;
    },

    toPay() {
      this.log('topay');

      /* #ifdef web */
      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      const url = args.add('https://cashier.youzan.com/pay/wsctrade_pay', {
        kdt_id: this.kdtId,
        orderNo: this.promptData.orderNo,
        banner_id: this.getBannerId(),
      });
      navigate({
        web: {
          type: 'safeLink',
          safeLink: {
            url,
          },
        },
      });
      /* #endif */

      /* #ifdef weapp */
      navigateToRantaPage({
        pageType: PAGE_TYPE.PAY,
        query: {
          orderNo: this.promptData.orderNo,
          banner_id: this.getBannerId(),
        },
      });
      /* #endif */
      // Tee.navigate({ url });
    },

    onOpen() {
      this.log('view');
      this.innerShow = true;
      /* #ifdef weapp */
      setStorage('order-pay-prompt-popup', true);
      /* #endif */
      this.ctx.event.emit('stateChange', {
        name: POP_NAME,
        state: 'OPEN',
      });
    },

    onClose() {
      this.log('cancel');
      this.innerShow = false;
      this.ctx.event.emit('stateChange', {
        name: POP_NAME,
        state: 'CLOSE',
      });
    },

    getOrderPayPrompt() {
      api.getOrderPayPrompt({ kdtId: this.kdtId, source: this.source }).then((data = {}) => {
        this.remotePrompt = data;

        if (data.popup) {
          this.onOpen();
          /* #ifdef web */
          ZNB.postMessage({
            data: { type: 'order-pay-prompt-popup', payload: true },
          });
          /* #endif */
        } else {
          /* #ifdef weapp */
          setStorage('order-pay-prompt-popup', true);
          /* #endif */
          this.log('view');
          /* #ifdef web */
          ZNB.postMessage({
            data: { type: 'order-pay-prompt-popup', payload: true },
          });
          /* #endif */

          this.ctx.event.emit('stateChange', {
            name: POP_NAME,
            state: 'CLOSE',
          });
        }
      });
    },

    showPopup() {
      if (this.needRequest) {
        this.getOrderPayPrompt();
      } else {
        this.onOpen();
      }
    },
  },
};
</script>

<style lang="scss">
@mixin van-button {
  width: 100%;
}
.order-pay-prompt {
  max-width: 95%;
  width: 311px;
  padding: 24px 16px 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: var(--theme-radius-dialog, 16px) !important;
  line-height: initial;

  &__title {
    color: #323233;
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: bold;
    text-align: center;
  }

  &__button {
    @include van-button;

    font-size: 16px !important;
    height: 36px !important;
    line-height: 36px !important;
    border: none !important;
    border-radius: var(--theme-radius-button, 999px) !important;
  }

  &__goods {
    width: 100%;
  }

  &__close-button {
    @include van-button;

    height: 16px !important;
    line-height: 16px !important;
    margin-top: 16px;
    color: #969799 !important;
    text-align: center;
  }
}
</style>
