<template>
  <!-- TODO: popup设置极大值覆盖tab栏的情况，https://qima.feishu.cn/record/YzZErfYomebeBuc5RumcrvqbnU4?ccm_open_type=im_card_automation_button -->
  <van-popup
    custom-class="cancel-order-popup"
    :show="isShowOrderCancelPopup"
    :z-index="999999999"
    @close="onClose"
    position="bottom"
  >
    <view class="cancel-order-popup__title">选择取消原因</view>
    <view class="cancel-order-popup__reason-list">
      <van-cell-group>
        <van-cell
          v-for="item in cancelOrderReasonInfo.buyerCloseReason"
          :key="item.value"
          clickable
          :title="item.desc"
          @click="onClickReasonItem(item)"
          :border="false"
        >
          <van-radio
            slot="right-icon"
            icon-size="18px"
            :name="item.value"
            :value="formData.reason"
            checked-color="var(--general)"
          >
            {{ item.reason }}
          </van-radio>
        </van-cell>
      </van-cell-group>
    </view>
    <view
      v-if="
        cancelOrderReasonInfo.isShowItemPutInCartButton && cancelOrderPopupConfig.autoAddCart.isShow
      "
      class="cancel-order-popup__put-cart"
    >
      <van-checkbox
        icon-size="18px"
        checked-color="var(--general)"
        :value="formData.isBuyAgain"
        @change="onChangeAutoAddCart"
        :disabled="cancelOrderPopupConfig.autoAddCart.isDisabled"
        >提交后将本单商品放入购物车</van-checkbox
      >
    </view>
    <view class="cancel-order-popup__btns">
      <van-button class="cancel-order-popup__btn" round @click="onClose">再想想</van-button>
      <van-button
        color="var(--general)"
        class="cancel-order-popup__btn theme-background theme-border"
        :class="{ disabled: disabled }"
        round
        @click="onConfirm"
        type="primary"
        >取消订单</van-button
      >
    </view>
  </van-popup>
</template>

<script>
// Components
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Radio from '@youzan/vant-tee/dist/radio/index.vue';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import Checkbox from '@youzan/vant-tee/dist/checkbox/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';

// Dependencies
import { mapState } from '@ranta/store';
import Toast from '@youzan/vant-tee/dist/toast/toast';

const DEFAULT_FORM_DATA = {
  reason: 0,
  isBuyAgain: true,
};

export default {
  components: {
    'van-cell': Cell,
    'van-popup': Popup,
    'van-radio': Radio,
    'van-button': Button,
    'van-checkbox': Checkbox,
    'van-cell-group': CellGroup,
  },

  data() {
    return {
      formData: { ...DEFAULT_FORM_DATA },
      ...mapState(this, [
        'isShowOrderCancelPopup',
        'cancelOrderReasonInfo',
        'cancelOrderPopupConfig',
      ]),
    };
  },

  computed: {
    disabled() {
      return this.formData.reason === 0;
    },
  },

  watch: {
    isShowOrderCancelPopup(newVal) {
      if (newVal) {
        const formData = {
          ...DEFAULT_FORM_DATA,
          isBuyAgain:
            this.store?.cancelOrderPopupConfig?.autoAddCart?.isChecked ??
            DEFAULT_FORM_DATA.isBuyAgain,
        };
        this.formData = formData;
      }
    },
  },

  methods: {
    onChangeAutoAddCart(value) {
      this.formData.isBuyAgain = value;
    },

    onClickReasonItem(item) {
      this.formData.reason = item.value;
    },

    onClose() {
      this.store.toggleOrderCancelPopup({ isShow: false });
    },

    onConfirm() {
      if (this.disabled) {
        Toast('请选择取消原因');
        return;
      }
      this.store.confirmCancel(this.formData);
      this.store.toggleOrderCancelPopup({ isShow: false });
    },
  },
};
</script>

<style lang="scss">
.cancel-order-popup {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  background-color: #f7f7fa;

  &__title {
    display: block;
    text-align: center;
    width: 100%;
    font-weight: bold;
    padding: 10px 0;
    background-color: #fff;
    box-sizing: border-box;
  }

  &__reason-list {
    margin-bottom: 10px;
    .t-cell {
      padding-left: 10px;
      padding-right: 5px;

      &::after {
        display: none;
      }
    }
  }

  &__put-cart {
    padding: 12px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    font-size: 14px;
  }

  &__btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 5px 10px 30px;
    box-sizing: border-box;
    background-color: #fff;
  }
  &__btn {
    flex: 1 !important;
    margin: 0 6px;
    height: 40px;
    line-height: 40px;
    button {
      width: 100% !important;
    }
    &.disabled {
      button {
        background-color: #c8c9cc !important;
        border-color: #c8c9cc !important;
      }
    }
  }
}
</style>
