<template>
  <view>
    <view
      v-if="couponDeliveryActivitiesList.length"
      class="activity-item"
      @click="toggleActivitiesPopup"
    >
      <text class="activity-title">运费</text>
      <view class="activity-value-wrapper">
        <text class="activity-value">
          {{ postage }}
        </text>
        <van-icon
          v-if="showPopupTip"
          class="activity-question-icon"
          color="#1989fa"
          name="question-o"
          size="14px"
          @click.stop="toggleActivitiesPopup"
        />
      </view>
    </view>

    <!-- 活动 popup -->
    <activities-popup
      :show="showPopup"
      :list="couponDeliveryActivitiesList"
      :decrease-amount="decreaseAmount"
      :theme-colors="themeColors"
      decrease-text="-"
      @close="toggleActivitiesPopup"
    >
      <view slot="title" class="header">运费券</view>
    </activities-popup>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import ActivitiesPopup from './components/ActivitiesPopup';

export default {
  components: {
    'van-icon': Icon,
    'activities-popup': ActivitiesPopup,
  },
  props: {
    postage: Number,
  },
  data() {
    return {
      deliveryActivitiesList: [],
      showPopup: false,
      themeColors: {},
    };
  },

  computed: {
    decreaseAmount() {
      return this.couponDeliveryActivitiesList.reduce((result, item) => {
        result += item.decrease;
        return result;
      }, 0);
    },
    couponDeliveryActivitiesList() {
      return (this.deliveryActivitiesList || [])
        .filter((item) => item.type === 'ump_postage')
        .map((item) => {
          return {
            ...item,
            promotionTypeName: '运费券',
            activityName: item.discountCouponStr,
          };
        });
    },

    showPopupTip() {
      return this.couponDeliveryActivitiesList.length > 0;
    },
  },

  created() {
    mapData(this, ['deliveryActivitiesList', 'themeColors']);
  },

  methods: {
    toggleActivitiesPopup() {
      // 当没有提示图标的时候，不响应弹起事件
      if (!this.showPopup && !this.showPopupTip) return;
      this.showPopup = !this.showPopup;
    },
  },
};
</script>

<style scoped>
.header {
  text-align: center;
  line-height: 48px;
  font-size: 16px;
  color: #323232;
}
.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}
.activity-title {
  font-size: 14px;
  color: #969799;
}
.activity-value {
  font-size: 14px;
  color: #323233;
}
</style>
