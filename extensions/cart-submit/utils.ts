import type { CartInfo, CartModeEnum, TradeCartPayment } from '@youzan-cloud/cloud-biz-types';
import type { CartStateV1, PaymentV1 } from './types';

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  getPaymentV1({ cartBottomData }): PaymentV1 {
    return {
      totalPrice: cartBottomData?.goodsTotalPrice, // 商品优惠前合计金额字段
      totalDiscount: cartBottomData?.totalDiscount, // 商品优惠金额
    };
  },
  getPayment({ cartBottomData }): TradeCartPayment {
    return {
      realPrice: cartBottomData?.totalPrice,
      goodsPrice: cartBottomData?.goodsTotalPrice,
    };
  },
  getCartStateV1({ cartBottomData, editMode, isCheckedAll }): CartStateV1 {
    return {
      editShopIndex: editMode === 'edit' ? 0 : -1,
      checkedAll: isCheckedAll,
      totalCheckedPrice: cartBottomData.goodsTotalPrice,
      totalDiscount: cartBottomData.totalDiscount,
    };
  },
  getCartInfo({ editMode, isCheckedAll }): CartInfo {
    return {
      mode: (editMode === 'edit' ? 'edit' : 'normal') as CartModeEnum,
      isSelectedAll: isCheckedAll,
    };
  },
};
