<template>
  <view>
    <view v-if="show" class="postpone-mask" @click="onClickHide"></view>
    <view v-if="show" class="t-popover" :style="{ top: top + 'px', left: left + 'px' }">
      <view class="t-popover-arrow t-popover-arrow-right"></view>
      <van-button
        size="small"
        custom-class="postpone-action"
        @click="onClick('postponeDeliveryUrl')"
      >
        顺延
      </van-button>
      <van-button
        size="small"
        custom-class="postpone-action--last"
        @click="onClick('cancelPostponeDeliveryUrl')"
      >
        取消顺延
      </van-button>
    </view>
  </view>
</template>

<script>
import Overlay from '@youzan/vant-tee/dist/overlay/index';
import Button from '@youzan/vant-tee/dist/button/index';
import navigate from '@youzan/tee-biz-navigate';
import { mapEvent, mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-button': Button,
    'van-overlay': Overlay,
  },

  data() {
    return {
      goods: [],
      show: false,
      left: 0,
      top: 0,
      itemId: 0,
    };
  },

  created() {
    mapData(this, ['goods']);
    mapEvent(this, {
      showPostponePopup: ({ position, itemId }) => {
        this.itemId = itemId;
        this.left = position.left;
        this.top = position.top;
        this.show = true;
      },
    });
  },

  methods: {
    onClickHide() {
      this.show = false;
    },

    onClick(key) {
      const item = this.goods.find((item) => item.itemId === this.itemId);
      if (item && item[key]) {
        navigate({
          // 通用配置
          url: item[key],
          type: 'navigateTo',
        });
        this.show = false;
      }
    },
  },
};
</script>

<style lang="scss">
$popover-bg-color: #e5e5e5;
$popover-font-color: #fff;
$popover-border-radius: 3px;
$popover-border-width: 5px;

.postpone-mask {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 400;
  background-color: rgba(0, 0, 0, 0);
}

.t-popover {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  color: $popover-font-color;
  border: 1px solid #e5e5e5;
  border-radius: $popover-border-radius;
  z-index: 500;
  transform: translate(-100%, -50%);
  background: #fff;
}

.t-popover-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.t-popover-arrow-up {
  border-left: $popover-border-width solid transparent;
  border-right: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid $popover-bg-color;
  left: 50%;
  transform: translateX(-50%);
  top: -$popover-border-width;
}

.t-popover-arrow-down {
  border-left: $popover-border-width solid transparent;
  border-right: $popover-border-width solid transparent;
  border-top: $popover-border-width solid $popover-bg-color;
  left: 50%;
  transform: translateX(-50%);
  bottom: -$popover-border-width;
}

.t-popover-arrow-left {
  border-top: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid transparent;
  border-right: $popover-border-width solid $popover-bg-color;
  top: 50%;
  transform: translateY(-50%);
  left: -$popover-border-width;
}

.t-popover-arrow-right {
  border-top: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid transparent;
  border-left: $popover-border-width solid $popover-bg-color;
  top: 50%;
  transform: translateY(-50%);
  right: -$popover-border-width;
}

.postpone-action {
  display: flex !important;
  border: 0 !important;
  border-bottom: 1px solid #eee !important;
}

.postpone-action--last {
  display: flex !important;
  border: 0 !important;
}
</style>
