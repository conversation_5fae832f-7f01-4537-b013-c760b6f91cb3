<template>
  <view v-if="selectablePresents.length">
    <view class="present">
      <view class="present__title">
        <text class="present__title-type">赠品</text>
        <text v-if="isCanSelect" class="present__title-num">
          （可选{{ selectablePresentNum }}种）
        </text>
      </view>
      <view v-if="isCanSelect" @click.stop="handlePickPresent" class="present__pick">
        <text class="present__pick-text">去挑选</text>
        <van-icon class="present__pick-icon" name="arrow" />
      </view>
    </view>
    <!-- TODO: extension provide component: present-goods -->
    <present-goods
      v-for="goods in presentList"
      :activity-id="activityId"
      :key="goods.skuId"
      :is-can-choose="false"
      :goods="goods"
      @change-sku="handleChangeSku"
    />
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

export default {
  components: {
    'van-icon': Icon,
  },

  props: {
    presentList: {
      type: Array,
      default: () => [],
    },
    // 活动信息
    groupActivityInfo: {
      type: Object,
      default: () => ({}),
    },
    // 获取满减赠送是否支持自选
    canSelectPresent: {
      type: Boolean,
      default: false,
    },
    activityId: Number,
  },

  computed: {
    selectablePresentNum() {
      return (this.groupActivityInfo && this.groupActivityInfo.selectablePresentNum) || 0;
    },

    selectablePresents() {
      return (this.groupActivityInfo && this.groupActivityInfo.selectablePresents) || [];
    },

    isCanSelect() {
      return this.canSelectPresent && this.selectablePresentNum < this.selectablePresents.length;
    },
  },

  methods: {
    handlePickPresent() {
      const { activityId, selectablePresents, selectablePresentNum } = this.groupActivityInfo || {};
      const goodsList =
        selectablePresents?.map((item) => {
          this.presentList.some((present) => {
            if (present.id === item.id) {
              item = {
                ...item,
                sku: present.sku,
                skuId: present.skuId,
              };
              return true;
            }
            return false;
          });
          return item;
        }) ?? [];

      this.$emit('on-show', {
        show: true,
        pickGoodsList: this.presentList,
        activityId,
        goodsList,
        selectablePresentNum,
      });
    },

    handleChangeSku(data) {
      this.$emit('change-sku', data);
    },
  },
};
</script>

<style lang="scss" scoped>
.present {
  display: flex;
  justify-content: space-between;
  padding: 0 var(--theme-trade-md-gutter, 12px) 8px 44px;
  align-items: center;
  font-size: 12px;

  &__title {
    &-type {
      font-weight: var(--theme-common-field-font-weight, 500);
    }

    &-num {
      font-size: 11px;
      color: #969799;
    }
  }

  &__pick {
    display: flex;
    align-items: center;
    color: #969799;
  }

  &__pick-text {
    line-height: 0;
  }

  &__pick-icon {
    display: flex;
    align-items: center;
  }
}
</style>
