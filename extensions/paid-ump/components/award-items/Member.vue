<template>
  <!-- 权益卡 -->
  <award-container>
    <view class="member-card-container">
      <view class="member-card-content">
        <view class="header">
          <view class="title">{{ name }}</view>
          <view class="desc">{{ desc }}</view>
        </view>
        <view class="member-items" v-if="benefitList.length">
          <view class="member-item" v-for="item in benefitList" :key="item.key">
            <view class="member-item-icon">
              <image class="icon-image" :src="item.icon" />
            </view>
            <view class="member-item-text t-ellipsis">
              {{ item.showName }}
            </view>
          </view>
        </view>
        <view class="button" @click="handelClick">{{ buttonText }}</view>
      </view>
    </view>
  </award-container>
</template>

<script>
import Tee from '@youzan/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import args from '@youzan/utils/url/args';
import AwardContainer from './Container';
import fullfillImage from '@youzan/utils/url/fullfillImage';

const ALLICON = fullfillImage('/public_files/a1fb1079aa056c48631a190bc5247dd9.png', 'middle');

export default {
  name: 'award-member',
  components: {
    'award-container': AwardContainer,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    desc() {
      if (this.info.needActivated) {
        if (this.benefitList.length) {
          return '尚未激活，激活可享以下权益';
        }
        return '尚未激活，激活即享专属权益';
      }
      if (this.benefitList.length) {
        return '可享受以下权益';
      }
      return '可享受专属权益';
    },
    benefitList() {
      const { benefitList = [] } = this.info;
      if (benefitList.length > 4) {
        return benefitList.slice(0, 3).concat({
          showName: `更多${benefitList.length}项权益`,
          benefitCount: benefitList.length,
          icon: ALLICON,
        });
      }
      return benefitList;
    },
    buttonText() {
      return this.info.needActivated ? '立即激活' : '立即查看';
    },
    name() {
      return `恭喜你，获得${this.info.cardName}`;
    },
  },
  mounted() {
    const { needActivated } = this.info;
    this.$emit('log-attach', {
      et: 'view',
      ei: 'show_quanyika',
      en: '返权益卡曝光',
    });
    if (needActivated) {
      this.$emit('log-attach', {
        et: 'view',
        ei: 'show_quanyika_jihuo',
        en: '返权益卡“立即激活”曝光',
      });
    }
  },
  methods: {
    handelClick() {
      const { url, needActivated } = this.info;
      this.$emit('log-attach', {
        et: 'click',
        ei: needActivated ? 'click_quanyika_jihuo' : 'click_detail_quanyika',
        en: needActivated ? '返权益卡“立即激活”点击' : '返权益卡查看详情点击',
      });
      if (url) {
        const alias = args.get('card_alias', url);
        if (alias) {
          Tee.navigate({
            url: `/packages/card/detail/index?alias=${alias}`,
          });
        } else {
          Toast('card_alias参数解析失败');
        }
      }
    },
  },
};
</script>

<style lang="scss">
.member-card-container {
  .member-card-content {
    background: url(https://img01.yzcdn.cn/upload_files/2021/06/29/FlTPD66OALsJkVh8l4GE-DtD1Zkf.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 4px;
    box-sizing: border-box;

    .header {
      display: flex;
      flex-direction: column;

      .title {
        margin-top: 8px;
        font-size: 16px;
        color: #654009;
        line-height: 20px;
        font-weight: 500;
      }

      .desc {
        margin-top: 5px;
        font-size: 12px;
        color: #724804;
        text-align: center;
        line-height: 18px;
        padding-bottom: 16px;
      }
    }

    .member-items {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 0 12px;

      .member-item {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #646566;
        font-size: 12px;
        .member-item-icon {
          .icon-image {
            height: 32px;
            width: 32px;
            background: #fff;
            border-radius: 50%;
          }
        }
        .member-item-text {
          margin-top: 5px;
          font-size: 12px;
          max-width: 66px;
          color: #a16a1f;
          text-align: center;
          line-height: 18px;
        }

        .member-item-image {
          height: 80px;
          width: 107px;
        }
      }
    }
    .button {
      height: 32px;
      width: 90%;
      line-height: 32px;
      background-image: linear-gradient(90deg, #feae79 0%, #ff3d3d 100%);
      border-radius: 19px;
      font-size: 14px;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
  }
}

.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
</style>
