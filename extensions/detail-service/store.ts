import { buildUrl } from '@youzan/tee-biz-util';
import { createStore as _createStore } from '@ranta/store';
import type { StoreModule } from '@youzan/wsc-tee-trade-common/lib/types';

const rootStore: StoreModule = {
  state: {
    kdtId: 0,
    orderNo: '',
    invoiceStatus: 0,
  },
  getters: {
    invoice() {
      const res: Record<string, any> = {
        show: true,
        type: '',
        value: '',
        tip: '',
      };

      const { kdtId = 0, orderNo = '', invoiceStatus = 0 } = this;

      let successUrl = `/packages/trade/order/invoice/index?type=success&order_no=${orderNo}`;
      let detailUrl = `/packages/trade/order/invoice/index?type=detail&order_no=${orderNo}&kdt_id=${kdtId}`;

      /* #ifdef web */
      successUrl = buildUrl(`/wsctrade/order/invoice/success?order_no=${orderNo}`, 'h5', kdtId);
      detailUrl = buildUrl(
        `/wsctrade/order/invoice?order_no=${orderNo}&kdt_id=${kdtId}`,
        'h5',
        kdtId
      );

      /* #endif */

      switch (invoiceStatus) {
        case 1: // 待开票
          res.value = '待开票';
          res.tip = '交易成功后自动开票';
          res.url = detailUrl;
          break;
        case 10: // 开票中
          res.value = '开票中';
          res.url = detailUrl;
          break;
        case 20: // 失败
          res.value = '开票失败';
          res.tip = '请联系商家客服处理';
          break;
        case 30: // 成功
        case 40: // 冲红
          res.value = '开票成功';
          res.url = successUrl;
          break;
        case 50: // 已填写开票信息（对应非电子发票服务填了抬头的情况）
          res.value = '已填写开票信息';
          res.url = detailUrl;
          break;
        default:
          res.show = false;
          break;
      }
      return res;
    },
  },
  actions: () => ({}),
};

export default function createStore(ctx) {
  return _createStore({
    state: () => ({
      ...rootStore?.state,
    }),
    getters: {
      ...rootStore?.getters,
    },
    actions: {
      ...rootStore?.actions(ctx),
    },
  });
}
