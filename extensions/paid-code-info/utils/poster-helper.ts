import Toast from '@youzan/vant-tee/dist/toast/toast';
import { saveImageToPhotosAlbum } from '@youzan/tee-api';

interface IOptions {
  getPoster: Function;
  needRetry?: Boolean;
  onSuccess?: Function;
}

function fail() {
  Toast({ message: '生成海报失败', icon: 'none' });
}

export function getPosterAndDownload(options: IOptions) {
  let { needRetry = true } = options;
  const { getPoster, onSuccess } = options;
  Toast.loading({
    message: '生成海报中',
    mask: true,
  });
  getPoster()
    .then((res) => {
      Toast.clear();
      if (!res) {
        fail();
        return null;
      }
      /* #ifdef weapp */
      const app = getApp();
      app.downloadFile({
        url: res,
        success: ({ statusCode, tempFilePath }) => {
          if (statusCode === 200) {
            saveImageToPhotosAlbum(tempFilePath)
              .then(() => {
                Toast.success('保存成功');
              })
              .catch(() => {
                Toast('保存失败');
              });
          } else {
            fail();
          }
        },
        fail,
      });
      /* #endif */
      /* #ifdef web */
      onSuccess && onSuccess(res);
      /* #endif */
    })
    .catch(() => {
      if (needRetry) {
        setTimeout(() => {
          needRetry = false;
          getPoster();
        }, 1000);
      } else {
        Toast.clear();
        fail();
      }
    });
}
