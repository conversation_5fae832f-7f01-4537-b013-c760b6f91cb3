<template>
  <view class="present-goods" :style="customStyle">
    <view v-if="isCanChoose" @click.stop="chooseGoods" class="present-goods__checkbox">
      <van-icon size="18px" :color="themeGeneralColor" :name="iconName" :custom-style="iconStyle" />
      <!--选择部分蒙层-->
      <view class="present-goods__invalid" v-if="isInvalid" />
    </view>
    <view @click="goToGoodsPage" class="present-goods__card" :style="cardStyle">
      <!--失效商品蒙层-->
      <view class="present-goods__invalid" v-if="isInvalid" />
      <view class="present-goods__img-wrap">
        <text
          class="present-goods__img-tag"
          v-if="!isPop"
          :style="'background:' + themeGeneralColor"
          >赠品</text
        >
        <image mode="aspectFit" class="present-goods__img" :src="imgUrl" />
        <invalid-mask v-if="isInvalid" />
      </view>

      <view class="present-goods__desc-wrap">
        <view>
          <view class="present-goods__title">
            <text v-if="goodsTitleTag" class="present-goods__title-tag">
              <image :src="goodsTitleTag" />
            </text>

            <view class="present-goods__title-text">
              {{ goods.title }}
            </view>
          </view>

          <view v-if="!isInvalid && skuStr" class="present-goods__sku">
            <view v-if="!canChooseSku">{{ skuStr }}</view>
            <view v-else class="present-goods__sku-container" @click.stop="changeSku">
              <text>
                {{ skuStr }}
              </text>
              <van-icon v-if="!isRetailWeappScene" name="arrow-down" />
            </view>
          </view>
        </view>

        <!--商品失效-->
        <view v-if="isInvalid">
          <view class="present-goods__invalid-text">
            {{ goods.reason }}
          </view>
        </view>

        <view v-else>
          <view>
            <view class="present-goods__price-num">
              <!-- TODO: extension provide component: price -->
              <price
                :price="0"
                :theme-general-color="isPop ? '#323233' : themeGeneralColor"
                :origin-price="goods.originPrice || goods.goodsPrice"
                :is-pop="isPop"
              />
              <view class="present-goods__num">
                <text class="common-num-font">x{{ goods.num }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import Tee from '@youzan/tee';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { cdnImage } from '@youzan/tee-biz-util';
import { mapData } from '@youzan/ranta-helper-tee';

import { getGoodsSkuProperty } from './utils';

const GOODS_TAG_MAP = {
  HAITAO: cdnImage('public_files/3a774609c08dc284f27ba5a64be85fa6.png'), // 海淘
  PERIOD_BUY: cdnImage('public_files/61954b0fdd8319a9c5722f16ca2e31de.png'), // 周期购
  MEMBER_DISCOUNT: cdnImage('cdn/FkhVnpHh7ZwFAvBaUwO8B0F2Gf4V-1.png'), // 会员折扣
  IS_DRUG_GOOD: cdnImage('path/to/cdn/dir/isDrugTag_3x.png'), // 处方药
};

export default {
  components: {
    'van-icon': Icon,
  },
  props: {
    goods: Object,
    activityId: Number,
    // 是否可以选中
    isCanChoose: {
      type: Boolean,
      default: true,
    },
    // 当前商品是否选中
    isChoose: {
      type: Boolean,
      default: false,
    },
    // 自定义样式
    customStyle: {
      type: String,
      default: '',
    },
    cardStyle: {
      type: String,
      default: '',
    },
    mustChoose: {
      type: Boolean,
      default: false,
    },
    canChooseSku: {
      type: Boolean,
      default: true,
    },
    isPop: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      GOODS_TAG_MAP,
      themeGeneralColor: '',
      isRetailWeappScene: false,
    };
  },

  computed: {
    goodsTitleTag() {
      const { settlementRule } = this.goods;
      const { settlementMark } = settlementRule || {};

      // 如果是处方药就增加处方药的标

      if (this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1') {
        return GOODS_TAG_MAP.IS_DRUG_GOOD;
      }
      return GOODS_TAG_MAP[settlementMark] || '';
    },

    // 是否失效
    isInvalid() {
      return this.goods.isAvailable === false;
    },

    // 是否可选
    checkboxDisable() {
      return this.isInvalid;
    },

    iconStyle() {
      return !this.checkboxDisable
        ? ''
        : 'background-color: #ebedf0; border-radius: 18px; color: #c8c9cc;';
    },

    iconName() {
      if (this.mustChoose || (this.isChoose && !this.checkboxDisable)) {
        return 'checked';
      }

      return 'circle';
    },

    imgUrl() {
      const { attachmentUrl, imgUrl } = this.goods;
      return cdnImage(attachmentUrl || imgUrl, '!300x300.jpg');
    },

    skuStr() {
      return getGoodsSkuProperty(this.goods);
    },
  },

  mounted() {
    /* #ifdef weapp */
    if (getApp().getShopInfoSync()?.shop_type === 7) this.isRetailWeappScene = true;
    /* #endif */
    mapData(
      this,
      {
        themeColors: (val) => {
          const themeGeneralColor = val.general;
          this.themeGeneralColor = themeGeneralColor;
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    goToGoodsPage() {
      const { alias } = this.goods;
      if (!alias) {
        return;
      }
      /* #ifdef weapp */
      Tee.navigate({
        url: `/pages/goods/detail/index?alias=${alias}`,
      });
      /* #endif */

      /* #ifdef web */
      // 私域直播间来源的购物车，禁止跳到商详
      const isPdLive = !!window._global?.pdlive;
      if (isPdLive) return;
      Tee.navigate({
        url: `/wscgoods/detail/${alias}`,
      });
      /* #endif */
    },

    chooseGoods() {
      if (this.checkboxDisable || (this.isChoose && this.mustChoose)) {
        return;
      }
      const type = this.isChoose ? 'remove' : 'add';
      const payload = {
        rangeType: 'single',
        goods: this.goods,
        type,
      };

      this.$emit('change-item-checked', payload);
    },

    changeSku() {
      if (this.isRetailWeappScene) return;
      this.$emit('change-sku', {
        activityId: this.activityId,
        goods: this.goods,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.present-goods {
  position: relative;
  background: #fff;

  &__invalid {
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    z-index: 3;
  }

  &__checkbox {
    float: left;
    height: 120px;
    width: 44px;
    text-align: center;
    line-height: 100px;
    color: #38f;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  &__card {
    box-sizing: content-box;
    min-height: 80px;
    margin: 12px var(--theme-trade-md-gutter, 12px) 12px 0;
    margin-left: 44px;
    background: #fff;
    position: relative;

    a {
      display: block;
    }
  }

  &__desc-wrap {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 104px;
    min-height: 80px;
  }

  &__title {
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    &-not-exist {
      margin-bottom: 0;
    }
  }

  &__title-text {
    font-size: 14px;
    color: #323233;
    line-height: 20px;
    vertical-align: middle;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

    &_invalid {
      color: #969799;
    }
  }

  &__title-tag {
    color: #fff;
    margin-right: 2px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;

    img {
      height: 100%;
    }
  }

  &__invalid-text {
    font-size: 12px;
    z-index: 5;
    position: relative;
  }

  &__img-wrap {
    width: 80px;
    height: 80px;
    float: left;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    border-radius: var(--theme-radius-card, 8px);
    overflow: hidden;
    background: #fff;
    background-size: cover;
    text-align: center;
  }

  &__img {
    width: 80px;
    height: 80px;
  }

  &__img-tag {
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    border-radius: var(--theme-radius-card, 8px) 0 var(--theme-radius-card, 8px) 0;
    width: 32px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    z-index: 2;
  }

  &__sku {
    line-height: 16px;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    color: #969799;
  }

  &__sku-container {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: #f7f8fa;
    border-radius: var(--theme-radius-card, 4px);
    font-size: 12px;
    color: #969799;

    text {
      margin-right: 8px;
    }

    &--normal {
      padding: 0;
      background: transparent;

      text {
        margin: 0;
      }
    }
  }

  &__price {
    display: block;
    flex-shrink: 1;
    word-break: break-all;
    font-family: Avenir;
    font-weight: var(--theme-common-price-font-weight, 600);
  }

  &__price-num {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    color: #323233;
  }

  &__num {
    flex-shrink: 0;
    color: #666;
    font-size: 12px;
  }
}
</style>
