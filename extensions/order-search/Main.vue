<template>
  <view>
    <search-input @history-change="updateHistory" />
    <search-history
      :kdt-id="kdtId"
      @handleItemClick="handleItemClick"
      :show-bottom-border="itemLength"
      :history-list="historyList"
      ref="searchHistory"
    />
    <frequently-purchase-items :item-list="goodsItemList" />
  </view>
</template>

<script>
import { formatOrder } from './format';
import SearchHistory from './components/SearchHistory';
import get from '@youzan/utils/object/get';
import fetchData from './data-source';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'search-history': SearchHistory,
  },

  data() {
    return {
      keyword: '',
      showSearchBar: true,
      goodsItemList: [],
      kdtId: '',
    };
  },

  computed: {
    itemLength() {
      return this.goodsItemList.length > 0;
    },
  },

  async created() {
    mapData(this, ['kdtId']);
    Object.assign(this.ctx.data, formatOrder());
    const { goods = [] } = await fetchData();
    this.goodsItemList = goods;
    if (this.ctx.lambdas.getUserInfo) {
      this.ctx.lambdas.getUserInfo().then((userInfo) => {
        this.ctx.data.buyerId = userInfo.buyerId;
      });
    }
  },

  methods: {
    handleItemClick(item) {
      this.ctx.lambdas.triggerEvent('change-history', item);
    },
    updateHistory() {
      setTimeout(() => {
        const getHistoryList = get(this, '$refs.searchHistory.getHistoryList', '');
        if (getHistoryList) {
          getHistoryList();
        }
      }, 500);
    },
  },
};
</script>
<style lang="scss" scope>
input {
  caret-color: #155bd4;
}
page {
  background: #f6f7f9;
}
</style>
