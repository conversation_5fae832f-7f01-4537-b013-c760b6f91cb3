<template>
  <view v-if="showPopup" class="wish-msg-dialog">
    <van-popup
      :show="showPopup"
      safe-area-inset-bottom
      :close-on-click-overlay="false"
      position="bottom"
      get-container="body"
      custom-class="wish-msg"
    >
      <view class="wish-msg__header">赠送成功，给TA留言吧</view>
      <view class="wish-msg__name-selection">
        <van-button
          :disabled="!isWeixin"
          :custom-class="
            !isWeixin
              ? 'wish-msg__name-selection-disabled'
              : !validateName
              ? 'wish-msg__name-selection-active'
              : 'wish-msg__name-selection-normal'
          "
          @click="validateName = false"
        >
          使用微信昵称
        </van-button>
        <van-button
          plain
          :custom-class="
            validateName ? 'wish-msg__name-selection-active' : 'wish-msg__name-selection-normal'
          "
          @click="validateName = true"
        >
          我要匿名
        </van-button>
      </view>
      <view
        class="wish-msg__name-wrap t-hairline--bottom"
        :custom-class="
          (validateName ? 'slide-name ' : '') + (errorType === 'giver' ? ' blink-border' : '')
        "
      >
        <van-field
          :value="giver"
          @input="setGiver"
          custom-class="wish-msg__name-wrap__input"
          placeholder="请填写昵称"
          clearable
          label="昵称"
          :border="false"
          @focus="clearErrorMsg"
        />
      </view>
      <view
        class="wish-msg__msg-wrap t-hairline--bottom"
        :class="{ 'blink-border': errorType === 'specify' }"
      >
        <van-field
          :value="specify"
          @input="setSpecify"
          input-class="wish-msg__msg-wrap__textarea"
          type="textarea"
          label="留言"
          placeholder="请填写你想对TA说的话"
          :rows="5"
          :maxlength="280"
          :border="false"
          @focus="clearErrorMsg"
        />
      </view>
      <view class="wish-msg__msg-wrap__action" @click="changeMsg">
        <text style="color: var(--link, #576b95); font-size: 12px">帮我想一段</text>
      </view>
      <view class="wish-msg__error-msg">{{ errorMsg }}</view>
      <view class="wish-msg__confirm-wrap">
        <van-button
          custom-class="wish-msg__confirm"
          block
          plain
          :loading="btnLoading"
          @click="doSubmit"
        >
          确定
        </van-button>
      </view>
    </van-popup>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/toast/toast';
import Popup from '@youzan/vant-tee/dist/popup/index';
import Field from '@youzan/vant-tee/dist/field/index';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';
import { confirmWishMsg } from '../utils/wish-msg';
import { errorToast } from '@youzan/tee-biz-util';

/** 只有h5展示 */

let isWeixin;
/* #ifdef web */
isWeixin = _global?.env?.isWeixin ?? false;
/* #endif */

/* #ifdef weapp */
isWeixin = false;
/* #endif */

const MSG_LIMIT = 140;
const NAME_LIMIT = 15;
const INPUT_ERROR_MSG = {
  giver: '名字不填的话我们就帮你想了哦',
  specify: '留言不填的话我们就帮你想了哦',
};

export default {
  name: 'wish-msg-popup',

  components: {
    'van-popup': Popup,
    'van-field': Field,
    'van-button': Button,
  },

  data() {
    return {
      isWeixin,
      showPopup: false,
      validateName: !isWeixin,
      btnLoading: false,
      showNameInput: true,
      wish: {},
      buyerMsgGroup: [],
      giver: '',
      specify: '',
      errorType: '',
      errorMsg: '',
      themeColors: {},
    };
  },

  created() {
    mapData(this, ['kdtId', 'themeColors']);
    mapData(this, {
      payResult: (val) => {
        this.payResult = val;
        if (val.wishDTO) {
          this.wish = val.wishDTO;

          this.showPopup = Boolean(this.wish && !this.wish.userCommit);
          // 不展示则需要触发下一个弹窗
          if (!this.showPopup) {
            this.$emit('close');
            return;
          }

          this.buyerMsgGroup = this.wish.buyerMsgGroup ?? [];
          const len = this.buyerMsgGroup.length;
          this.giver = len > 0 ? this.getRandomMsg().buyerName : '有钱就是那么任性';
        } else {
          this.$emit('close');
        }
      },
    });
  },

  methods: {
    doSubmit() {
      const giver = this.validateName ? this.giver : '';
      const { orderNo } = this.ctx.data;

      if (!this.validate(giver, this.specify)) {
        return;
      }

      this.btnLoading = true;
      confirmWishMsg(
        {
          giver,
          msg: this.specify,
          order_no: orderNo,
        },
        this.kdtId
      )
        .then(() => {
          Toast('留言成功');
          this.showPopup = false;
          this.$emit('close');
        })
        .catch((response) => {
          const res = response?.data || {};
          if (+res.code === 200) {
            Toast('留言成功');
            this.showPopup = false;
            this.$emit('close');
          } else {
            errorToast(response, { message: '咦，你姿势不对，信息提交不成功' });
          }
        })
        .then(() => {
          this.btnLoading = false;
        });
    },

    setGiver({ value = '' }) {
      this.giver = value;
    },

    setSpecify({ value = '' }) {
      this.specify = value;
    },

    getRandomMsg(currMsg) {
      const msgs = (this.wish && this.wish.buyerMsgGroup) || [];
      if (msgs.length === 1) {
        return msgs[0];
      }
      if (msgs.length > 1) {
        const index = msgs.indexOf(currMsg);
        const rest = index >= 0 ? msgs.slice(0, index).concat(msgs.slice(index + 1)) : msgs;
        return rest[Math.floor(Math.random() * rest.length)];
      }
      return {
        buyerName: '神秘天使',
        specify: '每年的情人节礼物我都包了，只要你愿意。',
      };
    },

    validate(buyerName = '', specify = '') {
      if (this.validateName) {
        if (!buyerName.trim()) {
          this.giver = this.getRandomMsg().buyerName;
          this.errorType = 'giver';
          this.errorMsg = INPUT_ERROR_MSG.giver;
          return false;
        }
        if (buyerName.length > NAME_LIMIT) {
          Toast('姓名字数不能超过' + NAME_LIMIT);
          return false;
        }
      }

      if (!specify.trim()) {
        this.changeMsg();
        this.errorType = 'specify';
        this.errorMsg = INPUT_ERROR_MSG.specify;
        return false;
      }

      if (specify.length > MSG_LIMIT) {
        Toast('留言字数不能超过' + MSG_LIMIT);
        return false;
      }

      return true;
    },

    changeMsg() {
      this.currMsg = this.getRandomMsg(this.currMsg);
      this.specify = this.currMsg.specify;
    },

    clearErrorMsg() {
      this.errorType = '';
    },
  },
};
</script>

<style lang="scss">
.wish-msg {
  border-radius: var(--theme-radius-dialog, 20px) var(--theme-radius-dialog, 20px) 0 0;

  &__header {
    text-align: center;
    font-weight: 500;
    padding: 12px 0;
  }

  &__name-selection {
    display: flex;
    justify-content: flex-start;
    padding: 16px;

    &-active {
      border: 1px solid var(--theme-page-active-btn-border-color, #323233) !important;
      color: var(--theme-page-active-btn-text-color, #323233) !important;
    }

    &-normal {
      border: 1px solid var(--theme-page-btn-border-color, #f2f3f5) !important;
      color: var(--theme-page-btn-text-color, #323233) !important;
    }

    &-disabled {
      border: 1px solid var(--theme-page-disabled-btn-border-color, #c8c9cc) !important;
      color: var(--theme-page-btn-text-color, #c8c9cc) !important;
    }

    .t-button {
      position: relative;
      padding: 4px 12px;
      height: 28px;
      margin-right: 12px;
      border-radius: var(--theme-radius-button, 999px);
      background: var(--theme-page-btn-bg-color, #f2f3f5);
    }
  }

  &__name-wrap,
  &__msg-wrap {
    position: relative;
    text-align: left;
  }

  &__name-wrap {
    &.slide-name {
      padding-top: 50px;
    }
  }

  &__msg-wrap {
    &__textarea {
      height: 72px;
      min-height: 72px;
    }

    &__action {
      text-align: right;
      height: 36px;
      line-height: 36px;
      padding-right: 16px;
      margin-bottom: 38px;
    }
  }

  &__error-msg {
    margin-top: 10px;
    color: #ed5050;
    font-size: 12px;
  }

  &__confirm-wrap {
    margin: 0 16px;
  }

  &__confirm {
    margin-top: 10px;
    margin-bottom: 8px;
    height: 40px;
    background: var(--main-bg, #323233);
    color: var(--main-text, #fff);
    border-radius: var(--theme-radius-button, 999px) !important;
  }
}

@keyframes blink-border {
  0% {
    border: 1px solid #e5e5e5;
  }

  20% {
    border: 1px solid #ed5050;
  }

  50% {
    border: 1px solid #fff;
  }

  80% {
    border: 1px solid #ed5050;
  }

  100% {
    border: 1px solid #e5e5e5;
  }
}

.blink-border::after {
  animation-name: blink-border;
  animation-duration: 1s;
  top: -50%;
  left: -54%;
  right: -54%;
  bottom: -50%;
}
</style>
