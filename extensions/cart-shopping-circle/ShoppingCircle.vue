<template>
  <view v-if="isShow && isAuthProtocol" class="shop-circle" @click="handleClick">
    <view class="shop-circle__icon" />
    <view class="shop-circle__text">已同步至好物圈</view>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      isShow: false,
      isAuthProtocol: false,
    };
  },

  created() {
    mapData(this, ['isAuthProtocol']);
    mapData(
      this,
      {
        showShoppingCircle: (val) => {
          this.isShow = !!val;
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    handleClick() {
      Tee.navigate({
        url: '/packages/common/shopping-list/entry/index',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.shop-circle {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;

  &__icon {
    background-image: url(https://img01.yzcdn.cn/weapp/wsc/XiofBH.png);
    background-repeat: no-repeat;
    background-size: contain;
    width: 18px;
    height: 18px;
  }

  &__text {
    margin-left: 5px;
    font-size: 13px;
    color: #9b9b9b;
  }
}
</style>
