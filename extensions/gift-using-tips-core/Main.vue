<template>
  <view class="using-tips">
    <view class="top-section">
      <view class="using-tips-title"> 如何送礼 </view>
      <view class="tips-text">1. 点击“挑选礼物”</view>
      <view class="tips-img-wrap tips-img-wrap-1">
        <image
          class="tips-img-1"
          src="https://img01.yzcdn.cn/public_files/2018/07/03/a647b78358e3b2f5657dc0374e8960e1.png"
        />
        <view class="touch-icon"></view>
      </view>

      <view class="tips-text">2. 支付后分享给好友或将海报分享到朋友圈</view>

      <view class="tips-img-wrap tips-img-wrap-2">
        <image
          class="tips-img-2"
          src="https://img01.yzcdn.cn/public_files/2018/07/03/b3d280605811b32517e081da569bb756.png"
        />
        <view class="touch-icon"></view>
      </view>
    </view>

    <view class="bottom-section">
      <view class="using-tips-title"> 领取规则 </view>
      <view class="tips-text">1. 每个人最多能领取一件礼物。</view>
      <view class="tips-text"
        >2. 可在<text class="emphasize">个人中心 - 我的礼物</text
        >查看，送礼人可选择继续转赠或自己领取，也可选择退款。</view
      >
      <view class="tips-text"
        >3.
        礼物领取后，需填写地址才能收到礼物。礼物兑换有效期以商家设置为准，若有超出有效期未填地址的礼物，可联系商家处理。</view
      >
      <view class="tips-text"
        >4.
        送礼订单暂不支持用户主动发起退款，未领取及超出兑换有效期仍未填写地址的礼物可以联系商家退款。</view
      >
    </view>
  </view>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped>
$COMMON_COLOR: #e0493f;

page {
  background: $COMMON_COLOR;
  padding: 0 15px;
  width: 100%;
  box-sizing: border-box;
}

.using-tips {
  margin-top: 10px;
  background: #fff;
  border-radius: 5px;
  padding-left: 42px;
  padding-right: 18px;
  margin-bottom: 20px;
}

.tips-img-1,
.tips-img-2 {
  width: 250px;
}

.tips-img-1 {
  margin-top: 5px;
  height: 133px;
  margin-bottom: 26px;
}

.tips-img-2 {
  margin-top: 9px;
  height: 152px;
}

.tips-text {
  line-height: 17px;
}

.bottom-section {
  padding-bottom: 30px;
  .tips-text {
    margin-top: 15px;
  }

  .using-tips-title {
    padding-top: 33px;

    &::after {
      top: 33px;
    }
  }
}

.top-section {
  .using-tips-title {
    padding-top: 23px;
  }
}

.using-tips-title {
  position: relative;

  &::after {
    content: '';
    display: block;
    width: 15px;
    height: 15px;
    background-image: url('https://img01.yzcdn.cn/public_files/2018/11/16/a2c0299600d56e8f5e3ad9efe58ee205.png');
    background-size: 15px 15px;
    background-position: center center;
    position: absolute;
    top: 23px;
    left: -22px;
  }
}

.first-line-text {
  margin-top: 7px;
}

.tips-img-wrap {
  position: relative;
}

.tips-img-wrap-1 {
  .touch-icon {
    top: 93px;
    left: 186px;
    position: absolute;
    width: 29px;
    height: 43px;
    background-size: 29px 43px;
    background-image: url('https://img01.yzcdn.cn/public_files/2018/07/03/abef47b956e3b67de56deef6b833a4fc.png');
  }
}

.tips-img-wrap-2 {
  .touch-icon {
    top: 121px;
    left: 173px;
    position: absolute;
    width: 29px;
    height: 43px;
    background-size: 29px 43px;
    background-image: url('https://img01.yzcdn.cn/public_files/2018/07/03/abef47b956e3b67de56deef6b833a4fc.png');
  }
}

.emphasize {
  color: $COMMON_COLOR;
}
</style>
