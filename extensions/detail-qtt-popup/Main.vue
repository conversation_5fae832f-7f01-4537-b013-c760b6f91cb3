<template>
  <view>
    <follow-popup :show="showFollowPopup" @close="closeFollowPopup" />
  </view>
</template>

<script>
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { args } from '@youzan/tee-util/lib/common/url';
import { mapData } from '@youzan/ranta-helper-tee';

import { queryMpFocus, querySubscribeRelation } from './api';
import { createSubscribeWithSyncTakeVoucher } from './utils/subscribe';

import FollowPopup from './components/follow-popup';

const openSubscriptionSettings = args.get('openSubscriptionSettings', window.location.href) || 0;

export default {
  components: {
    'van-toast': VanToast,
    'follow-popup': FollowPopup,
  },

  data() {
    return {
      showFollowPopup: false,
      tradePartyId: 0,
    };
  },

  computed: {},

  created() {
    const isFxZpp = window._global?.env?.isFxZpp;
    const isWeapp = window._global?.miniprogram?.isWeapp;
    mapData(this, ['tradePartyId'], {
      callback: () => {
        // 群团团订单且微信小程序环境下
        if (isFxZpp && isWeapp && this.tradePartyId) {
          this.init();
        }
      },
    });
  },

  methods: {
    // 订阅成功回调
    subscribeSuccessCallback() {
      Toast('已帮你订阅该团长，福利早知道');
    },

    // 发券成功回调
    takeVoucherSuccessCallback() {},

    init() {
      // 1. 走自动订阅流程 1表示小程序订阅设置已开，且接口未订阅
      if (+openSubscriptionSettings === 1) {
        querySubscribeRelation({
          subscribedId: this.tradePartyId,
        })
          .then((res) => {
            this.subscribed = res?.relationExist;
            // 已订阅
            if (this.subscribed) {
              this.followPublicAccount();
            } else {
              createSubscribeWithSyncTakeVoucher(
                this.tradePartyId,
                this.subscribeSuccessCallback,
                this.takeVoucherSuccessCallback
              );
            }
          })
          .catch(() => {
            this.followPublicAccount();
          });
      } else {
        this.followPublicAccount();
      }
    },

    // 2. 走引导关注公众号流程
    followPublicAccount() {
      queryMpFocus().then((res) => {
        const { focusedQttMp = true } = res;
        // 未关注则弹起引导公众号弹窗
        this.showFollowPopup = !focusedQttMp;
      });
    },

    closeFollowPopup() {
      this.showFollowPopup = false;
    },
  },
};
</script>

<style lang="scss">
$text-color: #323233;
</style>
