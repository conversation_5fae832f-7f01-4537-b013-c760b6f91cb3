<template>
  <van-popup
    round
    closeable
    :show="show"
    position="bottom"
    @close="handleClose"
    safe-area-inset-bottom
  >
    <view class="address-list-container">
      <!-- 顶部导航栏 -->
      <view class="nav-bar">
        <text v-if="showReceiverModifyPopup" @click="handleClose">
          <van-icon name="arrow-left" />
          <text class="text-gray">返回</text>
        </text>
        <text class="title">选择收货地址</text>
      </view>

      <scroll-view class="content" scroll-y type="list" :bounces="false">
        <!-- 地址列表 -->
        <view class="address-list">
          <address-item
            v-for="(address, index) in addressList"
            :key="index"
            :address="address"
            :selected="address.id === selectedId"
            @selected="handleSelect"
            @edit="handleEdit"
          ></address-item>
        </view>

        <view v-if="disableAddressList.length">
          <view v-for="(item, _index) in disableAddressList" :key="_index">
            <block v-if="item.list && item.list.length">
              <view class="disable-reason">以下地址{{ item.reason }}</view>
              <view class="address-list">
                <address-item
                  v-for="(address, index) in item.list"
                  :key="index"
                  :address="address"
                  disabled
                ></address-item>
              </view>
            </block>
          </view>
        </view>
        <view style="height: 100px"></view>
      </scroll-view>

      <!-- 添加新地址按钮 -->
      <button-group :btns="BUTTONS" @click="handleClick" />
    </view>
  </van-popup>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import AddressItem from './address-item.vue';
import buttonGroup from '../../components/button-group.vue';
import { AUTO_SHOW_MODIFY_ADDRESS_POPUP } from '../../constants';
import { setStorage } from '@youzan/tee-api';
import { mapState, mapActions } from '@ranta/store';

/* #ifdef weapp */
import { PAGE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
/* #endif */
/* #ifdef web */
import Tee from '@youzan/tee';
/* #endif */

const BUTTONS = [
  {
    type: 'create',
    primary: false,
    text: '新增地址',
  },
  {
    type: 'ok',
    primary: true,
    text: '确定',
  },
];

export default {
  components: {
    'van-icon': Icon,
    'van-popup': Popup,
    AddressItem,
    buttonGroup,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      BUTTONS,
      selectedId: null,
      ...mapState(this, ['disableAddressList', 'addressList', 'showReceiverModifyPopup']),
    };
  },
  computed: {
    // 获取格式化的地址列表（仅包含可用地址）
    formattedActiveList() {
      return this.addressList || [];
    },
    // 是否强制选择POI
    forcePoiSelect() {
      // 这里可以根据实际业务逻辑来确定是否强制选择POI
      // 暂时返回false，可以根据需要从store或props中获取
      return false;
    },
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.disableBodyScroll();
      } else {
        this.enableBodyScroll();
      }
    },
  },
  created() {
    mapActions(this, ['selectAddress']);
  },
  methods: {
    disableBodyScroll() {
      /* #ifdef web */
      document.querySelector('body').style.overflow = 'hidden';

      /* #endif */
      /* #ifdef weapp */
      this.$root.setPageStyle({ overflow: 'hidden', height: '100vh' });
      /* #endif */
    },
    enableBodyScroll() {
      /* #ifdef web */
      document.querySelector('body').style.overflow = 'visible';
      /* #endif */
      /* #ifdef weapp */
      this.$root.setPageStyle({ overflow: 'unset', height: 'inherit' });
      /* #endif */
    },
    handleClose() {
      this.ctx.data.showAddressModifyPopup = false;
    },
    handleSelect({ id }) {
      console.log('选中地址', id);
      this.selectedId = id;
    },
    handleClick({ type }) {
      if (type === 'create') {
        this.onAddAddress();
      } else if (type === 'ok') {
        if (!this.selectedId) {
          return;
        }
        const selected = this.addressList.find((item) => item.id === this.selectedId);
        this.selectAddress(selected);
      }
    },
    onAddAddress() {
      setStorage(AUTO_SHOW_MODIFY_ADDRESS_POPUP, true);
      /* #ifdef weapp */
      // 需要接统跳 不然大客定制会白屏
      navigateToRantaPage({
        pageType: PAGE_TYPE.ADDRESS_EDIT,
      });
      /* #endif */

      /* #ifdef web */
      Tee.navigate({
        url: `/wsctrade/order/tee-address/edit`,
      });
      /* #endif */
    },
    handleEdit(address) {
      // 处理可用地址的编辑逻辑
      setStorage(AUTO_SHOW_MODIFY_ADDRESS_POPUP, true);

      const id = address.id || null;
      /* #ifdef weapp */
      const dbid = this.ctx.lambdas.setDb({
        id,
        list: this.formattedActiveList, // 只传递可用地址列表
        forcePoiSelect: this.forcePoiSelect,
        delta: 1,
      });
      navigateToRantaPage({
        url: `/packages/trade-buy-subpage/order/address-edit/index?dbid=${dbid}`,
        pageType: PAGE_TYPE.ADDRESS_EDIT,
      });
      /* #endif */

      /* #ifdef web */
      Tee.navigate({
        url: `/wsctrade/order/tee-address/edit?address_id=${id}`,
      });
      /* #endif */
    },
  },
};
</script>

<style scoped lang="scss">
.address-list-container {
  width: 100%;
  background-color: #f5f5f5;

  .nav-bar {
    height: 52px;
    display: flex;
    align-items: center;
    background: #fff;
    padding: 0 12px;
    .text-gray {
      color: #999;
      font-size: 14px;
    }

    .back-button {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    .title {
      font-weight: 500;
      font-size: 18px;
      color: #323233;
      flex: 1;
      text-align: center;
      margin-right: 34px;
    }
  }

  .address-list {
    padding: 10px 16px;

    > view {
      margin-bottom: 10px;
    }
  }
  .disable-reason {
    margin-left: 16px;
    font-size: 14px;
    color: #666;
  }

  .content {
    height: calc(80vh - 44px);
    overflow-y: auto;
  }
}
</style>
