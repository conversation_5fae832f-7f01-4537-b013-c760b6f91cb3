import type { OrderDetailPayment } from '@youzan-cloud/cloud-biz-types';
import { getPostageLabel } from '@youzan/wsc-tee-trade-common/lib/order-utils/postageUtils';

export const formatPrice = (price = 0, operator = '') => {
  const symbol = operator ? `${operator} ¥` : '¥';
  return `${symbol}${(Math.abs(price) / 100).toFixed(2)}`;
};

export function formatedPayUmpDiscountMoney(payUmpDiscountMoney) {
  return formatPrice(payUmpDiscountMoney, payUmpDiscountMoney >= 0 ? '-' : '+');
}

export const cloudData = {
  getPayment: ({ order, price, payment, extraPrices = [] }): OrderDetailPayment => {
    const { total, postage, payUmpDiscountMoney, prepayCardPay, realPay } = price;
    const {
      orderExtraPrices,
      deductionPay = 0,
      buyerRealPay,
      expressPayMode,
      payPrice = 0,
    } = payment;
    const { pointsPrice = 0 } = order;

    return {
      realPay: buyerRealPay,
      payPrice,
      pointsPrice,
      // expressPayMode,
      prepayCardPay: deductionPay,
      payUmpDiscountMoney,
      extraFees: orderExtraPrices,
      // postage: payment.postage,
      formatted: {
        total,
        postageTitle: getPostageLabel(expressPayMode),
        postage,
        payUmpDiscountMoney: formatedPayUmpDiscountMoney(payUmpDiscountMoney),
        // @ts-ignore
        extraFees: extraPrices,
        prepayCardPay,
        realPay,
      },
    };
  },
};

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
