import { bridge, cloud } from '@youzan/ranta-helper';
import { mapData } from '@youzan/ranta-helper-tee';
import { WechatReceiptOrder } from '@youzan-cloud/cloud-biz-types';
import Main from './Main.vue';
import PaidState from './PaidState.vue';
import { ShopInfo, Payment } from './type';
import getCloudData from './cloud';

export default class PaidStateExtension {
  ctx: any;

  /**
   * shopInfo
   * @deprecated 从2.0开始
   * @desc 店铺信息
   * @type {ShopInfo}
   */
  @bridge('shopInfo', 'data')
  shopInfoV1: ShopInfo;

  /**
   * payment
   * @deprecated 从2.0开始
   * @desc 支付信息
   * @type {Payment}
   */
  @bridge('payment', 'data')
  paymentV1: Payment;

  /**
   * orderNo
   * @deprecated 从2.0开始
   * @desc 订单号
   * @type {string}
   */
  @bridge('orderNo', 'data')
  orderNo: string;

  /**
   * order
   * @desc 订单信息
   * @type {WechatReceiptOrder}
   */
  @cloud('order', 'data')
  orderInfo: WechatReceiptOrder;

  constructor(options) {
    this.ctx = options.ctx;

    this.initCloudData();
  }

  static widgets = {
    Main,
    PaidState,
  };

  initCloudData() {
    // 初始化云数据
    mapData(this, ['payResult'], {
      callback: () => {
        const { payResult } = this.ctx.data;
        if (!payResult.payResultVO) {
          return;
        }
        const { shopInfoV1, paymentV1 } = getCloudData(payResult.payResultVO);
        this.shopInfoV1 = shopInfoV1;
        this.paymentV1 = paymentV1;
      },
    });
    mapData(this, ['orderNo'], {
      callback: () => {
        this.orderNo = this.ctx.data.orderNo;
        this.orderInfo = {
          orderNo: this.ctx.data.orderNo,
        };
      },
    });
  }
}
