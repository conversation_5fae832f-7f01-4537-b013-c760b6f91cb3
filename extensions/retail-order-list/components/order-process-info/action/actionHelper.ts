/**
 * 所有订单流程中的用户操作行为的定义和执行逻辑
 */
import { makePhoneCall } from '@youzan/tee-api';
import Tee from '@youzan/tee';
import { url } from '@youzan/tee-util';
import navigate from '@youzan/tee-biz-navigate';
import { baiduToGcj } from '@youzan/shop-tee-shared';
import { beforePageSkip } from '../../../chainShoreHelper';

// 所有用户操作的类型枚举
export const ActionType = {
  Tel: 'tel',
  Location: 'location',
  VirtualCode: 'virtualCode',
  SelfFetchCode: 'selfFetchCode',
  Delivery: 'delivery',
};
// 所有type的数组
export const actionTypeValues = Object.values(ActionType);

export const actionHandler = (type, opt) => {
  switch (type) {
    // 电话
    case ActionType.Tel: {
      makePhoneCall({ phoneNumber: opt.tel });
      return;
    }
    // 定位
    case ActionType.Location: {
      const { lat, lng, formatAddress: address, name } = opt;
      if (lng && lat) {
        // 后端存储的经纬度都是百度，百度坐标系转高德坐标系再请求wx的api
        const gcjLocation = baiduToGcj(+lng, +lat);
        Tee.$native.openLocation({
          address,
          latitude: +gcjLocation.lat,
          longitude: +gcjLocation.lng,
          name,
        });
      }
      return;
    }
    // 核销码
    case ActionType.VirtualCode: {
      const { orderNo, kdtId } = opt;
      beforePageSkip(kdtId);
      Tee.navigate({
        url: `packages/trade/cert/verify-ticket/index?order_no=${orderNo}&kdt_id=${kdtId}`,
      });
      return;
    }
    // 自提码
    case ActionType.SelfFetchCode: {
      const { orderNo, kdtId } = opt;
      beforePageSkip(kdtId);
      const selfFetchUrl = `/wsctrade/order/selffetch/detail?orderNo=${orderNo}&kdtId=${kdtId}`;
      // openWebView
      navigate({ url: selfFetchUrl, type: 'navigateTo', needPrefixDomain: true });
      return;
    }
    // 物流
    case ActionType.Delivery: {
      const { orderNo, kdtId } = opt;
      beforePageSkip(kdtId);

      const path = url.args.add('/packages/trade/order/express/index', { orderNo, kdtId });
      Tee.navigate({
        url: path,
        type: 'navigateTo',
      });
      return null;
    }
  }
};
