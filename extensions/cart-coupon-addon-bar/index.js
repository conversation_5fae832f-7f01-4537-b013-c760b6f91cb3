import CouponAddonBar from './CouponAddonBar.vue';
import { mapCtxData } from '@ranta/store';
import createStore from './store';

export default class CouponAddon {
  static widgets = { CouponAddonBar };

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    mapCtxData(this, [
      'kdtId',
      'offlineId',
      'shopCart',
      'themeColors',
      'themeStyle',
      'checkedGoodsList',
      'selectedPromotions',
    ]);
  }
}
