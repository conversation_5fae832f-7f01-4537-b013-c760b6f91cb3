import { getNewPromotionUrl, CashbackType, SupportedActivity } from './constants';

export const formatAwardInfo = (info, orderNo) => {
  // 处理积分的 url 拼接逻辑
  if (info.credit) {
    info.credit.url = '/packages/pointstore/point-center/index';
  }

  // 先用后付逻辑
  if (info?.creditPay?.show === false) {
    delete info.creditPay;
  }

  // 处理储值金/现金的 url 拼接逻辑
  if (info.cashBack?.[0]) {
    if (info.cashBack?.[0]?.cashbackType === CashbackType.Store) {
      info.cashBack[0].url = '/packages/pre-card/home/<USER>';
    } else {
      // 兼容处理V单返现
      const isV = (orderNo || '').substr(0, 1) === 'V';
      if (isV) {
        info.cashBack[0].url = `/packages/user/cashback/list/index`;
      } else {
        // E单
        info.cashBack[0].url = `/packages/user/cashback/detail/index?order_no=${orderNo}`;
      }
    }
  } else if (info.cashBack) delete info.cashBack;

  // 处理支付有礼 - 老默认图片转换
  if (info.paidPromotionInfo) {
    const { paidImageUrl, promotionType, imageType } = info.paidPromotionInfo ?? {};
    // 处理老的默认图片的转换
    if (paidImageUrl) {
      info.paidPromotionInfo.imgUrl = getNewPromotionUrl({
        imageUrl: paidImageUrl,
        imgType: imageType,
        type: promotionType,
      });
    }
    // 仅展示目前小程序支持的
    if (
      Object.keys(SupportedActivity)
        .map((key) => SupportedActivity[key])
        .indexOf(promotionType) === -1
    ) {
      delete info.paidPromotionInfo;
    }
  }

  return info;
};
