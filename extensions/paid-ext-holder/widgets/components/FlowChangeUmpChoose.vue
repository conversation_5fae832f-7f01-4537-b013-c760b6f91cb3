<template>
  <view class="fc-ump_choose">
    <view
      v-for="(item, index) in originData"
      :key="item.cooperateKdtId"
      @click="handleClick(item, index)"
    >
      <view v-if="item.adType === adType.seckill" class="fc-ump_choose-item">
        <image class="image" :src="item.picture" />
        <!-- #ifdef weapp -->
        <native-text class="price">
          <native-text>秒杀价 ¥</native-text>
          <native-text class="price-weight-big price-weight">{{ originData.bigPrice }}</native-text>
          <native-text class="price-weight">.{{ originData.centPrice }}</native-text>
        </native-text>
        <!-- #else -->
        <view class="price"
          >秒杀价 ¥
          <text class="price-weight"
            ><text class="price-weight-big">{{ originData.bigPrice }}</text
            >.{{ originData.centPrice }}</text
          ></view
        >
        <!-- #endif -->
        <van-button custom-class="button" type="danger" round size="mini">立即领</van-button>
      </view>
      <view v-if="item.adType === adType.coupon" class="fc-ump_choose-item choose-item-coupon">
        <view class="coupon">
          <view class="hole hole-left"></view>
          <view class="hole hole-right"></view>
          <view class="coupon-top">
            <!-- #ifdef weapp -->
            <native-text class="quota" :style="item.couponValueFontSize">
              <native-text class="unit" v-if="item.isShowMax">最高</native-text>{{ item.couponQuota
              }}<native-text class="unit" v-if="item.isShowUnit">元</native-text
              ><native-text class="unit" v-if="item.isShowDiscount">折</native-text>
            </native-text>
            <!-- #else -->
            <view class="quota" :style="item.couponValueFontSize">
              <text class="unit" v-if="item.isShowMax">最高</text>{{ item.couponQuota
              }}<text class="unit" v-if="item.isShowUnit">元</text
              ><text class="unit" v-if="item.isShowDiscount">折</text>
            </view>
            <!-- #endif -->
          </view>
          <view class="coupon-bottom">
            <native-text class="message">{{ item.preferentialCopyWriting }}</native-text>
          </view>
        </view>
        <van-button custom-class="button" type="danger" round size="mini">立即领</van-button>
      </view>
    </view>
  </view>
</template>

<script>
import Button from '@youzan/vant-tee/dist/button/index';

import { adType } from '../../utils/flow-change';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'flow-change-ump-choose',

  components: {
    'van-button': Button,
  },

  props: {
    originData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      adType,
    };
  },
  computed: {
    originDataFormat() {
      return (this.originData || []).map((item) => {
        return {
          ...item,
          picture: fullfillImage(item.picture, 'small'),
        };
      });
    },
  },

  created() {
    (this.originData || []).forEach((item, index) => {
      this.logger(item, index, 'view');
    });
  },

  methods: {
    logger(data, index, eventType) {
      this.$emit('onLogger', {
        data,
        type: adType.choose,
        eventType,
        index,
      });
    },
    handleClick(data, index) {
      this.logger(data, index, 'click');
    },
  },
};
</script>

<style lang="scss" scoped>
.fc-ump {
  &_choose {
    width: 702rpx;
    height: 184px;
    background: url('https://b.yzcdn.cn/flow-change-ump/choose-bg-3.x.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    padding: 32px 7.5px 0 7.5px;
    box-sizing: border-box;
    justify-content: space-around;
    .choose-item-coupon {
      display: block;
      text-align: center;
      padding: 8px 8px 0 8px;
      box-sizing: border-box;
      margin-right: 5px;

      .coupon {
        width: 92px;
        height: 88px;
        margin-bottom: 12px;
        border-radius: 8px;
        background: rgba(255, 68, 68, 0.1);
        position: relative;
        .hole {
          width: 10px;
          height: 10px;
          position: absolute;
          background: #fff;
          border-radius: 50%;
        }

        .hole-left {
          left: -3px;
          top: 48.5px;
        }

        .hole-right {
          right: -3px;
          top: 48.5px;
        }

        &-top {
          width: 100%;
          height: 53.5px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-bottom: 1px rgba(255, 68, 68, 0.2) dashed;
          .quota {
            /* #ifdef weapp */
            display: flex;
            align-items: baseline;
            /* #endif */
            width: 71px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 30px;
            font-weight: bold;
            color: #f44;
            .unit {
              font-size: 12px;
              font-weight: normal;
            }
          }
        }

        &-bottom {
          width: 100%;
          height: 34.5px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #f44;
          font-size: 12px;
          .message {
            display: block;
            width: 75px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    &-item {
      width: 109px;
      height: 144px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .image {
        width: 60px;
        height: 60px;
        border-radius: 8px;
      }
      .price {
        font-size: 12px;
        color: #f44;
        margin-top: 6px;
        margin-bottom: 10px;
        width: 100px;
        text-align: center;
        /* #ifdef weapp */
        display: flex;
        align-items: baseline;
        /* #endif */
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &-weight {
          /* #ifdef web */
          display: inline-block;
          /* #endif */
          font-weight: bold;
          &-big {
            font-size: 16px;
          }
        }
      }
      .button {
        color: white;
        background: #f44;
        border-color: #f44;
        font-size: 14px;
        width: 64px;
      }
    }
  }
}
</style>
