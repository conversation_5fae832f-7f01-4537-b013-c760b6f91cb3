<template>
  <view>
    <view class="express-goods-card" :style="themeCSS">
      <view class="main-info">
        <view class="goods-image" :style="goodsImageStyle">
          <span v-if="firstGoods.num > 1" class="tips"> x{{ firstGoods.num }} </span>
        </view>
        <view class="express">
          <p v-if="firstGoods.statusDesc" class="delivery">
            <span :class="'state-' + firstGoods.status">
              {{ firstGoods.statusDesc }}
            </span>
            <span class="detail" @click="handleGotoDetail">详情<van-icon name="arrow" /></span>
          </p>
          <p v-if="cityDetail.channelStr" class="company">
            承运来源：
            <span>{{ cityDetail.channelStr }}</span>
          </p>
          <p v-if="expressNo" class="express-no">
            运单编号：
            <span>{{ expressNo }}</span>
            <span class="copy" @click="copy">复制</span>
          </p>
        </view>
      </view>
    </view>
    <logistics-map
      v-if="showMap"
      :dist-id="expressDetail.distId"
      :local-delivery="expressDetail.localDeliveryVO"
      :city-detail="cityDetail"
      @refetchData="getExpressDetail"
    />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import { baiduToGcj } from '@youzan/shop-tee-shared';
import { setClipboardData } from '@youzan/tee-api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Icon from '@youzan/vant-tee/dist/icon/index';
import fullfillImage from '@youzan/utils/url/fullfillImage';

import { getCityDetail } from '../api/index';
import LogisticsMap from '../components/LogisticsMap.vue';

export default {
  name: 'retial-logistics',
  components: {
    'van-icon': Icon,
    'logistics-map': LogisticsMap,
  },
  props: {
    expressData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      cityDetail: {},
      themeCSS: '',
      kdtId: 0,
    };
  },

  created() {
    mapData(this, ['orderNo', 'kdtId', 'themeCSS']);
    this.getExpressDetail();
  },
  computed: {
    expressDetail() {
      return this.expressData?.[0] ?? {};
    },
    showMap() {
      const { status } = this.cityDetail;
      return [2,3].includes(status)  // 待取货 || 配送中
    },
    firstGoods() {
      const { cityDetail, expressDetail = {} } = this;
      const { orderItems: goodsList } = expressDetail;
      if (!goodsList) return {};
      let statusStr = '';
      if (cityDetail.record) {
        statusStr = cityDetail.record[0].statusStr;
      }
      return (
        goodsList.map((item) => {
          return {
            ...item,
            status: String(item.status || 0),
            statusDesc: statusStr || item.deliveryStatusDesc || '暂无',
          };
        })[0] || {}
      );
    },

    goodsImageStyle() {
      const imgUrl = this.firstGoods.imgUrl;
      return `background-image: url("${fullfillImage(imgUrl, '!200x0.jpg')}")`;
    },

    expressNo() {
      if (!this.expressDetail.localDeliveryVO) return '';
      console.log(`🔥 : this.expressDetail`, this.expressDetail)
      return this.expressDetail.localDeliveryVO.deliveryNo;
    },
  },

  methods: {
    copy() {
      setClipboardData(this.expressNo).then(() => {
        Toast.success('已复制');
      });
    },
    getExpressDetail() {
      if (!this.expressDetail.distId) return;
      getCityDetail({
        order_no: this.orderNo,
        pack_id: this.expressDetail.distId,
        kdt_id: this.kdtId,
      }).then((cityData) => {
        const { buyerLat, buyerLng, shopLat, shopLng } = cityData || {};
        if (buyerLat && buyerLng) {
          const { longitude, latitude } = baiduToGcj(+buyerLng, +buyerLat);
          cityData.buyerLng = longitude;
          cityData.buyerLat = latitude;
        }
        if (shopLat && shopLng) {
          const { longitude, latitude } = baiduToGcj(+shopLng, +shopLat);
          cityData.shopLng = longitude;
          cityData.shopLat = latitude;
        }
        this.cityDetail = cityData;
      });
    },
    handleGotoDetail() {
      this.$emit('gotoDetail');
    },
  },
};
</script>

<style lang="scss" scoped>
.express-goods-card {
  background: #fff;
  padding: 10px 0;
}

.main-info {
  display: flex;
  align-items: center;
  padding: 0 15px;
  overflow: hidden;
}


.express {
  display: flex;
  flex: 1;
  flex-direction: column;
  font-size: 12px;
  line-height: 1.5;
  vertical-align: middle;

  .delivery {
    display: flex;
    justify-content: space-between;
    margin: 2px 0 8px;
    font-size: 14px;
    line-height: 1;
    font-weight: bold;
  }

  .state-0,
  .state-1 {
    color: var(--icon, #323233);
  }

  .company,
  .express-no {
    color: #999;
    display: flex;
  }
}

.goods-image {
  margin-right: 12px;
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 60px;
  height: 60px;
  .tips {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: px;
    line-height: 16px;
    font-size: 12px;
    text-align: center;
    background: #323233;
    opacity: 0.6;
    color: #fff;
  }
}

.copy {
  color: #000;
  margin-left: 8px;
}

.detail {
  font-size: 12px;
  font-weight: 400;
  color: #999;
}
</style>
