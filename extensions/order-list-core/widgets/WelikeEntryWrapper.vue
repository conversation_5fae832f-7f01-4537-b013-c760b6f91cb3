<template>
  <view :class="[isShowWelikeEntry ? 'welike-entry__wrapper' : '']" v-if="allowShowWelikeEntry">
    <welike-entry
      @show="onShowWelikeEntry"
      source-page="order_list"
      :custom-style="WELIKE_ENTRY_STYLE"
    />
  </view>
</template>

<script>
// TODO: 需要检查welike-entry的样式是否正常，有两种展示场景

// Dependencies
import { mapState } from '@ranta/store';

const WELIKE_ENTRY_STYLE = {
  marginTop: 0,
  borderRadius: '8px',
};
export default {
  data() {
    return {
      WELIKE_ENTRY_STYLE,
      ...mapState(this, ['isShowWelikeEntry', 'isXhsApp', 'isKsApp']),
    };
  },
  computed: {
    // 是否允许展示大家喜欢入口
    allowShowWelikeEntry() {
      return !this.isXhsApp && !this.isKsApp;
    },
  },
  methods: {
    onShowWelikeEntry() {
      this.store.SET_IS_SHOW_WELIKE_ENTRY(true);
    },
  },
};
</script>

<style lang="scss" scoped>
.welike-entry__wrapper {
  padding: 12px 12px 0;
}
</style>
