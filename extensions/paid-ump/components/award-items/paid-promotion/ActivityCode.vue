<template>
  <award-container custom-class="container">
    <view>
      <view class="title">{{ info.title }}</view>
      <view v-if="isPerson" class="tips">
        <image
          class="icon"
          style="width: 60px"
          src="https://b.yzcdn.cn/public_files/b48d25e3fd8e8b0c8fba0f7a630f501c.png"
        />
        售后、商品上新都问我
      </view>
      <view v-else class="tips">
        <image
          class="icon"
          style="width: 50px"
          src="https://b.yzcdn.cn/public_files/7f7118cbfef177a1875d269a925c836c.png"
        />限时抢购、商品上新福利
      </view>
      <view class="add-qrcode">
        <image
          class="finger-icon"
          src="https://b.yzcdn.cn/public_files/8d84791002a76fa071039b5cba32dc35.svg"
        />
        <view style="margin: 0 4px">长按识别二维码{{ isPerson ? '立即添加' : '立即加群' }}</view>
        <image
          class="arrow-icon"
          src="https://b.yzcdn.cn/public_files/12ce371fdd87272ca09f8a363944dd34.svg"
        />
      </view>
    </view>

    <view>
      <image
        class="qrcode"
        :src="info.activityQrUrl"
        mode="aspectFit"
        @touchstart="longTouch"
        show-menu-by-longpress
      />
      <view class="low-version-tips">
        <view class="text">无法识别？</view>
        <concat-button
          custom-class="ump-concat-button"
          @after-contact="afterContact"
          :h5-link="info.detailUrl"
        >
          <view class="link-button">点击此处</view>
        </concat-button>
      </view>
    </view>
  </award-container>
</template>

<script>
import AwardContainer from '../Container';
import ConcatButton from '../../widgets/ConcatButton.vue';
import { ActivityQrCodeTypes } from '../../../constants';

export default {
  name: 'award-activity-code',
  components: {
    'award-container': AwardContainer,
    'concat-button': ConcatButton,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isPerson() {
      return this.info.activityQrCodeType === ActivityQrCodeTypes.Person;
    },
  },
  methods: {
    afterContact() {
      this.$emit('pure-log', {
        et: 'click', // 事件类型
        ei: 'click_marketing_paidpro_service', // 事件标识
        en: '无法识别按钮的点击', // 事件名称
      });
    },
    longTouch() {
      this.$emit('pure-log', {
        et: 'click', // 事件类型
        ei: 'long_press_paidpro_activity_code', // 事件标识
        en: '支付有礼长按活码', // 事件名称
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  padding: 12px;
  height: 144px;
  justify-content: space-between;
  background-image: url('//img01.yzcdn.cn/public_files/89f7502b133d8b590b1be5866f7479c1.jpg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.qrcode {
  width: 100px;
  height: 100px;
}

.add-qrcode {
  font-size: 12px;
  line-height: 16px;
  display: flex;
  align-items: center;
  color: #646566;
  margin-top: 28px;
}

.icon {
  height: 16px;
  margin-right: 6px;
}

.title {
  font-size: 16px;
  line-height: 22px;
  font-weight: bold;
}

.tips {
  color: #969799;
  font-size: 12px;
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.finger-icon {
  width: 24px;
  height: 24px;
}

.arrow-icon {
  width: 12px;
  height: 10px;
  display: flex; // 修复奇怪的 bug，修改 display
}

.low-version-tips {
  color: #969799;
  display: flex;
  padding: 0 4px;
  border: 1px solid #969799;
  margin-top: 4px;
  border-radius: 8px;
  align-items: center;
}

.text {
  font-size: 10px;
  height: 14px;
  line-height: 14px;
  display: inline-flex;
}

.link-button {
  color: #1989fa;
  display: inline-block;
}

.ump-concat-button {
  display: flex !important;
  margin: 0 !important;
  font-size: 10px !important;
  padding: 0 !important;
  height: 14px !important;
  background: transparent !important;
  border: none !important;
}
</style>
