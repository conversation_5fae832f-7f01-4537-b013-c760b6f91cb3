<template>
  <view :style="themeCSS" class="tg-box">
    <view class="tg-box__code-text">取货号</view>
    <view class="tg-box__code-value">{{ takeGoodsCodeStr }}</view>
    <view class="tg-box__code-tip">{{
      hasError ? '请前往订单详情查看' : '到店后出示，即可取货'
    }}</view>
  </view>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index';
import Button from '@youzan/vant-tee/dist/button/index';
import Loading from '@youzan/vant-tee/loading/index';
import { mapData } from '@youzan/ranta-helper-tee';

import { getSelfFetchTakeGoodsMessage } from '../utils/take-goods';

export default {
  components: {
    'van-popup': Popup,
    'van-button': Button,
    'van-loading': Loading,
  },

  data() {
    return {
      themeCSS: '',
      takeGoodsData: {
        loading: true,
      },
      retryTimes: 0,
      orderNo: '',
      hasError: false,
    };
  },

  computed: {
    takeGoodsCodeStr() {
      if (this.hasError) return '出错了';
      if (this.takeGoodsData.loading) return '加载中';
      return this.takeGoodsData.takeGoodsCode;
    },
  },

  created() {
    mapData(this, ['themeCSS', 'orderNo']);
    this.ctx.watch('hasPaid', (val) => {
      // 支付成功再去拿取货号
      if (val) {
        this.showContent();
      }
    });
    this.ctx.data.paidCodeInfoIsShow = true;
  },

  methods: {
    showContent() {
      if (this.hasError) {
        this.retryTimes = 0;
        this.hasError = false;
        this.takeGoodsData.loading = true;
      }
      this.getTakeGoodsMessage();
    },

    requestErrorHandler() {
      // 轮询接口，响应后每秒一次，最多3次
      this.retryTimes += 1;
      if (this.retryTimes < 3) {
        setTimeout(() => {
          this.getTakeGoodsMessage();
        }, 1000);
        return;
      }
      this.hasError = true;
    },

    getTakeGoodsMessage() {
      getSelfFetchTakeGoodsMessage({
        requestNo: this.orderNo,
      })
        .then((res) => {
          if (res?.takeGoodsCode) {
            this.takeGoodsData = {
              ...res,
              loading: false,
            };
            return;
          }
          this.requestErrorHandler();
        })
        .catch(() => this.requestErrorHandler());
    },
  },
};
</script>

<style lang="scss" scoped>
.tg-box {
  /* skyline模式下子组件width无法和父组件一样的宽度需要手动算 */
  width: calc(100vw - 24px);
  padding: 12tpx 0;
  border-radius: 12tpx;
  text-align: center;
  margin: 0 12px 16px;
  background-color: #fff;

  &__code-text {
    font-size: 14tpx;
    color: #333;
    line-height: 20tpx;
  }

  &__code-value {
    margin: 2tpx auto 6tpx;
    color: var(--general, #333);
    font-size: 36tpx;
    line-height: 50tpx;
    font-weight: 600;
  }

  &__code-tip {
    font-size: 14tpx;
    line-height: 20tpx;
    text-align: center;
    color: #999;
  }
}
</style>
