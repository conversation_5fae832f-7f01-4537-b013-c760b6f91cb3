import { string } from '@youzan/tee-util';
import { requestV2 } from '@youzan/tee-biz-request';

export default {
  /**
   * 获取退款状态
   */
  getRefundState({ orderNo, kdtId = 0, itemId }) {
    return new Promise((resolve, reject) => {
      requestV2({
        path: '/wsctrade/refund/getRefundState.json',
        method: 'GET',
        data: {
          kdtId,
          itemId,
          orderNo,
        },
      })
        .then((res = {}) => {
          if (Object.keys(res).length) {
            resolve(string.mapKeysToCamelCase(res));
          } else {
            reject(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  /* #ifdef web */
  getUrlLinkRedirectUrl(data) {
    return requestV2({
      path: '/wsctrade/weapp/mini-program/get-url-link-redirect-url.json',
      method: 'GET',
      data,
    });
  },
  /* #endif */
};
