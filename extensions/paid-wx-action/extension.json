{"extensionId": "@wsc-tee-trade/paid-wx-action", "name": "@wsc-tee-trade/paid-wx-action", "version": "0.1.0", "bundle": "<builtin>", "lifecycle": ["pageDestroyed"], "widget": {"default": "Main", "provide": ["PaidAction"], "consume": ["PaidAction"]}, "data": {"consume": {"kdtId": ["r"], "payResult": ["r"], "orderNo": ["r"], "returnUrl": ["r"], "themeColors": ["r"], "themeCss": ["r"]}, "provide": {"paidActionHeight": ["r"]}}, "event": {"emit": ["openSubscription"], "listen": ["openSubscription"]}, "process": {"invoke": ["navigateTo", "logger", "openShare", "openDetail", "openHome", "openResult"], "define": ["openShare", "openDetail", "openHome", "openResult", "openOutLink"]}, "platform": ["web"]}