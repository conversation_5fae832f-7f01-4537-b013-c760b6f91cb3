import GoodsItemUmp from './widgets/GoodsItemUmp.vue';
import GoodsGroupUmp from './widgets/GoodsGroupUmp.vue';
import ExchangeModel from './widgets/ExchangeModel.vue';
import ExchangeList from './widgets/exchange-list/index.vue';
import UmpInfoPopup from './widgets/UmpInfoPopup.vue';

export default class CartUmpBlock {
  ctx: any;

  constructor(options) {
    this.ctx = options.ctx;
  }

  static widgets = {
    GoodsGroupUmp,
    GoodsItemUmp,
    ExchangeModel,
    ExchangeList,
    UmpInfoPopup,
  };
}
