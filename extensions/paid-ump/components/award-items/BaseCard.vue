<template>
  <!-- 营销基本展示卡片 -->
  <award-container>
    <view class="award-base-card" :style="themeBackground5Style">
      <view class="left">
        <view class="icon">
          <image class="icon-image" v-if="info.icon" :src="info.icon" />
        </view>
        <view class="content t-ellipsis">
          <view class="text">{{ info.text }}</view>
          <view class="desc">{{ info.desc }}</view>
        </view>
      </view>
      <view class="right">
        <van-button
          custom-class="button"
          :color="themeColor"
          round
          size="small"
          @click="selfClick"
          >{{ info.buttonText }}</van-button
        >
      </view>
    </view>
  </award-container>
</template>

<script>
import AwardContainer from './Container';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import { RGBA2RGB } from '../../utils';

export default {
  name: 'award-base-card',
  components: {
    'award-container': AwardContainer,
    'van-button': VanButton,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    themeColor: String,
  },
  computed: {
    themeBackground5Style() {
      return `background: ${RGBA2RGB(this.themeColor, 0.05)};`;
    },
    themeBackgroundStyle() {
      return `background: ${this.themeColor};`;
    },
    themeBorderStyle() {
      return `border-color: ${this.themeColor};`;
    },
  },
  methods: {
    selfClick() {
      this.$emit('click-handle');
    },
  },
};
</script>

<style scoped lang="scss">
.award-base-card {
  height: 56px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  border: 0 solid #ebedf0;

  .left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .icon {
      .icon-image {
        height: 40px;
        width: 40px;
      }
    }
    .content {
      margin-left: 10px;
      text-align: left;
      .text {
        font-size: 15px;
        color: #323233;
        font-weight: bold;
        line-height: 20px;
      }
      .desc {
        margin-top: 4px;
        font-size: 12px;
        color: #646566;
        line-height: 16px;
        padding-top: 6px;
      }
    }
  }
  .right {
    .button {
      color: #fff;
      font-size: 14px;
      height: 24px;
      white-space: nowrap;
      margin-top: 8px;
      padding: 0 12px;
      width: 82px;
    }
  }
}
</style>
