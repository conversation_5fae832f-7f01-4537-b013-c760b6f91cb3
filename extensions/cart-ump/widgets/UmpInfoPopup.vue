<template>
  <van-popup
    :show="showPopup"
    :style="themeCss"
    :custom-style="customPopupStyle"
    custom-class="ai"
    safe-area-inset-bottom
    get-container="body"
    position="bottom"
    @close="handlePopupClose"
  >
    <view class="ai__title">活动详细规则</view>

    <view class="ai__body">
      <view class="ai__subtitle">活动说明</view>
      <view class="ai__explain">{{ showExplain }}</view>

      <view class="ai__subtitle">活动时间</view>

      <view class="ai__time">
        {{ activityInfo.activityDuration }}
      </view>

      <view class="ai__subtitle">活动内容</view>

      <view class="ai__desc">
        <van-tag
          v-for="(tag, index) in activityInfo.activityTags"
          :key="index"
          custom-class="ai__tag"
          :plain="themeTag.plain"
          :hairline="themeTag.plain"
        >
          {{ tag }}
        </van-tag>
        <view class="ai__text">{{ activityInfo.activityDesc }}</view>
      </view>
    </view>
    <view class="ai__btn" @click="handlePopupClose">知道了</view>
  </van-popup>
</template>
<script>
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */

const ACTIVITY_TYPE_EXPLAIN_MAP = {
  101: '参与的满减满赠活动仅含虚拟商品时不送实物赠品。',
  261: '1）参与的实付满赠活动仅含虚拟商品时不送实物赠品；2）实付金额门槛计算不含运费，商品优惠扣减积分抵现之后即为实付金额。商城订单使用储值余额支付时仍可享受实付满赠。',
};

export default {
  components: {
    'van-popup': Popup,
    'van-tag': Tag,
  },
  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */
  props: {
    activityInfo: {
      type: Object,
    },
    showPopup: {
      type: Boolean,
      default: false,
    },
    themeGeneralColor: String,
    themeGeneralAlpha10Color: String,
    themeCss: String,
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    themeTag: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    customPopupStyle() {
      let bottomHeight = 0;
      /* #ifdef weapp */
      bottomHeight = this.popupBottom;
      if (this.safeBottom) {
        bottomHeight = this.noSafeBottom;
      }
      /* #endif */
      return `bottom: ${bottomHeight}px`;
    },
    showExplain() {
      return ACTIVITY_TYPE_EXPLAIN_MAP[this.activityInfo?.activityType] || '';
    },
  },
  methods: {
    handlePopupClose() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss">
.ai {
  padding: 0 16px;
  border-radius: var(--theme-radius-dialog, 20px) var(--theme-radius-dialog, 20px) 0 0;
  box-sizing: border-box;

  &__title {
    padding: 11px 0;
    text-align: center;
    font-size: 16px;
    color: #323233;
    font-weight: 500;
  }

  &__body {
    overflow: auto;
    box-sizing: border-box;
    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去122px（标题栏和行动按钮栏高度和）,
    min-height: calc(50vh - 122px);
    min-height: calc(50vh - 122px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 122px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 122px);
    max-height: calc(80vh - 122px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 122px - env(safe-area-inset-bottom));
  }

  &__subtitle {
    font-size: 14px;
    margin-bottom: 12px;
    font-weight: bold;
    color: #323233;
  }

  &__explain {
    font-size: 12px;
    margin-bottom: 24px;
    color: #323233;
  }

  &__time {
    font-size: 13px;
    margin-bottom: 24px;
    color: #323233;
  }

  &__desc {
    font-size: 13px;
    color: #323233;
  }

  &__tag {
    margin-right: 4px;
    padding: 0 4px;
    line-height: 16px;
    vertical-align: middle;
    color: var(--theme-tag-ump-color, --main-bg) !important;
    background: var(--theme-tag-ump-bg-color, --main-bg) !important;

    &::after {
      border-color: var(--theme-tag-ump-border-color, --main-bg) !important;
      border-radius: var(--theme-radius-tag, 0) !important;
    }
  }

  &__text {
    line-height: 17px;
    vertical-align: middle;
    display: inline;
  }

  &__btn {
    margin: 32px auto 10px;
    height: 36px;
    line-height: 36px;
    border-radius: var(--theme-radius-button, 18px);
    color: #fff;
    text-align: center;
    font-size: 14px;
    background: var(--main-bg, #f44);
  }
}
</style>
