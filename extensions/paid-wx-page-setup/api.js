import { requestV2 } from '@youzan/tee-biz-request';

// 获取支付结果数据
export function getPayResult(requestNo, kdtId) {
  return requestV2({
    path: '/wsctrade/order/wxPayresult.json',
    data: {
      requestNo,
      kdtId,
    },
  });
}

// 是否可用新版支付营销优化，因为点金计划无cookie，必须显式传入kdtId
// TODO: 看起来这个方法没有用到，需要后续再确认下，如果的确没有用到需要删除
export function getTradeUmpV1(kdtId) {
  return requestV2({
    path: '/wscump/trade/use-trade-ump-v1.json',
    data: {
      kdtId,
    },
  });
}

// 是否可用新版支付营销优化
// TODO: 看起来这个方法没有用到，需要后续再确认下，如果的确没有用到需要删除
export function getNewUmpAwardInfo(orderNo, kdtId) {
  return requestV2({
    path: '/wscump/trade/pay-result-ump-info.json',
    data: {
      orderNo,
      kdtId,
    },
  });
}
