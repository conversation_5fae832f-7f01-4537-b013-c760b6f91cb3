{"extensionId": "@wsc-tee-trade/detail-page-setup", "name": "@wsc-tee-trade/detail-page-setup", "version": "1.6.8", "platform": ["web", "weapp"], "bundle": "<builtin>", "lifecycle": ["onShareAppMessage", "onPullDownRefresh", "onPageShow"], "data": {"provide": {"kdtId": ["r", "w"], "headKdtId": ["r"], "orderNo": ["r", "w"], "orderStateInfo": ["r", "w"], "groupon": ["r", "w"], "logistics": ["r", "w"], "receiverInfo": ["r", "w"], "showReceiverInfo": ["r", "w"], "selfFetchInfo": ["r", "w"], "isPeriodBuy": ["r", "w"], "showSelfFetch": ["r", "w"], "showVirtualTicket": ["r", "w"], "isScanBuyOrder": ["r", "w"], "virtualTicket": ["r", "w"], "showLogisticsInfo": ["r", "w"], "showWxShoppingList": ["r", "w"], "styleSet": ["r", "w"], "showGrouponCollection": ["r", "w"], "activityType": ["r", "w"], "cashBack": ["r", "w"], "returnValueCard": ["r", "w"], "idCardNumber": ["r", "w"], "showIdCard": ["r", "w"], "showQrCode": ["r", "w"], "qrCode": ["r", "w"], "showDeliveryType": ["r", "w"], "showCheckDeliveryScope": ["r", "w"], "showDeliveryTime": ["r", "w"], "showLookCoupon": ["r", "w"], "service": ["r", "w"], "invoiceStatus": ["r", "w"], "freight": ["r", "w"], "coupon": ["r", "w"], "coupons": ["r", "w"], "couponCode": ["r", "w"], "time": ["r", "w"], "orderBizUrl": ["r", "w"], "isUsingNewSnapshot": ["r", "w"], "showShopVerifyAddress": ["r", "w"], "verifyAddressInfo": ["r", "w"], "showPresaleSteps": ["r", "w"], "presaleSteps": ["r", "w"], "payment": ["r", "w"], "showPostage": ["r", "w"], "buyWayDetailUrl": ["r", "w"], "fissionTicketNum": ["r", "w"], "microTransferInfo": ["r", "w"], "activitiesList": ["r", "w"], "deliveryActivitiesList": ["r", "w"], "decreaseAmount": ["r", "w"], "pointsName": ["r", "w"], "isAllowShareOrder": ["r", "w"], "isNewHotelGood": ["r", "w"], "goods": ["r", "w"], "showAfterSaleMobile": ["r", "w"], "contact": ["r", "w"], "guaranteeOrderInfo": ["r", "w"], "order": ["r", "w"], "totalPrice": ["r", "w"], "shopInfo": ["r", "w"], "isSelfFetch": ["r", "w"], "isShowPostponeShip": ["r", "w"], "isMultiPeriodBuy": ["r", "w"], "gift": ["r", "w"], "invoice": ["r", "w"], "orderBizExtra": ["r", "w"], "orderExtra": ["r", "w"], "flowEntranceBannerBizName": ["r", "w"], "bizName": ["r", "w"], "buyerId": ["r", "w"], "cpsConfigKey": ["r", "w"], "wrapperType": ["r", "w"], "requestExtraParams": ["r", "w"], "dialogRequestExtraParams": ["r", "w"], "isChoosedCard": ["r", "w"], "isOrderPage": ["r", "w"], "eduAttributeItems": ["r", "w"], "education": ["r", "w"], "showEducation": ["r", "w"], "giftInvite": ["r", "w"], "showRefundInfo": ["r", "w"], "orderMark": ["r", "w"], "sourceInfo": ["r", "w"], "isRetailOrder": ["r", "w"], "env": ["r", "w"], "miniprogram": ["r", "w"], "isEduOrder": ["r", "w"], "showPaidPromotion": ["r", "w"], "paidPromotion": ["r", "w"], "shopName": ["r", "w"], "payTime": ["r", "w"], "isIVR": ["r", "w"], "isIvrOwner": ["r", "w"], "ivrRedirectUrl": ["r", "w"], "isHotel": ["r", "w"], "mpAccount": ["r", "w"], "isSourceFromWx": ["r", "w"], "goodsType": ["r", "w"], "isFans": ["r", "w"], "courses": ["r", "w"], "addressInfo": ["r"], "itemList": ["r"], "recommendTitle": ["r", "w"], "dialogRecommendTitle": ["r", "w"], "recommendBizName": ["r", "w"], "dialogRecommendBizName": ["r", "w"], "recommendPageSize": ["r", "w"], "recommendLayoutConfig": ["r", "w"], "dialogRecommendPageSize": ["r", "w"], "dialogRecommendLayoutConfig": ["r", "w"], "buyAgainCouponAvailable": ["r", "w"], "ump": ["r", "w"], "marketingInfo": ["r", "w"], "webviewPath": ["r", "w"], "isPrePayPage": ["r", "w"], "isShowStoreInfo": ["r", "w"], "pageBgColor": ["r", "w"], "platformInfo": ["r", "w"], "useBeforePayData": ["r", "w"], "rxNo": ["r", "w"], "canUseTradeUmpV1": ["r", "w"], "isWholesaleOrder": ["r", "w"], "outBizNo": ["r", "w"], "receivingContent": ["r", "w"], "showMessage": ["r", "w"], "bottomBtnsDisplay": ["r", "w"], "showOrderAddress": ["r", "w"], "showOrderStatus": ["r", "w"], "privacyWaybill": ["r", "w"], "outerPromotion": ["r", "w"], "stockCouponGoods": ["r"], "rechargeInfo": ["r", "w"]}}, "event": {"listen": ["refreshOrderInfo", "onLoadOrderInfo"], "emit": ["onLoadOrderInfo"]}, "process": {"define": ["afterReceiverInfoUpdated", "setReceivingContent", "setShowMessage", "setItemList", "setBottomBtnsDisplay", "toggleBlockDisplay"], "invoke": ["getShareAppMessage", "waitingOrderProgress", "getOrderEnterShopPolicy", "setBottomBtnsDisplay", "setReceivingContent", "setItemList", "setShowMessage", "toggleBlockDisplay"]}}