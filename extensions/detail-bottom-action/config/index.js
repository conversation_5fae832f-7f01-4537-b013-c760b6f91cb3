import { buildUrl } from '@youzan/tee-biz-util';

export default {
  EXTEND_RECEIVE: {
    CHECK_EXTEND_URL: buildUrl('/trade/order/isAllowLaterReceiveTime.json', 'wap'),
    DO_EXTEND_URL: buildUrl('/trade/order/laterReceiveTime.json', 'wap'),
  },

  CONFIRM_RECEIVE: {
    CONFIRM_URL: buildUrl('/trade/order/confirmReceive.json', 'wap'),
  },

  REMIND_SEND: {
    REMIND_SEND_URL: buildUrl('/wsctrade/order/remindExpress.json', 'h5'),
  },

  CANCEL_ORDER: {
    CANCEL_URL: buildUrl('/wsctrade/order/cancelOrder.json', 'h5'),
  },

  BUY_AGAIN: {
    BUY_AGAIN_URL: buildUrl('/wsctrade/order/buyAgain.json', 'h5'),
    CART_URL: buildUrl('/wsctrade/cart', 'h5'),
  },

  STATUS_MAP: {
    TIME_NOT_ENOUGH: 102580009,
    ALREADY_EXTENDED: 102580012,
  },
};
