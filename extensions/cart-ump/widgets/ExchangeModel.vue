<template>
  <!-- 换购商品弹框组件 -->
  <view class="em" :style="themeCss">
    <van-popup
      :show="show"
      safe-area-inset-bottom
      custom-class="em__popup"
      :custom-style="customStyle"
      position="bottom"
      @closed="closeExchangePop"
    >
      <!-- 标题 -->
      <view class="em__header">
        <view class="header-title"> 换购商品 </view>
        <van-icon custom-class="close-btn" name="cross" @click="closeExchangePop" />
      </view>

      <view v-if="goodsList.length" class="em__prompt">
        {{ activityDesc }}
        <view v-if="supportMulti">可换购以下任意商品</view>
        <view v-else> 可换购<text>1件</text>商品 </view>
      </view>

      <!-- 换购商品列表 -->
      <view v-if="goodsList.length" class="em__body">
        <van-loading v-if="fetchExchangeListIng" class="em__loading" type="spinner" />

        <exchange-error v-if="getListError" />

        <exchange-list
          v-else
          :support-multi="supportMulti"
          :goods-list="goodsList"
          :theme-css="themeCss"
          :theme-general-color="themeGeneralColor"
          :current-selected-sku="currentSelectedSku"
          @updateGoodsListData="updateGoodsListData"
          @to-goods-detail="toGoodsDetail"
          @close-sku="closeSku"
          @change-sku-show="changeSkuShow"
        />
      </view>

      <view v-else class="em__no">
        <image
          src="https://img01.yzcdn.cn/public_files/2019/09/03/24bdd475c943797a3a676aff2f6b9c99.png"
          class="em__no__img"
        />
        <p class="em__no__tip">暂无可换购商品，请稍后再试</p>
      </view>

      <!-- 底部导航 -->
      <view class="em-footer">
        <view class="combined-num">
          <text>已选 {{ totalNum }} 件</text>
        </view>
        <van-button
          :loading="btnLoading"
          loading-type="spinner"
          custom-class="confirm-btn"
          @click="onAddToCart"
        >
          确定
        </van-button>
      </view>
    </van-popup>
    <van-toast ref="van-toast" />
  </view>
</template>

<script>
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import Loading from '@youzan/vant-tee/dist/loading/index.vue';
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import { errorToast } from '@youzan/tee-biz-util';
import ExchangeError from './exchange-list/ExchangeError';
import { getGoodsPropertiesIds } from '../utils';
import { deleteBatch, batchAddGoods, findExchangeSkusJson } from '../api';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */
import Button from '@youzan/vant-tee/dist/button/index.vue';
import { mapEvent } from '@youzan/ranta-helper-tee';

export default {
  name: 'exchange-model',

  components: {
    'van-button': Button,
    'van-toast': VanToast,
    'van-loading': Loading,
    'van-icon': Icon,
    'van-popup': Popup,
    'exchange-error': ExchangeError,
  },

  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */

  props: {
    activityId: Number,
    activityType: Number,
    isShow: Boolean,
    kdtId: Number,
    activityDesc: String,
    plusBuyGoods: Array,
    themeCss: String,
    themeGeneralColor: String,
    offlineId: Number,
  },

  data() {
    return {
      show: this.isShow,
      supportMulti: false, // 是否支持多选
      goodsList: [],
      TotalNum: 0,
      btnLoading: false,
      fetchExchangeListIng: false,
      currentSelectedSku: {},
      plusBuyGoodsCase: [],
    };
  },

  computed: {
    getListError() {
      return !this.fetchExchangeListIng && !this.goodsList.length;
    },

    totalNum() {
      return this.goodsList.filter((goods) => !!goods.selected).length;
    },
    customStyle() {
      let style = 'bottom: 0px';
      /* #ifdef weapp */
      style = `bottom: ${this.popupBottom}px; ${this.safeBottom ? 'padding-bottom: 34px;' : ''}`;
      /* #endif */
      return `background: #f6f7f9;${style}`;
    },
  },

  watch: {
    isShow: {
      immediate: true,
      handler(val) {
        this.show = val;
        if (val) {
          this.getExchangeGoods();
        } else {
          // 优化关闭 弹框 恢复初始数据
          setTimeout(() => {
            this.supportMulti = false;
            this.fetchExchangeListIng = false;
            this.selectedGoods = [];
            this.goodsList = [];
          }, 300);
        }
      },
    },
    plusBuyGoods(value) {
      this.plusBuyGoodsCase = mapKeysCase.toCamelCase(value);
    },
  },

  async created() {
    mapEvent(this, {
      'exchangeGoodsSku:submit': this.onConfirmSku,
      'exchangeGoodsSku:hide': () => {
        this.currentSelectedSku = null;
      },
    });
  },

  methods: {
    onConfirmSku(data) {
      this.currentSelectedSku = data;
    },

    closeSku() {
      this.ctx.event.emit('exchangeGoodsSku:afterSubmit');
    },

    // 获取换购商品
    getExchangeGoods() {
      const { kdtId, activityId, offlineId } = this;
      this.fetchExchangeListIng = true;
      findExchangeSkusJson({ kdtId, activityId, offlineId })
        .then((exchangeGoods = []) => {
          if (!exchangeGoods.length) {
            return;
          }

          this.goodsList = exchangeGoods.slice(0)?.map((goods = {}) => ({
            ...goods,
            /** 保存一下接口获取的 exchangePrice和 originalPrice，为了在取消勾选时，显示原来的数据 */
            reserveExchangePrice: goods.exchangePrice,
            reserveOriginalPrice: goods.originalPrice,
          }));
          this.supportMulti = exchangeGoods[0] ? exchangeGoods[0].supportMulti : false;
          // 获取到换购商品后，查询购物车中的换购商品
          this.searchCartGoods();
          this.fetchExchangeListIng = false;
        })
        .catch((_err) => {
          this.goodsList = [];
          this.fetchExchangeListIng = false;
        });
    },

    // 查看购物车中的换购商品
    searchCartGoods() {
      this.plusBuyGoodsCase.forEach((item) => {
        const {
          goodsId,
          sku,
          skuData,
          skuId,
          payPrice,
          originPrice,
          attachmentUrl,
          messages = '',
          properties = [],
          propertyIds = [],
        } = item;
        const goodsListIndex = this.goodsList.findIndex((g) => g.goodsId === goodsId);
        if (goodsListIndex > -1) {
          this.goodsList.splice(goodsListIndex, 1, {
            ...this.goodsList[goodsListIndex],
            selected: true,
            skuStr: sku,
            properties,
            skuData,
            skuId,
            messages,
            exchangePrice: payPrice,
            originalPrice: originPrice,
            picture: attachmentUrl,
            propertyIds,
          });
        }
      });
    },

    // 换购商品添加购物车校验
    async onAddToCart() {
      if (this.btnLoading) return;

      if (this.getListError) {
        this.closeExchangePop();
        return;
      }

      this.btnLoading = true;
      let refresh = true;
      const selectedGoods = this.goodsList.filter((goods) => !!goods.selected);

      const shouldDeleteGoods = this.getShouldDeleteGoods(selectedGoods);
      const shouldAddGoods = this.getShouldAddGoods(selectedGoods);

      if (!shouldDeleteGoods.length && !shouldAddGoods.length) {
        refresh = false;
        this.btnLoading = false;
      }

      // 如果没有已经选择的换购商品，直接添加即可
      if (!shouldDeleteGoods.length) {
        await this.addCart(shouldAddGoods, refresh);
        this.btnLoading = false;
        return;
      }

      const parseGoods = this.parseDeleteGoods(shouldDeleteGoods);

      // 先删除之前的换购商品
      deleteBatch(parseGoods)
        .then(async (data) => {
          if (data) {
            await this.addCart(shouldAddGoods, refresh);
            this.btnLoading = false;
          } else {
            throw new Error();
          }
        })
        .catch((error) => {
          this.btnLoading = false;
          errorToast(error, { message: '删除商品失败，请稍后重试' });
        });
    },

    // 换购商品加入购物车
    addCart(goodsItems, refresh = true) {
      if (!goodsItems.length) {
        this.$emit('close');
        if (refresh) this.$emit('add-cart');
        return Promise.resolve();
      }

      const parseGoodsItems = this.parseAddGoods(goodsItems);

      return batchAddGoods(parseGoodsItems)
        .then(() => {
          Toast('已成功添加到购物车');
          this.$emit('close');
          this.$emit('add-cart');
        })
        .catch((err) => {
          errorToast(err, { message: '添加购物车失败，请稍后重试' });
        });
    },

    parseDeleteGoods(goods) {
      const { kdtId, activityId } = this;

      return goods.map((item) => ({
        skuId: item.skuId,
        goodsId: item.goodsId,
        kdtId,
        activityId,
      }));
    },

    parseAddGoods(goods) {
      const { kdtId, activityId, activityType } = this;

      return goods.map((item) => {
        const propertyIds = getGoodsPropertiesIds(item.properties);
        return {
          skuId: item.skuId || item.thinSkus[0].id,
          goodsId: item.goodsId,
          payPrice: +item.exchangePrice * 100,
          messages: item.messages || '',
          kdtId,
          activityId,
          propertyIds: propertyIds?.length > 0 ? propertyIds : item.propertyIds,
          activityType: activityType.toString(),
          num: 1,
        };
      });
    },

    // 获取应该加购商品
    getShouldAddGoods(selectedGoods) {
      const addGoods = [];

      selectedGoods.forEach((goods) => {
        const goodsInExistGoods = this.plusBuyGoodsCase.find(
          (item) => item.goodsId === goods.goodsId
        );

        if (!goodsInExistGoods || !this.compareGoodsIsSame(goods, goodsInExistGoods)) {
          addGoods.push(goods);
        }
      });
      return addGoods;
    },

    // 获取应该删除的商品
    getShouldDeleteGoods(selectedGoods) {
      const deleteGoods = [];
      this.plusBuyGoodsCase.forEach((goods) => {
        const goodsInSelectedGoods = selectedGoods.find((item) => item.goodsId === goods.goodsId);

        if (!goodsInSelectedGoods || !this.compareGoodsIsSame(goods, goodsInSelectedGoods)) {
          deleteGoods.push(goods);
        }
      });

      return deleteGoods;
    },

    // 更新数据
    updateGoodsListData(currentGoods) {
      this.goodsList = this.goodsList.map((goods) =>
        goods.goodsId === currentGoods.goodsId ? currentGoods : goods
      );
    },

    // 对比两个换购商品是否一致
    compareGoodsIsSame(goods, newGoods) {
      const KEYS = ['goodsId', 'skuId'];
      let flag = true;

      KEYS.forEach((key) => {
        flag = flag && goods[key] === newGoods[key];
      });

      // 对比商品属性，如果没有属性，给默认值
      goods.properties = goods.properties || [];
      newGoods.properties = newGoods.properties || [];
      flag = flag && JSON.stringify(goods.properties) === JSON.stringify(newGoods.properties);

      // 对比商品属性ID，如果没有属性ID，给默认值 properties可能为空
      goods.propertyIds = goods.propertyIds || [];
      newGoods.propertyIds = newGoods.propertyIds || [];
      flag = flag && JSON.stringify(goods.propertyIds) === JSON.stringify(newGoods.propertyIds);

      // 对比商品留言
      flag =
        flag &&
        this.parseMessageToArrString(goods.messages) ===
          this.parseMessageToArrString(newGoods.messages);

      return flag;
    },

    // 把留言格式化成数组字符串
    parseMessageToArrString(messages) {
      let parseMessage = [];
      try {
        parseMessage = JSON.parse(messages);
      } catch (error) {
        parseMessage = [];
      }
      if (Object.prototype.toString.call(parseMessage) === '[object Object]') {
        parseMessage = Object.keys(parseMessage).map((key) => parseMessage[key]);
      }

      return JSON.stringify(parseMessage);
    },

    // 关闭换购商品弹窗
    closeExchangePop() {
      this.$emit('close');
    },

    toGoodsDetail(params) {
      this.ctx.process.invoke('navigateFromCart', params);
    },

    changeSkuShow(params) {
      this.ctx.process.invoke('setSkuInfo', params);
    },
  },
};
</script>

<style lang="scss">
.em {
  &__popup {
    border-radius: var(--theme-radius-dialog, 20px) var(--theme-radius-dialog, 20px) 0 0;
    background: #f6f7f9;
  }

  &__header {
    text-align: center;
    position: relative;
    background: #fff;

    .header-title {
      color: #000;
      padding: 11px 0;
      font-size: 16px;
      letter-spacing: 0.3px;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 16px;
      top: 10px;
      color: #dcdde0;
      font-size: 20px;
    }
  }

  &__prompt {
    display: flex;
    padding: 12px;
    color: #323233;
    font-size: 14px;
  }

  &__no {
    text-align: center;
    height: 65vh;

    &__img {
      margin-top: 12.3vh;
      width: 150px;
      height: 150px;
    }

    &__tip {
      color: #969799;
      font-size: 14px;
      margin-top: 20px;
    }
  }

  &__body {
    height: 476px;
    font-size: 12px;
    margin: 0 12px;
    position: relative;
    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去138px（标题栏和行动按钮栏高度和）
    min-height: calc(50vh - 138px);
    min-height: calc(50vh - 138px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 138px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 138px);
    max-height: calc(80vh - 138px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 138px - env(safe-area-inset-bottom));

    &-item:not(:last-child) {
      .good-item__info {
        border-bottom: 1px solid #f2f2f2;
      }
    }
  }

  &__loading {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-footer {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    background: #fff;
    padding: 0 12px;
    box-sizing: border-box;

    .combined-num {
      flex: 1;
      font-size: 14px;
    }

    .confirm-btn {
      flex: 0 0 126px;
      height: 36px;
      padding: 0 30px;
      border-radius: var(--theme-radius-button, 18px);
      font-size: 14px;
      line-height: 36px;
      text-align: center;
      border: none;
      color: var(--main-text, #fff) !important;
      background: var(--main-bg, #f44) !important;
    }
  }
}
</style>
