import get from '@youzan/utils/object/get';
import pick from '@youzan/weapp-utils/lib/pick';
import accDiv from '@youzan/utils/number/accDiv';
import { cdnImage } from '@youzan/tee-biz-util';

const exist = (item) => !!item;

const getCouponTotalCount = (benefit) => {
  const { couponList } = benefit;
  return couponList.reduce((total, item) => {
    return total + item.number;
  }, 0);
};

/**
 * format 某一个权益的名称
 * @param {object} benefit 某一条权益
 * @param {string} subType 权益类型
 */
const formatBenefitName = (benefit = {}, subType = '') => {
  const desc = get(benefit, 'benefitPluginInfo.showName', '');
  switch (subType) {
    case 'discount':
      return `${get(benefit, 'discount', 10) / 10}折`;
    case 'points':
      return `送${get(benefit, 'points', 0)}积分`;
    case 'pointsFeedBack':
      return `${accDiv(get(benefit, 'rate', 0), 10)}倍积分`;
    default:
      return desc;
  }
};

/**
 * format 某一个权益
 * @param {object} benefit 某一条权益
 */
const formatBenefit = (benefit = {}) => {
  const name = formatBenefitName(benefit, benefit.key);
  return {
    ...benefit,
    appName: name || benefit.appName,
    icon: get(benefit, 'benefitPluginInfo.icon', '') || benefit.icon,
  };
};

const getBenefitByNames = (benefitDTO, nameArr) => {
  return Object.keys(pick(benefitDTO, nameArr))
    .forEach((key) => {
      if (benefitDTO[key]) {
        const benefit = { ...benefitDTO[key] };
        if (key === 'coupon') {
          benefit.totalCount = getCouponTotalCount(benefit);
        }
        benefit.key = key;
        return benefit;
      }
    })
    .filter(exist);
};

export const formatBenefits = (benefitDTO = {}, planBenefits) => {
  const { diyTemplateList = [], experienceCard } = benefitDTO;
  let benefits = [];
  const benefits1 = getBenefitByNames(benefitDTO, [
    'coupon',
    'present',
    'discount',
    'points',
    'pointsFeedBack',
    'memberPrice',
    'postage',
  ]);
  const benefits2 = getBenefitByNames(benefitDTO, ['paidContent']);
  const benefits3 = getBenefitByNames(benefitDTO, ['growth']);

  const experienceCardBenefit = [];
  experienceCard &&
    experienceCardBenefit.push({
      ...experienceCard,
      key: 'experienceCard',
    });

  // 优惠券>赠品>会员折扣>积分>积分倍率>会员价>包邮 > 生日 > 会员日 > 节日 >（付费体验卡）> 知识付费 > 自定义 > 成长值
  benefits = benefits
    .concat(benefits1)
    .concat(planBenefits)
    .concat(experienceCardBenefit)
    .concat(benefits2)
    .concat(diyTemplateList)
    .concat(benefits3);

  return benefits.map(formatBenefit);
};

const termTypeText = {
  31: '天',
  32: '周',
  33: '月',
};

const TermTypeEm = {
  DAY: 31,
  WEEK: 32,
  MONTH: 33,
};

/**
 * 原始 planBenefit 数据转化为可展示数据
 * @param {object} planBenefits 原始 planBenefits 数据
 */
export const transformPlanBenefitsForDisplay = (planBenefits = {}) => {
  if (!planBenefits) return [];

  const res = [];
  const { birthdayInfoList, festivalInfoList, memberdayInfoList, memberVoucherInfoList } =
    planBenefits;

  if (birthdayInfoList && birthdayInfoList.length) {
    const birthdayInfo = birthdayInfoList[0] || {};
    const { benefit: birthdayBenefit, planExecuteTime } = birthdayInfo;
    const { termType } = birthdayInfo.effectiveTime || {};
    const benefit = {
      benefitDetail: { benefit: birthdayBenefit, planExecuteTime },
      name: 'birthday',
      pluginName: '生日特权',
      icon: cdnImage('memberlevel/v2/<EMAIL>'),
    };

    benefit.remark = termTypeText[termType]
      ? `会员生日当${termTypeText[termType]}享生日特权`
      : '会员生日享生日特权';

    res.push(benefit);
  }

  if (memberdayInfoList && memberdayInfoList.length) {
    const memberdayInfo = memberdayInfoList[0] || {};
    const { termType } = memberdayInfo.effectiveTime || {};
    const benefit = {
      name: 'memberday',
      pluginName: '专属会员日',
      icon: cdnImage('memberlevel/v2/<EMAIL>'),
    };

    if (termType === TermTypeEm.DAY) {
      benefit.remark = '每日专享优惠';
    } else if (termType === TermTypeEm.WEEK) {
      benefit.remark = '每周专享优惠';
    } else if (termType === TermTypeEm.MONTH) {
      benefit.remark = '每月专享优惠';
    } else {
      benefit.remark = '';
    }

    res.push(benefit);
  }

  if (festivalInfoList && festivalInfoList.length) {
    res.push({
      name: 'festival',
      pluginName: '节日特权',
      remark: '节日专属优惠',
      icon: cdnImage('memberlevel/v2/<EMAIL>'),
    });
  }

  if (memberVoucherInfoList && memberVoucherInfoList.length) {
    res.push({
      name: 'membervoucher',
      pluginName: '会员专享券',
      icon: cdnImage('memberlevel/v2/<EMAIL>'),
      remark: '会员定期可领的优惠券',
    });
  }

  // 兼容两个地方的使用
  res.forEach((item) => {
    item.desc = item.remark;
    item.appName = item.pluginName;
  });

  return res;
};

export function isNotNull(obj) {
  return !!obj && JSON.stringify(obj) !== '{}';
}

export function checkAfterPayGuide(payResult) {
  let afterPayGuide = {};
  try {
    afterPayGuide = JSON.parse(payResult?.extension?.AFTER_PAY_GUIDE || '{}');
  } catch (e) {
    console.log('数据获取失败');
  }
  return afterPayGuide?.needGuide;
}

function colorToRgba(hex, alpha = 1) {
  // 十六进制颜色值的正则表达式
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  /* 16进制颜色转为RGB格式 */
  let color = hex.toLowerCase();
  if (color && reg.test(color)) {
    if (color.length === 4) {
      let colorNew = '#';
      for (let i = 1; i < 4; i += 1) {
        colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
      }
      color = colorNew;
    }
    //  处理六位的颜色值
    const colorChange = [];
    for (let i = 1; i < 7; i += 2) {
      // eslint-disable-next-line radix
      colorChange.push(parseInt('0x' + color.slice(i, i + 2)));
    }
    // return colorChange.join(',')
    // 或
    return 'rgba(' + colorChange.join(',') + ',' + alpha + ')';
  }
  return color;
}

/** 处理主题色 */
export function RGBA2RGB(rgbColor, opacity = 1, backgroundColor = 'rgb(255,255,255)') {
  // 注：rgba_color的格式为 #abcdef back_color的格式为 rgb (255,255,255)
  const rgba = colorToRgba(rgbColor, opacity);
  const bgArray = backgroundColor.split('(')[1].split(')')[0].split(',');
  const rgbaArr = rgba.split('(')[1].split(')')[0].split(',');
  const a = rgbaArr[3];
  const r = bgArray[0] * (1 - a) + rgbaArr[0] * a;
  const g = bgArray[1] * (1 - a) + rgbaArr[1] * a;
  const b = bgArray[2] * (1 - a) + rgbaArr[2] * a;
  return 'rgb(' + Math.round(r) + ',' + Math.round(g) + ',' + Math.round(b) + ')';
}
