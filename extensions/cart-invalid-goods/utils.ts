import type { TradeCartGoods } from '@youzan-cloud/cloud-biz-types';
import type { UnAvailableGoodsListV1 } from './types';

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  getUnAvailableGoodsListV1({ unavailableItems = [] }): UnAvailableGoodsListV1 {
    return unavailableItems;
  },
  getInvalidGoodsList({ unavailableItems = [] }): TradeCartGoods[] {
    return unavailableItems.map((item) => {
      const goods: TradeCartGoods = {
        cartId: item.cartId,
        activityId: item.activityId,
        activityType: item.activityType,
        num: item.num,
        storeId: item.storeId,
        // @ts-ignore
        goodsType: item.goodsType,
        skuId: item.skuId,
        messages: item.messages,
        sku: item.sku,
        id: item.goodsId,
        imgUrl: item.imgUrl,
        payPrice: item.payPrice,
        originPrice: item.originPrice,
        title: item.title,
        alias: item.alias,
        price: item.price,
      };

      return goods;
    });
  },
};
