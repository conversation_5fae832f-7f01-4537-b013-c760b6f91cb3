<template>
  <view v-if="showExtHolder">
    <!-- 推荐商品 -->
    <recommend-goods v-if="widgetRecommendGoodsInit" />
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data() {
    return {
      showExtHolder: false,
      widgetRecommendGoodsInit: false,
    };
  },

  created() {
    mapData(this, ['showExtHolder']);

    this.widgetRecommendGoodsInit = !!this.ctx.widgets.RecommendGoods;
  },
};
</script>
