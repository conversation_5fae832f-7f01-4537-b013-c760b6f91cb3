type Order = {
  kdtId: number;
  orderNo: string;
};

interface ITicketItem {
  ticketNo: string;

  verifyTime?: number;
  userId?: number;
  verifyState?: number;
}

type VirtualTicket = {
  verifyState: number;
  /** 电子卡券有效期结束时间 */
  effectiveTimeDesc: string;
  ticketItemResponseDTOList: ITicketItem[];
};

export default interface ExtensionMetadata {
  data: {
    consume: {
      showVirtualTicket: boolean;
      orderNo: string;
      order: Order;
      virtualTicket: VirtualTicket;
    };
  };
}
