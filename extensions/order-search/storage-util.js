/* #ifdef web */
import YZLocalStorage from '@youzan/utils/browser/local_storage';
/* #endif */
/* #ifdef weapp */
import { getStorageSync, removeStorageSync, setStorageSync } from '@youzan/tee-api';
/* #endif */

let storage;
/* #ifdef web */
storage = YZLocalStorage;
/* #endif */
/* #ifdef weapp */
storage = {
  getItem: getStorageSync,
  removeItem: removeStorageSync,
  setItem: setStorageSync,
};
/* #endif */

export default storage;
