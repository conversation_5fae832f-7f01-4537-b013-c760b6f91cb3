<template>
  <view class="process-info">
    <view class="process-info-content">
      <!--目前title只有文本-->
      <view :class="['process-info-title', isPrimary ? 'process-info-primary' : '']">{{
        title
      }}</view>
      <!--desc目前有多种特殊的类型，需要C端单独实现-->
      <view class="process-info-desc" v-if="descOpt">
        <block v-if="descOpt.type === 'distance'">
          <distance-node :opt="descOpt.distanceOpt" :location="location" />
        </block>
        <block v-else-if="descOpt.type === 'queue'">
          <queue-node :opt="descOpt.queueOpt" />
        </block>
        <block v-else>{{ descOpt.text }}</block>
      </view>
    </view>
    <view class="process-action">
      <action-item
        v-for="action in actions"
        :icon="action.icon"
        :text="action.text"
        :type="action.type"
        :opt="action.opt"
        :key="action.type"
      />
    </view>
  </view>
</template>

<script>
import DistanceNode from './DistanceNode.vue';
import QueueNode from './QueueNode.vue';
import ActionItem from './action/index.vue';
import { actionTypeValues } from './action/actionHelper';

export default {
  name: 'process-item',
  components: {
    DistanceNode,
    QueueNode,
    ActionItem,
  },
  props: {
    opt: Object,
    isPrimary: Boolean,
    location: Object,
  },
  computed: {
    title() {
      return this.opt.titleOpt.text;
    },
    descOpt() {
      return this.opt.descOpt;
    },
    actions() {
      // 根据当前的action值过滤node返回的配置，node返回的action不一定展示，因为可能返回的action是当前版本没有支持的
      return (this.opt.actions || []).filter((item) => actionTypeValues.includes(item.type));
    },
  },
};
</script>

<style lang="scss" scoped>
.process-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #969799;
  font-size: 14px;
  position: relative;
  padding: 16px 0;
  background-color: #fff;

  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    height: 1px;
    background: #ebedf0;
    bottom: 0;
    transform: scaleY(0.5);
    left: 0;
    right: 0;
  }
  &-content {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-title {
    width: 220px;
    color: #111111;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &-primary {
    font-weight: 500;
  }
  &-desc {
    width: 220px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .process-action {
    flex-basis: 90px;
    display: inline-flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
  }
}
</style>
