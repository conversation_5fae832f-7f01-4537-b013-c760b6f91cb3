<template>
  <!--当商品列表中存在处方药商品时。才出现去填写处方药的信息 -->
  <view class="index__block" v-if="hasPrescriptionDrugGood && showRxStatus">
    <!-- 处于等待买家付款状态，展示用药人信息 -->
    <view
      v-if="currentRxStatus && (currentRxStatus.value === 'draft' || orderData.closeType === 1)"
      class="info-card"
      @click="gotoPrescription"
    >
      <view class="info-card__title--block">
        <view>
          <text class="info-card__title">{{ info.title }}</text>
          <van-tag
            round
            type="danger"
            color="#FDE6E9"
            text-color="#EE0A24"
            class="info-card__icon theme-color theme-background-alpha-10"
          >
            用药人
          </van-tag>
        </view>
        <van-icon name="arrow" class="info-card__arrow" size="16px" color="#969799" />
      </view>
      <view class="info-card__msg">
        {{ checkMedical }}
      </view>
    </view>

    <!-- 其他状态，展示当前状态流程 -->
    <view class="info-card" v-else-if="currentRxStatus" @click="gotoPrescription">
      <view class="info-card__title--block">
        <view>
          <text v-if="currentRxStatus.value === 'closed'" class="info-card__title">{{ currentClosedType }}</text>
          <text v-else class="info-card__title">{{ currentRxStatus.title }}</text>
        </view>
        <van-icon name="arrow" class="info-card__arrow" size="16px" color="#969799" />
      </view>
      <view class="info-card__msg">
        {{ currentRxStatus.value === 'closed' ? rxStatus.failureReason : currentRxStatus.content }}
      </view>
    </view>
  </view>
</template>

<script>
import VanTag from '@youzan/vant-tee/dist/tag/index.vue';
import VanIcon from '@youzan/vant-tee/dist/icon/index.vue';
import Tee from '@youzan/tee';
import { mapState } from '@ranta/store';
import { requestV2 } from '@youzan/tee-biz-request';
import { mapData } from '@youzan/ranta-helper-tee';
import { errorToast } from '@youzan/tee-biz-util';

const EnumRxStatus = {
  1: { value: 'draft', desc: '已创建' },
  10: {
    value: 'wait_open_rx',
    desc: '待医生开方',
    title: '已提交预约，等待医生开方',
    content: '买家预约-医生开方-药师审方-药房发货',
  },
  20: {
    value: 'wait_review',
    desc: '待药师审核',
    title: '医生开方完成，等待药师审核',
    content: '买家预约-医生开方-药师审方-药房发货',
  },
  30: {
    value: 'audit_pass',
    desc: '审核通过',
    title: '药师审方通过，等待药房发货',
    content: '买家预约-医生开方-药师审方-药房发货',
  },
  40: {
    value: 'used',
    desc: '已使用',
    title: '查看用药建议',
    content: '请仔细阅读用药建议，严格遵循医嘱服药。',
  },
  50: { title: '已关闭', value: 'closed', desc: '已关闭' },
};
const EnumCloseType = {
  1: '医生开方失败',
  2: '药师审核不通过',
  3: '订单取消导致处方关闭',
  4: '处方过期',
};

export default {
  components: {
    'van-tag': VanTag,
    'van-icon': VanIcon,
  },
  name: 'drug-user',
  data() {
    return {
      orderData: {},
      itemInfo: [],
      ...mapState(this, ['rxNo', 'showRxStatus', 'currentUser', 'drugMessage']),
    };
  },
  computed: {
    // 当前处方状态
    currentRxStatus() {
      return EnumRxStatus[this.rxStatus.rxStatus];
    },
    currentClosedType() {
      if (!this.rxStatus.closeType) return {};
      return EnumCloseType[this.rxStatus.closeType];
    },
    hasPrescriptionDrugGood() {
      return this.itemInfo.some((item) => item.extra.IS_PRESCRIPTION_DRUG_GOODS);
    },
    rxStatus() {
      const rxStatus = this.drugMessage?.rxStatus || {};
      this.ctx.data.rxStatus = rxStatus;
      return rxStatus;
    },
    // 当商品列表中存在处方药商品时。才出现去填写处方药的信息
    hasRxNo() {
      return !!this.rxNo;
    },
    info() {
      return {
        title: `${this.currentUser.name} ${this.currentUser.phoneNum}`,
      };
    },
    checkMedical() {
      const { diagnosedDiseases = [] } = this.drugMessage;
      const nameList = diagnosedDiseases.map((item) => item.diseaseName);
      return nameList.join('、');
    },
    drugAdvUrl() {
      return this.drugMessage.orderCreatedMsg?.prescribeInfo?.rxImgUrl;
    },
  },
  created() {
    mapData(this, ['orderData', 'rxNo']);
    mapData(this, {
      goods: (val) => {
        this.itemInfo = val;
      },
    });
  },
  watch: {
    hasPrescriptionDrugGood(val) {
      if (val) {
        this.fetchDrugUserList();
      }
    },
  },
  methods: {
    gotoPrescription() {
      if (this.rxStatus.rxStatus < 30 || !this.drugAdvUrl) {
        return Tee.navigate({
          url: `https://h5.youzan.com/wsctrade/order/drug?rxNo=${this.rxNo}`,
          type: 'navigateTo',
        });
      }
      Tee.navigate({
        url: `https://h5.youzan.com/wsctrade/order/drug-adv?imgUrl=${this.drugAdvUrl}`,
        type: 'navigateTo',
      });
    },
    fetchDrugUser() {
      return requestV2({
        path: '/wsctrade/order/drug/queryUser.json',
        data: { patientId: '' },
        method: 'get',
      })
        .then((res) => {
          const { items = [] } = res;
          // 这里需要先mock一下数据
          // 是获取用药人列表 还要设置当前选中的用药人呢
          /* const currentUser = items.find((item) => item.isDefault);
          if (currentUser) {
            this.currentUser = currentUser;
            // 存在默认的用药人
            !this.drugMessage.currentUserId && (this.drugMessage.currentUserId = currentUser.patientId);
          } else {
            // 不存在默认用药人情况下
            if (items[0]) {
              // 取第一个用药人当作当前选中的用药人信息
              !this.drugMessage.currentUserId && (this.drugMessage.currentUserId = items[0].patientId);
            }
          } */
          this.drugMessage.userList = items;
          return items;
        })
        .catch((error) => {
          if (!this.showRxStatus) {
            return;
          }
          errorToast(error, { message: '查询用药人失败，请稍后再试' });
        });
    },
    fetchDrugUserList() {
      if (this.showRxStatus && this.hasPrescriptionDrugGood) {
        // 查询用药人列表
        this.fetchDrugUser();
        // this.$dispatch('VIEW_PRESCRIPTION');
        this.store.fetchPrescription();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  position: relative;
  margin-top: 10px;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: auto;
  padding: 12px;
  box-sizing: border-box;

  &__title--block {
    display: flex;
    justify-content: space-between;
  }

  &__title {
    font-size: 14px;
    color: #323233;
    letter-spacing: 0;
    line-height: 18px;
    padding-right: 8px;

    &-right {
      display: flex;
      align-items: center;

      &_msg {
        font-size: 14px;
        letter-spacing: 0;
        text-align: right;
        line-height: 20px;
      }
    }
  }

  &__msg {
    font-size: 12px;
    color: #969799;
    padding-top: 4px;
    letter-spacing: 0;
    line-height: 16px;
  }

  &__icon {
    height: 14px;
  }
}
</style>
