# @wsc-tee-trade/cart-present-popup

挑选赠品 popup

![UI呈现](https://img.yzcdn.cn/public_files/0b075917ec72c3d649e58caddeca5b51.png)

## 调试方式

- 前往 [满减送](https://dian16911610.shangjia.youzan.com/v4/ump/reward/list)
- `满N元减/送` 点击创建
  - 活动名称：xxx 的满减送活动
  - 活动时间：今天 至 明天
  - 优惠叠加：叠加其他营销活动**（全选）**
  - 活动商品：指定商品参加 - 选择几个商品
  - 活动页规则展示：默认文案
  - 优惠设置
    - 优惠门槛：满 `0.01` 元
    - 优惠内容：送赠品
      - 添加赠品
  - 保存
- 点击刚才参加活动的商品
- 进入商详页
- 加入购物车
- 进入购物车
- 点击刚才加购商品右侧的 `去挑选`

## 存疑

## Widget.Provide

| 名称                | 说明 |
| ------------------- | ---- |
| OrderKeep `default` | ???  |

## Event.Listen

| 名称  | 说明 |
| ----- | ---- |
| open  | ???  |
| close | ???  |

## Process.Invoke

| 名称               | 说明 |
| ------------------ | ---- |
| navigateToTradeBuy | ???  |

## Data.Consume

| 名称        | 类型 | 默认值 | 说明 |
| ----------- | ---- | ------ | ---- |
| displayData | ???  | ???    | ???  |
| orderData   | ???  | ???    | ???  |
| themeColors | ???  | ???    | ???  |
