<template>
  <view class="fc-ump_seckill" @click="handleClick">
    <view class="fc-ump_seckill-countdown">
      <van-count-down
        ref="countDown"
        millisecond
        use-slot
        :time="baseTimestamp"
        format="mm:ss:SSS"
        @change="handleTimeChange"
      >
        <view class="fc-ump_seckill-countdown-container">
          <text class="item">{{ countdownMinute }}</text>
          <text class="item-unit">:</text>
          <text class="item">{{ countdownSecond }}</text>
          <text class="item-unit">.</text>
          <text class="item">{{ countdownMillisecond }}</text>
        </view>
      </van-count-down>
      <text>后结束</text>
    </view>
    <view class="fc-ump_seckill-goods">
      <image class="goods-image" :src="pictureUrl" />
      <view class="goods-info">
        <native-text class="goods-title">{{ originData.goodsTitle }}</native-text>
        <view class="goods-message">来自 [{{ originData.cooperateKdtName }}] 的商品</view>
        <view class="goods-footer">
          <!-- #ifdef weapp -->
          <native-text class="price"
            ><native-text>秒杀价 ¥</native-text>
            <native-text class="price-weight-big price-weight">{{
              originData.bigPrice
            }}</native-text>
            <native-text class="price-weight">.{{ originData.centPrice }}</native-text>
          </native-text>
          <!-- #else -->
          <view class="price"
            >秒杀价 ¥
            <text class="price-weight"
              ><text class="price-weight-big">{{ originData.bigPrice }}</text
              >.{{ originData.centPrice }}</text
            ></view
          >
          <!-- #endif -->
          <van-button custom-class="button" type="danger" round size="mini">立即参与</van-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Button from '@youzan/vant-tee/dist/button/index';
import CountDown from '@youzan/vant-tee/dist/count-down/index';

import { adType } from '../../utils/flow-change';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'flow-change-ump-seckill',

  components: {
    'van-button': Button,
    'van-count-down': CountDown,
  },

  props: {
    originData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      timeData: {
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
      },
      // 倒计时五分钟
      baseTimestamp: 300000,
    };
  },

  computed: {
    countdownMinute() {
      return this.handleLeftPad(this.timeData.minutes);
    },
    countdownSecond() {
      return this.handleLeftPad(this.timeData.seconds);
    },
    countdownMillisecond() {
      // 取前两位
      return parseInt(this.timeData.milliseconds / 10, 10);
    },
    pictureUrl() {
      return fullfillImage(this.originData.picture, 'middle');
    },
  },

  created() {
    this.logger('view');
  },

  methods: {
    handleTimeChange(data) {
      this.timeData = data;
      const { minutes, seconds, milliseconds } = data;
      // 循环倒计时
      if (minutes === 0 && seconds === 0 && milliseconds === 0) {
        this.$refs.countDown.reset();
      }
    },
    handleLeftPad(num) {
      const intNum = +num;
      if (isNaN(intNum)) {
        return num;
      }
      return intNum > 9 ? intNum : `0${intNum}`;
    },
    logger(eventType) {
      this.$emit('onLogger', {
        data: this.originData,
        type: adType.seckill,
        eventType,
      });
    },
    handleClick() {
      this.logger('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.fc-ump {
  &_seckill {
    width: 702rpx;
    height: 160px;
    background: url('https://b.yzcdn.cn/flow-change-ump/seckill-bg-3.x.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    padding-top: 44px;
    padding-left: 20px;
    box-sizing: border-box;
    &-countdown {
      position: absolute;
      top: 6px;
      right: 16px;
      width: 114px;
      color: white;
      font-size: 12px;
      display: flex;
      align-items: center;
      &-container {
        display: flex;
        align-items: baseline;
        color: white;
        font-size: 12px;
        line-height: 16px;
        margin-right: 4px;
        .item {
          background: white;
          color: #f44;
          border-radius: 4px;
          text-align: center;
          width: 16px;
          height: 16px;
          &-unit {
            margin: 0 1px;
          }
        }
      }
    }
    &-goods {
      display: flex;
      width: 100%;
      padding-right: 16px;
      box-sizing: border-box;
      .goods {
        &-image {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          margin-right: 8px;
        }
        &-info {
          /* #ifdef web */
          width: 100%;
          /* #endif */
          /* #ifdef weapp */
          width: 460rpx;
          /* #endif */
        }
        &-title {
          font-size: 14px;
          height: 38px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        &-message {
          margin-top: 8px;
          margin-bottom: 12px;
          font-size: 12px;
          color: #969799;
        }
        &-footer {
          display: flex;
          justify-content: space-between;
          .price {
            /* #ifdef weapp */
            display: flex;
            align-items: baseline;
            /* #endif */
            font-size: 14px;
            color: #f44;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: calc((100% * 100) / 140);
            &-weight {
              /* #ifdef web */
              display: inline-block;
              /* #endif */
              font-weight: bold;
              &-big {
                font-size: 20px;
              }
            }
          }
          .button {
            display: block;
            color: white;
            background: #f44;
            border-color: #f44;
            font-size: 14px;
            width: 82px;
          }
        }
      }
    }
  }
}
</style>
