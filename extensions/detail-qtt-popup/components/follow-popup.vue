<template>
  <view>
    <van-popup :show="show" custom-class="follow-popup">
      <view @click="handleClose" class="follow-popup__close">
        <van-icon size="18px" color="#989898" name="cross" />
      </view>
      <view class="follow-popup__title" :style="titleStyle">关注群团团公众号</view>
      <view class="follow-popup__text">及时获取订单、物流相关消息</view>
      <view>
        <van-button custom-class="follow-popup__button" @click="handleFollow">
          立即关注
        </van-button>
      </view>
    </van-popup>
  </view>
</template>

<script>
import Popup from '@youzan/vant-tee/dist/popup/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import args from '@youzan/utils/url/args';

import { saveFollowMpPagePath } from '../utils/subscribe';

export default {
  components: {
    'van-popup': Popup,
    'van-icon': Icon,
    'van-button': VanButton,
  },

  props: {
    show: Boolean,
  },

  data() {
    return {
      titleStyle: '',
    };
  },

  computed: {},

  created() {},

  methods: {
    // 点击关注
    handleFollow() {
      // 去掉openSubscriptionSettings，保障推送完链接后，后续链路不关心小程序订阅设置是否正常开启
      const url = args.remove(window.location.href, 'openSubscriptionSettings');
      const path = `/pages/web-view/index?src=${encodeURIComponent(url)}`;
      saveFollowMpPagePath(path);
      this.handleClose();
    },

    // 点击关闭弹窗
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss">
$text-color: #323233;

.follow-popup {
  display: flex;
  flex-direction: column;
  padding: 24px 20px;
  align-items: center;
  border-radius: 8px;

  &__close {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  &__title {
    color: #333;
    font-size: 18px;
    font-weight: 500;
  }

  &__text {
    color: #666;
    font-size: 14px;
    text-align: center;
    margin-top: 11px;
    margin-bottom: 21px;
    width: 250px;
  }

  & &__button {
    width: 260px;
    height: 44px;
    background: #00c261;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 18px;
  }
}
</style>
