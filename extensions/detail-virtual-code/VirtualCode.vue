<template>
  <view class="virtual-code" v-if="showQrCode">
    <view class="virtual-code__title">
      <text>订单二维码：</text>
      <text class="code-text">{{ qrCode.code }}</text>
    </view>

    <view class="t-hairline--top">
      <!-- #ifdef web -->
      <view class="virtual-code__tip">*长按二维码，保存电子凭证到手机</view>
      <!-- #endif -->
      <image class="virtual-code__image-container" mode="aspectFit" :src="qrCode.qrCode" />
    </view>
    <!-- 增加提示 -->
    <view class="virtual-code__qrcode__tips">
      <text>为保障您的权益，未消费前请不要将二维码/条形码 提供给商家</text>
    </view>
  </view>
</template>

<script>
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },

  data() {
    return {
      showQrCode: false,
      qrCode: {
        code: '',
        qrCode: '',
      },
    };
  },

  created() {
    mapData(this, ['showQrCode', 'qrCode']);
  },
};
</script>

<style lang="scss">
.virtual-code {
  background: #fff;
  margin-top: 10px;
}

.code-text {
  color: #666;
}

.virtual-code__title {
  font-size: 14px;
  padding: 12px 16px;
  line-height: 1.4;
}
.virtual-code__qrcode__tips {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 12px;
  color: #636566;
  padding: 0 20px 24px;
  margin-top: -10px;
}
.virtual-code__image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  padding: 12px 14px;
  text-align: center;
  margin: 0 auto;
}

.virtual-code__tip {
  padding: 5px 0 0;
  font-size: 12px;
  position: absolute;
  top: 5px;
  left: 0;
  width: 100%;
  color: #ed6a0c;
  text-align: center;
}

.t-hairline--top {
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    transform: scale(0.5);
    border-top-width: 1px;
  }
}
</style>
