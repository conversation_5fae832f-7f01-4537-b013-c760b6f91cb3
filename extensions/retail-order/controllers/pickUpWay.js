/* 取货方式 */
import PickUpWay from '../widgets/PickUpWay.vue';
import PickUpWayCard from '../widgets/PickUpWayCard.vue';
import { isDef } from '../utils';

export const widgets = {
  PickUpWay,
  PickUpWayCard,
};

export function initData(ctx) {
  // 取货方式
  ctx.data.retailPickUpWays = [];
}

export function initProcesses(ctx) {
  // 设置取货方式
  // 目前更新逻辑是每次从取货方式数组中向取出当前 idx + 1 位
  ctx.process.define('updatePickUpWay', (pickUpWay) => {
    const { name, value } = pickUpWay;
    ctx.data.retailPickUpWayName = name;
    ctx.data.retailPickUpWayValue = value;
    ctx.process.invoke('fetchShow', { loading: false });
  });
}

export function watchPrepareData(ctx, data) {
  // 初始化取货方式相关数据
  const pickUpWay = data?.tradeConfirmation?.pickUpWay;
  if (isDef(pickUpWay)) {
    const { calculatedPickUpWay = -1, supportedPickUpWays = {} } = pickUpWay;
    /* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
    const iconMaps = {
      '1': 'https://img01.yzcdn.cn/upload_files/2024/12/09/Fkgcztdnp47Pf12dQdD7mhjlNKXm.png',
      '2': 'https://img01.yzcdn.cn/upload_files/2024/12/09/FoOnZwi3heiC6DOCQC-UvTWeWUYI.png',
    };
    const retailPickUpWays = Object.keys(supportedPickUpWays).map((key) => {
      return {
        name: supportedPickUpWays[key],
        value: +key,
        icon: iconMaps[key],
      };
    });
    const target = retailPickUpWays.find((item) => item.value === +calculatedPickUpWay);
    ctx.data.retailPickUpWays = retailPickUpWays;
    if (target) {
      const { name, value } = target
      ctx.data.retailPickUpWayName = name;
      ctx.data.retailPickUpWayValue = value;
    }
  }
}
