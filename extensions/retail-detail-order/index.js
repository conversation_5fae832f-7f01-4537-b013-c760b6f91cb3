import TakeGoodsCode from './TakeGoodsCode.vue';
import WaitingProcess from './WaitingProcess.vue';
import RetailLogistics from './widgets/RetailLogistics';

import * as api from './api';

export default class SelfFetchExtension {
  static widgets = {
    TakeGoodsCode,
    WaitingProcess,
    RetailLogistics,
  };

  constructor(options) {
    this.ctx = options.ctx;
    this.initData();
    this.initProcesses();
    this.ctx.env.getQueryAsync().then(() => {
      this.initWaitingProgress();
    });
  }

  get data() {
    return this.ctx.data;
  }

  initData() {
    this.data.processData = {};
  }

  initProcesses() {
    this.ctx.process.define('waitingOrderProgress', () => this.initWaitingProgress());
  }

  initWaitingProgress() {
    const { order_no, orderNo, kdt_id, kdtId } = this.ctx.env.getQuery();
    const opt = { orderNo: this.data.orderNo || order_no || orderNo };
    if (kdt_id || kdtId) {
      opt.kdt_id = kdt_id || kdtId;
    }
    api.getOrderWaitingProgress(opt).then((data) => {
      this.data.processData = data;
      const status = {
        'self-1': 'selfFetchPending',
        'self-2': 'selfFetchMaking',
        'self-3': 'selfFetchDone',
        'express-1': 'expressPending',
        'express-4': 'expressShipping',
        'express-2': 'expressMaking',
      };
      if (data.bizType === 1) {
        this.data.processData[status[`self-${data.goodsMakingStatus}`]] = true;
        return;
      }
      this.data.processData[status[`express-${data.goodsMakingStatus}`]] = true;
    });
  }
}
