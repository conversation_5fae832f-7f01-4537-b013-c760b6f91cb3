<template>
  <view v-if="extraPrices.length">
    <view v-for="(item, index) in extraPrices" :key="index" class="price-panel__item">
      <text class="price-panel__item__title"
        >{{ item.name }}
        <van-icon
          v-if="item.showTips"
          @click="popupToggle(item.name)"
          name="info-o"
          custom-class="new-invoice__content__title__icon"
        />
        <view class="info-dialog__container">
          <packing-fee-dialog
            v-if="item.name === '打包费'"
            :show="popupShow[item.name] === true"
            :info="item"
            @close="popupToggle(item.name)"
          />
          <info-dialog
            v-else
            :title="item.name + '说明'"
            :show="popupShow[item.name] === true"
            @close="popupToggle(item.name)"
            >{{ item.desc }}</info-dialog
          >
        </view>
      </text>
      <text class="price-panel__item__value">{{ item.price }}</text>
    </view>
  </view>
</template>
<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  components: {
    'van-icon': Icon,
  },
  props: {
    popupShow: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      extraPrices: [],
    };
  },
  created() {
    mapData(
      this,
      {
        extraPrices: (value) => {
          this.extraPrices = value || [];
        },
      },
      { isSetData: false }
    );
  },
  methods: {
    popupToggle(name) {
      this.$emit('popup-toggle', name);
    },
  },
};
</script>
<style lang="scss">
/* #ifdef weapp */
// 微信小程序存在样式压缩类名会变更，需重新引入
@import '../../style';
/* #endif */
</style>
