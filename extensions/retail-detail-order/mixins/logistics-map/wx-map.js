import {
  LOCAL_DELIVERY_MAP,
  RIDER_DDIRECTION,
  SHOP_THUMB_URL,
  BUYER_THUMB_URL,
  MarkerIdEnum,
} from '../../constant';

export default {
  data() {
    this.mapContext = null;
    return {
      BUYER_THUMB_URL,
      RIDER_DDIRECTION,
      SHOP_THUMB_URL,
      MarkerIdEnum,
      riderContent: {},
      zoom: 11,
    };
  },

  methods: {
    initMap() {
      const position = this.position;
      if (position[0] <= 0 || position[1] <= 0) return;
      return new Promise((resolve) => {
        if (this.mapComplete) resolve();
        this.createSelectorQuery()
          .in(this)
          .select('#wx-map')
          .fields({ context: true })
          .exec((res) => {
            this.mapContext = res[0]?.context;
            this.mapComplete = true;
            resolve();
          });
      });
    },

    // 处理地图漂浮物
    initMapExtra() {
      this.addDeliveryPath(this.cityDetail);
      if (this.cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_TAKING) {
        // 待取货
        this.setShopMarker(this.cityDetail);
      } else if (this.cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_SENDING) {
        // 配送中
        this.setBuyerMarker(this.cityDetail);
      }
    },

    setRider(position, riderContent) {
      const rider = this.getMakerById(MarkerIdEnum.Rider);
      this.riderContent = riderContent;
      const { direct } = riderContent;
      if (!rider) {
        const data = {
          id: MarkerIdEnum.Rider,
          longitude: position[0],
          latitude: position[1],
          width: 60,
          height: 60,
          iconPath:
            direct === RIDER_DDIRECTION.TO_EAST
              ? 'https://img01.yzcdn.cn/public_files/2019/01/09/d5aa8cfffe64fe1b215087a4cc6e58d7.png'
              : 'https://img01.yzcdn.cn/public_files/2019/01/08/60134a95f52aab26657e8c65933343b0.png',
          customCallout: {
            anchorY: 0,
            anchorX: 0,
            display: 'BYCLICK',
          },
        }
        this.markers.push(data);
        // sb 微信，这里必须要延迟一下设置 ALWAYS，直接设置有概率渲染文字内容异常
        setTimeout(() => {
          data.customCallout.display = 'ALWAYS';
        }, 500)
      } else {
        rider.longitude = position[0];
        rider.latitude = position[1];
      }
    },

    addDeliveryPath(cityDetail) {
      const { transporterLat, transporterLng, buyerLat, buyerLng, shopLat, shopLng } =
        cityDetail || {};
      let riderContent = {};
      if (transporterLat && transporterLng) {
        const lngLat = [transporterLng, transporterLat];
        riderContent = {
          statusText: '请稍等',
          distanceMode: '',
          distanceText: '',
          direct: 'east',
        };
        if (cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_TAKING) {
          riderContent.statusText = '骑手已接单';
          riderContent.distanceMode = '距商家';
          if (shopLat && shopLng) {
            riderContent.direct = transporterLng > shopLng ? RIDER_DDIRECTION.TO_WEST : RIDER_DDIRECTION.TO_EAST;
            const distance = this.calcDistance(
              { lng: lngLat[0], lat: lngLat[1] },
              { lng: shopLng, lat: shopLat }
            );
            riderContent.distanceText = distance.toFixed(1) + 'km';
            riderContent.estimateArriveTimeTip = this.localDelivery.estimateArriveTimeTip || '';
            this.zoom = this.getCalculateZoom(Number(distance.toFixed(1)));
            this.setRider(lngLat, riderContent);
          }
        } else if (cityDetail.status === LOCAL_DELIVERY_MAP.STATUS_SENDING) {
          riderContent.statusText = '配送中';
          riderContent.distanceMode = '距你';
          if (buyerLat && buyerLng) {
            riderContent.direct = transporterLng > buyerLng ? RIDER_DDIRECTION.TO_WEST : RIDER_DDIRECTION.TO_EAST;
            const distance = this.calcDistance(
              { lng: lngLat[0], lat: lngLat[1] },
              { lng: buyerLng, lat: buyerLat }
            );
            riderContent.distanceText = distance.toFixed(1) + 'km';
            riderContent.estimateArriveTimeTip = this.localDelivery.estimateArriveTimeTip || '';

            this.zoom = this.getCalculateZoom(Number(distance.toFixed(1)));
            this.setRider(lngLat, riderContent);
          }
        }
      }
    },

    getCalculateZoom(distance) {
      let zoom = 11;
      if (distance <= 3) {
        zoom = 13;
      } else if (distance <= 6) {
        zoom = 12;
      }
      return zoom;
    },

    setBuyerMarker(cityDetail) {
      const { buyerLat, buyerLng } = cityDetail || {};
      if (buyerLat && buyerLng) {
        const buyer = this.getMakerById(MarkerIdEnum.Buyer);
        if (!buyer) {
          this.markers.push({
            id: MarkerIdEnum.Buyer,
            longitude: buyerLng,
            latitude: buyerLat,
            iconPath: BUYER_THUMB_URL,
            width: 32,
            height: 32,
          });
        } else {
          buyer.longitude = buyerLng;
          buyer.latitude = buyerLat;
        }
      }
    },

    setShopMarker(cityDetail) {
      const { shopLat, shopLng } = cityDetail || {};
      const shop = this.getMakerById(MarkerIdEnum.Shop);
      if (!shop) {
        this.markers.push({
          id: MarkerIdEnum.Shop,
          longitude: shopLng,
          latitude: shopLat,
          iconPath: SHOP_THUMB_URL,
          width: 32,
          height: 32,
        });
      } else {
        shop.longitude = shopLng;
        shop.latitude = shopLat;
      }
    },

    getMakerById(id) {
      return this.markers.find((item) => item.id === id);
    },
    calcDistance(a, b) {
      function rad(d) {
        return (d * Math.PI) / 180;
      }
      const radLat1 = rad(+a.lat);
      const radLat2 = rad(+b.lat);
      const rLat = radLat1 - radLat2;
      const rLng = rad(+a.lng) - rad(+b.lng);

      /* eslint-disable */
      let s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(rLat / 2), 2) +
              Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(rLng / 2), 2)
          )
        );
      s *= 6378137.0;
      s = Math.round(s * 10000) / 10000;
      return s / 1000;
    },
  },
};
