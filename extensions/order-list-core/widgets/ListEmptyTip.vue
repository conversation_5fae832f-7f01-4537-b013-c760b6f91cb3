<template>
  <view v-if="isEmptyList">
    <welike-entry-wrapper />
    <view v-show="isShowEmptyTip" :class="classname">
      <view class="empty-tip__img">
        <image src="https://b.yzcdn.cn/public_files/7e264e3a04e94f2912abf16dee24b0e7.png" />
      </view>
      <view class="empty-tip__title">暂无订单</view>
      <view class="empty-tip__text">近期没有下单记录</view>
    </view>
  </view>
</template>

<script>
import { mapState } from '@ranta/store';
import Button from '@youzan/vant-tee/dist/button/index.vue';

export default {
  components: {
    'van-button': Button,
  },

  data() {
    return {
      ...mapState(this, ['isEmptyList', 'isShowEmptyTip', 'showRecommend']),
    };
  },

  computed: {
    classname() {
      return `empty-tip${this.showRecommend ? ' empty-tip_small' : ''}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.empty-tip {
  text-align: center;
  padding-top: 120px;
  height: 100vh;
  box-sizing: border-box;

  &_small {
    padding: 12px 0 15px;
    height: auto;
  }

  &__img {
    margin-bottom: 24px;

    image {
      width: 100px;
      height: 100px;
    }
  }

  &__title {
    color: #111;
    font-weight: 500;
    line-height: 20px;
    font-size: 16px;
    margin-bottom: 8px;
  }

  &__text {
    color: #969799;
    line-height: 20px;
    font-size: 14px;
  }
}
</style>
