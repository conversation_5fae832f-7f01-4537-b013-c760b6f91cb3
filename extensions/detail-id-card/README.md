# @wsc-tee-trade/detail-id-card

身份证

![UI呈现](https://img.yzcdn.cn/public_files/6a4ecfc7407ec018a10ca298c009e202.png)

## 调试方式

- TODO: 需要确认后端什么时候返回 `showIdCard`
- 开发中直接 mock 即可

## 存疑

## Widget.Provide

| 名称             | 说明   |
| ---------------- | ------ |
| IdCard `default` | 身份证 |

## Component.Consume

| 名称         | 类型      | 说明       |
| ------------ | --------- | ---------- |
| showIdCard   | _boolean_ | 是否展示   |
| idCardNumber | _string_  | 身份证号码 |

## 关键字速查

| 关键字 | 说明 |
| --- | --- |
| showIdCard | 后端好像已经不返回这个值了，正常应该在 mainOrderInfo 中存在<br>zanapi: http://zanapi.qima-inc.com/site/service/view/1132479 |
