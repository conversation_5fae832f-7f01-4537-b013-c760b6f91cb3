<template>
  <view class="goods-card">
    <view class="goods-card__thumb">
      <image
        lazy-load
        @load="$emit('goods-img-load')"
        :src="thumb"
        width="72px"
        height="72px"
        mode="aspectFit"
      />
    </view>

    <view class="goods-card__content">
      <view class="goods-card__content-left">
        <view class="goods-card__title t-multi-ellipsis--l2">
          <text class="goods-card__title-text">
            <image
              v-if="goods.titleTag.isShow"
              :class="goods.titleTag.className"
              :src="goods.titleTag.src"
              :alt="goods.titleTag.alt"
            />
            {{ goods.title }}
          </text>
        </view>

        <view v-if="goods.subTitle" class="goods-card__sub-title">
          {{ goods.subTitle }}
        </view>

        <view v-if="goods.skuStr" class="goods-card__sku">
          {{ goods.skuStr }}
        </view>

        <view v-if="goods.hotelDesc" class="goods-card__hotel-desc">
          {{ goods.hotelDesc }}
        </view>

        <block v-if="goods.orderExplanation.length">
          <view
            v-for="(item, index) in goods.orderExplanation"
            :key="index"
            class="goods-card__explanation"
          >
            {{ item }}
          </view>
        </block>

        <view v-for="(tags, index) in goods.tagList" :key="index">
          <view v-if="tags.length" class="goods-card__tags">
            <van-tag
              v-for="(tag, subIndex) in tags"
              :key="subIndex"
              class="goods-card__tag"
              color="var(--ump-tag-bg, #f2f2ff)"
              text-color="var(--ump-tag-text, #323233)"
              :plain="tag.plain"
              round
            >
              {{ tag.text }}
            </van-tag>
          </view>
        </view>
      </view>

      <view class="goods-card__content-right">
        <view class="goods-card__price">
          {{ goods.itemPrice }}
        </view>

        <view v-if="goods.isShowItemNum" class="goods-card__num"> x{{ goods.num }} </view>

        <view v-if="goods.statusStr" class="goods-card__status">
          {{ goods.statusStr }}
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'goods-card',

  components: {
    'van-tag': Tag,
  },

  props: {
    goods: {
      type: Object,
      default: () => ({}),
    },
  },

  computed: {
    thumb() {
      // 由于node端暂时无法使用fullfillImage(会提示 window 对象未定义) 因此暂时放在客户端处理
      // 如果后续node端可以压缩图片了可以继续下沉到node
      return fullfillImage(this.goods.image, '!200x0.jpg');
    },
  },
};
</script>
<style scoped lang="scss">
.goods-card {
  padding: 12px 12px 0;
  display: flex;
  align-items: flex-start;

  &__thumb {
    border-radius: 8px;
    overflow: hidden;
    line-height: 0;
    margin-right: 8px;
    width: 90px;
    height: 90px;
  }

  &__content {
    flex: 1;
    display: flex;
    align-items: flex-start;
  }

  &__content-left {
    flex: 1;
    margin-right: 12px;
    width: 0;
  }

  &__content-right {
    text-align: right;
  }

  &__title {
    color: #323233;
    line-height: 20px;
    font-size: 14px;
    /* #ifdef weapp */
    height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    /* #endif */
  }

  &__title-text {
    // vertical-align: middle;
    line-height: 16px;
  }

  &__title-tag {
    vertical-align: middle;
    &.period {
      width: 40px;
    }
    &.haitao {
      width: 30px;
    }
    &.drug {
      width: 40px;
    }

    height: 16px;
    margin-right: 4px;
  }

  &__sub-title {
    font-size: 14px;
    color: #969799;
  }

  &__sku,
  &__hotel-desc {
    color: #969799;
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__explanation {
    color: var(--notice, #ed6a0c);
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__price {
    color: #323233;
    letter-spacing: 0;
    font-size: 14px;
  }

  &__num {
    color: #969799;
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__status {
    font-size: 12px;
    color: var(--highlight, #ee0a24);
    margin-top: 8px;
  }

  &__tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  &__tag {
    margin-right: 4px;
    padding: 0 4px;
    flex-shrink: 0;
    line-height: 16px;
    font-size: 12px;
    //background-color: var(--ump-tag-bg, #f2f2ff);
    color: var(--ump-tag-text, #323233) !important;
  }
}
</style>
