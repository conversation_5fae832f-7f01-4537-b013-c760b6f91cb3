import type { HandleOrderActionParams } from '@youzan-cloud/cloud-biz-types';

/**
 * 订单级操作类型
 * 'more' = 更多
 * 'confirm' = 确认收货
 * 'confirmHotel' = 确认入住
 * 'groupon' = 查看团详情
 * 'coupon' = 领优惠券
 * 'gift' = 查看礼包
 * 'evaluate' = 立即评价
 * 'viewEvaluate' = 查看评价
 * 'invoice' = 补开发票
 * 'share' = 我要晒单
 * 'batchRefund' = 批量退款
 * 'wholeAfterSales' = 多商品售后
 * 'extend' = 延长收货
 * 'cancel' = 取消订单
 * 'peerpay' = 代付详情
 * 'buyAgain' = 再来一单
 * 'courseDetail' = 查看课程
 * 'deleteOrder' = 删除订单
 * 'directBuyAgain' = 再来一单
 * 'modifyAddress' = 修改地址
 * 'afterSalePhoneCall' = 拨打售后电话
 * 'contactIM' = 联系在线客服
 */
export enum OrderActionEnum {
  MORE = 'more',
  CONFIRM = 'confirm',
  CONFIRM_HOTEL = 'confirmHotel',
  GROUPON = 'groupon',
  COUPON = 'coupon',
  GIFT = 'gift',
  EVALUATE = 'evaluate',
  VIEW_EVALUATE = 'viewEvaluate',
  INVOICE = 'invoice',
  SHARE = 'share',
  BATCH_REFUND = 'batchRefund',
  WHOLE_AFTER_SALES = 'wholeAfterSales',
  EXTEND = 'extend',
  CANCEL = 'cancel',
  PEERPAY = 'peerpay',
  BUY_AGAIN = 'buyAgain',
  COURSE_DETAIL = 'courseDetail',
  DELETE_ORDER = 'deleteOrder',
  DIRECT_BUY_AGAIN = 'directBuyAgain',
  MODIFY_ADDRESS = 'modifyAddress',
  AFTER_SALE_PHONE_CALL = 'afterSalePhoneCall',
  CONTACT_IM = 'contactIM',
}
export interface OrderAction {
  /** 操作类型 */
  type: OrderActionEnum;
}
export interface BottomBtns
  extends Array<{
    /** 按钮名称 */
    name: string;
    /** 按钮类型 */
    type: OrderActionEnum;
  }> {}
export interface RenderBottomBtns {
  /** 将会渲染的按钮列表 */
  btns: BottomBtns;
}

export interface BeforeHandleOrderAction {
  (payload: HandleOrderActionParams): Promise<void>;
}

export interface IOrderInfo {
  /** 订单编号 */
  orderNo: string;
  /** 创建订单时间 */
  createTime: number;
  /** 付款时间 */
  payTime: number;
  /** 订单留言 */
  buyerMemo: string;
  /** 订单状态 */
  stateStr: string;
  /** 配送方式 */
  expressType: unknown;
  /** 是否存在“在线客服” */
  isShowIM: boolean;
  /** 是否存在“延长收货”按钮 */
  isShowExtendOrder: boolean;
  /** 是否存在“确认收货”按钮 */
  isShowConfirmOrder: boolean;
  /** 是否存在“取消订单”按钮 */
  isShowCancelOrder: boolean;
  /** 是否存在“我要晒订单”按钮 */
  isShowShareOrder: boolean;
  /** 是否存在“再来一单”按钮 */
  isShowBuyAgain: boolean;
  /** 是否存在“评价”按钮 */
  isShowEvaluateOrder: boolean;
  /** 是否存在“查看评价”按钮 */
  isShowViewEvaluate: boolean;
  /** 是否允许确认收货 */
  isAllowConfirmReceive: boolean;
  /** 是否存在“物流信息”按钮 */
  isShowExpress: boolean;
}
