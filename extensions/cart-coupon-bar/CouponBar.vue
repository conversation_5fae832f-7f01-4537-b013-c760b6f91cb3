<template>
  <view class="coupon-wrap" @click="setPopupVisible(true)">
    <view class="coupon-list">
      <text class="coupon-tag theme-color" v-for="coupon in topCouponList" :key="coupon.id">
        <view v-if="coupon.isVip" class="coupon-vip">{{ memberConfig.name }}</view>
        {{ coupon.use_threshold_and_value_copywriting }}
      </text>
    </view>
    <view class="t-icon">
      <text class="coupon-wrap__title">去领券</text>
      <van-icon class="t-icon" name="arrow" />
    </view>
  </view>
</template>

<script>
import { mapState } from '@ranta/store';
import VanIcon from '@youzan/vant-tee/dist/icon/index.vue';
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  name: 'coupon-bar',

  components: {
    'van-icon': VanIcon,
  },

  data() {
    return {
      ...mapState(this, ['topCouponList']),
      memberConfig: {},
    };
  },

  created() {
    mapData(this, ['memberConfig']);
  },

  methods: {
    setPopupVisible(visible) {
      visible && this.ctx.event.emit('showCouponList');
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin concave-border {
  position: absolute;
  z-index: 1;
  width: 4px;
  height: 8px;
  background: #fff;
  top: 4px;
  content: '';
}

.coupon-wrap {
  background: #fff;
  margin-top: var(--theme-page-card-margin-top, 0);
  margin-bottom: var(--theme-page-card-margin-bottom, 12px);
  margin-right: var(--theme-page-card-margin-right, 12px);
  margin-left: var(--theme-page-card-margin-left, 12px);
  border-radius: var(--theme-page-card-border-radius, 8px);
  padding: var(--theme-page-card-padding-top, 12px) var(--theme-page-card-padding-right, 12px)
    var(--theme-page-card-padding-bottom, 12px) var(--theme-page-card-padding-left, 12px);
  font-size: 0;
  display: flex;
  justify-content: space-between;

  &__title {
    font-size: var(--eo-font-size-12, 12px);
    color: var(--ump-icon, #323233);
    font-weight: 400;
    margin-right: 4px;
    white-space: nowrap;
  }

  .t-icon {
    font-size: var(--eo-font-size-12, 12px);
    float: right;
    height: var(--eo-font-size-18, 18px);
    line-height: var(--eo-font-size-18, 18px);
    display: flex;
    align-items: center;
    color: var(--ump-icon, #323233);
  }

  .coupon-list {
    display: inline-flex;
    vertical-align: top;
    flex-wrap: wrap;
    // 这里计算优惠券的高度
    height: calc(var(--eo-font-size-16, 16px) + 4px);
    overflow: hidden;
  }

  .coupon-tag {
    margin-right: 8px;
    position: relative;
    display: inline-flex;
    background-color: var(--ump-tag-bg, #f2f2ff);
    box-sizing: border-box;
    border-radius: 2px;
    font-size: var(--eo-font-size-12, 12px);
    line-height: var(--eo-font-size-16, 16px);
    color: var(--ump-tag-text, #323233);
    border-width: 1px;
    border-style: solid;
    border-color: var(--ump-border, #323233);
    padding: 1px 6px;
    cursor: pointer;
    vertical-align: middle;

    &:last-child {
      margin-right: 0;
    }
  }

  .coupon-vip {
    display: flex;
    margin-right: 4px;
    &::after {
      content: '';
      margin-left: 4px;
      width: 1px;
      height: 100%;
      background: linear-gradient(
        to bottom,
        var(--ump-border, #c9c9ff) 20%,
        var(--ump-border, #c9c9ff) 20%,
        var(--ump-border, #c9c9ff) 20%,
        rgba(255, 255, 255, 0) 20%,
        rgba(255, 255, 255, 0) 35%,
        rgba(136, 76, 255, 1) 35%,
        var(--ump-border, #c9c9ff) 35%,
        var(--ump-border, #c9c9ff) 35%,
        var(--ump-border, #c9c9ff) 60%,
        rgba(255, 255, 255, 0) 60%,
        rgba(255, 255, 255, 0) 55%,
        rgba(255, 255, 255, 0) 75%,
        var(--ump-border, #c9c9ff) 75%,
        var(--ump-border, #c9c9ff) 75%,
        var(--ump-border, #c9c9ff) 100%
      );
    }
  }
}
</style>
