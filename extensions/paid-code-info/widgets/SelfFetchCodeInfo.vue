<template>
  <view :style="themeCSS" class="sf-content">
    <view class="sf-content__code-text">{{ selfFetchCodeName }}</view>

    <block v-if="selfFetch.hasError">
      <image
        class="sf-content__error-img"
        src="https://img.yzcdn.cn/upload_files/2020/03/12/FoFlIZNte-bq-nCcHIiZHDdvdwNa.png"
        alt="自提码生成失败"
      />
      <view class="sf-content__error-text">
        {{ selfFetchCodeName }}生成失败，请稍后再试或前往订单详情查看
      </view>
    </block>

    <block v-else>
      <view v-if="selfFetch.loading" class="sf-content__loading">
        <van-loading type="img" size="36" class="sf-content__loading-icon" />
      </view>

      <view v-else class="sf-content__qrcode">
        <image class="sf-content__qrcode-img" :src="selfFetch.selfFetchQRCode" alt="二维码" />
      </view>

      <view class="sf-content__qrnum">
        {{ selfFetch.loading ? '加载中' : selfFetch.selfFetchNo }}
      </view>

      <view class="sf-content__tips">为了保证你的权益，请勿提前提供给商家</view>
      <view class="sf-content__bottom-box">
        <view>
          <view v-if="isScanBuyOrder" class="sf-content__desc">请将核销码出示给收银员</view>
          <view v-else class="sf-content__desc">
            <view class="sf-content__desc-item"
              ><view class="title">取货地址：</view
              >{{ selfFetch.loading ? '加载中' : selfFetch.addressDetail }}</view
            >
            <view class="sf-content__desc-item"
              ><view class="title">取货时间：</view
              >{{ selfFetch.loading ? '加载中' : selfFetch.userTime }}</view
            >
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import Loading from '@youzan/vant-tee/loading/index';
import { mapData } from '@youzan/ranta-helper-tee';
import { mapActions, mapState } from '@ranta/store';

export default {
  components: {
    'van-loading': Loading,
  },
  data() {
    return {
      ...mapState(this, [
        'selfFetch',
        'orderNo',
        'themeCSS',
        'themeColors',
        'isScanBuyOrder',
        'selfFetchCodeName',
      ]),
    };
  },

  created() {
    mapActions(this, ['initSelfFetchData', 'selfFetchMessage']);
    mapData(this, ['payResult'], {
      callback: () => {
        const { payResult } = this.ctx.data;
        const { selfFetch = {} } = payResult || {};
        this.initSelfFetchData(selfFetch);
      },
    });
    this.selfFetchMessage();
    this.ctx.data.paidCodeInfoIsShow = true;
  },
};
</script>

<style lang="scss" scoped>
.sf-content {
  /* skyline模式下子组件width无法和父组件一样的宽度需要手动算 */
  width: calc(100vw - 24px);
  padding: 12tpx 0;
  margin: 0 12tpx 16tpx;
  border-radius: var(--theme-radius-card, 12px);
  text-align: center;
  background-color: #fff;

  &__error-img {
    display: block;
    width: 136tpx;
    height: 136tpx;
    margin: 16tpx auto;
  }

  &__error-text {
    font-weight: 400;
    font-size: 14tpx;
    line-height: 20tpx;
    text-align: center;
    color: #666;
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130tpx;
    height: 130tpx;
    margin: 8tpx auto;
    background: #eee;
    border-radius: 8tpx;
  }

  &__loading-icon {
    margin-left: 4px;
    width: 28px;
    height: 28px;
  }

  &__code-text {
    font-size: var(--eo-font-size-14, 14px);
    color: #333;
    line-height: 20tpx;
  }

  &__qrcode {
    position: relative;
    width: 130tpx;
    height: 130tpx;
    margin: 8tpx auto;

    &::after {
      position: absolute;
      content: ' ';
      border-radius: 2tpx;
      transform: scale(0.5);
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      transform-origin: 0 0;
    }
  }

  &__qrcode-img {
    width: 130tpx;
    height: 130tpx;
  }

  &__qrnum {
    font-size: var(--eo-font-size-14, 14px);
    text-align: center;
    color: #000;
  }

  &__tips {
    margin-top: 4tpx;
    font-weight: 400;
    font-size: var(--eo-font-size-12, 12px);
    color: #999;
  }

  &__bottom-box {
    margin: 8tpx 20tpx 0;
    padding-top: 8tpx;
    border-top: 1tpx dashed #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__desc {
    font-size: var(--eo-font-size-12, 12px);
    font-weight: 400;
    text-align: left;
    color: #333;
    &-item {
      display: flex;
      margin-top: 8tpx;
      .title {
        color: #999;
      }

      &:first-child {
        margin-top: 0;
      }
    }
  }

  &__title {
    display: inline;
    color: #999;
  }
}
</style>
