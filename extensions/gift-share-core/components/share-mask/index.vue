<template>
  <van-popup
    :show="show"
    custom-style="left: 0;right:0;top:0;bottom:0;background-color: transparent;transform: none;"
  >
    <view class="h5-cpns-style" @click="closeMask" v-if="show">
      <view class="cube-share-mask__ele-wrap">
        <view class="cube-share-mask__wechat">
          <view class="cube-share-mask__wechat-title"> 立即推广给好友吧 </view>
          <view class="cube-share-mask__wechat-desc"> 点击屏幕右上角将本页面分享给好友 </view>
          <view class="cube-share-mask__wechat-icon cube-share-mask__miniapp__wechat-icon">
            <image
              src="https://img01.yzcdn.cn/public_files/2017/08/30/0db0c59e46d67472ed92a4ced7c70903.png"
            />
          </view>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script>
import Popup from '@youzan/vant-tee/popup/index.vue';

export default {
  name: 'share-mask',
  components: {
    'van-popup': Popup,
  },
  props: {
    show: Boolean,
  },
  data() {
    return {
      iconClass: 'cube-share-mask__wechat-icon',
    };
  },
  methods: {
    closeMask() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
.cube-share-mask__ele-wrap {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.cube-share-mask__wechat-title {
  font-size: 20px;
  line-height: 30px;
  height: 30px;
  margin: 140px auto auto;
  text-align: center;
  color: #fff;
}

.cube-share-mask__wechat-desc {
  line-height: 20px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  margin-top: 4px;
}

.cube-share-mask__wechat-icon {
  width: 66px;
  height: 117px;
  position: fixed;
  top: 12px;
  right: 12px;

  &.cube-share-mask__miniapp__wechat-icon {
    right: 20px;
  }
}

.cube-share-mask__wechat-icon img {
  height: 100%;
  width: 100%;
}
</style>
