{"extensionId": "@wsc-tee-trade/cart-ump", "name": "@wsc-tee-trade/cart-ump", "version": "1.3.1", "bundle": "<builtin>", "widget": {"provide": ["GoodsGroupUmp", "GoodsItemUmp", "ExchangeModel", "ExchangeList", "UmpInfoPopup"], "consume": ["ExchangeList", "Price"]}, "data": {"consume": {"themeTag": ["r"]}}, "lambda": {"consume": ["isGoodsCheckboxEnable", "isEduIosOnlineGoods"]}, "process": {"invoke": ["navigateFromCart", "setSkuInfo"]}, "event": {"emit": ["exchangeGoodsSku:afterSubmit"], "listen": ["exchangeGoodsSku:submit", "exchangeGoodsSku:hide"]}, "platform": ["web", "weapp"]}