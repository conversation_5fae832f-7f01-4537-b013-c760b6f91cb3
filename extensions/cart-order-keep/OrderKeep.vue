<template>
  <van-dialog
    use-slot
    :title="displayData.title"
    :show="show"
    v-if="initialShow"
    :show-cancel-button="false"
    :show-confirm-button="false"
    :close-on-click-overlay="false"
    @close="onClose"
    @confirm="onConfirm"
  >
    <view class="keep__container">
      <view class="keep__items">
        <!--已下单-->
        <view v-if="type === 'FORMAT'" class="keep__item">
          <view class="keep__text">
            <view v-if="format.prefix" class="keep__text--plain">{{ format.prefix }}</view>
            <view v-if="format.value" class="keep__text--important keep__text--plain">{{
              format.value
            }}</view>
            <view v-if="format.suffix" class="keep__text--plain">{{ format.suffix }}</view>
          </view>
        </view>
        <!-- 普通文本展示-->
        <view v-if="type === 'PLAIN_TEXT'" class="keep__item">
          <view v-if="hint" class="keep__text">{{ hint }}</view>
        </view>
      </view>
      <!--优惠券-->
      <view class="keep__coupon--block" v-if="type === 'COUPON'">
        <view class="keep__text" v-if="hint">
          {{ hint }}
        </view>
        <view v-if="loading" class="coupon-view">
          <van-loading />
        </view>
        <view class="keep__coupon--container">
          <view class="keep__coupon--left">
            <view class="keep__coupon--left--block" :style="mainColorStyle">
              <view class="keep__coupon--price" :style="couponValueStyle">{{
                coupon.valueDesc
              }}</view>
              <view
                :class="[coupon.unitDesc ? 'keep__coupon--unit' : 'keep__coupon--unit--empty']"
                >{{ coupon.unitDesc || '' }}</view
              >
            </view>
          </view>
          <view class="keep__coupon--right">
            <view class="keep__coupon--name">{{ coupon.condition }}</view>
            <view class="keep__coupon--desc">{{ coupon.timeDesc }}</view>
          </view>
        </view>
      </view>

      <!--评价-->
      <view v-if="type === 'TAG_LIST'">
        <view v-if="hint" :class="[tags.length ? 'keep__text' : 'keep__text--tag']">{{
          hint
        }}</view>
        <view v-if="tags.length" class="keep__eval--block">
          <van-tag
            round
            custom-class="keep__tag"
            :color="themeGeneralAlpha10Color"
            v-for="(it, idx) in tags"
            :key="idx"
            >{{ it.value }}</van-tag
          >
        </view>
      </view>

      <van-button
        round
        @click="onConfirm"
        type="danger"
        :custom-style="btnCustomStyle"
        custom-class="keep__button--continue"
        >继续支付</van-button
      >
      <view @click="onClose" class="keep__btn--disabled">暂时放弃</view>
    </view>
  </van-dialog>
</template>

<script>
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import VanButton from '@youzan/vant-tee/dist/button/index.vue';
import VanLoading from '@youzan/vant-tee/dist/loading/index.vue';
import VanTag from '@youzan/vant-tee/dist/tag/index.vue';
import { mapEvent, mapData } from '@youzan/ranta-helper-tee';
import { PAGE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import hexToRgba from '@youzan/utils/string/hexToRgba';
import Tee from '@youzan/tee';
import { args } from '@youzan/tee-util/lib/common/url';

const ENUM = {
  coupon: '优惠券',
  points: '积分',
  stock: '库存紧张',
  praise: '好评数',
  evaluationTag: '评价标签',
  sales: '销量',
  activity: '活动',
  discount: '优惠金额',
};

export default {
  components: {
    'van-dialog': VanDialog,
    'van-button': VanButton,
    'van-tag': VanTag,
    'van-loading': VanLoading,
  },

  data() {
    return {
      displayData: {},
      orderData: {},
      show: false,
      couponValueStyle: '',
      loading: false,
      hint: '',
      type: '',
      coupon: {},
      tags: [],
      format: {},
      themeMainColor: '',
      themeGeneralAlpha10Color: '',
      initialShow: false,
      btnCustomStyle:
        'color: var(--main-text, #fff); background: var(--main-bg, #323233); border: 1px solid var(--main-bg, #323233)',
    };
  },

  computed: {
    hasCoupon() {
      return !!Object.keys(this.coupon);
    },
    mainColorStyle() {
      return `color: ${this.themeMainColor}!important;`;
    },
  },

  watch: {
    show(val) {
      if (val && !this.initialShow) {
        this.initialShow = true;
      }
    },
  },

  created() {
    this.maxWidth = 80;
    this.maxFontSize = 34;
    this.minFontSize = 16;
    this.trackType = '';

    mapEvent(this, {
      open: () => {
        /* #ifdef weapp */
        this.show = true;
        this.logDialogShow();
        /* #endif */
      },
      close: () => {
        this.show = false;
      },
    });

    mapData(this, ['orderData']);

    mapData(this, {
      displayData: (val) => {
        const { content = {} } = val || {};
        const { type = '', hint = '', coupon = {}, tags = [], format = {}, trackType } = content;
        this.trackType = trackType;
        this.type = type;
        this.hint = hint;
        this.coupon = coupon;
        this.tags = tags;
        this.format = format;
        this.displayData = val;
        if (type === 'COUPON') {
          this.loading = true;
          setTimeout(() => {
            this.computeFontSize();
          }, 500);
        }
      },
      themeColors: (val) => {
        const themeMainColor = val.general;
        this.themeMainColor = themeMainColor;
        this.themeGeneralAlpha10Color = hexToRgba(themeMainColor, 0.1);
      },
    });
  },

  methods: {
    logDialogShow() {
      const displayData = this.displayData || {};
      const { content = {} } = displayData;
      const { trackType } = content;
      this.ctx.logger &&
        this.ctx.logger.log({
          et: 'view',
          ei: 'continuepay_show',
          en: '下单挽留弹窗展示',
          pt: 'trade',
          params: {
            ordernumber: displayData.orderNo || '',
            money: displayData.price || '',
            type: ENUM[trackType],
          },
        });
    },

    async computeFontSize() {
      const { coupon } = this.displayData;
      const { valueDesc, unitDesc } = coupon || {};

      const priceWidth = await this.getBoundingClientRectWidth('.keep__coupon--price');

      const width = await this.getBoundingClientRectWidth('.keep__coupon--unit');

      const unitWidth = width;
      const decorateWidth = unitWidth;
      const scaleRatio = (this.maxWidth - decorateWidth) / priceWidth;
      let finalSize = 0;
      if (scaleRatio < 1) {
        const scaleFontSize = Math.trunc(scaleRatio * this.maxFontSize);
        finalSize = Math.max(scaleFontSize, this.minFontSize);
      } else {
        // 没有单位 是兑换商品类的折扣券
        const isNotVal = unitDesc === undefined || unitDesc === null || unitDesc === '';
        if (valueDesc && valueDesc.length > 3 && isNotVal) {
          // 兜底 担心样式破了
          finalSize = 20;
        }
      }
      if (finalSize) {
        this.couponValueStyle = `font-size: ${finalSize}px!important;`;
      }
      this.loading = false;
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = this.createSelectorQuery();
        query
          .select(selector)
          .boundingClientRect()
          .exec((res) => {
            const result = res[0] ? res[0] : {};
            resolve(result.width || 0);
          });
      });
    },

    onClose() {
      const displayData = this.displayData || {};
      this.ctx.logger &&
        this.ctx.logger.log({
          et: 'click',
          ei: 'continuepay_clickcancel',
          en: '下单挽留弹窗点击取消支付',
          pt: 'trade',
          params: {
            ordernumber: displayData.orderNo || '',
            money: displayData.price || '',
            type: ENUM[this.trackType],
          },
        });
      this.show = false;
    },

    onConfirm() {
      const displayData = this.displayData || {};
      this.ctx.logger &&
        this.ctx.logger.log({
          et: 'click',
          ei: 'continuepay_clickcontinue',
          en: '下单挽留弹窗点击继续支付',
          pt: 'trade',
          params: {
            ordernumber: displayData.orderNo || '',
            money: displayData.price || '',
            type: ENUM[this.trackType],
          },
        });
      this.show = false;
      const { bookKey, addressId, orderFrom, orderNo, bookKey: prevBookKey } = this.orderData || {};
      let params;
      if (orderNo) {
        // 是否已生单
        params = {
          addressId,
          orderFrom,
          orderNo,
          prevBookKey,
        };

        navigateToRantaPage({
          pageType: PAGE_TYPE.PAY,
          query: params,
        });
      } else {
        params = {
          addressId,
          orderFrom,
          bookKey,
        };

        /* #ifdef web */
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        const url = args.add('https://cashier.youzan.com/pay/wsctrade_buy', {
          book_key: bookKey,
          showwxpaytitle: 1,
          kdt_id: this.ctx.data.kdtId,
        });
        Tee.navigate({ url });
        /* #endif */
        /* #ifdef weapp */
        this.ctx.process.invoke('navigateToTradeBuy', {
          navigateParams: params,
        });
        /* #endif */
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.keep {
  &__item {
    margin-bottom: 24px;
  }

  &__items {
    margin-top: 8px;
  }

  &__container {
    padding-right: 24px;
    padding-left: 24px;
    padding-bottom: 16px;
  }

  &__container__reset__padding {
    padding-right: 0;
    padding-left: 0;
  }

  &__icon {
    height: 22px;
    width: 22px;
    padding-right: 12px;
  }

  &__text {
    color: #646566;
    font-size: 14px;
    line-height: 20px;
    text-align: center;

    &--plain {
      display: inline-block;
    }

    &--important {
      font-size: 14px;
      color: #ee0a24;
      padding-right: 4px;
      padding-left: 4px;
      font-weight: bold;
    }
  }

  &__text--tag {
    color: #646566;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    margin-bottom: 24px;
  }

  &--created {
    text-align: center;
    padding-bottom: 12px;
  }

  &__important {
    color: #ee0a24;
    font-size: 20px;
    font-weight: bold;
    padding-left: 2px;
    padding-right: 2px;
  }

  &__tag {
    display: inline-block !important;
    white-space: nowrap;
    margin-bottom: 12px;
    padding: 4px 8px !important;
    font-size: 12px;
    color: #323233 !important;
    letter-spacing: 0;
    line-height: 16px;
    margin-right: 6px;
    margin-left: 6px;

    &--container {
      margin-top: 8px;
      display: block;
    }
  }

  &__btn--disabled {
    color: #969799;
    font-size: 16px;
    text-align: center;
  }

  &__button--continue {
    height: 36px !important;
    line-height: 34px !important;
    margin-bottom: 16px;
    font-size: 16px !important;
    margin-left: auto;
    margin-right: auto;
    border: none;
    display: block;
    width: 100%;
  }

  &__eval {
    &--block {
      display: flex;
      margin-top: 12px;
      margin-bottom: 12px;
      padding: 0 2px;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  &__coupon {
    &--left {
      padding: 12px 8px;
      box-sizing: content-box;
      box-sizing: border-box;
      display: inline-block;
      margin-top: auto;
      margin-bottom: auto;

      &--block {
        min-width: 80px;
        box-sizing: border-box;
        text-align: center;
        white-space: nowrap;
      }
    }

    &--right {
      padding: 16px 0;
      width: 175px;
      box-sizing: border-box;
      display: inline-block;
    }

    &--price {
      font-size: 34px;
      line-height: 1;
      // color: #ee0a24;
      height: inherit;
      font-weight: 600;
      display: inline-block;
      white-space: nowrap;
    }

    &--unit {
      font-size: 12px;
      // color: #ee0a24;
      height: inherit;
      padding-left: 2px;
      line-height: 30px;
      display: inline-block;
    }

    &--unit--empty {
      font-size: 12px;
      color: #ee0a24;
      height: inherit;
      display: inline-block;
      line-height: 30px;
    }

    &--name {
      font-size: 14px;
      line-height: 14px;
      padding-bottom: 8px;
      color: #323233;
    }

    &--desc {
      font-size: 12px;
      color: #323233;
      line-height: 12px;
    }

    &--container {
      background: #f7f8fa;
      margin-top: 8px;
      height: 72px;
      display: flex;
      align-items: center;
      border-radius: 4px;
    }

    &--block {
      margin-bottom: 24px;
      position: relative;
    }
  }
}
.coupon-view {
  z-index: 1000;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
