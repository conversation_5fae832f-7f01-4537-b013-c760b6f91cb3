export interface IStoreObject {
  state?: Record<string, unknown>;
  getters?: Record<string, Function>;
  actions?: Record<string, Function>;
}

export const mergeStore = (
  srcStore: IStoreObject = {},
  destStore: IStoreObject = {}
): IStoreObject => {
  return {
    state: {
      ...srcStore.state,
      ...destStore.state,
    },
    getters: {
      ...srcStore.getters,
      ...destStore.getters,
    },
    actions: {
      ...srcStore.actions,
      ...destStore.actions,
    },
  };
};
