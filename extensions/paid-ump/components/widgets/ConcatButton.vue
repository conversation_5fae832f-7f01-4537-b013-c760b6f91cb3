<template>
  <van-button
    custom-class="custom-class"
    open-type="contact"
    :send-message-title="messageConcat.messageTitle"
    :show-message-card="messageConcat.showMessageCard"
    :send-message-path="messageConcat.messagePath"
    :send-message-img="messageConcat.msgImg"
    :session-from="messageConcat.sessionFrom"
    :business-id="messageConcat.businessId"
    @contact="onContactBack"
  >
    <slot></slot>
  </van-button>
</template>

<script>
import Tee from '@youzan/tee';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import args from '@youzan/weapp-utils/lib/args';
import { ConcatButtonConfig } from '../../constants';

const { msgImg, h5Logo, baseSharePath } = ConcatButtonConfig;

export default {
  components: {
    'van-button': Button,
  },
  props: {
    h5Link: String,
  },
  data() {
    return {
      messageTitle: ' ',
      showMessageCard: true,
      messagePath: '',
      msgImg,
    };
  },
  externalClasses: ['custom-class'],
  computed: {
    messageConcat() {
      const urlParams = {
        yz_live_code_link: encodeURIComponent(this.h5Link) || '', // 消息后端定义字段，返回消息的指定链接
        yz_live_code_image: encodeURIComponent(h5Logo), // 消息后端定义字段，返回消息的指定图片
        yz_live_code_title: '点击加好友', // 消息后端定义字段，返回消息的指定标题
        yz_live_code_desc: '点击识别二维码加客服微信专属福利享不停', // 消息后端定义字段，返回消息的指定描述文案
      };

      const messagePath = args.add(baseSharePath, urlParams);

      return {
        messageTitle: ' ',
        showMessageCard: true,
        messagePath,
        msgImg,
        sessionFrom: '',
        businessId: '',
      };
    },
  },
  methods: {
    onContactBack(detail) {
      this.$emit('after-contact');
      if (detail && detail.path) {
        Tee.navigate({
          url: args.add(detail.path, detail.query),
        });
      }
    },
  },
};
</script>
