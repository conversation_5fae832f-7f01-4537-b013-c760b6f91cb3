<template>
  <van-dialog
    use-slot
    title="请您确认收货地址"
    :show="show"
    :show-cancel-button="false"
    :show-confirm-button="false"
    :close-on-click-overlay="false"
  >
    <view class="address-confirm-dialog">
      <!-- 地址卡片 -->
      <view class="address-card">
        <view class="address-detail"
          >{{ currentAddress.province }}{{ currentAddress.city }}{{ currentAddress.county }}</view
        >
        <view class="address-name">{{ currentAddress.addressDetail }}</view>
        <view class="address-contact">{{ currentAddress.userName }} {{ currentAddress.tel }}</view>
      </view>

      <!-- 按钮组 -->
      <view class="button-group">
        <view class="btn-item switch-btn" type="default" round @click="switchAddress"
          >切换收货地址</view
        >
        <view class="btn-item confirm-btn" type="danger" round @click="confirmAddress"
          >对，就这个</view
        >
      </view>

      <!-- 不再提示 -->
      <view class="no-tip" @click="handleClickNoTip">
        <van-radio :value="noRemindValue" icon-size="14px" name="1">不再提示</van-radio>
      </view>
    </view>
  </van-dialog>
</template>

<script>
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import VanRadio from '@youzan/vant-tee/dist/radio/index.vue';
import { mapState, mapActions } from '@ranta/store';
import { setStorageSync } from '@youzan/tee-api';
import { mapData } from '@youzan/ranta-helper-tee';
import { NO_REMIND_ADDRESS_CONFIRM } from '../../constants';

export default {
  name: 'address-confirm-dialog',
  components: {
    VanDialog,
    VanRadio,
  },
  props: {
    address: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isShowed: false,
      show: false,
      showNoRemind: false,
      currentAddress: {},
      ...mapState(this, ['showAddressCard', 'expressType']),
    };
  },
  computed: {
    noRemindValue() {
      return this.showNoRemind ? '1' : '0';
    },
  },
  created() {
    mapData(this, ['currentAddress']);
    mapActions(this, ['onAddressCardClick']);

    this.handleConfirmAddress = this.handleBeforeOrder.bind(this);

    this.ctx.process.define('addressConfirm', this.handleConfirmAddress);
  },
  destroyed() {
    this.addressConfirmResolve = null;
    this.addressConfirmReject = null;
    this.ctx.process.undef('addressConfirm', this.handleConfirmAddress);
  },
  methods: {
    handleBeforeOrder() {
      // 判断是否需要弹出确认地址弹窗（expressType == 'express' 并且 showAddressCard才会展示地址栏），除此之外，不需要校验。
      const isShowAddress = this.expressType === 'express' && this.showAddressCard;
      // 不支持展示或者展示过：直接resolve不阻塞流程。
      if (!isShowAddress || this.isShowed) {
        return Promise.resolve();
      }
      this.isShowed = true;
      this.show = true;

      return new Promise((resolve, reject) => {
        this.addressConfirmResolve = resolve;
        this.addressConfirmReject = reject;
      });
    },
    afterClick() {
      if (this.showNoRemind) {
        setStorageSync(NO_REMIND_ADDRESS_CONFIRM, '1');
      }
      this.show = false;
    },
    confirmAddress() {
      this.afterClick();
      this.addressConfirmResolve();
    },
    switchAddress() {
      // 切换地址逻辑
      this.onAddressCardClick();
      this.addressConfirmReject();
      this.afterClick();
    },
    handleClickNoTip() {
      this.showNoRemind = !this.showNoRemind;
    },
  },
};
</script>

<style scoped>
.address-confirm-dialog {
  box-sizing: border-box;
  width: 100%;
  background: #fff;
  padding: 20px 16px;
}

.address-card {
  width: 100%;
  box-sizing: border-box;
  background: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.address-detail {
  font-weight: 400;
  font-size: 13px;
  color: #999;
  line-height: 1.38em;
  margin-bottom: 4px;
}

.address-name {
  font-weight: 500;
  font-size: 14px;
  color: #323233;
  line-height: 1.57em;
  margin-bottom: 4px;
}

.address-contact {
  font-size: 13px;
  color: #333;
  line-height: 1.38em;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 14px;
}
.btn-item {
  width: 138px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 16px;
  line-height: 48px;
  text-align: center;
}

.switch-btn {
  background: #fff;
  border: 1px solid #e0e0e0;
  color: #333;
}

.confirm-btn {
  background: var(--main-bg, '#7d6a57');
  color: #fff;
  border: none;
}

.no-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #999 !important;
  line-height: 1.28em;
}
</style>
