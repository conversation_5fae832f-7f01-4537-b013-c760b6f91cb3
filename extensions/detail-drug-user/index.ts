// Widgets
import Main from './Main.vue';

// Utils
import createStore from './store';

// Dependencies
import { bridge } from '@youzan/ranta-helper';
import { mapProcess } from '@youzan/ranta-helper-tee';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';

export default class DrugUserExtension {
  static widgets = {
    Main,
  };
  
  ctx: any;
  
  store: any;

  /**
   * hideRxStatus
   * @deprecated 从 2.0 开始
   * @desc 隐藏订单详情页的处方单状态
   */
  @bridge('hideRxStatus', 'process')
  hideRxStatus() {
    this.store.setShowRxStatus(false);
  }

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    
    mapCtxData(this, ['rxNo']);
    mapStoreToCtx(this, ['rxStatus']);
    mapProcess(this, {
      setShowRxStatus: this.store.setShowRxStatus,
    });
  }
}
