export const cloudData = {
  getPresale({ presaleSteps }) {
    return presaleSteps.map((item) => {
      const { phase, phaseArrived, phaseDesc, phasePayStatusDesc, payUmpDiscountMoney, realPrice } =
        item;
      return {
        phase,
        phaseArrived,
        phaseDesc,
        phasePayStatusDesc,
        payUmpDiscountMoney,
        realPrice,
        buyerRealPayText: `¥${(item.buyerRealPay / 100).toFixed(2)}`,
      };
    });
  },
};
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
