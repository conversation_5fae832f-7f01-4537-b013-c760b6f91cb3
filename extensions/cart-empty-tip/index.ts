import EmptyTip from './EmptyTip.vue';
import CartEmptyBlock from './CartEmptyBlock.vue';
import { cloud } from '@youzan/ranta-helper';

interface CartEmptyInfo {
  /** 空状态购物车图标链接 */
  iconUrl: string;
}
export default class EmptyTipExtension {
  @cloud('cartEmptyInfo', 'data')
  cartEmptyInfo: CartEmptyInfo;

  ctx: any;

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.watch('shopConfigs', (shopConfigs) => {
      this.cartEmptyInfo = {
        iconUrl: shopConfigs?.buy_cart_logo_url || '',
      };
    });
  }

  static widgets = {
    EmptyTip,
    CartEmptyBlock,
  };
}
