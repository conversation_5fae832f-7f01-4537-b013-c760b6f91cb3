<!-- eslint-disable max-len -->
<template>
  <van-popup
    v-if="initialShow"
    round
    safe-area-inset-bottom
    position="bottom"
    get-container="body"
    closeable
    class="present-popup"
    :custom-style="wrapperStyle"
    :show="show"
    @close="handleClose"
  >
    <view>
      <view class="present-popup__title">挑选赠品</view>
      <view class="present-popup__content">
        <view class="present-popup__content-des">可选{{ selectablePresentNum }}种赠品</view>
        <view
          class="present-popup__content-list"
          :style="disabledConfirm ? 'padding-bottom: 40px;' : ''"
        >
          <present-goods
            v-for="gds in goodsList"
            custom-style="border-radius: var(--theme-radius-card,16px); margin-bottom: 8px; overflow: hidden;"
            :key="gds.skuId"
            :is-can-choose="true"
            :must-choose="selectablePresentNum === goodsList.length"
            :activity-id="activityId"
            :goods="gds"
            :is-choose="!!checkedGoodsMap[gds.skuId]"
            @change-item-checked="handleCheckedGoods"
            @change-sku="handleChangeSku"
            is-pop
          />
        </view>
      </view>
      <view class="present-popup__notice-wrap">
        <view v-if="disabledConfirm" class="present-popup__notice">
          最多可选{{ selectablePresentNum }}种赠品
        </view>
      </view>
      <view class="present-popup__bottom">
        <text>已选：{{ checkedGoodsList.length }}</text>
        <van-button
          custom-style="border-radius: var(--theme-radius-button,18px);color: #fff;height: 36px;width: 96px;"
          :color="themeGeneralColor"
          :disabled="disabledConfirm"
          @click="handleConfirm"
        >
          确定
        </van-button>
      </view>
    </view>
  </van-popup>
</template>

<script>
/* #ifdef weapp */
import tabMixin from '@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin';
/* #endif */
import NoticeBar from '@youzan/vant-tee/dist/notice-bar/index';
import Popup from '@youzan/vant-tee/dist/popup/index';
import Button from '@youzan/vant-tee/dist/button/index';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { requestV2 } from '@youzan/tee-biz-request';
import { errorToast } from '@youzan/tee-biz-util';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { getOfflineId as getOfflineIdFromNpm } from '@youzan/shop-tee-shared/dist/adapter/index';

export default {
  components: {
    'van-popup': Popup,
    'van-button': Button,
    'van-notice-bar': NoticeBar,
  },

  /* #ifdef weapp */
  mixins: [tabMixin],
  /* #endif */

  data() {
    return {
      // 被选中商品
      checkedGoodsList: [],
      // 是否展示弹框
      show: false,
      // 赠送商品列表
      goodsList: [],
      // 活动id
      activityId: 0,
      // 已选择赠品商品列表
      pickGoodsList: [],
      // 可选赠品数量
      selectablePresentNum: 0,
      themeGeneralColor: '',
      initialShow: false,
    };
  },

  computed: {
    // 被选中商品键值对
    checkedGoodsMap() {
      return this.checkedGoodsList.reduce((memo, goods) => {
        memo[goods.skuId] = goods;

        return memo;
      }, {});
    },

    // 选择商品数量是否超出
    disabledConfirm() {
      return this.checkedGoodsList.length > this.selectablePresentNum;
    },

    // 下标映射值
    indexMap() {
      return this.goodsList.reduce((memo, { skuId }, index) => {
        memo[skuId] = index;

        return memo;
      }, {});
    },

    wrapperStyle() {
      let style =
        'bottom: 0px; border-radius: var(--theme-radius-dialog,20px) var(--theme-radius-dialog,20px) 0 0;';
      /* #ifdef weapp */
      style = `bottom: ${this.popupBottom}px; ${this.safeBottom ? 'padding-bottom: 34px;' : ''}`;
      /* #endif */
      return style;
    },
  },

  watch: {
    pickGoodsList() {
      this.getCheckedGoodsList();
    },
    goodsList(newList) {
      // 由于外层会修改可选赠品的sku信息，同步修改当前选中的商品列表中的sku信息
      this.checkedGoodsList = this.checkedGoodsList.map((goods) => {
        newList.forEach((item) => {
          if (item.id === goods.id) {
            goods = item;
          }
        });
        return goods;
      });
    },
    show: {
      handler(val) {
        if (val) {
          !this.initialShow && (this.initialShow = true);
          this.displayTimer && clearTimeout(this.displayTimer);
        } else {
          this.displayTimer = setTimeout(() => {
            this.initialShow = false;
          }, 600);
        }
      },
      immediate: true,
    },
  },

  mounted() {
    this.subCtxDataAndEvent();
  },

  methods: {
    // 监听数据和事件
    subCtxDataAndEvent() {
      mapData(this, {
        presentPopupInfo: (newVal) => {
          const { show, goodsList, activityId, pickGoodsList, selectablePresentNum } = newVal || {};
          this.show = show;
          this.goodsList = goodsList;
          this.activityId = activityId;
          this.pickGoodsList = pickGoodsList;
          this.selectablePresentNum = selectablePresentNum;
        },
        themeColors: (val) => {
          const themeGeneralColor = val.general;
          this.themeGeneralColor = themeGeneralColor;
        },
      });
      mapEvent(this, {
        'sku:selected': (skuData) => {
          this.selectedSku = skuData;
        },
        'sku:custom': () => {
          this.handleSkuChange(this.selectedSku);
          this.ctx.event.emit('present-sku:changed', {
            skuData: this.selectedSku,
            activityId: this.activityId,
          });
        },
        // 赠品sku数据获取
        'present-sku:fetch': (data) => this.getPresentData(data),
      });
    },

    // 关闭弹窗
    handleClose() {
      this.ctx.event.emit('presentPopupClose', { show: false });
    },

    // 获取当前选中的商品列表
    getCheckedGoodsList() {
      this.checkedGoodsList = [...(this.pickGoodsList || [])].filter((goods) => {
        return !goods.reason;
      });
    },

    handleSkuChange(skuData) {
      // 找到选中的sku信息
      const selectedSku = skuData.selectedSkuComb;
      const currentPresentId = this.ctx.data.presentGoodsId;

      const updateSku = (item) => {
        if (item.id === currentPresentId) {
          item.sku = selectedSku.sku;
          item.skuId = selectedSku.id;
        }
        return item;
      };

      // 渲染的是goodsList
      this.goodsList = this.goodsList.map(updateSku);

      this.checkedGoodsList = this.checkedGoodsList.map(updateSku);

      this.ctx.event.emit('present-sku:afterSubmit');
    },

    // 选择某个商品
    handleCheckedGoods({ goods, type }) {
      let { checkedGoodsList } = this;
      if (type === 'add') {
        checkedGoodsList.push(goods);
        checkedGoodsList = checkedGoodsList.sort((a, b) => {
          return this.indexMap[a.skuId] - this.indexMap[b.skuId];
        });
      } else {
        let index = -1;
        for (const checkedGoods of checkedGoodsList) {
          index++;

          if (checkedGoods.skuId === goods.skuId) {
            checkedGoodsList.splice(index, 1);
            break;
          }
        }
      }

      this.checkedGoodsList = [...checkedGoodsList];
    },

    handleChangeSku(data) {
      this.ctx.data.presentGoodsId = data.goods.id;
      const params = {
        ...data,
        goods: {
          ...data.goods,
          presentSkuList: data.goods?.goodsSkuInfoList || data.goods?.presentSkuList || [],
        },
      };
      this.getPresentData(params);
    },

    // 只供赠品活动使用的sku面板
    getPresentData(params) {
      const { goods = {} } = params;
      const { alias = '', presentSkuList = [], skuId } = goods;
      const offlineId = getOfflineIdFromNpm() || 0;

      requestV2({
        origin: 'cashier',
        withCredentials: true,
        method: 'GET',
        path: '/wsctrade/fetch-sku.json',
        data: {
          alias,
          offlineId,
        },
      })
        .then((sku) => mapKeysCase.toCamelCase(sku))
        .then((data) => {
          // 拿独立的赠品库存覆盖商品库存
          if (presentSkuList.length) {
            data.list = data.list.map((item) => {
              const hasMatch = presentSkuList.some((sku) => {
                if (sku.skuId === item.id) {
                  item.stockNum = sku.stockNum;
                  return true;
                }
                return false;
              });

              // 没有相匹配的则表示没有库存
              if (!hasMatch) {
                item.stockNum = 0;
              }
              item.price = 0;
              return item;
            });

            // 计算赠品的独立库存总和，覆盖商品库存
            data.stockNum = presentSkuList.reduce((total, item) => {
              return total + item.stockNum;
            }, 0);
          }

          // 初始化选中的sku
          const selectedSku = presentSkuList.find((item) => item.skuId === skuId) || {};
          const initialSku = data.list?.find((item) => item.id === selectedSku.skuId) ?? {};

          data.messages = []; // 本期赠品隐藏留言
          data.limit = { quota: 1 }; // 赠品限购1个

          this.ctx.process.invoke('setSkuInfo', {
            sku: data,
            goods: { ...goods, picture: goods.attachmentUrl || goods.imgUrl },
            price: 0, // 赠品的价格都是0
            skuConfig: {
              customBtnText: '完成',
              skuScene: 'custom',
            },
            initialSku: {
              ...initialSku,
              selectedNum: 1,
            },
            event: {
              skuScene: 'custom',
              submitAction: 'custom',
              skuOptions: {
                resetSku: true,
              },
            },
            goodsAttributes: { reset: true },
          });
        })
        .catch((error) => {
          errorToast(error, { message: '商品信息获取失败，请稍后重试' });
        });
    },

    // 确认选择商品
    handleConfirm() {
      if (this.disabledConfirm) {
        // 如果禁用，则不能点击确认
        return;
      }
      this.ctx.event.emit('presentPopupConfirm', {
        goodsList: this.checkedGoodsList,
        activityId: this.activityId,
        selectablePresentNum: this.selectablePresentNum,
      });
      this.handleClose();
    },
  },
};
</script>

<style scoped lang="scss">
.present-popup {
  &__title {
    height: 44px;
    line-height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
  }

  &__content {
    background: #f7f8fa;
  }

  &__content-des {
    font-size: 14px;
    padding: 12px 12px;
  }

  &__content-list {
    padding: 0 12px;
    height: 476px;
    overflow-y: scroll;
    box-sizing: border-box;

    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去138px（标题栏和行动按钮栏高度和）
    min-height: calc(50vh - 138px);
    min-height: calc(50vh - 138px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 138px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 138px);
    max-height: calc(80vh - 138px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 138px - env(safe-area-inset-bottom));
  }

  &__bottom {
    display: flex;
    background: #fff;
    height: 50px;
    width: 100%;
    font-size: 14px;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 12px;
  }

  &__bottom-button {
    button {
      border-radius: var(--theme-radius-button, 18px);
      color: #fff;
      height: 36px;
      width: 96px;
    }
  }

  &__notice-wrap {
    position: relative;
    z-index: 10;
  }

  &__notice {
    position: absolute;
    left: 0;
    top: -40px;
    text-align: center;
    height: 40px;
    line-height: 40px;
    width: 100%;
    background: #fffbe8;
    color: #ed6a0c;
    font-size: 14px;
  }

  &::v-deep {
    .t-popup__close-icon {
      top: 12px;
    }
  }
}
</style>
