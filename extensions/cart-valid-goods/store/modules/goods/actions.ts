import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { errorToast } from '@youzan/tee-biz-util';

import {
  deleteGoods,
  setGoodsNum,
  reselectGoods,
  getGoodSkusJson,
  batchDeleteGoods,
  updateSelectGoods,
  updateSelectAllGoods,
  batchUpdateSelectGoods,
} from '../../../api';
import { isEduIosOnlineGoods } from '../../../utils';
import skynet from '@youzan/wsc-tee-trade-common/lib/utils/skynet';

export default function (ctx) {
  return {
    // 全选变更
    handleToggleCheckedAll() {
      const { shopCart: shopGoods, editMode, isCheckedAll } = ctx.data;
      if (editMode === 'edit') {
        // 如果已全选，则取消选中
        if (isCheckedAll) {
          this.editCheckedGoods = {};
        } else {
          // 如果未全选，则全部选中
          (shopGoods.goodsGroupList || []).forEach((goodsGroup) => {
            (goodsGroup.goodsList || []).forEach((goods) => {
              this.editCheckedGoods = {
                ...this.editCheckedGoods,
                [goods.cartId + '']: 1,
              };
            });
          });
        }
        this.updateShopList({ isChecked: isCheckedAll });
      } else {
        if (this.loading) return;
        let promise;
        if (isCheckedAll) {
          promise = ctx.cloud.invoke('beforeGoodsSelect', { isAll: true });
        } else {
          promise = ctx.cloud.invoke('beforeGoodsUnselect', { isAll: true });
        }
        promise.then(() => {
          this.loading = true;
          this.onlineCourseUnselectFlag = false;
          // 结算模式: 批量选中或取消选中
          this.handleBatchUpdateSelectGoods({
            rangeType: 'shop',
            type: isCheckedAll ? 'remove' : 'add',
          });
        });
      }
    },

    handleBatchUpdateSelectGoods(payload) {
      const { rangeType, type } = payload;

      const params = {
        kdtId: this.kdtId,
        type,
      };

      // 选中或取消选中店铺所有商品
      if (rangeType === 'shop') {
        updateSelectAllGoods(params)
          .then(() => {
            this.loading = false;
            //
            ctx.event.emit('updateCartGoodsList');
          })
          .catch((err) => {
            this.loading = false;
            errorToast(err);
          });
      }
    },

    handleToggleSelectCartGoods({ rangeType, goods, kdtId, isActivity, type }) {
      let promise;
      if (rangeType === 'single') {
        if (type === 'add') {
          promise = ctx.cloud.invoke('beforeGoodsSelect', {
            goodsList: [goods],
          });
        } else {
          promise = ctx.cloud.invoke('beforeGoodsUnselect', {
            goodsList: [goods],
          });
        }
        return promise.then(() => {
          const params = {
            kdtId: this.kdtId,
            goods,
            type,
            isActivity,
          };
          return updateSelectGoods(params)
            .then(() => {
              ctx.event.emit('updateCartGoodsList', { scene: 'selectedChange' });
              ctx.cloud.emit('onGoodsChange', { goodsList: [goods] });
              return true;
            })
            .catch((err) => {
              err.code = err.code || err.data?.code;
              err.msg = err.msg || err.data?.msg;
              errorToast(err);
              return Promise.reject(err);
            });
        });
      }

      if (rangeType === 'shop') {
        if (!kdtId) {
          Toast('kdtId为空');
          return Promise.resolve();
        }
        const params = {
          kdtId,
          type,
        };
        return updateSelectAllGoods(params)
          .then(() => {
            ctx.event.emit('updateCartGoodsList');
            return true;
          })
          .catch((e) => {
            errorToast(e, { message: `${e?.msg || '操作失败'}, 请刷新或者重试` });
            return Promise.reject(e);
          });
      }

      if (rangeType === 'all') {
        if (type === 'add') {
          promise = ctx.cloud.invoke('beforeGoodsSelect', {
            isAll: true,
          });
        } else {
          promise = ctx.cloud.invoke('beforeGoodsUnselect', {
            isAll: true,
          });
        }
        return promise.then(() => {
          const { shopList } = ctx.data;
          const promises = shopList.map((item) => {
            const params = {
              kdtId: item.kdtId,
              type,
            };
            return updateSelectAllGoods(params)
              .then(() => {
                ctx.event.emit('updateCartGoodsList');
                return true;
              })
              .catch((e) => {
                errorToast(e, { message: `${e?.msg || '操作失败'}, 请刷新或者重试` });
                return Promise.reject(e);
              });
          });
          return Promise.all(promises).then(() => {
            ctx.cloud.emit('onGoodsChange', { isAll: true });
            return true;
          });
        });
      }
    },

    // 选中/取消选中单个商品
    handleItemChecked(goodsIndex, goodsGroupIndex, { detail: payload }) {
      const { rangeType, type, goods, goodsList = [], isActivity, clickStartTime } = payload;
      // 编辑模式
      if (ctx.data.editMode === 'edit') {
        if (rangeType === 'batch') {
          goodsList.forEach((goods) => {
            this.editCheckedGoods = {
              ...this.editCheckedGoods,
              [goods.cartId + '']: type === 'add' ? 1 : 0,
            };
          });
        } else {
          this.editCheckedGoods = {
            ...this.editCheckedGoods,
            [goods.cartId + '']: type === 'add' ? 1 : 0,
          };
        }
        // 更新一下checked
        this.updateShopList({
          propsGoodsList: rangeType === 'batch' ? goodsList : [goods],
          isChecked: type === 'add',
        });
      } else if (rangeType === 'batch') {
        const filterGoodsList = goodsList.filter((goods) => !isEduIosOnlineGoods(goods));
        if (!filterGoodsList.length) {
          Toast(`购物车里面尚未选中商品，请重新返回活动页/商品页加购`);
          return;
        }
        // 批量操作
        const params = {
          kdtId: this.kdtId,
          goodsList: filterGoodsList,
          type,
          isActivity,
        };
        let promise;
        if (!filterGoodsList[0].checked) {
          promise = ctx.cloud.invoke('beforeGoodsSelect', {
            goodsList: filterGoodsList,
          });
        } else {
          promise = ctx.cloud.invoke('beforeGoodsSelect', {
            goodsList: filterGoodsList,
          });
        }
        this.setHummerLogger({ key: 'cart-goods-selected', startTime: clickStartTime });
        promise.then(() => {
          batchUpdateSelectGoods(params)
            .then(() => {
              ctx.cloud.emit('onGoodsChange', { goodsList: filterGoodsList });
              goodsList.forEach((goods, goodsIndex) => {
                if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
                  this.updateGoodsList({
                    type: 'UPDATE_GOODS_SELECT_STATE',
                    goodsGroupIndex,
                    goodsIndex,
                    val: type === 'add' ? 1 : 0,
                  });
                }
                ctx.event.emit('updateCartGoodsList');
              });
            })
            .catch((err) => {
              // 报错则反向还原
              this.updateGoodsList({
                type: 'UPDATE_GOODS_SELECT_STATE',
                goodsGroupIndex,
                goodsIndex,
                val: type === 'add' ? 0 : 1,
              });
              errorToast(err);
            });
        });
      } else {
        const params = {
          kdtId: this.kdtId,
          goods,
          type,
          isActivity,
        };
        let promise;
        if (!goods.checked) {
          promise = ctx.cloud.invoke('beforeGoodsSelect', {
            goodsList: [goods],
          });
        } else {
          promise = ctx.cloud.invoke('beforeGoodsUnselect', {
            goodsList: [goods],
          });
        }
        this.setHummerLogger({ key: 'cart-goods-selected', startTime: clickStartTime });
        promise.then(() => {
          ctx.cloud.emit('onGoodsChange', { goodsList: [goods] });
          updateSelectGoods(params)
            .then(() => {
              // 更新当前选中商品列表
              this.updateGoodsList({
                type: 'UPDATE_GOODS_SELECT_STATE',
                goodsGroupIndex,
                goodsIndex,
                val: type === 'add' ? 1 : 0,
              });
              // noToast不需要显示加载中
              ctx.event.emit('updateCartGoodsList', { scene: 'selectedChange', noToast: true });
            })
            .catch((e) => {
              console.error('e: ', e);
              errorToast(e, { message: `${e?.msg || '操作失败'}, 请刷新或者重试` });
            });
        });
      }
    },
    updateShopList({ propsGoodsList = [], isChecked }) {
      const shopList = (ctx.data?.shopList || []).map((shop) => {
        const goodsGroupList = (shop.goodsGroupList || []).map((goodsGroup) => {
          const goodsList = (goodsGroup.goodsList || []).map((goods) => {
            if (propsGoodsList.length) {
              // 指定商品勾选状态
              const isFind = propsGoodsList.find((g) => g.cartId === goods.cartId);
              if (isFind) {
                goods.checked = isChecked;
              }
            } else {
              goods.checked = isChecked;
            }
            return goods;
          });
          return { ...goodsGroup, goodsList };
        });
        return { ...shop, goodsGroupList };
      });
      ctx.data.shopList = shopList;
    },
    // 更新商品列表
    updateGoodsList({ type, goodsGroupIndex, goodsIndex, val }) {
      if (type === 'UPDATE_GOODS_NUM') {
        if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
          const { shopCart } = ctx.data;
          // TODO 空值判断
          shopCart.goodsGroupList[goodsGroupIndex].goodsList[goodsIndex].num = val;
          ctx.data.shopCart = { ...shopCart };
        }
      } else if (type === 'UPDATE_GOODS_SELECT_STATE') {
        if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
          const { shopCart } = ctx.data;
          // TODO 空值判断
          shopCart.goodsGroupList[goodsGroupIndex].goodsList[goodsIndex].checked = !!val;
          ctx.data.shopCart = { ...shopCart };
        }
      } else if (type === 'UPDATE_GOODS_AFTER_DELETE') {
        if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
          const { shopCart } = ctx.data;

          if (shopCart.goodsGroupList[goodsGroupIndex].goodsList.length) {
            shopCart.goodsGroupList[goodsGroupIndex].goodsList.splice(goodsIndex, 1);
            ctx.data.shopCart = { ...shopCart };
          }
        }
      }
    },
    // 删除指定商品
    handleDeleteGoods({ goods, isActivity }) {
      const needConfirm = ctx.data.editMode === 'edit';
      return ctx.cloud
        .invoke('beforeGoodsDelete', {
          goodsList: [goods],
        })
        .then(() => {
          return deleteGoods({ goods, isActivity, needConfirm })
            .then(() => {
              ctx.event.emit('updateCartGoodsList');
              ctx.cloud.emit('onGoodsChange', { goodsList: [goods] });
              return true;
            })
            .catch((err) => Promise.reject(err));
        });
    },
    // 删除商品
    handleItemDelete(goodsIndex, goodsGroupIndex, { detail: { goods, isActivity } }) {
      const needConfirm = ctx.data.editMode === 'edit';
      ctx.cloud
        .invoke('beforeGoodsDelete', {
          goodsList: [goods],
        })
        .then(() => {
          deleteGoods({ goods, isActivity, needConfirm })
            .then(() => {
              this.updateGoodsList({
                type: 'UPDATE_GOODS_AFTER_DELETE',
                goodsGroupIndex,
                goodsIndex,
              });

              const { sku, skuData, skuId, goodsId, title } = goods;

              const eventParams = {
                no_sku: sku ? 0 : 1,
                sku_id: skuId,
                sku_name: skuData,
                goods_id: goodsId,
                goods_name: title,
              };

              ctx.logger &&
                ctx.logger.log({
                  et: 'click',
                  ei: 'cart_decrease_goods_num',
                  en: '购物车页面-删除商品',
                  params: eventParams,
                });

              // 用于区分删除与减少商品数量
              ctx.logger &&
                ctx.logger.log({
                  et: 'click',
                  ei: 'remove_from_cart',
                  en: '购物车移除商品',
                  params: eventParams,
                });

              ctx.event.emit('updateCartGoodsList');
              ctx.cloud.emit('onGoodsChange', { goodsList: [goods] });
            })
            .catch((err) => errorToast(err, { message: '商品删除失败，请稍后重试' }));
        });
    },
    handleSetCartGoodsNum({ val, goods }) {
      this.numChangeInvalid = true;
      // 1. 每一次触发数量变更流程，都会生成一个唯一标识
      const markId = Math.random().toString(16).slice(2);
      // 2. 每一次触发数量变更流程，都会更新全局的标识为最新的
      ctx.data.globalMarkId = markId;

      return ctx.cloud
        .invoke('beforeGoodsNumChange', {
          num: val,
          cartId: goods.cartId,
          goodsId: goods.goodsId,
        })
        .then(() => {
          // 支持商品套餐
          const params = { ...goods, num: val };
          if (goods.comboDetail) {
            params.combo = {
              comboType: goods.comboDetail.comboType,
              groupList: goods.comboDetail.groupList,
            };
          }
          return setGoodsNum(params)
            .then(() => {
              ctx.cloud.emit('onGoodsChange', { goodsList: [{ ...goods, num: val }] });
              ctx.cloud.emit('onGoodsNumChange', { ...goods, num: val });
              ctx.event.emit('updateCartGoodsList', { scene: 'numChange', markId });
            })
            .catch((err) => {
              this.numChangeInvalid = false;
              errorToast(err);
            });
        })
        .catch(() => {
          this.numChangeInvalid = false;
        });
    },
    // TODO: 需要刷新购物车数据情况处理
    handleItemNumChange(goodsIndex, goodsGroupIndex, { detail: payload }) {
      this.numChangeInvalid = true;
      Toast.loading({
        message: '加载中...',
        duration: 500,
        forbidClick: true,
      });
      const { num, goods, clickStartTime } = payload;
      this.setHummerLogger({ key: 'cart-goods-num-changed', startTime: clickStartTime });

      // 1. 每一次触发数量变更流程，都会生成一个唯一标识
      const markId = Math.random().toString(16).slice(2);
      // 2. 每一次触发数量变更流程，都会更新全局的标识为最新的
      ctx.data.globalMarkId = markId;

      ctx.cloud
        .invoke('beforeGoodsNumChange', {
          num,
          cartId: goods.cartId,
          goodsId: goods.goodsId,
        })
        .then(() => {
          // 更新商品数量到ctx
          this.updateGoodsList({
            type: 'UPDATE_GOODS_NUM',
            cartId: '',
            goodsGroupIndex,
            goodsIndex,
            val: num,
          });
          // 支持商品套餐
          const params = { ...goods, num };
          if (goods.comboDetail) {
            params.combo = {
              comboType: goods.comboDetail.comboType,
              groupList: goods.comboDetail.groupList,
            };
          }
          setGoodsNum(params)
            .then(() => {
              this.numChangeInvalid = false;
              ctx.cloud.emit('onGoodsChange', { goodsList: [{ ...goods, num }] });
              ctx.cloud.emit('onGoodsNumChange', { ...goods, num });
              ctx.event.emit('updateCartGoodsList', { scene: 'numChange', markId });
            })
            .catch((err) => {
              errorToast(err);
              this.updateGoodsList({
                type: 'UPDATE_GOODS_NUM',
                cartId: '',
                goodsGroupIndex,
                goodsIndex,
                val: goods.num,
              });
              this.numChangeInvalid = false;
            });
        })
        .catch(() => {
          this.numChangeInvalid = false;
        });
    },

    // 批量删除
    handleBatchDeleteGoods({ goodsList }) {
      return ctx.cloud
        .invoke('beforeGoodsDelete', {
          goodsList: [goodsList],
        })
        .then(() => {
          Toast.loading();
          return batchDeleteGoods(goodsList)
            .then((resp) => {
              if (!resp) {
                Toast(`请刷新或者重试`);
                return;
              }
              // 获取购物车数据
              ctx.event.emit('updateCartGoodsList');
              ctx.cloud.emit('onGoodsChange', { goodsList });

              Toast.clear();
              Dialog.close();

              return Promise.resolve(true);
            })
            .catch((err) => {
              errorToast(err, { message: '删除商品失败，请稍后重试' });
              return Promise.reject(err);
            });
        });
    },
    handleUpdateGoodsSku(goods) {
      return reselectGoods(goods)
        .then(() => {
          ctx.event.emit('updateCartGoodsList');
          ctx.event.emit('cartGoodsSku:afterSubmit');
          ctx.cloud.emit('onGoodsChange', { goodsList: [goods] });
          return Promise.resolve(true);
        })
        .catch((err) => {
          errorToast(err);
          return Promise.reject(err);
        });
    },
    // sku重选
    async handleChangeGoodsSkuCallback(data) {
      // TODO: 协助定位问题，问题解决后删除: https://jira.qima-inc.com/browse/ONLINE-830515
      skynet.info(
        '购物车修改商品规格',
        {
          data,
          currentSkuData: this.currentSkuData,
        },
        {
          kdtId: this.kdtId,
          /* #ifdef weapp */
          buyerId: getApp().getBuyerId(),
          /* #endif */
          /* #ifdef web */
          // @ts-ignore
          buyerId: window._global.buyerId,
          /* #endif */
        }
      );
      if (
        !this.currentSkuData ||
        (this.currentSkuData && Object.keys(this.currentSkuData).length === 0)
      ) {
        return;
      }
      const { skuData = {}, pluginsResult = {} } = data || {};

      const { currentShowSkuGoods, currentSkuData } = this;

      const { messages, selectedSkuComb, selectedNum } = skuData;
      const { id: skuId } = selectedSkuComb;
      const { goodsAttributes = {} } = pluginsResult;
      const formatMessages = Object.keys(messages).map((key) => messages[key]);

      const messagesStr = formatMessages.length ? JSON.stringify(formatMessages) : '';

      // 获取属性ID
      const propertyIds = Object.values(goodsAttributes).reduce(
        (total: [], item: []) => total.concat(item),
        []
      ) as unknown[];

      let skuDataGoodsMessage = {};
      try {
        skuDataGoodsMessage = JSON.parse(currentShowSkuGoods.messages);
      } catch (error) {
        skuDataGoodsMessage = {};
      }
      const skuDataGoodsMessageArr = Object.keys(skuDataGoodsMessage).map(
        (key) => skuDataGoodsMessage[key]
      );

      const skuDataGoodsMessageStr = skuDataGoodsMessageArr.length
        ? JSON.stringify(skuDataGoodsMessageArr)
        : '';

      // 没有修改不需要发送请求
      if (
        skuId === currentShowSkuGoods.skuId &&
        messagesStr === skuDataGoodsMessageStr &&
        selectedNum === currentShowSkuGoods.num &&
        propertyIds.join(',') === (currentShowSkuGoods.propertyIds || []).join(',') // 属性
      ) {
        ctx.event.emit('cartGoodsSku:afterSubmit');
        return;
      }
      const { itemDataModel = {} } = currentSkuData;
      const { comboMark = {}, comboDetailModel = {} } = itemDataModel;
      // 支持组合商品
      let combo = null;
      if (comboMark.isCombo) {
        const { comboGroupModels = [] } = comboDetailModel;
        const currentCombo = comboGroupModels.filter((comboGroup) => comboGroup.skuId === skuId);
        if (currentCombo?.length) {
          const { comboSubItemModels = [], goodsComboGroupId } = currentCombo[0];
          const subComboList = [];
          comboSubItemModels.forEach((comboSubItem) => {
            const { skuRelatedModels = [], propModels = [] } = comboSubItem;
            const propertyIds = propModels.reduce((propertyIds, propModel) => {
              const { textModels = [] } = propModel;
              textModels.forEach((item) => {
                propertyIds.push(item.id);
              });
              return propertyIds;
            }, []);
            skuRelatedModels.forEach(({ combineNum, itemId, price, skuId: subSkuId }) => {
              subComboList.push({
                goodsId: itemId,
                num: combineNum,
                price,
                propertyIds,
                skuId: subSkuId,
              });
            });
          });
          combo = {
            comboType: comboMark.comboType,
            groupList: [
              {
                id: goodsComboGroupId,
                subComboList,
              },
            ],
          };
        }
      }
      const params = {
        ...this.currentShowSkuGoods,
        skuId,
        propertyIds,
        messages: messagesStr,
        combo,
      };

      return reselectGoods(params)
        .then(() => {
          ctx.event.emit('updateCartGoodsList');
          ctx.event.emit('cartGoodsSku:afterSubmit');
          let newSku = '';
          try {
            newSku = (JSON.parse(selectedSkuComb.sku) || []).map((_) => _.v).join(',');
          } catch (_e) {}
          const newData = {
            ...params,
            sku: newSku,
          };
          ctx.cloud.emit('onGoodsSkuChange', newData);
          ctx.cloud.emit('onGoodsChange', { goodsList: [newData] });
        })
        .catch((e) => {
          errorToast(e, { message: `${e?.msg || e?.data?.msg || '操作失败'}, 请刷新或者重试` });
        });
    },
    handleChangeGoodsSku(goods) {
      this.currentSkuData = null;
      this.currentShowSkuGoods = goods;
      const { shopCart } = ctx.data;

      const params: any = {
        alias: goods?.alias || '',
        activityType: goods.activityType,
      };

      if (goods.storeId) params.offlineId = goods.storeId;

      getGoodSkusJson(params)
        .then((skuData = {}) => {
          skuData = mapKeysCase.toCamelCase(skuData);

          this.currentSkuData = skuData;

          const activityInfo =
            skuData.itemActivitySpuModels?.length > 0 ? skuData.itemActivitySpuModels[0] : {};

          const data = {
            sku: {
              ...skuData,
              ...activityInfo,
              // 价格标签
              priceTags: activityInfo.priceTitle ? [{ text: activityInfo.priceTitle }] : [],
            },
            goods: {
              id: goods.goodsId,
              title: goods.title,
              picture: goods.imgUrl,
              alias: goods.alias,
              propertyIds: goods.propertyIds,
            },
            skuConfig: {
              buyText: '确定',
              skuSence: 'buy',
            },
            messageConfig: {
              initialMessages: JSON.parse(goods.messages || '{}'),
            },
            baseConfig: {
              isShowSkuStepper: false,
            },
            pointsConfig: {
              name: shopCart.pointsName,
            },
          };

          const initialSku = skuData.list?.find((item) => item.id === goods.skuId) ?? {};

          ctx.process.invoke('setSkuInfo', {
            ...data,
            initialSku: {
              ...initialSku,
              selectedNum: goods.num,
            },
            event: {
              skuScene: 'buy',
              skuOptions: {
                resetSku: true,
              },
            },
            goodsAttributes: { reset: true },
          });
        })
        .catch((error) => {
          errorToast(error, { message: '商品信息获取失败，请稍后重试' });
        });
    },
    handleHideSkuShow() {
      this.currentSkuData = null;
      this.currentShowSkuGoods = null;
      ctx.process.invoke('setSkuInfo', null);
    },

    setHummerLogger({ key, startTime }) {
      const { mark = {} } = ctx.hummer || {};
      mark.start && mark.start(key, startTime);
      mark.end && mark.end(key);
    },
  };
}
