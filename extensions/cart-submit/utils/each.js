function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

function baseEach(collection, iteratee) {
  if (isObject(collection)) {
    const keys = Object.keys(collection);
    let index = -1;
    const len = keys.length;
    while (++index < len) {
      const key = keys[index];
      if (iteratee(collection[key], key, collection)) {
        break;
      }
    }
  }

  return collection;
}

function arrayEach(collection, iteratee) {
  let index = -1;
  const len = collection.length;
  while (++index < len) {
    if (iteratee(collection[index], index, collection) === false) {
      break;
    }
  }

  return collection;
}

/**
 *
 * _.forEach([1, 2], function(value) {
 *   console.log(value);
 * });
 * // => Logs `1` then `2`.
 *
 * _.forEach({ 'a': 1, 'b': 2 }, function(value, key) {
 *   console.log(key);
 * });
 * // => Logs 'a' then 'b' (iteration order is not guaranteed).
 */
export default function each(collection, iteratee) {
  const func = Array.isArray(collection) ? arrayEach : baseEach;
  return func(collection, iteratee);
}
