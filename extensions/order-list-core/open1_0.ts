import get from '@youzan/utils/object/get';

export const processOrderBtnsByYun = ({ orderItem, actionBtns, yunConfig }) => {
  let processedBtns = [...actionBtns];

  try {
    // 添加按钮
    const filterOrderStatus = get(yunConfig, 'btnListCondition.condition.filterOrderStatus', []);
    const activityTypes = get(yunConfig, 'btnListCondition.condition.activityTypes', []);
    const isMatchedOrderStatus = !filterOrderStatus.find((v) => v === orderItem.status);
    const isMatchedActivityType =
      !activityTypes.length ||
      (orderItem.items || []).find((v) => activityTypes.includes(+v.activityType));

    if (isMatchedOrderStatus && isMatchedActivityType) {
      const newBtns = get(yunConfig, 'btnListCondition.cloudBtnList', []).map((item) => ({
        text: item.value,
        value: item.key,
      }));
      processedBtns = [...processedBtns, ...newBtns];
    }

    // 移除按钮
    processedBtns = processedBtns.filter(
      (btn) => !get(yunConfig, 'hideButtonTypes', []).includes(btn.value)
    );
  } catch (e) {
    // 如果定制报错则什么都不做，原样返回 actionBtns
    console.error(e);
  }

  return processedBtns;
};
