import { requestV2 } from '@youzan/tee-biz-request';

const fetchV2 = (requestConfig) => requestV2({ ...requestConfig, config: {} });

/**
 * 获取换购商品
 * @param {*} data
 */
const findExchangeSkusJson = (data) =>
  fetchV2({
    data,
    path: '/wsctrade/cart/find-exchange-goods.json',
  });

/**
 * 获取商品skus信息
 * @param {*} data
 */
const getGoodSkusJson = (data) =>
  fetchV2({
    data,
    path: '/wsctrade/fetch-sku.json',
  });

// 批量添加购物车
const batchAddGoods = (data) =>
  fetchV2({
    path: '/wsctrade/cart/batchAddGoods.json',
    method: 'POST',
    data: {
      items: data,
    },
  });

// 批量删除
const deleteBatch = (data) =>
  fetchV2({
    path: '/wsctrade/cart/deleteBatchList.json',
    method: 'POST',
    data: {
      ids: data,
    },
  });

export { deleteBatch, batchAddGoods, getGoodSkusJson, findExchangeSkusJson };
