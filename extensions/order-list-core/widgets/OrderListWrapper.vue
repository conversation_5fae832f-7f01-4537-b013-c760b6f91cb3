<template>
  <view>
    <order-list />
    <view ref="list-reach-bottom-line" />
  </view>
</template>

<script>
/* #ifdef web */
const isElementAtBottom = (e) => {
  const rect = e.$el.getBoundingClientRect();
  return rect.bottom - 300 <= window.innerHeight;
};
/* #endif */

export default {
  created() {
    /**
     * 你可能会觉得奇怪，为什么不直接用 mapState(this, ['activityTab'])
     * 因为这里的activityTab和store.activityTab是不同的，如果直接复制给store的
     * activityTab 类似：this.store.setActivityTab({ ... }) 是会内存溢出的
     * 所以这里需要手动监听，后续这里可以把activityTab优化成一个getter
     */
    this.store.watch('activityTab', (val = {}, oldVal = {}) => {
      this.activityTab = { ...val };

      if (val.type === oldVal.type) {
        return;
      }

      /* #ifdef web */
      // 切换 tab 滚动到顶部
      document.documentElement.scrollTop = 0;
      // 兼容安卓手机，安卓机 document.documentElement.scrollTop 无效
      document.body.scrollTop = 0;
      /* #endif */
    });
  },

  mounted() {
    /* #ifdef weapp */
    getApp().trigger('first-render', 'order-list');
    /* #endif */
    /* #ifdef web */
    window.addEventListener('scroll', () => {
      const target = this.$refs['list-reach-bottom-line'];
      if (isElementAtBottom(target)) {
        if (this.loading) return;
        this.store.loadOrder();
      }
    });
    /* #endif */
  },
};
</script>

<style lang="scss" scoped>
.welike-entry__wrapper:not(:empty) {
  padding: 12px 12px 0;
}

.order-list {
  overflow: hidden;

  .t-loading {
    margin: 30px auto;
  }

  .list-item {
    margin: 12px;
    padding-bottom: 16px;
    border-radius: 8px;
    background: #fff;

    &:first-child {
      margin-bottom: 0;
    }
  }
}

.shop-cert-info {
  margin-top: 40px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &__fixed {
    position: fixed;
    width: 100%;
    bottom: 0;
  }

  &-title {
    color: #666;
    font-size: 14px;
    line-height: 14px;
    display: flex;
    align-items: center;
    padding: 12px 12px 0;
    width: 100%;
    box-sizing: border-box;
  }

  &__left,
  &__right {
    background: #e0e0e0;
    height: 1px;
    flex: 1;
    transform: scaleY(0.5);
  }

  &__text {
    margin: 0 4px;
  }

  &-support {
    padding: 16px 0 12px;
    text-align: center;
  }

  &__img {
    width: 96px;
    height: 52px;
  }
}
</style>
