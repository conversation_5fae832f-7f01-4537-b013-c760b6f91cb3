# @wsc-tee-trade/detail-postpone-popup

顺延弹层 `纯展示`

![依赖商品列表的「我要顺延」按钮](https://img.yzcdn.cn/public_files/bb7fec58a5f903215d7d48722a194ded.png) ![UI呈现](https://img.yzcdn.cn/public_files/bb7fec58a5f903215d7d48722a194ded.png)

## 调试方式

### mock 方法

由于依赖 `detail-goods` 所以直接调整 `ext-tee-wsc-trade/extensions/detail-goods/format.js` 中的 `isShowPostponeShip` 即可显示

```javascript
// 周期购顺延
if (isShowPostponeShip) {
  res.btns.push(BTN.postpone);
}
```

改成

```javascript
// 周期购顺延
if (true) {
  res.btns.push(BTN.postpone);
}
```

### 正常流程

## 存疑

- 是否可以直接放在 `detail-goods` 中，作为一个 Widget？

## Widget.Provide

| 名称                    | 说明     |
| ----------------------- | -------- |
| PostponePopup `default` | 顺延弹层 |

## Data.Consume

| 名称  | 类型    | 说明     |
| ----- | ------- | -------- |
| goods | _Goods_ | 商品信息 |

## Event.Listen

| 名称              | 说明         |
| ----------------- | ------------ |
| showPostponePopup | 展示顺延弹窗 |
