<template>
  <view :style="themeCSS">
    <receiver-modify :show="showReceiverModifyPopup" />
    <select-address-popup :show="showAddressModifyPopup" />
    <select-time-popup :show="showTimeModifyPopup" />
  </view>
</template>

<script>
import { mapState } from '@ranta/store';

export default {
  data() {
    return {
      ...mapState(this, [
        'showReceiverModifyPopup',
        'showAddressModifyPopup',
        'showTimeModifyPopup',
        'themeCSS',
      ]),
    };
  },
};
</script>
