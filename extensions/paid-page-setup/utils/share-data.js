export function normalShare({ shareMessage = {}, orderItems = [], query }) {
  const { title, desc, imgUrl: imageUrl } = shareMessage;

  let nameMaxlen = 0;
  let message = '';
  let subTitle = '';
  if (orderItems.length > 1) {
    nameMaxlen = 14;
    message = '等，必须推荐给你';
  } else {
    nameMaxlen = 15;
    message = '，必须推荐给你';
  }

  if (orderItems[0]?.title.length > nameMaxlen) {
    subTitle = orderItems[0].title.substr(0, nameMaxlen) + '...等' || '一些东西';
  } else {
    subTitle = orderItems[0]?.title || '一些东西';
  }

  return {
    ...shareMessage,
    title: `我买了${subTitle}${message}` || `${title}，${desc}`,
    imageUrl,
    path: `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      `/packages/order/share-page/index?order_no=${query.orderNo}&kdt_id=${query.kdtId}&is_share=1`
    )}`, // 讲分享的url改成分享落地页的url,
  };
}
