<template>
  <view class="coupon" :style="rootStyle" v-if="couponInfo.show">
    <van-cell-group :border="false">
      <van-cell title="优惠" title-width="80px" title-class="font-color--black" :border="false">
        <view v-for="couponItem in couponInfo.list" :key="couponItem.name">
          <view class="coupon-value">
            <view>{{ couponItem.condition }}</view>
          </view>
        </view>
      </van-cell>
    </van-cell-group>
  </view>
</template>

<script>
// Components
import Cell from '@youzan/vant-tee/dist/cell/index.vue';
import CellGroup from '@youzan/vant-tee/dist/cell-group/index.vue';

// Dependencies
import { mapState } from '@ranta/store';

export default {
  components: {
    'van-cell-group': CellGroup,
    'van-cell': Cell,
  },

  data() {
    return {
      ...mapState(this, ['coupon', 'couponCode', 'display', 'rootStyle', 'couponInfo']),
    };
  },
};
</script>

<style lang="scss">
.coupon {
  margin-top: 10px;
}

.font-color--black {
  font-size: 14px;
  color: #969799;
}

.coupon-value {
  font-size: 14px;
  color: #323233;
}
</style>
