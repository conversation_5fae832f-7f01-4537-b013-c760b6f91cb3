import Main from './Main.vue';
import SearchInput from './SearchInput.vue';
import FrequentlyPurchaseItems from './FrequentlyPurchaseItems.vue';
import { setRequestDep } from '@youzan/tee-biz-request';
import { initLogger } from './logs';
import { mapData } from '@youzan/ranta-helper-tee';
import Event from '@youzan/tee-event';

export default class Extension {
  constructor(options) {
    this.ctx = options.ctx;
    mapData(this, {
      kdtId: (kdtId) => {
        setRequestDep({
          kdtId,
        });
      },
    });
    initLogger(options.ctx);
  }

  static widgets = {
    Main,
    SearchInput,
    FrequentlyPurchaseItems,
  };

  pageDestroyed() {
    setTimeout(() => {
      Event.off('search-order');
    }, 100 /* 延迟100ms 是希望订单列表能正常监听到再销毁，避免订单列表无法感知事件，对用户无感 */);
  }
}
