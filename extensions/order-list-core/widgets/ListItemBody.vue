<template>
  <view>
    <!-- TODO: 不明白为什么这里要加一层 -->
    <order-list-item
      v-for="(item, index) in listItem.orderItems"
      :key="item.orderNo"
      :order-index="index"
      :list-index="listIndex"
      :list-item="listItem"
    />
  </view>
</template>

<script>
export default {
  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: {
      type: Object,
      default: () => ({ orderItems: [] }),
    },
  },
};
</script>
