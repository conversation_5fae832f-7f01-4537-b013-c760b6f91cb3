<template>
  <view class="sale-base">
    <text v-for="item in descArray" :key="item.type">
      <text class="sale-base__countdown">{{ item.value }}{{ item.text }}</text>
    </text>
    <text>后开售</text>
  </view>
</template>

<script>
import CountDown from '@youzan/weapp-utils/lib/countdown';

const TIME_MAP = {
  DAY: 'day',
  HOUR: 'hour',
  MIN: 'min',
  SEC: 'sec',
};
const countdownData = [
  {
    type: TIME_MAP.DAY,
    value: '',
    text: '天',
    width: '0',
  },
  {
    type: TIME_MAP.HOUR,
    value: '',
    text: '时',
    width: '0',
  },
  {
    type: TIME_MAP.MIN,
    value: '',
    text: '分',
    width: '0',
  },
  {
    type: TIME_MAP.SEC,
    value: '',
    text: '秒',
    width: '0',
  },
];

export default {
  name: 'sale-countdown',

  props: {
    startSoldTime: Number,
  },

  data() {
    return { descArray: [] };
  },

  mounted() {
    this.saleCountDown();
  },

  destroyed() {
    this.stopCountDown();
  },

  methods: {
    stopCountDown() {
      this.sendedCountDown && this.sendedCountDown.stop();
    },

    saleCountDown() {
      const nowTime = new Date().getTime() / 1000;
      const successTime = this.startSoldTime;
      const leftTime = successTime > nowTime ? successTime - nowTime : 0;

      this.sendedCountDown = new CountDown(leftTime * 1000, {
        onChange: (timeData, strData) => {
          const { day, hour, minute, second } = strData;
          const count = countdownData.map((item) => {
            let value = '';
            switch (item.type) {
              case TIME_MAP.DAY:
                value = day;
                break;
              case TIME_MAP.HOUR:
                value = hour;
                break;
              case TIME_MAP.MIN:
                value = minute;
                break;
              default:
                value = second;
                break;
            }
            return {
              ...item,
              value,
            };
          });
          let descArray = [];
          if (day > 0) {
            descArray = count.slice(0);
          } else if (
            (hour === 0 || hour === '0') &&
            (minute === 0 || minute === '0') &&
            (second === 0 || second === '0')
          ) {
            this.$emit('hide-countdown');
          } else {
            const countdown = [hour, minute, second];
            for (let i = 0; i < 3; i++) {
              if (countdown[i] !== 0 && countdown[i] !== '0') {
                descArray = count.slice(i + 1);
                break;
              }
            }
          }
          this.descArray = descArray;
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.sale-base {
  font-family: PingFangSC-Regular, sans-serif;
  color: #323233;
  font-size: 12px;
  line-height: 14px;
  letter-spacing: 0;
  margin-bottom: 8px;
  display: flex;

  &__countdown {
    min-width: 6px;
    display: inline-flex;
  }
}
</style>
