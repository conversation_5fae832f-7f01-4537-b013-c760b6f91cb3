<template>
  <view
    class="order-pay-prompt-goods__container o-private"
    :class="[
      prompt.goods.length === 1
        ? 'order-pay-prompt-goods__container_single'
        : 'order-pay-prompt-goods__container_multi',
    ]"
  >
    <view class="template" @click="onClick" v-if="prompt.goods.length === 1">
      <view class="order-pay-prompt-goods">
        <image
          class="order-pay-prompt-goods__image"
          mode="aspectFill"
          style="width: 56px; height: 56px"
          :src="prompt.goods[0].imgUrl"
        />

        <view class="order-pay-prompt-goods__body">
          <native-text
            overflow="ellipsis"
            class="order-pay-prompt-goods__name"
            style="display: inline-block"
          >
            {{ prompt.goods[0].goodsName }}</native-text
          >
          <view class="order-pay-prompt-goods__price">
            <text>应付：</text>
            <cap-price :price="prompt.payPrice" />
            <text class="order-pay-prompt-goods__service">
              {{ prompt.goods[0].service }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="template" v-else>
      <view class="order-pay-prompt-goods__box">
        <view class="order-pay-prompt-goods__list">
          <scroll-view
            type="list"
            :scroll-x="true"
            :enable-flex="true"
            style="flex-direction: row"
            class="order-pay-prompt-goods__list-inner"
            :class="{ 'no-more-than-3': prompt.goods.length <= 2 }"
          >
            <view
              :key="item.name"
              v-for="item in prompt.goods"
              class="order-pay-prompt-goods__item"
            >
              <image
                class="order-pay-prompt-goods__image"
                mode="aspectFill"
                style="width: 56px; height: 56px"
                :src="item.imgUrl"
              />
            </view>
          </scroll-view>
        </view>
        <view class="order-pay-prompt-goods__more">
          <van-icon color="#999999" name="arrow" />
        </view>
      </view>
      <view class="order-pay-prompt-goods__price">
        共{{ prompt.goods.length }}件，应付
        <cap-price :price="prompt.payPrice" />
      </view>
    </view>
  </view>
</template>

<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import CapPrice from './CapPrice';

export default {
  components: {
    'van-icon': Icon,
    'cap-price': CapPrice,
  },

  props: {
    prompt: {
      type: Object,
      default: () => ({}),
    },

    goods: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss">
.order-pay-prompt-goods__container {
  .cap-price {
    font-size: 0;
    color: inherit;

    &__unit {
      font-weight: bold;
    }

    &__integer {
      font-size: 18px;
      font-weight: bold;
    }

    &__decimal {
      font-weight: bold;
    }
  }
}

.o-private.order-pay-prompt-goods {
  &__container {
    padding: 8px;
    border-radius: var(--theme-radius-card, 4px);
    margin-bottom: 24px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #f7f8fa;

    .template {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }

    &_single {
      .order-pay-prompt-goods {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        &__image {
          border-radius: var(--theme-radius-card, 4px);
          margin-right: 8px;
          overflow: hidden;
          min-width: 56px;
        }

        &__body {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          overflow: hidden;
          width: 0;
        }

        &__name {
          color: #323233;
          font-size: 12px;
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }

        &__price {
          color: #323233;
          font-size: 12px;
          text-align: center;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        &__service {
          font-size: 12px;
          color: #faab0c;
          margin-left: 5px;
        }
      }
    }

    &_multi {
      .order-pay-prompt-goods {
        &__box {
          width: 100%;
          position: relative;
          margin-bottom: 4px;
        }

        &__list {
          display: flex;
          overflow: scroll;
          width: 100%;

          &::-webkit-scrollbar {
            width: 0;
            height: 0;
          }
        }

        &__list-inner {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          justify-content: flex-start;
          overflow: visible;
          height: 56px;
        }

        &__item {
          margin-right: 8px;
          &:last-child {
            margin-right: 32px;
          }
        }

        &__list-inner.no-more-than-3 {
          min-width: 100%;
          justify-content: center;
          .order-pay-prompt-goods__item {
            &:last-child {
              margin-right: 0;
            }
          }
        }

        &__image {
          border-radius: var(--theme-radius-card, 4px);
          overflow: hidden;
        }

        &__more {
          position: absolute;
          top: 0;
          right: 0;
          width: 32px;
          height: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;
          background: url('https://b.yzcdn.cn/public_files/e94ec6fb5356250763cddf04f5151412.png')
            no-repeat;
          background-size: 100% 100%;
        }

        &__price {
          font-size: 12px;
          color: #323233;
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }
    }
  }
}
</style>
