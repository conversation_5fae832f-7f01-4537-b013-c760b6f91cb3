<template>
  <view v-if="showTag" class="list-item-tag">{{ tagText }}</view>
</template>

<script>
import { CRM_OFFLINE_TYPE, CRM_OFFLINE_TEXT, PAGE_SCENE } from '../utils/constants';

export default {
  name: 'list-item-tag',

  props: {
    listItem: Object,
  },

  computed: {
    showTag() {
      // 零售订单列表不展示标签
      const isRetailOrderList = this.ctx.scene === PAGE_SCENE.RETAIL;
      if (isRetailOrderList) {
        return false;
      }
      return this.listItem.crmOfflineType !== CRM_OFFLINE_TYPE.NONE;
    },
    tagText() {
      return CRM_OFFLINE_TEXT[this.listItem.crmOfflineType];
    },
  },
};
</script>

<style lang="scss" scoped>
.list-item-tag {
  width: 56px;
  line-height: 16px;
  background-color: #e8eaee;
  border-radius: 8px 0;
  color: #2c2c2d;
  font-size: 12px;
  text-align: center;
  margin-bottom: -8px;
}
</style>
