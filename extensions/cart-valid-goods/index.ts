import { cloud, bridge, useAsHook, useAsEvent } from '@youzan/ranta-helper';
import { mapData, mapProcess, mapEvent } from '@youzan/ranta-helper-tee';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import type { TradeCartGoods } from '@youzan-cloud/cloud-biz-types';
import ValidGoods from './ValidGoods.vue';
import Present from './Present.vue';
import GoodsGroupItem from './widgets/GoodsGroupItem.vue';
import GoodsItem from './widgets/GoodsItem.vue';
import Goods from './widgets/Goods.vue';
import ComboDetailPopup from './widgets/ComboDetailPopup.vue';
import { updateSelectGoods } from './api';
import createStore from './store';
import { cloudData, isSameObject, isGoodsCheckboxEnable, isEduIosOnlineGoods } from './utils';
import type {
  TradeCartSetGoodsNumParams,
  TradeCartGoodsChangePayload,
  ShopList,
  GoodsList,
} from './types';

export default class ValidGoodsExt {
  widget;

  ctx: any;

  store: any;

  /**
   * shopList
   * @desc 购物车店铺列表数据
   */
  @bridge('shopList', 'data')
  shopList: ShopList;

  /**
   * goodsList
   * @desc 购物车商品列表数据
   */
  @bridge('goodsList', 'data')
  goodsList: GoodsList;

  /**
   * emptyCart
   * @deprecated 从 2.0 开始
   * @desc 清空购物车
   */
  @bridge('emptyCart', 'process')
  emptyCart(): Promise<boolean> {
    return this.ctx.process.invokePipe('emptyCart');
  }

  /**
   * beforeGoodsNumChange
   * @desc [可中断]商品购买数量改变前触发
   */
  @cloud('beforeGoodsNumChange', 'hook', { allowMultiple: true })
  beforeGoodsNumChange = useAsHook<(payload: TradeCartSetGoodsNumParams) => Promise<void>>();

  /**
   * beforeGoodsSelect
   * @desc [可中断]选中商品前触发
   */
  @cloud('beforeGoodsSelect', 'hook', { allowMultiple: true })
  beforeGoodsSelect = useAsHook<(payload: TradeCartGoodsChangePayload) => Promise<void>>();

  /**
   * beforeGoodsUnselect
   * @desc [可中断]取消商品选中前触发
   */
  @cloud('beforeGoodsUnselect', 'hook', { allowMultiple: true })
  beforeGoodsUnselect = useAsHook<(payload: TradeCartGoodsChangePayload) => Promise<void>>();

  /**
   * beforeGoodsDelete
   * @desc [可中断]删除商品前触发
   */
  @cloud('beforeGoodsDelete', 'hook', { allowMultiple: true })
  beforeGoodsDelete = useAsHook<(payload: TradeCartGoodsChangePayload) => Promise<void>>();

  /**
   * onGoodsNumChange
   * @desc 商品购买数量改变时触发
   */
  @cloud('onGoodsNumChange', 'event', { allowMultiple: true })
  onGoodsNumChange = useAsEvent<(payload: TradeCartGoods) => void>();

  /**
   * onGoodsSkuChange
   * @desc 商品SKU改变时触发
   */
  @cloud('onGoodsSkuChange', 'event', { allowMultiple: true })
  onGoodsSkuChange = useAsEvent<(payload: TradeCartGoods) => void>();

  /**
   * onGoodsChange
   * @desc 购物车商品数据变更时触发
   */
  @cloud('onGoodsChange', 'event', { allowMultiple: true })
  onGoodsChange = useAsEvent<(payload: TradeCartGoodsChangePayload) => void>();

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx);
    // 控制推荐商品渲染
    this.ctx.data.isValidGoodsLoadFinish = false;
    console.log('isValidGoodsLoadFinish 设置为false');
    this.initData();
    this.initCtxData();
    this.initCloudData();
    this.initProcess();
    this.initEvents();
    mapStoreToCtx(this, ['numChangeInvalid']);
  }

  onPageShow() {
    this.store.updateListObNeedReLoad(true);
  }

  initData() {
    mapData(this, {
      hasResponseValidGoods: (newVal) => {
        // 如果从true变为false，则表明没有有效商品
        if (!newVal) {
          console.log('isValidGoodsLoadFinish 变更为true');
          this.ctx.data.isValidGoodsLoadFinish = true;
        }
      },
    });
  }

  initCtxData() {
    mapCtxData(this, [
      'offlineId',
      'shopCart',
      'editMode',
      'themeCSS',
      'themeColors',
      'themeStyle',
      'themeTag',
      'canSelectPresent',
      'hasResponseValidGoods',
    ]);
  }

  static widgets = {
    ValidGoods,
    Present,
    GoodsGroupItem,
    GoodsItem,
    Goods,
    ComboDetailPopup,
  };

  static lambdas = {
    isGoodsCheckboxEnable,
    isEduIosOnlineGoods,
  };

  initEvents() {
    mapEvent(this, {
      /** Sub Events */
      'cartGoodsSku:submit': this.store.handleChangeGoodsSkuCallback,
      // 赠品sku确认
      'cartPresentSku:submit': () => {
        if (this.store.currentShowSkuGoods?.goodsData) {
          this.store.handleChangePresentSkuCallback(this.store.currentShowSkuGoods);
        }
      },
      // 赠品sku切换
      'cartPresentSku:selected': this.store.handlePresentSkuSelect,
      'cartGoodsSku:hide': this.store.handleHideSkuShow,
      toggleCheckedAll: this.store.handleToggleCheckedAll,
      // 删除商品
      deleteCartItems: () => {
        // 批量删除商品
        const toDeleteGoodsList = this.ctx.data?.checkedGoodsList || [];
        return Dialog.confirm({
          message: `确定删除所选店铺的${toDeleteGoodsList.length}个商品？`,
        })
          .then(() => {
            this.store.handleBatchDeleteGoods({ goodsList: toDeleteGoodsList });
          })
          .catch(() => {
            Dialog.close();
          });
      },
      // 关闭赠品弹窗
      presentPopupClose: this.store.handleChangePresentPopup,
      // 赠品弹窗点击确认
      presentPopupConfirm: this.store.handlePickPresent,
      // 下拉刷新后需要重置load
      onPullDownRefresh: this.store.handlePullDownRefresh,
      stopPullDownRefresh: this.store.handleStopLoading,
    });
  }

  initProcess() {
    mapProcess(this, {
      emptyCart: () => {
        return this.ctx.process
          .invokePipe('beforeCartClearHook', { clearGoodsType: ['validGoods'] })
          .then(() => {
            const { shopList = [] } = this.ctx.data;
            let allGoods = [];
            shopList.forEach(({ goodsGroupList = [] }) => {
              goodsGroupList.forEach(({ goodsList = [] }) => {
                allGoods = allGoods.concat(goodsList);
              });
            });
            return this.store.handleBatchDeleteGoods({ goodsList: allGoods });
          });
      },
      deleteCartGoods: ({ goods, isActivity }) =>
        this.store.handleDeleteGoods({ goods, isActivity }),
      batchDeleteCartGoods: ({ goodsList }) => this.store.handleBatchDeleteGoods({ goodsList }),
      selectCartGoods: ({ rangeType, goods, kdtId, isActivity }) =>
        this.store.handleToggleSelectCartGoods({
          rangeType,
          goods,
          kdtId,
          isActivity,
          type: 'add',
        }),
      cancelSelectCartGoods: ({ rangeType, goods, kdtId, isActivity }) =>
        this.store.handleToggleSelectCartGoods({
          rangeType,
          goods,
          kdtId,
          isActivity,
          type: 'remove',
        }),
      setCartGoodsNum: ({ val, goods, isActivity }) =>
        this.store.handleSetCartGoodsNum({ val, goods, isActivity }),
      reselectGoods: (goods) => this.store.handleUpdateGoodsSku(goods),
      changeGoodsSku: (goods) => this.store.handleChangeGoodsSku(goods),
      showCartActivityPopup: (activityInfo) => this.store.changeCartActivityPopupShow(activityInfo),
      showExchangeModal: (params) => this.store.showExchangeModal(params),
      updateSelectGoods: (params) => updateSelectGoods(params),
    });
  }

  initCloudData() {
    mapData(this, ['shopList'], {
      isSetData: false,
      callback: () => {
        const { shopList } = this.ctx.data;

        const newOpenData = {
          shopList: cloudData.getShopList({ shopList }),
          goodsList: cloudData.getGoodsList({ shopList }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
