<template>
  <van-dialog
    use-slot
    class="activate-dialog"
    theme="round-button"
    :show="visible"
    :close-on-popstate="false"
    :show-confirm-button="false"
    custom-style="background-color: transparent;"
  >
    <view class="activate-dialog-content">
      <image class="activate-dialog-content__bg" :src="bgUrl" />

      <view class="activate-dialog-content__body">
        <view class="dialog-body-header">
          <view class="dialog-title">{{ name }}</view>
          <view class="dialog-desc">{{ desc }}</view>
        </view>
        <view v-if="benefitList.length" class="activate-dialog-content__body-content">
          <view v-for="item in benefitList" :key="item.key" class="dialog-benefit-item">
            <image class="dialog-benefit-item-image" :src="item.icon" />
            <view class="dialog-benefit-item-text">{{ item.showName }}</view>
          </view>
        </view>
        <view v-else class="activate-dialog-content__body-empty">
          <image class="empty-image" :src="EMPTYICON" />
        </view>
      </view>
    </view>

    <view class="activate-dialog-footer">
      <van-button
        @click="activate"
        block
        color="linear-gradient(90deg, #E2BB7C 0%, #E8C388 100%)"
        text-style="opacity: 0.94;font-weight: 500;font-size: 16px; color: #724804;"
        type="info"
        custom-class="activate-dialog-footer__btn"
      >
        {{ btnText }}
      </van-button>
      <view class="activate-dialog-footer__close">
        <image @click="close" :src="CLOSEICON" />
      </view>
    </view>
  </van-dialog>
</template>
<script>
import { Dialog, Button } from '@vant/tee';
import fullfillImage from '@youzan/utils/url/fullfillImage';
import Tee from '@youzan/tee';
import { args } from '@youzan/tee-util/lib/common/url';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';

const bgUrl = fullfillImage('upload_files/2021/11/15/Fp2OuQMKpWewaa6upVwdmO-Bswcq.png', 'middle');

const EMPTYICON = fullfillImage(
  'upload_files/2021/12/07/Fp1oGBTjo7fPZ4d6TD2quT_mvLFJ.png',
  'middle'
);

const CLOSEICON = fullfillImage(
  'upload_files/2021/11/15/FoRtIwFZwemPJqchrDKL-1sX40N8.png',
  'middle'
);

const ALLICON = fullfillImage('public_files/a1fb1079aa056c48631a190bc5247dd9.png', 'middle');

export default {
  name: 'activate-benefitcard-dialog',
  components: {
    'van-dialog': Dialog,
    'van-button': Button,
  },

  data() {
    return {
      visible: false,
      bgUrl,
      EMPTYICON,
      CLOSEICON,
      newAwardInfo: {},
      kdtId: 0,
    };
  },
  computed: {
    benefitcard() {
      return this.newAwardInfo.formatedMemberCard ?? {};
    },
    benefitList() {
      const { benefitList = [] } = this.benefitcard;

      if (benefitList.length > 4) {
        return benefitList.slice(0, 3).concat({
          showName: `更多${benefitList.length}项权益`,
          benefitCount: benefitList.length,
          icon: ALLICON,
        });
      }
      return benefitList;
    },
    name() {
      return `恭喜你，获得${this.benefitcard.cardName ?? ''}`;
    },
    desc() {
      if (this.benefitcard.needActivated) {
        if (this.benefitList.length) {
          return '尚未激活，激活可享以下权益';
        }
        return '尚未激活，激活即享专属权益';
      }
      if (this.benefitList.length) {
        return '可享受以下权益';
      }
      return '可享受专属权益';
    },
    btnText() {
      if (this.benefitcard.needActivated) {
        return '立即激活';
      }
      return '立即查看';
    },
    gotoUrl() {
      const { kdtId } = this;
      if (this.benefitcard.needActivated) {
        /** #ifdef weapp */
        return `/packages/benefit-card/active/index?alias=${this.benefitcard.cardAlias}&kdt_id=${kdtId}`;
        /** #endif */
        /** #ifdef web */
        // eslint-disable-next-line no-unreachable
        return args.add('/wscuser/scrm/benefitcard/active', {
          alias: this.benefitcard.cardAlias,
          kdt_id: kdtId,
        });
        /** #endif */
      }

      /** #ifdef weapp */
      return `/packages/benefit-card/detail/index?alias=${this.benefitcard.cardAlias}&kdt_id=${kdtId}`;
      /** #endif */

      /** #ifdef web */
      // eslint-disable-next-line no-unreachable
      return args.add('/wscuser/scrm/benefitcard', {
        card_alias: this.benefitcard.cardAlias,
        kdt_id: kdtId,
      });
      /** #endif */
    },
  },

  created() {
    mapData(this, ['kdtId', 'newAwardInfo']);
    mapEvent(this, {
      showActivateBenefitcardDialog: () => {
        this.visible = true;

        this.ctx.logger.log({
          et: 'view', // 事件类型
          ei: 'view_rulecard_post', // 事件标识
          en: '规则卡发卡曝光-弹窗', // 事件名称
          pt: 'paySuccess', // 页面类型
          params: {
            act: this.benefitcard.needActivated ? '1' : '0',
          },
        });
      },
    });
  },

  methods: {
    close() {
      this.visible = false;
      this.ctx.event.emit('closeAutoOpenPopup', {
        isRealClose: true,
      });
    },
    activate() {
      this.ctx.logger.log({
        et: 'click', // 事件类型
        ei: 'click_rulecard_post', // 事件标识
        en: '规则卡发卡点击-弹窗', // 事件名称
        pt: 'paySuccess', // 页面类型
        params: {
          act: this.benefitcard.needActivated ? '1' : '0',
        },
      });
      Tee.navigate({
        url: this.gotoUrl,
      });
    },
  },
};
</script>

<style lang="scss">
.activate-dialog {
  overflow: visible;

  &-content {
    border-radius: 20px;
    position: relative;
    background: #fff;

    &__bg {
      position: absolute;
      right: 0;
      width: 178px;
      height: 85px;
    }

    &__body {
      .dialog-body-header {
        padding-top: 48px;
        .dialog-title {
          font-size: 18px;
          color: #323233;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }
        .dialog-desc {
          font-size: 14px;
          color: #7d7e80;
          text-align: center;
          line-height: 20px;
          margin-top: 8px;
        }
      }

      &-empty {
        padding: 16px 0 24px;
        .empty-image {
          display: block;
          margin: 0 auto;
          width: 214px;
          height: 160px;
        }
      }
      &-content {
        padding: 32px 16px;
        text-align: center;
        display: flex;
        justify-content: center;

        .dialog-benefit-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 4px;
          width: 66px;
          &:first-child {
            margin-left: 0;
          }
        }

        .dialog-benefit-item-image {
          height: 40px;
          width: 40px;
        }

        .dialog-benefit-item-text {
          font-size: 12px;
          color: #646566;
          margin-top: 4px;
        }
      }
    }
  }

  &-footer {
    margin-top: 24px;
    width: 100%;
    text-align: center;
    &__close {
      margin: 24px auto 0;
      height: 32px;
      width: 32px;
      display: block;
    }

    &__btn {
      border-radius: 22px !important;
    }
  }
}
</style>
