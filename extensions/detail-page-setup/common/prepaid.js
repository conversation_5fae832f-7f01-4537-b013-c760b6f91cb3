function parseJSON(data, defaultValue = {}) {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (e) {
      return defaultValue;
    }
  }
  return defaultValue;
}

export function getRechargeInfo({
  AGGREGATED_PAY_TYPE: aggregatedPayType,
  AGGREGATED_PAY_INFO: aggregatedPayInfo,
}) {
  let payInfo = null;
  if (aggregatedPayType === 'RECHARGE') {
    const afterParsed = parseJSON(aggregatedPayInfo, null);
    payInfo = Array.isArray(afterParsed) ? afterParsed[0] : afterParsed;
  }
  return aggregatedPayType === 'RECHARGE'
    ? {
        origin: aggregatedPayInfo,
        jump: true,
        ...payInfo,
      }
    : null;
}
