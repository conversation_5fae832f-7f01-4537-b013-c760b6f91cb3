{"extensionId": "@wsc-tee-trade/paid-action", "name": "@wsc-tee-trade/paid-action", "version": "2.0.4", "bundle": "<builtin>", "lifecycle": ["beforePageMount"], "widget": {"default": "PaidAction", "provide": ["SubscriptionDialog", "WishMsgPopup", "PaidSubscriptionMsg", "ActivateBenefitcardDialog", "LongClickSaveImageDialog", "PaidSubscriptionMsgTips"], "consume": ["SubscriptionDialog", "GroupbuySelfFetchButton", "GroupbuyShareButton", "GroupbuyContinueButton", "SolitaireShareButton", "PriorUseDialog", "WishMsgPopup", "PaidSubsidyDialog", "PaidSubscriptionMsg", "ActivateBenefitcardDialog", "PaidSubscriptionMsgTips"]}, "component": {"consume": ["MsgSubscribe"]}, "data": {"provide": {"showWeappSubscribe": ["w", "r"]}, "consume": {"kdtId": ["r"], "payResult": ["r"], "orderNo": ["r"], "hasPaid": ["r"], "orderItems": ["r"], "canUseTradeUmpV1": ["r"], "showAwardV2Block": ["r"], "newAwardInfo": ["r"], "themeCSS": ["r"], "themeColors": ["r"], "changeSubscribe": ["r"], "phasePaymentStage": ["r"], "yunDesignConfig": ["r"], "themeRadius": ["r"], "isSkyline": ["r"], "paidCodeInfoIsShow": ["r"], "emptyPaidExtHolderBlock": ["r"]}}, "event": {"listen": ["gotoOrderDetail", "showSubscriptionDialog", "hideSubscriptionDialog", "showSelfFetchDialog", "hideSelfFetchDialog", "showVoucherCardDialog", "hideVoucherCardDialog", "closeAutoOpenPopup", "showActivateBenefitcardDialog", "showTakeGoodsDialog", "buyAgain", "disableAutoPopup", "hidePickUpCodeBtn", "handleOpenShare", "SubscribeNotice", "gotoIndentorCenter", "gotoIndentorShipment", "showSaveImageDialog", "handlerWeappSubscription"], "emit": ["closeAutoOpenPopup", "openGroupbuyShareDialog", "openSolitaireDialog", "showSelfFetchDialog", "hideSelfFetchDialog", "showVoucherCardDialog", "hideVoucherCardDialog", "showSubscriptionDialog", "hideSubscriptionDialog", "showTradeShareDialog", "showShopAdDialog", "gotoOrderDetail", "openFissionPopup", "openAutoSendCouponPopup", "showPriorUseDialog", "openSubsidyPopup", "showActivateBenefitcardDialog", "showTakeGoodsDialog", "gotoIndentorCenter", "gotoIndentorShipment", "disableAutoPopup", "buyAgain", "hidePickUpCodeBtn", "saveVoucherCardPoster", "saveSelfFetchPoster", "handlerWeappSubscription"]}, "process": {"invoke": ["requestSubscribeMessagePush"]}, "platform": ["weapp", "web"]}