import get from '@youzan/utils/object/get';
import Main from './Main.vue';
import FlowChangeUmp from './widgets/FlowChangeUmp.vue';
import { mapData, mapProcess } from '@youzan/ranta-helper-tee';
import { bridge } from '@youzan/ranta-helper';
import { getInSourcingCoupon, getInSourcingCouponShare } from './utils/in-sourcing-fission';

import createPaidExtHolderStore from './store';
import { mapCtxData, mapStoreToCtx } from '@ranta/store';

export default class PaidExtHolderExtension {
  ctx: any;

  store: any;

  isFxZppOrder = false;

  isWxReceipt = false;

  canUseTradeUmpV1 = false;

  /* #ifdef weapp */
  /**
   * updateComponent
   * @deprecated 从 2.0 开始
   * @desc 控制指定UI的显示隐藏
   * @param moduleName = 'SubscribeNotice'
   */
  @bridge('updateComponent', 'method')
  updateComponent(moduleName, display) {
    if (moduleName === 'SubscribeNotice') {
      this.ctx.event.emit('SubscribeNotice', display);
    }
  }
  /* #endif */

  constructor(options) {
    this.ctx = options.ctx;

    this.initWatch();

    this.store = createPaidExtHolderStore();

    mapCtxData(this, [
      'hasPaid',
      'canUseTradeUmpV1',
      'isWxReceipt',
      'kdtId',
      'returnUrl',
      'showExtHolder',
      'payResult',
    ]);
    mapStoreToCtx(this, ['emptyPaidExtHolderBlock', 'paidExtHolderLoaded']);

    mapProcess(this, {
      // 获取内购券分享数据
      getInSourcingCouponShare: () => {
        const inSourcingFission = getInSourcingCoupon(this.ctx.data.payResult);
        return getInSourcingCouponShare(inSourcingFission);
      },
    });
  }

  initWatch() {
    mapData(this, {
      isWxReceipt: (isWxReceipt) => {
        this.isWxReceipt = isWxReceipt;
        this.setShowExtHolder();
      },
      canUseTradeUmpV1: (canUseTradeUmpV1) => {
        this.canUseTradeUmpV1 = canUseTradeUmpV1;
        this.setShowExtHolder();
      },
      payResult: (payResult) => {
        this.isFxZppOrder = get(payResult, 'isFxZppOrder', false); // 赞拼拼订单
        this.setShowExtHolder();
      },
    });
  }

  setShowExtHolder() {
    this.ctx.data.showExtHolder =
      !this.isFxZppOrder && (!this.isWxReceipt || !this.canUseTradeUmpV1);
  }

  static widgets = {
    Main,
    FlowChangeUmp,
  };
}
