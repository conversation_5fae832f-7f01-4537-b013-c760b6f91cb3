# @wsc-tee-trade/detail-logistics

物流信息

![UI呈现](https://img.yzcdn.cn/public_files/a4eafc5ddec89a80db292e8bd7460a4f.png)

## 调试方式

- 找一个实物商品，[比如这个](https://shop16911610.m.youzan.com/wscgoods/detail/2xa950hf0v1huoz?alias=2xa950hf0v1huoz)
- 下单 - 支付 - 支付成功 - 订单详情
- 复制订单号
- 前往 [订单-订单管理-订单查询](https://dian16911610.shangjia.youzan.com/v4/trade/order/index)
- 输入订单号 - 查询 - 找到刚才的订单
- 发货 - 选择商品 - 发货方式：商家自行配送 - 发货
- 返回订单详情页 - 刷新

## 存疑

## Widget.Provide

| 名称                | 说明     |
| ------------------- | -------- |
| Logistics `default` | 物流信息 |

## Component.Consume

| 名称 | 类型 | 说明 |
| --- | --- | --- |
| kdtId | _number_ | 店铺 ID |
| orderNo | _string_ | 订单号 |
| logistics | ??? | 配送信息 `_global.orderInfo.goodsPackages[0]` |
| showLogisticsInfo | _boolean_ | 是否展示<br> 依赖 Node 返回的 `_global.orderInfo.goodsPackages` 这个字段在 `DeliveryHandler.handler` 中有做处理 |
| isSelfFetch | _boolean_ | 是否是自提 `_global.orderBizExtra.isSelfFetch` |
| isPeriodBuy | _boolean_ | 是否是周期购 `_global.orderBizExtra.isPeriodBuy` |
| isMultiPeriodBuy | _boolean_ | 是否多周期购，**新希望独有** `_global.orderInfo.goodsPackages.length > 1 && _global.itemInfo.length > 0 && _global.itemInfo[0].goodsType === 24` |

## 关键字速查

| 关键字 | 说明 |
| ------ | ---- |
|        |      |
