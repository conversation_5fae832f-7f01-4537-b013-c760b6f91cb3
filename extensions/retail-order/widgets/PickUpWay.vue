<template>
  <view style="padding-bottom: 10px; background: #fff">
    <van-cell
      custom-class="long-cell"
      title="提货方式"
      title-width="70px"
      :border="false"
    >
      <view class="pickupway">
        <view
          class="item"
          v-for="way in pickUpWays"
          :key="way.value"
          @click="onSwitchPickUpWay(way.value)"
        >
          <view class="icon" :class="{ active: way.value === value }" />
          <view>{{ way.name }}</view>
        </view>
      </view>
    </van-cell>
    <view v-if="showRetailPickUpWayErrorToast" class="error">请选择提货方式</view>
  </view>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-tee';
import Cell from '@youzan/vant-tee/dist/cell/index.vue';

export default {
  components: {
    'van-cell': Cell,
  },

  data() {
    return {
      value: this.ctx.data.retailPickUpWayValue || '',
      pickUpWays: this.ctx.data.retailPickUpWays || [],
      showRetailPickUpWayErrorToast: false,
    };
  },

  created() {
    mapData(this, [
      'showRetailPickUpWayErrorToast',
    ]);
  },

  methods: {
    onSwitchPickUpWay(val) {
      if (val === this.value) return;
      this.value = val;
      this.ctx.data.showRetailPickUpWayErrorToast = false;
      const pickUpWay = this.pickUpWays.find((item) => item.value === val);
      this.ctx.process.invoke('updatePickUpWay', pickUpWay);
    },
  },
};
</script>

<style lang="scss" scoped>
.long-cell {
  padding: 10px 12px 0 !important;
}

.pickupway {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.item {
  word-wrap: break-word;
  color: #323233;
  line-height: 20tpx;
  display: flex;
  align-items: center;
  &:last-child {
    margin-left: 25tpx;
  }
}

.icon {
  flex-shrink: 0;
  position: relative;
  width: 16tpx;
  height: 16tpx;
  border: 1tpx solid #7d7e80;
  border-radius: 50%;
  box-sizing: border-box;
  // 加上是因为h5被其他地方样式影响了
  margin-bottom: 0;
  margin-right: 10tpx;
  &.active {
    border-color: var(--general);
    &:after {
      content: '';
      position: absolute;
      top: 3tpx;
      left: 3tpx;
      width: 8tpx;
      height: 8tpx;
      border-radius: 50%;
      background-color: var(--general);
    }
  }
}

.error {
  padding: 0 12px;
  font-size: 12tpx;
  font-weight: 400;
  line-height: 20tpx;
  text-align: right;
  color: #f00;
}
</style>
