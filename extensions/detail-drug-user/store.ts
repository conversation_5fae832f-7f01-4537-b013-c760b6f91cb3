import { requestV2 } from '@youzan/tee-biz-request';
import { errorToast } from '@youzan/tee-biz-util';
import { createStore as _createStore } from '@ranta/store';
import type { StoreModule } from '@youzan/wsc-tee-trade-common/lib/types';

const rootStore: StoreModule = {
  state: {
    rxNo: '',
    showRxStatus: true,
    drugMessage: {},
    currentUser: {},
  },
  getters: {
    rxStatus() {
      const rxStatus = this.drugMessage?.rxStatus || {};
      return rxStatus;
    },
  },
  actions(ctx) {
    return {
      setShowRxStatus(isShow: boolean) {
        this.showRxStatus = isShow;
      },
      fetchPrescription() {
        if (!this.rxNo) {
          return;
        }
        return requestV2({
          path: '/wsctrade/order/drug/queryID.json',
          data: {
            rxNo: this.rxNo,
          },
          method: 'post',
        })
          .then((res: Record<string, any>) => {
            const { medicalInformation, patientInfo } = res;
            this.drugMessage = {
              ...this.drugMessage,
              ...res,
              orderCreatedMsg: res,
              diagnosedDiseases: medicalInformation,
            };
            this.currentUser = {
              ...patientInfo,
            };
          })
          .catch((error) => {
            if (!this.showRxStatus) {
              return;
            }
            errorToast(error, { message: '创建处方单失败，请稍后再试' });
          });
      },
    }
  }
};

export default function createStore(ctx) {
  return _createStore({
    state: () => ({
      ...rootStore?.state,
    }),
    getters: {
      ...rootStore?.getters,
    },
    actions: {
      ...rootStore?.actions(ctx),
    },
  });
}
