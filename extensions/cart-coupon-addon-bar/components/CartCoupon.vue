<template>
  <view class="cap-couponbox">
    <coupon-card
      :coupon="coupon"
      :disabled="!coupon.ableUse"
      :custom-style="customStyle"
      :is-super="isVip"
      @action="sendCoupon"
    >
      <view slot="coupon-additional" :class="{ 'coupon-addon-extra': true, extra_vip: isVip }">
        <view class="addon-info">
          <view style="padding-bottom: 8px; color: #323233">
            <view v-if="!coupon.ableUse">{{ coupon.unableUseDesc }}</view>
            <view v-else>
              <text
                :key="item"
                v-for="item in addOnCopywritingFormats"
                :style="item.highlight && !isVip ? 'color:' + themeColors.general : ''"
              >
                {{ item.text }}
              </text>
            </view>
          </view>
          <view v-if="coupon.ableUse" class="cart-item-list">
            <view
              v-for="item in coupon.allAbleCartGoods"
              :key="item.skuId"
              class="cart-item"
              @click="clickItem(item)"
            >
              <view
                class="item-thumb"
                :style="{ backgroundImage: 'url(' + item.attachmentUrl + ')' }"
              />
              <view class="item-price">￥{{ item.price }}</view>
              <view class="item-num">x{{ item.num }}</view>
              <check-box
                custom-class="item-check"
                :value="item.selectState"
                :checked-color="checkedColor"
                icon-size="16px"
              />
            </view>
          </view>
        </view>
        <view
          v-if="
            !coupon.satisfy &&
            coupon.couponId &&
            coupon.ableUse &&
            !exchangeGoodsCoupon &&
            !isPdLive
          "
          :class="['extra-action', coupon.allAbleCartGoods.length > 4 ? 'shadow' : '']"
          @click="goToAddOnPage(coupon)"
        >
          去凑单
          <van-icon name="arrow" size="16px" />
        </view>
      </view>
    </coupon-card>
  </view>
</template>
<script>
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import CheckBox from '@youzan/vant-tee/dist/checkbox/index.vue';
import CouponCard from '@youzan/wsc-tee-goods-common/components/promotion-block/promotion-pop/components/CouponCard.vue';
import { navigateToMember } from '@youzan/wsc-tee-goods-common/components/promotion-block/promotion-pop/utils/navigate';
import TakeIcon from './TakeIcon.vue';
import { url } from '@youzan/tee-util';
import { getGuideInfo } from '../api';

const GUIDE_TYPE = {
  FREE: 1,
  PAID: 2,
  CARD: 3,
};

export default {
  components: {
    'van-icon': Icon,
    'check-box': CheckBox,
    'take-icon': TakeIcon,
    'coupon-card': CouponCard,
  },
  props: {
    coupon: { type: Object, default: () => ({}) },
    kdtId: [Number, String],
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    isPdLive: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    customStyle() {
      if (!this.coupon.ableUse) {
        return `color: #C8C9CC; border: 1px solid #C8C9CC`;
      }
    },
    validOverArea() {
      if (this.coupon.valid.includes('生效') && this.coupon.valid.length > 15) {
        return 'margin-top: -10px;';
      }
    },

    isVip() {
      return [GUIDE_TYPE.FREE, GUIDE_TYPE.PAID].includes(this.coupon.guideType);
    },

    checkedColor() {
      return this.isVip ? '#674531' : this.themeColors.general;
    },

    exchangeCouponText() {
      return `门槛${!this.coupon.excludeExchangeGoods ? '包含' : '不包含'}可兑换商品金额`;
    },

    addOnCopywritingFormats() {
      const { addOnCopywriting = '' } = this.coupon;
      const list = addOnCopywriting.match?.(/(.*)\[(.*)\](.*)/);
      if (list) {
        const rs = [
          {
            text: list[1],
          },
          {
            highlight: true,
            text: list[2] || '233',
          },
          {
            text: list[3] || '233',
          },
        ];
        return rs;
      }
      return [{ text: addOnCopywriting }];
    },

    exchangeGoodsCoupon() {
      // 是否是商品兑换券
      return this.coupon.preferentialMode === 3;
    },
  },
  methods: {
    sendCoupon() {
      const { needGuide, activityId } = this.coupon;
      if (needGuide) {
        // 快速入会
        getGuideInfo({
          guideType: 'coupon',
          voucherActivityId: activityId,
        }).then((data) => {
          if (data.guideType === GUIDE_TYPE.FREE) {
            this.$emit('launchFastJoinSDK', { coupon: this.coupon });
          } else if (data.needGuide) {
            navigateToMember(data, this.kdtId);
          }
        });
      } else {
        this.$emit('sendCoupon', this.coupon);
      }
    },
    clickItem(item) {
      this.$emit('clickItem', { item, coupon: this.coupon });
    },
    goToAddOnPage(coupon) {
      this.$emit('goToAddOnPage', coupon);
      /**
       * 网店商品使用范围 applicableOnlineGoodsRangeType
       * 0: 该渠道商品不可用 1: 不限制 2: 部分可用 3: 部分不可用
       *
       */
      const { applicableOnlineGoodsRangeType } = coupon;
      if (applicableOnlineGoodsRangeType === 0 || applicableOnlineGoodsRangeType === 1) {
        this.goToShopIndex();
      } else if (applicableOnlineGoodsRangeType === 2 || applicableOnlineGoodsRangeType === 3) {
        this.goToCouponAddOn(coupon);
      }
    },
    goToShopIndex() {
      let link = 'packages/home/<USER>/index';

      /* #ifdef web */
      link = `${window._global?.url?.wap}/showcase/homepage?kdt_id=${this.kdtId}`;
      /* #endif */

      this.$emit('to-navigate', { link });
    },
    goToCouponAddOn({ couponId, alias }) {
      let link = url.args.add('packages/user/coupon/goods-list/index', {
        kdtId: this.kdtId,
        alias,
        id: couponId,
      });

      /* #ifdef web */
      link = url.args.add('/wscump/coupon/goodslist', { kdtId: this.kdtId, alias, id: couponId });
      /* #endif */

      this.$emit('to-navigate', { link });
    },
  },
};
</script>
<style lang="scss" scoped>
.cap-couponbox {
  margin-bottom: 12px;
}

.extra_vip {
  .extra-action {
    color: #674531 !important;
  }
}

.coupon-addon-extra {
  position: relative;
  border-top: 1px dashed #ebedf0;
  display: flex;
  height: 100%;

  .addon-info {
    height: 100%;
    padding: 8px 12px;
    flex-grow: 1;
    overflow: hidden;
  }

  .t-checkbox__icon .t-icon {
    width: 16px;
    height: 16px;
    background: white;
  }

  .shadow {
    box-shadow: -2px 0 6px 0 rgba(100, 101, 102, 0.08);
  }

  .extra-action {
    display: flex;
    align-items: center;
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 12px;
    color: var(--ump-icon, #323233);
    width: 18.1vw;
    min-width: 18.1vw;
    justify-content: center;
  }

  .cart-item-list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    overflow-x: scroll;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      display: none;
    }

    .cart-item {
      width: 56px;
      height: 84px;
      padding-right: 8px;
      position: relative;
    }

    .item-thumb {
      width: 56px;
      height: 56px;
      border-radius: var(--theme-radius-card, 4px);
      background-size: cover;
    }

    .item-price {
      font-family: PingFangSC-Medium, sans-serif;
      font-size: 10px;
      color: #323233;
      height: 14px;
      text-align: center;
    }

    .item-num {
      font-family: PingFangSC-Regular, sans-serif;
      font-size: 10px;
      color: #969799;
      height: 14px;
      text-align: center;
    }

    .item-check {
      position: absolute;
      left: 2px;
      top: 2px;
    }
  }
}
</style>
