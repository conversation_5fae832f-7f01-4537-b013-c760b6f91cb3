/**
 * 监听数据变化，执行特定副作用函数
 */
export default function (store) {
  store.watch('shopCart', (newVal) => {
    const newShopCart = store.updatePresentSkuData(newVal);
    store.updateShopGoods(newShopCart);
    store.updatePresentInfo();
    store.batchUnSelectEduOnlineGoods();
  });

  store.watch('editMode', () => {
    // 重置编辑模式选中状态
    store.resetEditCheckedGoods();
  });
  store.watch('canSelectPresent', () => {
    store.getPresentListCookie();
  });
  store.watch('provideData', (newVal) => {
    store.handleWatchProvideData(newVal);
  });
  store.watch('presentData', () => {
    store.updatePresentInfo();
  });

  store.watch('computedGoodsGroupList', (newVal, oldVal) => {
    if (oldVal?.length > 0 || newVal?.length > 0) {
      store.updateLoadGoodsList();
    }
  });
}
