<template>
  <view>
    <van-share-sheet
      :show="isShowOrderSharePopup"
      title="立即分享给好友"
      :options="shareOptions"
      @cancel="toggleOrderSharePopup(false)"
      @close="toggleOrderSharePopup(false)"
      @select="onSelect"
    />

    <!-- 海报 -->
    <share-poster
      :visible="isShowOrderSharePoster"
      :share-url="shareUrlNoGuide"
      :order-no="orderNo"
      @close="toggleOrderSharePoster(false)"
    />
  </view>
</template>
<script>
/* *****************************************************
 * 分享能力，目前仅支持web端，如果后续支持weapp之后请将此注释移除
 * 另外，请注意需要移除index.ts中的widget多端编译条件
 ***************************************************** */
// Components
import SharePoster from '../components/SharePoster.vue';
import ShareSheet from '@youzan/vant-tee/dist/share-sheet/index.vue';

// Dependencies
import { mapState } from '@ranta/store';
import args from '@youzan/utils/url/args';
import navigate from '@youzan/tee-biz-navigate';
import { setClipboardData } from '@youzan/tee-api';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import {
  LOG_CLICK_WINDOW_BILL,
  LOG_CLICK_WINDOW_COPY,
  LOG_CLICK_WINDOW_FRIEND,
} from '../utils/log';

export default {
  components: {
    'share-poster': SharePoster,
    'van-share-sheet': ShareSheet,
  },

  data() {
    return {
      ...mapState(this, [
        'shareUrl',
        'listItem',
        'isShowOrderSharePopup',
        'isShowOrderSharePoster',
        'shareOptions',
      ]),
    };
  },

  computed: {
    orderNo() {
      return this.listItem.orderNo;
    },

    shareUrlNoGuide() {
      return this.shareUrl ? args.remove(this.shareUrl, 'show_share_guide') : '';
    },
  },

  methods: {
    onSelect(option) {
      const { key } = option;

      switch (key) {
        case 'copylink':
          this.handleCopy();
          break;

        case 'wechat':
        case 'toutiao':
          this.handleShareUrl();
          break;

        case 'haibao':
          this.handleHaibao();
          break;

        default:
          break;
      }
    },

    handleHaibao() {
      LOG_CLICK_WINDOW_BILL(this.ctx);
      this.toggleOrderSharePopup(false);
      this.toggleOrderSharePoster(true);
    },

    handleShareUrl() {
      LOG_CLICK_WINDOW_FRIEND(this.ctx);
      this.toggleOrderSharePopup(false);

      if (this.shareUrl) {
        setTimeout(() => {
          navigate({
            web: {
              type: 'safeLink',
              safeLink: {
                url: args.add(this.shareUrl, {
                  is_share: 1, // 分享标识
                }),
              },
            },
          });
        }, 100);
      }
    },

    handleCopy() {
      LOG_CLICK_WINDOW_COPY(this.ctx);
      this.toggleOrderSharePopup(false);

      setClipboardData(this.shareUrlNoGuide)
        .then(() => {
          Toast('复制成功');
        })
        .catch((e) => {
          console.warn(e);
          Toast('复制失败');
        });
    },

    toggleOrderSharePopup(isShow) {
      this.store.toggleOrderSharePopup({ isShow });
    },

    toggleOrderSharePoster(isShow) {
      this.store.toggleOrderSharePoster({ isShow });
    },
  },
};
</script>
