/* 自提联系人 */
import GetPhoneButton from '../widgets/GetPhoneButton.vue';

export const widgets = {
  GetPhoneButton,
};

export const watchPrepareData = (ctx, data) => {
  const contact = {};
  const { RESERVE_CONTACT_NUMBER: phone = '' } = data.tradeConfirmation.extra;
  contact.phone = phone; // 联系人电话
  try {
    const {
      contact_number_required: requirePhone = 0,
      reserve_contact_name: showName = 0,
      reserve_contact_number: showPhone = 0,
    } = JSON.parse(data.tradeConfirmation.extra.RETAIL_MINAPP_SHELF_ORDER_SETTING);
    contact.showName = +showName; // 是否显示联系人
    contact.showPhone = +showPhone; // 是否显示联系电话
    contact.requirePhone = +showPhone === 1 ? +requirePhone : 0; // 联系电话是否必填，前提显示联系电话
    if (+showPhone && !+showName && phone) {
      // 如果是只要展示电话并且接口返回了phone
      // 从下面的 selectRetailContact 内cp来的
      // reset字段 干啥用的也不记得了，就知道当初写这个的大哥是碰到了下单报错的问题加上的
      ctx.process.invoke('mutateState', (state) => {
        state.contact = {
          ...state.contact,
          id: null,
          userName: '',
          telephone: phone,
          reset: 1
        };
        return [];
      })
    }
  } catch {
    contact.showName = 0;
    contact.showPhone = 0;
    contact.requirePhone = 0;
  }
  if (contact.showName) contact.name = '';
  ctx.data.retailSelfFetchContact = contact;
};

export const initProcesses = (ctx) => {
  ctx.process.define('selectRetailContact', ({ userName, phone }) => {
    const { showName, showPhone } = ctx.data.retailSelfFetchContact;
    const contact = {
      ...ctx.data.state.contact,
      id: null,
      userName: '',
      telephone: '',
      reset: 1,
    };
    const retailSelfFetchContact = ctx.data.isRetailShelfPage ? { ...ctx.data.retailSelfFetchContact } : ctx.data.retailSelfFetchContact
    if (showName) {
      contact.userName = userName;
      // 见下面注释
      retailSelfFetchContact.name = userName;
    };
    if (showPhone) {
      contact.telephone = phone;
      // 注意此写法不会触发ranta响应式，避免影响GetPhoneButton组件
      // 且不会触发自提模块预留电话部分的retailSelfFetchContact setData
      // 且不影响后续校验电话
      // 不理解框架不要轻易改此代码
      retailSelfFetchContact.phone = phone;
    }
    ctx.data.retailSelfFetchContact = retailSelfFetchContact
    ctx.process.invoke('mutateState', (state) => {
      state.contact = contact;
      return [];
    });
  });
};
