import { requestV2 } from '@youzan/tee-biz-request';

const UPDATE_GOODS_NUM_URL = '/wsctrade/cart/updateCartGoodsNum.json';
const UPDATE_GOODS_SKU_URL = '/wsctrade/cart/reselect-goods.json';
const DELETE_GOODS_SINGLE_URL = '/wsctrade/cart/deleteGoods.json';
const DELETE_GOODS_BATCH_URL = '/wsctrade/cart/deleteBatchList.json';
const BATCH_SELECT_GOODS_URL = '/wsctrade/cart/batchSelectGoods.json';
const BATCH_UNSELECT_GOODS_URL = '/wsctrade/cart/batchUnselectGoods.json';
const SELECT_GOODS_URL = '/wsctrade/cart/selectGoods.json';
const UNSELECT_GOODS_URL = '/wsctrade/cart/unselectGoods.json';
const SELECT_ALL_GOODS_URL = '/wsctrade/cart/selectAllGoods.json';
const UNSELECT_ALL_GOODS_URL = '/wsctrade/cart/unselectAllGoods.json';

const fetchV2 = (requestConfig) => requestV2({ ...requestConfig, config: {} });

function pickIdFromGoods(goods) {
  const {
    kdtId,
    goodsId,
    skuId,
    activityId = 0,
    cartId = null,
    activityAlias = '',
    activityType,
  } = goods;
  const data = {
    kdtId,
    goodsId,
    skuId,
    activityId,
    cartId,
    activityAlias,
    activityType,
  };

  ['storeId', 'channelId', 'canyinId'].forEach((key) => {
    if (goods[key] > 0) {
      data[key] = goods[key];
    }
  });

  return data;
}

/** 设置商品数量 */
export function setGoodsNum(goods) {
  // 支持商品套餐
  const { num, combo } = goods;
  return fetchV2({
    path: UPDATE_GOODS_NUM_URL,
    data: {
      ...pickIdFromGoods(goods),
      num,
      combo,
    },
    method: 'POST',
  });
}

/** 设置商品sku */
export function reselectGoods(goods) {
  const params = {
    ...pickIdFromGoods(goods),
    messages: goods.messages,
    propertyIds: goods.propertyIds,
    extraAttribute: goods.extraAttribute, // 扩展信息
  };

  // 组合套餐要加上combo数据
  if (goods.combo) {
    params.combo = goods.combo;
  }

  // 周期购
  if (typeof goods.deliverTime === 'number') {
    params.deliverTime = goods.deliverTime;
  }

  return fetchV2({
    path: UPDATE_GOODS_SKU_URL,
    data: params,
    method: 'POST',
  });
}

/** 批量删除 */
export function batchDeleteGoods(goodsList) {
  const ids = (goodsList || []).map((goods) => ({
    ...pickIdFromGoods(goods),
    num: goods.num,
  }));
  return fetchV2({
    path: DELETE_GOODS_BATCH_URL,
    method: 'POST',
    data: {
      ids,
    },
  });
}

/** 批零勾选或取消商品 */
export function batchUpdateSelectGoods(params) {
  const { goodsList, type } = params;
  const list = (goodsList || []).map((good) => ({
    ...pickIdFromGoods(good),
  }));

  return fetchV2({
    path: type === 'add' ? BATCH_SELECT_GOODS_URL : BATCH_UNSELECT_GOODS_URL,
    method: 'POST',
    data: {
      goodsList: list,
    },
  });
}

/** 单个勾选或取消商品 */
export function updateSelectGoods(params) {
  const { goods, type } = params;
  return fetchV2({
    path: type === 'add' ? SELECT_GOODS_URL : UNSELECT_GOODS_URL,
    method: 'POST',
    data: pickIdFromGoods(goods),
  });
}

/** 店铺全部商品勾选和取消 */
export function updateSelectAllGoods(params) {
  const { kdtId, type } = params;
  const data = {
    kdtId,
  };

  return fetchV2({
    path: type === 'add' ? SELECT_ALL_GOODS_URL : UNSELECT_ALL_GOODS_URL,
    method: 'POST',
    data,
  });
}

/** 删除商品 */
export function deleteGoods({ goods, ...others }) {
  const { num } = goods;
  return fetchV2({
    path: DELETE_GOODS_SINGLE_URL,
    data: {
      ...others,
      ...pickIdFromGoods(goods),
      num,
    },
    method: 'POST',
  });
}

/** 获取商品skus信息 */
export function getGoodSkusJson(data) {
  return fetchV2({
    data,
    path: '/wsctrade/fetch-sku.json',
  });
}
