<template>
  <!-- 保存到本地浮层 -->
  <long-click-save-image
    v-if="imageUrl"
    :width="300"
    :src="imageUrl"
    :visible="showSaveImage"
    @hide="onHide"
  />
</template>

<script>
import { mapEvent } from '@youzan/ranta-helper';
import LongClickSaveImage from './components/LongClickSaveImage.vue';
import fullfillImage from '@youzan/utils/url/fullfillImage';

export default {
  name: 'long-click-save-image-dialog',

  components: {
    'long-click-save-image': LongClickSaveImage,
  },

  data() {
    return {
      imageUrl: '',
      showSaveImage: false,
    };
  },

  mounted() {
    mapEvent(this, {
      showSaveImageDialog: (imgUrl) => {
        this.imageUrl = fullfillImage(imgUrl, 'large');
        this.showSaveImage = true;
      },
    });
  },

  methods: {
    onHide() {
      this.showSaveImage = false;
    },
  },
};
</script>
