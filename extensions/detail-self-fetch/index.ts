import Main from './Main.vue';
import SelfFetch from './components/SelfFetch.vue';
import { bridge } from '@youzan/ranta-helper';
import SelfFetchWrapper from './SelfFetchWrapper.vue';

export default class SelfFetchExtension {
  ctx: any;

  /**
   * setShowSelfFetch
   * @deprecated 从 2.0 开始
   * @desc 设置显示自提码等信息，目前只有给医药商品用，因为医药商品会隐藏自提码区域，但是三方又想显示自提码
   */
  @bridge('setShowSelfFetch', 'process')
  setShowSelfFetch(payload: boolean) {
    this.ctx.process.invoke('setShowSelfFetch', !!payload);
  }

  constructor(options) {
    this.ctx = options.ctx;
  }

  static widgets = {
    Main,
    SelfFetch,
    SelfFetchWrapper,
  };
}
