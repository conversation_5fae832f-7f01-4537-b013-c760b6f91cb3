function parseJSON(data, defaultValue = {}) {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (e) {
      return defaultValue;
    }
  }
  return defaultValue;
}

export default function ({ state }) {
  const {
    extensions: {
      AGGREGATED_PAY_TYPE: aggregatedPayType,
      AGGREGATED_PAY_INFO: aggregatedPayInfo,
    } = {},
  } = state;

  let payInfo = null;
  if (aggregatedPayType === 'RECHARGE') {
    const afterParsed = parseJSON(aggregatedPayInfo, null);
    payInfo = Array.isArray(afterParsed) ? afterParsed[0] : afterParsed;
  }
  return {
    // 充值信息
    rechargeInfo:
      aggregatedPayType === 'RECHARGE'
        ? {
            origin: aggregatedPayInfo,
            ...payInfo,
          }
        : null,
  };
}
