import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import get from '@youzan/utils/object/get';

// 获取属性值 id 数组
export function getGoodsPropertiesIds(properties = []) {
  const propertiesIds = [];

  mapKeysCase.toCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesIds.push(currentValue.propValueId);
    });
  });

  return propertiesIds;
}

// 获取商品属性
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  properties.forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

/**
 * 把sku 组件获取的数据格式化成下面格式
[
  {
    "prop_id": 131127,
    "prop_name": "杯型",
    "prop_value_list": [
      {
          "prop_value_id": 571382,
          "prop_value_name": "大杯"
      }
    ]
  }
]
 */
export function parseGoodsProperties(properties = []) {
  return mapKeysCase.toCamelCase(properties).map((property) => {
    return {
      propId: property.kId,
      propName: property.k,
      propValueList: (property.v || []).map((value) => ({
        propValueId: value.id,
        propValueName: value.name,
      })),
    };
  });
}

/**
 * 获取选中的 PropertyIds
 * @param {Array} properties
 * @return {Array} propertyIds
 */
export function parseGoodsPropertyIdsByProperties(properties = []) {
  return (
    properties
      ?.map((p) => p?.propValueList)
      .flat()
      .map((i) => i?.propValueId)
      .filter(Boolean) || []
  );
}

// 取得商品在当前店铺商品列表的索引
export function whereCurrentGoodsInList(list, goods) {
  const currentIndex = (list || []).findIndex((item) => {
    if (goods.cartId && item.cartId) {
      return goods.cartId === item.cartId;
    }
    // 兼容有赞云
    // 兼容没有activity_id  的情况
    item.activityId = item.activityId || 0;
    return (
      item.goodsId === goods.goodsId &&
      item.skuId === goods.skuId &&
      item.canyinId === goods.canyinId &&
      item.activityId === goods.activityId
    );
  });

  return currentIndex;
}

// 将选中sku格式化为 [{ k_s: xx, v_id: xx }]
export const getSkuData = (comb) => {
  return Object.keys(comb)
    .filter((key) => /^s\d$/.test(key))
    .reduce((selectedValues, skuKeyStr) => {
      if (+comb[skuKeyStr]) {
        selectedValues.push({
          kS: skuKeyStr,
          vId: comb[skuKeyStr],
        });
      }
      return selectedValues;
    }, []);
};

export const normalizeSkuTree = (skuTree = []) => {
  const normalizedTree = {};
  skuTree.forEach((treeItem) => {
    normalizedTree[treeItem.kS] = treeItem.v;
  });
  return normalizedTree;
};

// 获取已选择的sku名称
export const getSelectedSkuFromSkuData = (skuTree, selectedSku) => {
  const normalizedTree = normalizeSkuTree(skuTree);
  return Object.keys(selectedSku)
    .filter((key) => /^s\d$/.test(key))
    .reduce((selectedValues, skuKeyStr) => {
      const skuValues = normalizedTree[skuKeyStr];
      const skuValueId = selectedSku[skuKeyStr];

      if (+skuValueId) {
        const skuValue = skuValues.filter((value) => +value.id === +skuValueId)[0];
        skuValue && selectedValues.push(skuValue);
      }
      return selectedValues;
    }, []);
};

/**
 *
 * @param {Array} propList 商品属性数据
 * @param {Array} selectedPropIds 已选属性Id列表
 * @return {number} 属性加价（分）
 */
export function getPropPrice(propTree = [], selectedPropIds = []) {
  let price = 0;
  const propList = propTree?.map((i) => i.v).flat();
  propList.forEach((prop) => {
    if (selectedPropIds.includes(prop?.id)) price += prop.price;
  });
  return parseInt(Math.round(price), 10) || 0;
}
